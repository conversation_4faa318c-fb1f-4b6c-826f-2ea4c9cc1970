<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Multi-DEX Arbitrage Bot Dashboard</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #0a0e1a;
            color: #e0e6ed;
            line-height: 1.6;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #1e3a8a, #3b82f6);
            border-radius: 10px;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .card {
            background: #1f2937;
            border-radius: 10px;
            padding: 20px;
            border: 1px solid #374151;
        }

        .card h3 {
            color: #60a5fa;
            margin-bottom: 15px;
            font-size: 1.3em;
        }

        .stat-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding: 8px 0;
            border-bottom: 1px solid #374151;
        }

        .stat-label {
            color: #9ca3af;
        }

        .stat-value {
            font-weight: bold;
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-running {
            background: #10b981;
            box-shadow: 0 0 10px #10b981;
        }

        .status-stopped {
            background: #ef4444;
            box-shadow: 0 0 10px #ef4444;
        }

        .price-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }

        .price-table th,
        .price-table td {
            padding: 10px;
            text-align: left;
            border-bottom: 1px solid #374151;
        }

        .price-table th {
            background: #374151;
            color: #60a5fa;
        }

        .opportunity {
            background: #065f46;
            border: 1px solid #10b981;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 10px;
        }

        .opportunity-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .opportunity-pair {
            font-size: 1.2em;
            font-weight: bold;
            color: #10b981;
        }

        .opportunity-profit {
            font-size: 1.1em;
            color: #fbbf24;
        }

        .opportunity-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            font-size: 0.9em;
        }

        .refresh-btn {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 1em;
            margin-bottom: 20px;
        }

        .refresh-btn:hover {
            background: #2563eb;
        }

        .logs {
            background: #111827;
            border-radius: 10px;
            padding: 20px;
            max-height: 400px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
        }

        .log-entry {
            margin-bottom: 5px;
            padding: 5px;
            border-radius: 3px;
        }

        .log-success {
            background: rgba(16, 185, 129, 0.1);
            color: #10b981;
        }

        .log-error {
            background: rgba(239, 68, 68, 0.1);
            color: #ef4444;
        }

        .log-info {
            color: #60a5fa;
        }

        @media (max-width: 768px) {
            .grid {
                grid-template-columns: 1fr;
            }
            
            .opportunity-details {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 Multi-DEX Arbitrage Bot</h1>
            <p>Monitoring WETH/USDC, WETH/DAI, USDC/DAI across Uniswap V3, Curve & SushiSwap</p>
        </div>

        <button class="refresh-btn" onclick="refreshData()">🔄 Refresh Data</button>

        <div class="grid">
            <!-- Bot Status -->
            <div class="card">
                <h3>🤖 Bot Status</h3>
                <div class="stat-row">
                    <span class="stat-label">Status:</span>
                    <span class="stat-value" id="bot-status">
                        <span class="status-indicator status-stopped"></span>Loading...
                    </span>
                </div>
                <div class="stat-row">
                    <span class="stat-label">Uptime:</span>
                    <span class="stat-value" id="uptime">-</span>
                </div>
                <div class="stat-row">
                    <span class="stat-label">Monitored Pairs:</span>
                    <span class="stat-value" id="monitored-pairs">-</span>
                </div>
                <div class="stat-row">
                    <span class="stat-label">Cached Prices:</span>
                    <span class="stat-value" id="cached-prices">-</span>
                </div>
            </div>

            <!-- Performance Stats -->
            <div class="card">
                <h3>📊 Performance</h3>
                <div class="stat-row">
                    <span class="stat-label">Opportunities Found:</span>
                    <span class="stat-value" id="total-opportunities">-</span>
                </div>
                <div class="stat-row">
                    <span class="stat-label">Successful Arbitrages:</span>
                    <span class="stat-value" id="successful-arbitrages">-</span>
                </div>
                <div class="stat-row">
                    <span class="stat-label">Success Rate:</span>
                    <span class="stat-value" id="success-rate">-</span>
                </div>
                <div class="stat-row">
                    <span class="stat-label">Total Profit:</span>
                    <span class="stat-value" id="total-profit">-</span>
                </div>
                <div class="stat-row">
                    <span class="stat-label">Average Profit:</span>
                    <span class="stat-value" id="average-profit">-</span>
                </div>
            </div>

            <!-- Risk Management -->
            <div class="card">
                <h3>⚠️ Risk Management</h3>
                <div class="stat-row">
                    <span class="stat-label">Consecutive Failures:</span>
                    <span class="stat-value" id="consecutive-failures">-</span>
                </div>
                <div class="stat-row">
                    <span class="stat-label">Daily Loss:</span>
                    <span class="stat-value" id="daily-loss">-</span>
                </div>
                <div class="stat-row">
                    <span class="stat-label">Execution Status:</span>
                    <span class="stat-value" id="execution-status">-</span>
                </div>
            </div>
        </div>

        <!-- Current Prices -->
        <div class="card">
            <h3>💰 Current Prices</h3>
            <table class="price-table">
                <thead>
                    <tr>
                        <th>Pair</th>
                        <th>Uniswap V3</th>
                        <th>SushiSwap</th>
                        <th>Curve</th>
                        <th>Best Spread</th>
                    </tr>
                </thead>
                <tbody id="price-table-body">
                    <tr>
                        <td colspan="5">Loading prices...</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- Active Opportunities -->
        <div class="card">
            <h3>🎯 Active Opportunities</h3>
            <div id="opportunities-container">
                <p>No active opportunities</p>
            </div>
        </div>

        <!-- Logs -->
        <div class="card">
            <h3>📝 Recent Activity</h3>
            <div class="logs" id="logs-container">
                <div class="log-entry log-info">Bot dashboard loaded</div>
            </div>
        </div>
    </div>

    <script>
        let logCounter = 0;
        const maxLogs = 50;

        function addLog(message, type = 'info') {
            const logsContainer = document.getElementById('logs-container');
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry log-${type}`;
            logEntry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            
            logsContainer.insertBefore(logEntry, logsContainer.firstChild);
            
            // Remove old logs
            while (logsContainer.children.length > maxLogs) {
                logsContainer.removeChild(logsContainer.lastChild);
            }
        }

        async function refreshData() {
            try {
                const response = await fetch('/api/status');
                const data = await response.json();
                
                updateBotStatus(data);
                updatePrices(data.prices);
                
                addLog('Data refreshed successfully', 'success');
            } catch (error) {
                addLog(`Failed to refresh data: ${error.message}`, 'error');
            }
        }

        function updateBotStatus(data) {
            // Bot status
            const statusElement = document.getElementById('bot-status');
            const statusClass = data.isRunning ? 'status-running' : 'status-stopped';
            const statusText = data.isRunning ? 'Running' : 'Stopped';
            statusElement.innerHTML = `<span class="status-indicator ${statusClass}"></span>${statusText}`;

            // Stats
            document.getElementById('uptime').textContent = formatUptime(data.stats.uptime);
            document.getElementById('monitored-pairs').textContent = data.pairs.length;
            document.getElementById('cached-prices').textContent = data.monitoring.cachedPrices;
            document.getElementById('total-opportunities').textContent = data.stats.totalOpportunities;
            document.getElementById('successful-arbitrages').textContent = data.stats.successfulArbitrages;
            document.getElementById('success-rate').textContent = `${data.stats.successRate.toFixed(1)}%`;
            document.getElementById('total-profit').textContent = `${data.stats.totalProfit} ETH`;
            document.getElementById('average-profit').textContent = `${data.stats.averageProfit.toFixed(4)} ETH`;
            document.getElementById('consecutive-failures').textContent = data.execution.consecutiveFailures;
            document.getElementById('daily-loss').textContent = `${data.execution.dailyLoss} ETH`;
            document.getElementById('execution-status').textContent = data.execution.isExecuting ? 'Executing' : 'Idle';
        }

        function updatePrices(prices) {
            const tbody = document.getElementById('price-table-body');
            tbody.innerHTML = '';

            const pairs = ['WETH/USDC', 'WETH/DAI', 'USDC/DAI'];
            
            pairs.forEach(pair => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${pair}</td>
                    <td>-</td>
                    <td>-</td>
                    <td>-</td>
                    <td>-</td>
                `;
                tbody.appendChild(row);
            });
        }

        function formatUptime(ms) {
            const seconds = Math.floor(ms / 1000);
            const minutes = Math.floor(seconds / 60);
            const hours = Math.floor(minutes / 60);
            
            if (hours > 0) {
                return `${hours}h ${minutes % 60}m`;
            } else if (minutes > 0) {
                return `${minutes}m ${seconds % 60}s`;
            } else {
                return `${seconds}s`;
            }
        }

        // Auto-refresh every 5 seconds
        setInterval(refreshData, 5000);

        // Initial load
        refreshData();
    </script>
</body>
</html>
