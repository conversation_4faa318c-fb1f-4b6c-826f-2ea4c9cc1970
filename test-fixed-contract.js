const { ethers } = require('ethers');

async function testFixedContract() {
    console.log('🧪 Testing fixed contract on Hardhat fork...');
    
    // Connect to Hardhat fork
    const provider = new ethers.JsonRpcProvider('http://127.0.0.1:8547');
    const wallet = new ethers.Wallet('0xac0974bec39a17e36ba4a6b4d238ff944bacb478cbed5efcae784d7bf4f2ff80', provider);
    
    // Fixed contract address
    const contractAddress = '******************************************';
    
    console.log(`📋 Test Configuration:`);
    console.log(`   Provider: Hardhat fork`);
    console.log(`   Contract: ${contractAddress}`);
    console.log(`   Wallet: ${wallet.address}`);
    console.log(`   Balance: ${ethers.formatEther(await provider.getBalance(wallet.address))} ETH`);
    
    // Contract interface
    const contractInterface = [
        'function executeOptimalFlashloan(address asset, uint256 amount, bytes calldata params) external',
        'function checkProfitability(address asset, uint256 amount, bytes calldata params) external view returns (bool, uint256, uint256)',
        'function supportedRouters(address) external view returns (bool)',
        'function UNISWAP_V2_ROUTER() external view returns (address)',
        'function UNISWAP_V3_ROUTER() external view returns (address)'
    ];
    
    const contract = new ethers.Contract(contractAddress, contractInterface, wallet);
    
    // Get router addresses
    const v2Router = await contract.UNISWAP_V2_ROUTER();
    const v3Router = await contract.UNISWAP_V3_ROUTER();
    
    console.log(`   V2 Router: ${v2Router}`);
    console.log(`   V3 Router: ${v3Router}`);
    
    // Check router support
    const v2Supported = await contract.supportedRouters(v2Router);
    const v3Supported = await contract.supportedRouters(v3Router);
    
    console.log(`   V2 Supported: ${v2Supported ? '✅' : '❌'}`);
    console.log(`   V3 Supported: ${v3Supported ? '✅' : '❌'}`);
    
    // Token addresses
    const wethAddress = '******************************************';
    const daiAddress = '******************************************';
    
    console.log('\n🧪 Test 1: Same transaction that was failing');
    
    // Create the same parameters that were failing
    const failingParams = ethers.AbiCoder.defaultAbiCoder().encode(
        ['address[]', 'address[]', 'address', 'address', 'uint24[]', 'uint256', 'uint8', 'uint256', 'uint256'],
        [
            [wethAddress, daiAddress],               // buyPath: WETH → DAI
            [daiAddress, wethAddress],               // sellPath: DAI → WETH
            v3Router,                                // buyDex (V3)
            v2Router,                                // sellDex (V2)
            [3000],                                  // v3Fees (0.3%)
            ethers.parseEther('0.00001'),            // minProfit
            0,                                       // provider (AAVE)
            1000,                                    // slippageToleranceBps (10%)
            ethers.parseUnits('200', 'gwei')         // maxGasCostWei
        ]
    );
    
    try {
        // Test with profitability check first
        console.log('   Testing profitability check...');
        const [profitable, expectedProfit, gasEstimate] = await contract.checkProfitability(
            wethAddress,
            ethers.parseEther('2.0'),
            failingParams
        );
        
        console.log(`   ✅ Profitability check passed:`);
        console.log(`      Profitable: ${profitable}`);
        console.log(`      Expected profit: ${ethers.formatEther(expectedProfit)} ETH`);
        console.log(`      Gas estimate: ${ethers.formatUnits(gasEstimate, 'gwei')} gwei`);
        
    } catch (error) {
        console.log(`   ❌ Profitability check failed: ${error.message.split('(')[0]}`);
    }
    
    try {
        // Test with static call (validation)
        console.log('   Testing static call validation...');
        await contract.executeOptimalFlashloan.staticCall(
            wethAddress,
            ethers.parseEther('2.0'),
            failingParams
        );
        
        console.log(`   ✅ Static call validation passed!`);
        console.log(`   💡 The V3 fees validation fix worked!`);
        
    } catch (error) {
        const errorMsg = error.message;
        console.log(`   ❌ Static call failed: ${errorMsg.split('(')[0]}`);
        
        if (errorMsg.includes('require(false)')) {
            console.log(`      💡 Still getting require(false) - need more investigation`);
        } else if (errorMsg.includes('Arbitrage resulted in loss')) {
            console.log(`      ✅ Validation passed! Got expected arbitrage loss error`);
        } else if (errorMsg.includes('Invalid V3 fees')) {
            console.log(`      ❌ V3 fees validation still failing`);
        } else {
            console.log(`      💡 Different error - progress made!`);
        }
    }
    
    console.log('\n🧪 Test 2: Simple valid transaction');
    
    // Test with a simpler, more likely to succeed transaction
    const simpleParams = ethers.AbiCoder.defaultAbiCoder().encode(
        ['address[]', 'address[]', 'address', 'address', 'uint24[]', 'uint256', 'uint8', 'uint256', 'uint256'],
        [
            [wethAddress, daiAddress],               // buyPath: WETH → DAI
            [daiAddress, wethAddress],               // sellPath: DAI → WETH
            v3Router,                                // buyDex (V3)
            v2Router,                                // sellDex (V2)
            [3000],                                  // v3Fees (0.3%)
            ethers.parseEther('0.001'),              // minProfit (higher)
            0,                                       // provider (AAVE)
            500,                                     // slippageToleranceBps (5%)
            ethers.parseUnits('100', 'gwei')         // maxGasCostWei (lower)
        ]
    );
    
    try {
        console.log('   Testing simple transaction...');
        await contract.executeOptimalFlashloan.staticCall(
            wethAddress,
            ethers.parseEther('0.1'), // Smaller amount
            simpleParams
        );
        
        console.log(`   ✅ Simple transaction validation passed!`);
        
    } catch (error) {
        const errorMsg = error.message;
        console.log(`   Result: ${errorMsg.split('(')[0]}`);
        
        if (errorMsg.includes('Arbitrage resulted in loss')) {
            console.log(`   ✅ Contract validation working correctly!`);
        }
    }
    
    console.log('\n🧪 Test 3: Invalid parameters (should fail)');
    
    // Test with invalid parameters to ensure validation still works
    const invalidParams = ethers.AbiCoder.defaultAbiCoder().encode(
        ['address[]', 'address[]', 'address', 'address', 'uint24[]', 'uint256', 'uint8', 'uint256', 'uint256'],
        [
            [wethAddress],                           // buyPath: Too short!
            [daiAddress, wethAddress],               // sellPath: DAI → WETH
            v3Router,                                // buyDex (V3)
            v2Router,                                // sellDex (V2)
            [3000],                                  // v3Fees (0.3%)
            ethers.parseEther('0.001'),              // minProfit
            0,                                       // provider (AAVE)
            500,                                     // slippageToleranceBps (5%)
            ethers.parseUnits('100', 'gwei')         // maxGasCostWei
        ]
    );
    
    try {
        console.log('   Testing invalid parameters (should fail)...');
        await contract.executeOptimalFlashloan.staticCall(
            wethAddress,
            ethers.parseEther('0.1'),
            invalidParams
        );
        
        console.log(`   ❌ Invalid parameters were accepted! Validation not working.`);
        
    } catch (error) {
        const errorMsg = error.message;
        if (errorMsg.includes('Buy path too short')) {
            console.log(`   ✅ Validation correctly rejected invalid parameters`);
        } else {
            console.log(`   Result: ${errorMsg.split('(')[0]}`);
        }
    }
    
    console.log('\n🏁 Fixed contract testing completed!');
    
    console.log('\n📊 SUMMARY:');
    console.log('   The fixed contract should now handle V3 fees validation correctly.');
    console.log('   If tests pass, we can deploy to mainnet and update the bot.');
}

testFixedContract().catch(console.error);
