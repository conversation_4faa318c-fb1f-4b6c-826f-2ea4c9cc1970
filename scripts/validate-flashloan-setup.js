const { ethers } = require('ethers');
const fs = require('fs');
const path = require('path');

// Load configuration
require('dotenv').config();

async function validateFlashloanSetup() {
  console.log('🔍 Comprehensive Flashloan Attack Validation');
  console.log('=' .repeat(60));

  const issues = [];
  const warnings = [];

  try {
    // 1. Configuration Validation
    console.log('\n1. 📋 Configuration Validation');
    
    if (process.env.DRY_RUN === 'true') {
      issues.push('DRY_RUN is enabled - no real transactions will be executed');
    } else {
      console.log('✅ DRY_RUN disabled - live trading enabled');
    }

    if (!process.env.ENABLE_FLASHLOAN_ATTACKS || process.env.ENABLE_FLASHLOAN_ATTACKS !== 'true') {
      issues.push('ENABLE_FLASHLOAN_ATTACKS is not enabled');
    } else {
      console.log('✅ Flashloan attacks enabled');
    }

    if (!process.env.HYBRID_FLASHLOAN_CONTRACT) {
      issues.push('HYBRID_FLASHLOAN_CONTRACT address not configured');
    } else {
      console.log('✅ Hybrid flashloan contract configured:', process.env.HYBRID_FLASHLOAN_CONTRACT);
    }

    // 2. Network and RPC Validation
    console.log('\n2. 🌐 Network and RPC Validation');
    
    const provider = new ethers.JsonRpcProvider(process.env.RPC_URL);
    
    try {
      const network = await provider.getNetwork();
      const blockNumber = await provider.getBlockNumber();
      console.log('✅ RPC connection successful');
      console.log(`   Network: ${network.name} (Chain ID: ${network.chainId})`);
      console.log(`   Current block: ${blockNumber}`);
      
      if (network.chainId.toString() !== process.env.CHAIN_ID) {
        issues.push(`Chain ID mismatch: RPC reports ${network.chainId}, config expects ${process.env.CHAIN_ID}`);
      }
    } catch (error) {
      issues.push(`RPC connection failed: ${error.message}`);
    }

    // 3. Wallet Validation
    console.log('\n3. 💰 Wallet Validation');
    
    if (!process.env.PRIVATE_KEY) {
      issues.push('PRIVATE_KEY not configured');
    } else {
      try {
        const wallet = new ethers.Wallet(process.env.PRIVATE_KEY, provider);
        const balance = await provider.getBalance(wallet.address);
        const balanceEth = Number(ethers.formatEther(balance));
        
        console.log('✅ Wallet loaded successfully');
        console.log(`   Address: ${wallet.address}`);
        console.log(`   Balance: ${balanceEth.toFixed(4)} ETH`);
        
        if (balanceEth < 0.05) {
          warnings.push(`Low ETH balance: ${balanceEth.toFixed(4)} ETH - consider adding more for gas`);
        }
        
        if (balanceEth < 0.01) {
          issues.push(`Insufficient ETH balance: ${balanceEth.toFixed(4)} ETH - need at least 0.01 ETH for gas`);
        }
      } catch (error) {
        issues.push(`Invalid private key: ${error.message}`);
      }
    }

    // 4. Contract Validation
    console.log('\n4. 📜 Contract Validation');
    
    if (process.env.HYBRID_FLASHLOAN_CONTRACT) {
      try {
        const contractAddress = process.env.HYBRID_FLASHLOAN_CONTRACT;
        const code = await provider.getCode(contractAddress);
        
        if (code === '0x') {
          issues.push(`Hybrid flashloan contract not deployed at ${contractAddress}`);
        } else {
          console.log('✅ Hybrid flashloan contract deployed');
          console.log(`   Address: ${contractAddress}`);
          console.log(`   Code size: ${code.length} bytes`);
          
          // Test contract interface
          const contractInterface = new ethers.Interface([
            'function executeOptimalFlashloan(address asset, uint256 amount, bytes calldata params) external',
            'function owner() external view returns (address)'
          ]);
          
          try {
            const contract = new ethers.Contract(contractAddress, contractInterface, provider);
            const owner = await contract.owner();
            console.log(`   Owner: ${owner}`);
            
            const wallet = new ethers.Wallet(process.env.PRIVATE_KEY, provider);
            if (owner.toLowerCase() !== wallet.address.toLowerCase()) {
              issues.push(`Contract owner (${owner}) does not match wallet address (${wallet.address})`);
            } else {
              console.log('✅ Contract ownership verified');
            }
          } catch (error) {
            warnings.push(`Could not verify contract interface: ${error.message}`);
          }
        }
      } catch (error) {
        issues.push(`Error checking contract: ${error.message}`);
      }
    }

    // 5. Token Configuration Validation
    console.log('\n5. 🪙 Token Configuration Validation');
    
    const primaryToken = process.env.FLASHLOAN_PRIMARY_TOKEN || 'WETH';
    const targetTokens = (process.env.FLASHLOAN_TARGET_TOKENS || 'USDC,USDT,DAI').split(',');
    
    console.log(`   Primary token: ${primaryToken}`);
    console.log(`   Target tokens: ${targetTokens.join(', ')}`);
    
    // Check token addresses based on network
    const isMainnet = process.env.CHAIN_ID === '1';
    const tokenAddresses = {
      mainnet: {
        WETH: '******************************************',
        USDC: '******************************************',
        USDT: '******************************************',
        DAI: '******************************************'
      },
      sepolia: {
        WETH: '******************************************',
        USDC: '******************************************',
        USDT: '******************************************',
        DAI: '******************************************'
      }
    };
    
    const addresses = isMainnet ? tokenAddresses.mainnet : tokenAddresses.sepolia;
    
    for (const token of [primaryToken, ...targetTokens]) {
      if (!addresses[token]) {
        warnings.push(`Token ${token} address not configured for ${isMainnet ? 'mainnet' : 'sepolia'}`);
      } else {
        console.log(`   ${token}: ${addresses[token]}`);
      }
    }

    // 6. DEX Configuration Validation
    console.log('\n6. 🔄 DEX Configuration Validation');
    
    const dexPairs = (process.env.FLASHLOAN_DEX_PAIRS || 'UNISWAP_V2,UNISWAP_V3').split(',');
    const buyDex = process.env.FLASHLOAN_BUY_DEX || 'UNISWAP_V2';
    const sellDex = process.env.FLASHLOAN_SELL_DEX || 'UNISWAP_V3';
    
    console.log(`   DEX pairs: ${dexPairs.join(', ')}`);
    console.log(`   Buy DEX: ${buyDex}`);
    console.log(`   Sell DEX: ${sellDex}`);
    
    if (dexPairs.length < 2) {
      issues.push('Need at least 2 DEXs for arbitrage opportunities');
    }
    
    if (!dexPairs.includes(buyDex)) {
      issues.push(`Buy DEX ${buyDex} not in configured DEX pairs`);
    }
    
    if (!dexPairs.includes(sellDex)) {
      issues.push(`Sell DEX ${sellDex} not in configured DEX pairs`);
    }

    // 7. Profit Threshold Validation
    console.log('\n7. 💹 Profit Threshold Validation');
    
    const minProfitWei = process.env.MIN_PROFIT_WEI || '2985000000000000';
    const minProfitEth = Number(ethers.formatEther(minProfitWei));
    
    console.log(`   Minimum profit: ${minProfitEth.toFixed(6)} ETH (~$${(minProfitEth * 3350).toFixed(2)})`);
    
    if (minProfitEth < 0.001) {
      warnings.push(`Very low minimum profit threshold: ${minProfitEth.toFixed(6)} ETH`);
    }
    
    if (minProfitEth > 0.1) {
      warnings.push(`Very high minimum profit threshold: ${minProfitEth.toFixed(6)} ETH - may miss opportunities`);
    }

    // 8. Gas Configuration Validation
    console.log('\n8. ⛽ Gas Configuration Validation');
    
    const maxGasPrice = process.env.MAX_GAS_PRICE_GWEI || '10';
    const maxGasCost = process.env.MAX_GAS_COST_ETH || '0.005';
    
    console.log(`   Max gas price: ${maxGasPrice} gwei`);
    console.log(`   Max gas cost: ${maxGasCost} ETH`);
    
    if (Number(maxGasPrice) < 5) {
      warnings.push(`Low max gas price (${maxGasPrice} gwei) - transactions may not be included`);
    }
    
    if (Number(maxGasCost) > 0.02) {
      warnings.push(`High max gas cost (${maxGasCost} ETH) - may reduce profitability`);
    }

    // Summary
    console.log('\n' + '='.repeat(60));
    console.log('📊 VALIDATION SUMMARY');
    console.log('='.repeat(60));
    
    if (issues.length === 0) {
      console.log('✅ No critical issues found!');
    } else {
      console.log(`❌ ${issues.length} critical issue(s) found:`);
      issues.forEach((issue, i) => console.log(`   ${i + 1}. ${issue}`));
    }
    
    if (warnings.length > 0) {
      console.log(`⚠️  ${warnings.length} warning(s):`);
      warnings.forEach((warning, i) => console.log(`   ${i + 1}. ${warning}`));
    }
    
    console.log('\n🚀 NEXT STEPS:');
    if (issues.length > 0) {
      console.log('1. Fix all critical issues before running the bot');
      console.log('2. Review warnings and adjust configuration as needed');
      console.log('3. Test on testnet first before mainnet deployment');
    } else {
      console.log('1. Review warnings and adjust configuration as needed');
      console.log('2. Start with small amounts for initial testing');
      console.log('3. Monitor closely for the first few hours');
      console.log('4. Ready to run: npm run dev');
    }

  } catch (error) {
    console.error('❌ Validation failed:', error.message);
    process.exit(1);
  }
}

// Run validation
validateFlashloanSetup().catch(console.error);
