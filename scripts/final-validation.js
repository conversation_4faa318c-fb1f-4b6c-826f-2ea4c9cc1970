const { ethers } = require('ethers');

// Load configuration
require('dotenv').config();

/**
 * Final comprehensive validation of flashloan attack readiness
 */
async function finalValidation() {
  console.log('🔍 FINAL FLASHLOAN ATTACK VALIDATION');
  console.log('=' .repeat(60));
  console.log('Validating complete system readiness for mainnet execution');
  console.log('=' .repeat(60));

  const results = {
    critical: [],
    warnings: [],
    passed: []
  };

  try {
    const provider = new ethers.JsonRpcProvider(process.env.RPC_URL);
    const wallet = new ethers.Wallet(process.env.PRIVATE_KEY, provider);

    // 1. Configuration Validation
    console.log('\n1. ⚙️  CONFIGURATION VALIDATION');
    console.log('-'.repeat(40));

    // Check critical settings
    const criticalChecks = [
      { key: 'ENABLE_FLASHLOAN_ATTACKS', value: process.env.ENABLE_FLASHLOAN_ATTACKS, expected: 'true' },
      { key: 'DRY_RUN', value: process.env.DRY_RUN, expected: 'false' },
      { key: 'CHAIN_ID', value: process.env.CHAIN_ID, expected: '1' },
      { key: 'HYBRID_FLASHLOAN_CONTRACT', value: process.env.HYBRID_FLASHLOAN_CONTRACT, required: true }
    ];

    for (const check of criticalChecks) {
      if (check.expected && check.value !== check.expected) {
        results.critical.push(`${check.key} should be ${check.expected}, got ${check.value}`);
      } else if (check.required && !check.value) {
        results.critical.push(`${check.key} is required but not set`);
      } else {
        results.passed.push(`${check.key}: ✅`);
        console.log(`   ✅ ${check.key}: ${check.value}`);
      }
    }

    // 2. Network and Wallet Validation
    console.log('\n2. 🌐 NETWORK AND WALLET VALIDATION');
    console.log('-'.repeat(40));

    const network = await provider.getNetwork();
    const balance = await provider.getBalance(wallet.address);
    const balanceEth = Number(ethers.formatEther(balance));

    console.log(`   Network: ${network.name} (Chain ID: ${network.chainId})`);
    console.log(`   Wallet: ${wallet.address}`);
    console.log(`   Balance: ${balanceEth.toFixed(4)} ETH`);

    if (network.chainId.toString() !== process.env.CHAIN_ID) {
      results.critical.push(`Network mismatch: connected to ${network.chainId}, config expects ${process.env.CHAIN_ID}`);
    } else {
      results.passed.push('Network connection: ✅');
    }

    if (balanceEth < 0.01) {
      results.critical.push(`Insufficient ETH balance: ${balanceEth.toFixed(4)} ETH (need at least 0.01 ETH)`);
    } else if (balanceEth < 0.05) {
      results.warnings.push(`Low ETH balance: ${balanceEth.toFixed(4)} ETH (recommended: 0.05+ ETH)`);
    } else {
      results.passed.push('ETH balance: ✅');
    }

    // 3. Contract Validation
    console.log('\n3. 📜 CONTRACT VALIDATION');
    console.log('-'.repeat(40));

    const contractAddress = process.env.HYBRID_FLASHLOAN_CONTRACT;
    const code = await provider.getCode(contractAddress);

    if (code === '0x') {
      results.critical.push(`Contract not deployed at ${contractAddress}`);
    } else {
      console.log(`   ✅ Contract deployed at ${contractAddress}`);
      console.log(`   Code size: ${code.length} bytes`);

      // Test contract ownership
      try {
        const contract = new ethers.Contract(
          contractAddress,
          ['function owner() external view returns (address)'],
          provider
        );
        const owner = await contract.owner();
        
        if (owner.toLowerCase() === wallet.address.toLowerCase()) {
          results.passed.push('Contract ownership: ✅');
          console.log(`   ✅ Contract owner verified: ${owner}`);
        } else {
          results.critical.push(`Contract owner mismatch: ${owner} vs ${wallet.address}`);
        }
      } catch (error) {
        results.warnings.push(`Could not verify contract ownership: ${error.message}`);
      }
    }

    // 4. Market Data Validation
    console.log('\n4. 📊 MARKET DATA VALIDATION');
    console.log('-'.repeat(40));

    // Test pool data retrieval
    const v2PairAddress = '******************************************';
    const v3PoolAddress = '******************************************';

    try {
      // V2 pool test
      const v2Contract = new ethers.Contract(
        v2PairAddress,
        ['function getReserves() external view returns (uint112 reserve0, uint112 reserve1, uint32 blockTimestampLast)'],
        provider
      );
      const v2Reserves = await v2Contract.getReserves();
      
      // V3 pool test
      const v3Contract = new ethers.Contract(
        v3PoolAddress,
        ['function slot0() external view returns (uint160 sqrtPriceX96, int24 tick, uint16 observationIndex, uint16 observationCardinality, uint16 observationCardinalityNext, uint8 feeProtocol, bool unlocked)'],
        provider
      );
      const v3Slot0 = await v3Contract.slot0();

      console.log(`   ✅ V2 pool data: Reserve0=${ethers.formatUnits(v2Reserves.reserve0, 6)}, Reserve1=${ethers.formatUnits(v2Reserves.reserve1, 18)}`);
      console.log(`   ✅ V3 pool data: Tick=${v3Slot0.tick}, SqrtPrice=${v3Slot0.sqrtPriceX96.toString().slice(0, 20)}...`);
      
      results.passed.push('Pool data retrieval: ✅');
    } catch (error) {
      results.critical.push(`Pool data retrieval failed: ${error.message}`);
    }

    // 5. Opportunity Detection Logic Test
    console.log('\n5. 🎯 OPPORTUNITY DETECTION LOGIC TEST');
    console.log('-'.repeat(40));

    // Test with simulated profitable conditions
    const testSpread = 0.5; // 0.5% spread
    const testAmount = 5; // 5 WETH
    const grossProfit = testAmount * (testSpread / 100);
    const aaveFee = testAmount * 0.0009;
    const gasCost = 0.004; // 4000 gwei * 400k gas
    const netProfit = grossProfit - aaveFee - gasCost;

    console.log(`   Test scenario: ${testSpread}% spread, ${testAmount} WETH`);
    console.log(`   Gross profit: ${grossProfit.toFixed(6)} WETH`);
    console.log(`   Net profit: ${netProfit.toFixed(6)} WETH`);

    // Test confidence calculation
    const profitMargin = (netProfit / testAmount) * 100;
    let confidence = 0;
    confidence += Math.min(profitMargin * 10, 35);
    confidence += Math.min(netProfit * 15, 25);
    confidence += 10;
    confidence = Math.min(confidence, 100);

    console.log(`   Profit margin: ${profitMargin.toFixed(4)}%`);
    console.log(`   Confidence: ${confidence.toFixed(1)}%`);

    const minProfitThreshold = Number(ethers.formatEther(process.env.MIN_PROFIT_WEI || '2985000000000000'));
    const wouldExecute = netProfit > minProfitThreshold && confidence >= 70;

    console.log(`   Min threshold: ${minProfitThreshold.toFixed(6)} WETH`);
    console.log(`   Would execute: ${wouldExecute ? '✅ YES' : '❌ NO'}`);

    if (wouldExecute) {
      results.passed.push('Opportunity detection logic: ✅');
    } else {
      results.warnings.push('Opportunity detection requires higher spreads for execution');
    }

    // 6. Transaction Building Test
    console.log('\n6. 🔧 TRANSACTION BUILDING TEST');
    console.log('-'.repeat(40));

    try {
      const contractInterface = new ethers.Interface([
        'function executeOptimalFlashloan(address asset, uint256 amount, bytes calldata params) external'
      ]);

      const arbitrageParams = ethers.AbiCoder.defaultAbiCoder().encode(
        ['uint8', 'address', 'address', 'address', 'address', 'uint24', 'uint256'],
        [
          0, // Balancer
          '******************************************', // WETH
          '******************************************', // USDC
          '******************************************', // V2 Router
          '******************************************', // V3 Router
          3000, // Fee
          ethers.parseEther('0.001') // Min profit
        ]
      );

      const calldata = contractInterface.encodeFunctionData('executeOptimalFlashloan', [
        '******************************************',
        ethers.parseEther('5'),
        arbitrageParams
      ]);

      console.log(`   ✅ Parameter encoding successful`);
      console.log(`   ✅ Calldata generation successful (${calldata.length} chars)`);
      
      results.passed.push('Transaction building: ✅');
    } catch (error) {
      results.critical.push(`Transaction building failed: ${error.message}`);
    }

    // 7. Gas and Fee Analysis
    console.log('\n7. ⛽ GAS AND FEE ANALYSIS');
    console.log('-'.repeat(40));

    const feeData = await provider.getFeeData();
    const currentGasPrice = Number(ethers.formatUnits(feeData.gasPrice || 0n, 'gwei'));
    const maxGasPrice = Number(process.env.MAX_GAS_PRICE_GWEI || '10');

    console.log(`   Current gas price: ${currentGasPrice.toFixed(2)} gwei`);
    console.log(`   Max gas price: ${maxGasPrice} gwei`);

    if (currentGasPrice > maxGasPrice) {
      results.warnings.push(`High gas price: ${currentGasPrice.toFixed(2)} gwei > ${maxGasPrice} gwei`);
    } else {
      results.passed.push('Gas conditions: ✅');
    }

    // Final Summary
    console.log('\n' + '='.repeat(60));
    console.log('📋 FINAL VALIDATION SUMMARY');
    console.log('='.repeat(60));

    console.log(`\n✅ PASSED CHECKS (${results.passed.length}):`);
    results.passed.forEach(item => console.log(`   ${item}`));

    if (results.warnings.length > 0) {
      console.log(`\n⚠️  WARNINGS (${results.warnings.length}):`);
      results.warnings.forEach((item, i) => console.log(`   ${i + 1}. ${item}`));
    }

    if (results.critical.length > 0) {
      console.log(`\n❌ CRITICAL ISSUES (${results.critical.length}):`);
      results.critical.forEach((item, i) => console.log(`   ${i + 1}. ${item}`));
    }

    // Overall Status
    console.log('\n' + '='.repeat(60));
    if (results.critical.length === 0) {
      console.log('🎉 SYSTEM STATUS: READY FOR MAINNET EXECUTION');
      console.log('✅ All critical systems validated');
      console.log('✅ Flashloan attack logic is correct');
      console.log('✅ Contract deployment verified');
      console.log('✅ Opportunity detection working');
      console.log('✅ Transaction building functional');
      
      console.log('\n🚀 EXECUTION CONDITIONS:');
      console.log('• Bot will monitor for arbitrage opportunities 24/7');
      console.log('• Execution triggers when spread > 0.11% and confidence > 70%');
      console.log('• Balancer flashloans preferred (0% fees)');
      console.log('• Flashbots protection enabled for MEV resistance');
      console.log('• Minimum profit: ~$10 (0.003 ETH)');
      
      console.log('\n💡 READY TO START:');
      console.log('   npm run dev');
      
    } else {
      console.log('❌ SYSTEM STATUS: NOT READY - CRITICAL ISSUES FOUND');
      console.log('🛠️  Please fix critical issues before mainnet execution');
    }

  } catch (error) {
    console.error('❌ Validation failed:', error.message);
    console.error(error.stack);
  }
}

// Run validation
finalValidation().catch(console.error);
