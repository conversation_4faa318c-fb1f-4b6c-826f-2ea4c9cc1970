#!/bin/bash

# Production Mainnet Deployment Script
# This script deploys the MEV bot to mainnet with all safety checks

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

echo -e "${PURPLE}🚀 MEV Bot Mainnet Production Deployment${NC}"
echo "=========================================="
echo ""

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_critical() {
    echo -e "${RED}[CRITICAL]${NC} $1"
}

# Pre-deployment safety checks
echo -e "${YELLOW}🔒 PRODUCTION SAFETY CHECKS${NC}"
echo "================================"

# Check if we're in the right directory
if [ ! -f "package.json" ] || [ ! -f "hardhat.config.ts" ]; then
    print_error "Please run this script from the project root directory"
    exit 1
fi

# Check if production config exists
if [ ! -f ".env.mainnet-production" ]; then
    print_error "Production configuration file not found: .env.mainnet-production"
    exit 1
fi

# Backup current .env
if [ -f ".env" ]; then
    cp .env .env.backup
    print_status "Backed up current .env to .env.backup"
fi

# Load production configuration
cp .env.mainnet-production .env
print_status "Loaded production configuration"

# Check local mainnet node
print_status "Checking local mainnet node connection..."
if ! curl -s -X POST -H "Content-Type: application/json" \
   --data '{"jsonrpc":"2.0","method":"eth_chainId","params":[],"id":1}' \
   http://************:8545 > /dev/null 2>&1; then
    print_error "Local mainnet node not found at ************:8545"
    echo ""
    echo "Please ensure your local Ethereum mainnet node is running:"
    echo "  geth --http --http.api eth,net,web3 --ws --ws.api eth,net,web3"
    exit 1
fi

# Verify we're on mainnet
CHAIN_ID_HEX=$(curl -s -X POST -H "Content-Type: application/json" \
               --data '{"jsonrpc":"2.0","method":"eth_chainId","params":[],"id":1}' \
               http://************:8545 | grep -o '"result":"[^"]*"' | cut -d'"' -f4)

CHAIN_ID=$((CHAIN_ID_HEX))

if [ "$CHAIN_ID" != "1" ]; then
    print_critical "NOT CONNECTED TO MAINNET! Chain ID: $CHAIN_ID"
    print_critical "This script is for MAINNET deployment only"
    exit 1
fi

print_success "Connected to Ethereum Mainnet (Chain ID: 1)"

# Check node sync status
LATEST_BLOCK_HEX=$(curl -s -X POST -H "Content-Type: application/json" \
                   --data '{"jsonrpc":"2.0","method":"eth_blockNumber","params":[],"id":1}' \
                   http://************:8545 | grep -o '"result":"[^"]*"' | cut -d'"' -f4)

LATEST_BLOCK=$((LATEST_BLOCK_HEX))
print_status "Latest block: $LATEST_BLOCK"

if [ "$LATEST_BLOCK" -lt ******** ]; then
    print_warning "Node might not be fully synced (block: $LATEST_BLOCK)"
    read -p "Continue anyway? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
fi

# Check private key is set
if ! grep -q "PRIVATE_KEY=0x" .env || grep -q "your_production_private_key_here" .env; then
    print_critical "Production private key not set in .env file"
    print_critical "Please update PRIVATE_KEY in .env.mainnet-production"
    exit 1
fi

# Check account balance
DEPLOYER_ADDRESS=$(node -e "
const ethers = require('ethers');
const privateKey = process.env.PRIVATE_KEY || require('fs').readFileSync('.env', 'utf8').match(/PRIVATE_KEY=(.*)/)[1];
const wallet = new ethers.Wallet(privateKey);
console.log(wallet.address);
")

BALANCE_HEX=$(curl -s -X POST -H "Content-Type: application/json" \
              --data "{\"jsonrpc\":\"2.0\",\"method\":\"eth_getBalance\",\"params\":[\"$DEPLOYER_ADDRESS\",\"latest\"],\"id\":1}" \
              http://localhost:8545 | grep -o '"result":"[^"]*"' | cut -d'"' -f4)

BALANCE_WEI=$((BALANCE_HEX))
BALANCE_ETH=$(node -e "console.log(($BALANCE_WEI / 1e18).toFixed(4))")

print_status "Deployer address: $DEPLOYER_ADDRESS"
print_status "Account balance: $BALANCE_ETH ETH"

if (( $(echo "$BALANCE_ETH < 0.1" | bc -l) )); then
    print_error "Insufficient ETH balance for deployment (need at least 0.1 ETH)"
    exit 1
fi

# Final confirmation
echo ""
echo -e "${RED}⚠️  FINAL CONFIRMATION ⚠️${NC}"
echo "================================"
echo "You are about to deploy to ETHEREUM MAINNET"
echo "This involves REAL MONEY and REAL RISKS"
echo ""
echo "Deployment details:"
echo "  Network: Ethereum Mainnet"
echo "  Chain ID: $CHAIN_ID"
echo "  Deployer: $DEPLOYER_ADDRESS"
echo "  Balance: $BALANCE_ETH ETH"
echo "  Block: $LATEST_BLOCK"
echo ""
echo -e "${YELLOW}Are you absolutely sure you want to proceed?${NC}"
read -p "Type 'DEPLOY TO MAINNET' to continue: " confirmation

if [ "$confirmation" != "DEPLOY TO MAINNET" ]; then
    print_status "Deployment cancelled"
    exit 0
fi

echo ""
print_status "🚀 Starting mainnet deployment..."

# Deploy contracts
echo ""
print_status "📄 Deploying HybridFlashloanArbitrage contract..."
if npm run deploy:mainnet; then
    print_success "Contract deployment successful"
else
    print_error "Contract deployment failed"
    exit 1
fi

# Update contract address in .env
if [ -f "deployment-hybrid-mainnet.json" ]; then
    CONTRACT_ADDRESS=$(node -e "console.log(JSON.parse(require('fs').readFileSync('deployment-hybrid-mainnet.json')).contractAddress)")
    sed -i.bak "s/HYBRID_FLASHLOAN_CONTRACT=.*/HYBRID_FLASHLOAN_CONTRACT=$CONTRACT_ADDRESS/" .env
    print_success "Updated contract address in .env: $CONTRACT_ADDRESS"
fi

# Test contract deployment
print_status "🧪 Testing contract deployment..."
if npm run test:flashloan; then
    print_success "Contract tests passed"
else
    print_warning "Contract tests failed - please verify manually"
fi

# Final setup
echo ""
print_success "✅ Mainnet deployment completed successfully!"
echo ""
echo "📋 Next steps:"
echo "1. Verify contract on Etherscan"
echo "2. Start with small position sizes"
echo "3. Monitor closely for first 24 hours"
echo "4. Start the MEV bot: npm run start:mainnet"
echo ""
echo "🔧 Production commands:"
echo "  npm run start:mainnet     # Start MEV bot on mainnet"
echo "  npm run monitor:mainnet   # Monitor bot performance"
echo "  npm run stop:mainnet      # Emergency stop"
echo ""
echo -e "${GREEN}🎯 Your MEV bot is ready for mainnet operation!${NC}"
echo ""
echo -e "${YELLOW}⚠️  Remember:${NC}"
echo "- Start with conservative settings"
echo "- Monitor gas prices and network congestion"
echo "- Keep emergency funds for gas"
echo "- Have stop procedures ready"
echo "- Take profits regularly"

# Cleanup
rm -f .env.bak
