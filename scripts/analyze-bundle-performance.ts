#!/usr/bin/env ts-node

import { ethers } from 'ethers';
import { config } from '../src/config';
import { logger } from '../src/utils/logger';
import { bundleAnalyzer } from '../src/utils/bundle-analyzer';
import { FlashbotsBundleManager } from '../src/flashbots/bundle-provider';

/**
 * Bundle Performance Analysis Script
 * 
 * This script analyzes recent bundle submission performance and provides
 * optimization recommendations for improving inclusion rates.
 */

async function main() {
    try {
        logger.system('🔍 BUNDLE PERFORMANCE ANALYSIS');
        logger.system('================================');
        logger.system('');

        // Initialize provider and bundle manager
        const provider = new ethers.JsonRpcProvider(config.rpcUrl);
        const wallet = new ethers.Wallet(config.privateKey, provider);
        const bundleManager = new FlashbotsBundleManager(provider, wallet);

        // Print current configuration
        logger.system('📋 CURRENT CONFIGURATION');
        logger.system(`Network: ${config.chainId}`);
        logger.system(`Max Gas Price: ${config.maxGasPriceGwei} gwei`);
        logger.system(`Max Priority Fee: ${config.maxPriorityFeeGwei} gwei`);
        logger.system(`Gas Urgency: ${config.gasUrgency}`);
        logger.system(`Bundle Strategy: ${config.bundleSubmissionStrategy}`);
        logger.system(`Bundle Retry Count: ${config.bundleRetryCount}`);
        logger.system(`Bundle Timeout: ${config.bundleTimeoutMs}ms`);
        logger.system(`Enable Flashbots: ${config.enableFlashbots}`);
        logger.system('');

        // Get current network conditions
        const currentBlock = await provider.getBlockNumber();
        const block = await provider.getBlock(currentBlock);
        const baseFee = block?.baseFeePerGas || BigInt(0);
        
        logger.system('🌐 CURRENT NETWORK CONDITIONS');
        logger.system(`Current Block: ${currentBlock}`);
        logger.system(`Base Fee: ${ethers.formatUnits(baseFee, 'gwei')} gwei`);
        
        // Calculate current priority fee
        const priorityFee = await bundleManager.calculateBundlePriorityFee(currentBlock + 1, false);
        const highPriorityFee = await bundleManager.calculateBundlePriorityFee(currentBlock + 1, true);
        
        logger.system(`Current Priority Fee (normal): ${ethers.formatUnits(priorityFee, 'gwei')} gwei`);
        logger.system(`Current Priority Fee (high): ${ethers.formatUnits(highPriorityFee, 'gwei')} gwei`);
        logger.system('');

        // Print bundle analysis report
        bundleManager.printBundleAnalysisReport();

        // Get optimization recommendations
        const stats = bundleAnalyzer.getInclusionStats();
        const feeRecommendation = bundleAnalyzer.getOptimalPriorityFeeRecommendation();

        // Generate .env optimization suggestions
        logger.system('⚙️  SUGGESTED .ENV OPTIMIZATIONS');
        logger.system('==================================');
        
        if (stats.inclusionRate < 50) {
            logger.system('# CRITICAL: Low inclusion rate detected');
            logger.system(`MAX_PRIORITY_FEE_GWEI=${feeRecommendation.recommendedMaxGwei}`);
            logger.system('GAS_URGENCY=instant');
            logger.system('BUNDLE_SUBMISSION_STRATEGY=aggressive');
            logger.system('MAX_BLOCKS_AHEAD=7');
            logger.system('BUNDLE_RETRY_COUNT=5');
            logger.system('ENABLE_BUNDLE_MULTIPLEXING=true');
            logger.system('GAS_ESCALATION_FACTOR=3.0');
            logger.system('COMPETITIVE_GAS_BUFFER=4.0');
        } else if (stats.inclusionRate < 70) {
            logger.system('# MODERATE: Improving inclusion rate');
            logger.system(`MAX_PRIORITY_FEE_GWEI=${feeRecommendation.recommendedMaxGwei}`);
            logger.system('BUNDLE_SUBMISSION_STRATEGY=aggressive');
            logger.system('MAX_BLOCKS_AHEAD=5');
            logger.system('ENABLE_BUNDLE_MULTIPLEXING=true');
            logger.system('GAS_ESCALATION_FACTOR=2.5');
        } else if (stats.inclusionRate < 85) {
            logger.system('# GOOD: Fine-tuning for optimization');
            logger.system(`MAX_PRIORITY_FEE_GWEI=${feeRecommendation.recommendedMaxGwei}`);
            logger.system('# Current settings are working well');
            logger.system('# Consider monitoring and adjusting based on market conditions');
        } else {
            logger.system('# EXCELLENT: Consider cost optimization');
            logger.system(`MAX_PRIORITY_FEE_GWEI=${feeRecommendation.recommendedMaxGwei}`);
            logger.system('# You may be able to reduce costs while maintaining performance');
            logger.system('# Monitor inclusion rate if you reduce priority fees');
        }
        
        logger.system('');

        // Provide specific recommendations based on current vs recommended settings
        logger.system('🎯 IMMEDIATE ACTION ITEMS');
        logger.system('=========================');
        
        const currentMaxPriority = config.maxPriorityFeeGwei;
        const recommendedMax = feeRecommendation.recommendedMaxGwei;
        
        if (recommendedMax > currentMaxPriority * 1.5) {
            logger.system(`🚨 URGENT: Increase MAX_PRIORITY_FEE_GWEI from ${currentMaxPriority} to ${recommendedMax}`);
            logger.system('   Your current priority fees are too low for competitive MEV');
        } else if (recommendedMax > currentMaxPriority) {
            logger.system(`⚠️  RECOMMENDED: Increase MAX_PRIORITY_FEE_GWEI from ${currentMaxPriority} to ${recommendedMax}`);
            logger.system('   This should improve your inclusion rate');
        } else if (recommendedMax < currentMaxPriority * 0.7) {
            logger.system(`💰 OPTIMIZATION: You can reduce MAX_PRIORITY_FEE_GWEI from ${currentMaxPriority} to ${recommendedMax}`);
            logger.system('   This will save on gas costs while maintaining performance');
        } else {
            logger.system(`✅ OPTIMAL: Your current MAX_PRIORITY_FEE_GWEI (${currentMaxPriority}) is well-tuned`);
        }

        // Check bundle strategy
        if (stats.inclusionRate < 50 && config.bundleSubmissionStrategy !== 'aggressive') {
            logger.system('🎯 Set BUNDLE_SUBMISSION_STRATEGY=aggressive');
        }

        // Check retry count
        if (stats.inclusionRate < 70 && config.bundleRetryCount < 4) {
            logger.system('🔄 Increase BUNDLE_RETRY_COUNT to 4 or 5');
        }

        // Check multiplexing
        if (stats.inclusionRate < 60 && !config.enableBundleMultiplexing) {
            logger.system('📡 Enable ENABLE_BUNDLE_MULTIPLEXING=true');
        }

        logger.system('');
        logger.system('📊 MONITORING RECOMMENDATIONS');
        logger.system('=============================');
        logger.system('• Run this analysis script every 100 bundle submissions');
        logger.system('• Monitor inclusion rates during different network conditions');
        logger.system('• Adjust priority fees based on mempool congestion');
        logger.system('• Consider time-of-day patterns in MEV competition');
        logger.system('• Track profit vs gas cost ratios');
        logger.system('');

        logger.system('✅ Analysis complete! Apply recommended changes to improve bundle inclusion rates.');

    } catch (error) {
        logger.error('Bundle analysis failed:', error);
        process.exit(1);
    }
}

// Run the analysis
if (require.main === module) {
    main().catch(error => {
        logger.error('Script failed:', error);
        process.exit(1);
    });
}

export { main as analyzeBundlePerformance };
