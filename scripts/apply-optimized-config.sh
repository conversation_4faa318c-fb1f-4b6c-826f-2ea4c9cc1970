#!/bin/bash

echo "🔧 Applying Optimized MEV Bot Configuration"
echo "=========================================="
echo ""

# Backup current .env
if [ -f .env ]; then
    echo "📋 Backing up current .env to .env.backup..."
    cp .env .env.backup
    echo "✅ Backup created"
else
    echo "⚠️  No existing .env file found"
fi

# Apply optimized configuration
echo "🚀 Applying optimized configuration..."
cp .env.optimized .env
echo "✅ Optimized configuration applied"

echo ""
echo "📊 Key Changes Made:"
echo "==================="
echo "• MIN_PROFIT_WEI: Lowered to 0.001 ETH (~$3.35)"
echo "• MIN_ARBITRAGE_SPREAD: Lowered to 0.1% (from 1%)"
echo "• Added more token pairs for scanning"
echo "• Faster scanning intervals (5 seconds)"
echo "• Expanded Uniswap V3 trading pairs"
echo "• Lowered liquidity requirements"
echo ""

echo "🧪 Testing with new configuration..."
echo "===================================="

# Test the new configuration
npm run test:diagnostic

echo ""
echo "🎉 Configuration Applied Successfully!"
echo "====================================="
echo ""
echo "💡 Next Steps:"
echo "1. Monitor the bot for 10-15 minutes"
echo "2. If still no opportunities, run: npm run diagnose"
echo "3. Consider further lowering thresholds if needed"
echo ""
echo "🔄 To revert to original configuration:"
echo "cp .env.backup .env"
