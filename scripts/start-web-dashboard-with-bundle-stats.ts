#!/usr/bin/env ts-node

import { bundleAnalyzer } from '../src/utils/bundle-analyzer';
import { webDashboard } from '../src/server/webDashboard';
import { logger } from '../src/utils/logger';
import { ethers } from 'ethers';

/**
 * Start Web Dashboard with Bundle Statistics
 * 
 * This script starts the web dashboard with some test bundle statistics
 * so you can see the bundle inclusion section in action.
 */

async function main() {
    try {
        logger.system('🌐 STARTING WEB DASHBOARD WITH BUNDLE STATISTICS');
        logger.system('================================================');

        // Set environment variable for web dashboard
        process.env.WEB_DASHBOARD = 'true';
        process.env.WEB_DASHBOARD_PORT = '3001';

        // Add test bundle data to demonstrate the feature
        logger.system('📝 Adding test bundle data...');
        
        const testBundleData = [
            { included: true, priorityFee: '45', baseFee: '25' },
            { included: false, priorityFee: '30', baseFee: '28', error: 'Block passed without inclusion' },
            { included: true, priorityFee: '50', baseFee: '22' },
            { included: true, priorityFee: '55', baseFee: '26' },
            { included: false, priorityFee: '35', baseFee: '30', error: 'Block passed without inclusion' },
            { included: true, priorityFee: '60', baseFee: '24' },
            { included: false, priorityFee: '40', baseFee: '32', error: 'Account nonce too high' },
            { included: true, priorityFee: '65', baseFee: '23' },
            { included: true, priorityFee: '70', baseFee: '27' },
            { included: false, priorityFee: '45', baseFee: '35', error: 'Block passed without inclusion' },
            { included: true, priorityFee: '75', baseFee: '29' },
            { included: true, priorityFee: '80', baseFee: '31' },
            { included: false, priorityFee: '50', baseFee: '38', error: 'Block passed without inclusion' },
            { included: true, priorityFee: '85', baseFee: '28' },
            { included: true, priorityFee: '90', baseFee: '33' }
        ];

        testBundleData.forEach((data, index) => {
            const targetBlock = 3000000 + index;
            const priorityFee = ethers.parseUnits(data.priorityFee, 'gwei');
            const baseFee = ethers.parseUnits(data.baseFee, 'gwei');
            
            bundleAnalyzer.recordBundleSubmission(
                targetBlock,
                data.included,
                priorityFee,
                baseFee,
                data.error
            );
        });

        logger.system('✅ Test bundle data added');

        // Get current bundle statistics
        const bundleStats = bundleAnalyzer.getInclusionStats();
        logger.system('');
        logger.system('📊 BUNDLE STATISTICS TO BE DISPLAYED');
        logger.system('====================================');
        logger.system(`Total Submissions: ${bundleStats.totalSubmissions}`);
        logger.system(`Successful Inclusions: ${bundleStats.successfulInclusions}`);
        logger.system(`Inclusion Rate: ${bundleStats.inclusionRate.toFixed(1)}%`);
        logger.system(`Average Priority Fee: ${bundleStats.averagePriorityFee} gwei`);
        logger.system('');

        // Create dashboard data with bundle statistics
        const dashboardData = {
            currentBlock: 19500000,
            networkName: 'Ethereum Mainnet',
            ethBalance: ethers.parseEther('2.5'),
            lastBalanceUpdate: Date.now(),
            isRunning: true,
            uptime: Date.now(),
            lastActivity: Date.now(),
            flashloanEnabled: true,
            mevShareEnabled: true,
            arbitrageEnabled: true,
            totalTransactions: 150,
            relevantTransactions: 45,
            opportunitiesFound: 12,
            opportunitiesExecuted: 8,
            totalProfit: ethers.parseEther('0.25'),
            avgGasPrice: ethers.parseUnits('25', 'gwei'),
            bundleSubmissions: bundleStats.totalSubmissions,
            bundleInclusions: bundleStats.successfulInclusions,
            bundleInclusionRate: bundleStats.inclusionRate,
            avgPriorityFee: bundleStats.averagePriorityFee,
            configuration: {
                tokenPairs: ['WETH/USDC', 'USDC/DAI'],
                dexes: ['Uniswap V3', 'Balancer V2'],
                minProfitThreshold: '0.01',
                maxGasPrice: '100'
            },
            successfulTransactions: [
                {
                    timestamp: Date.now() - 300000,
                    type: 'Flashloan Arbitrage',
                    profit: ethers.parseEther('0.05'),
                    gasUsed: BigInt(150000),
                    txHash: '0x1234...5678',
                    confidence: 95,
                    details: 'USDC/DAI arbitrage via Uniswap V3 + Balancer V2'
                },
                {
                    timestamp: Date.now() - 600000,
                    type: 'Flashloan Arbitrage',
                    profit: ethers.parseEther('0.03'),
                    gasUsed: BigInt(120000),
                    txHash: '0xabcd...efgh',
                    confidence: 88,
                    details: 'WETH/USDC arbitrage via Uniswap V3 + Curve'
                }
            ],
            errors: 2,
            lastError: 'Bundle not included in target block'
        };

        // Start web dashboard
        logger.system('🌐 Starting web dashboard...');
        webDashboard.start();

        // Update dashboard with our test data
        webDashboard.updateDashboardData(dashboardData);

        logger.system('✅ Web dashboard started with bundle statistics');
        logger.system('');
        logger.system('🎯 DASHBOARD ACCESS INFORMATION');
        logger.system('===============================');
        logger.system(`🌐 URL: http://localhost:3001`);
        logger.system(`📊 API: http://localhost:3001/api/dashboard`);
        logger.system('');
        logger.system('📦 BUNDLE INCLUSION SECTION');
        logger.system('===========================');
        logger.system('Look for the "Bundle Inclusion" section in the dashboard with:');
        logger.system(`• Bundle Submissions: ${bundleStats.totalSubmissions}`);
        logger.system(`• Successful Inclusions: ${bundleStats.successfulInclusions}`);
        logger.system(`• Inclusion Rate: ${bundleStats.inclusionRate.toFixed(1)}% (${bundleStats.inclusionRate >= 70 ? 'GREEN' : bundleStats.inclusionRate >= 50 ? 'YELLOW' : 'RED'})`);
        logger.system(`• Avg Priority Fee: ${bundleStats.averagePriorityFee} gwei`);
        logger.system('');
        logger.system('🔄 REAL-TIME UPDATES');
        logger.system('====================');
        logger.system('The dashboard uses WebSocket for real-time updates.');
        logger.system('Bundle statistics will update automatically as new data is added.');
        logger.system('');
        logger.system('🛑 TO STOP: Press Ctrl+C');
        logger.system('');

        // Test API endpoint
        setTimeout(async () => {
            try {
                const axios = require('axios');
                const response = await axios.get('http://localhost:3001/api/dashboard');
                const data = response.data;
                
                logger.system('✅ API ENDPOINT TEST SUCCESSFUL');
                logger.system(`   Bundle Submissions: ${data.bundleSubmissions}`);
                logger.system(`   Bundle Inclusions: ${data.bundleInclusions}`);
                logger.system(`   Inclusion Rate: ${data.bundleInclusionRate}%`);
                logger.system(`   Avg Priority Fee: ${data.avgPriorityFee} gwei`);
                logger.system('');
                
            } catch (error) {
                logger.system(`❌ API test failed: ${(error as Error).message}`);
            }
        }, 3000);

        // Simulate some real-time updates
        let updateCounter = 0;
        const updateInterval = setInterval(() => {
            updateCounter++;
            
            // Add a new bundle submission every 10 seconds
            const newTargetBlock = 3000000 + testBundleData.length + updateCounter;
            const isIncluded = Math.random() > 0.3; // 70% inclusion rate
            const priorityFee = ethers.parseUnits((50 + Math.random() * 50).toFixed(0), 'gwei');
            const baseFee = ethers.parseUnits((20 + Math.random() * 20).toFixed(0), 'gwei');
            
            bundleAnalyzer.recordBundleSubmission(
                newTargetBlock,
                isIncluded,
                priorityFee,
                baseFee,
                isIncluded ? undefined : 'Block passed without inclusion'
            );
            
            // Update dashboard with new bundle stats
            const newBundleStats = bundleAnalyzer.getInclusionStats();
            webDashboard.updateDashboardData({
                bundleSubmissions: newBundleStats.totalSubmissions,
                bundleInclusions: newBundleStats.successfulInclusions,
                bundleInclusionRate: newBundleStats.inclusionRate,
                avgPriorityFee: newBundleStats.averagePriorityFee,
                lastActivity: Date.now()
            });
            
            logger.system(`🔄 Updated: ${newBundleStats.totalSubmissions} submissions, ${newBundleStats.inclusionRate.toFixed(1)}% rate`);
            
        }, 10000); // Update every 10 seconds

        // Handle graceful shutdown
        process.on('SIGINT', () => {
            logger.system('');
            logger.system('🛑 Stopping web dashboard...');
            clearInterval(updateInterval);
            webDashboard.stop();
            logger.system('✅ Web dashboard stopped');
            process.exit(0);
        });

        // Keep the process alive
        await new Promise(() => {});

    } catch (error) {
        logger.error('Failed to start web dashboard with bundle stats:', error);
        process.exit(1);
    }
}

// Run the script
if (require.main === module) {
    main().catch(error => {
        logger.error('Script failed:', error);
        process.exit(1);
    });
}

export { main as startWebDashboardWithBundleStats };
