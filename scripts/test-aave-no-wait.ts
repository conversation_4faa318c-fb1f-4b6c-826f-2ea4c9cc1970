#!/usr/bin/env ts-node

import { ethers } from 'ethers';
import { config } from '../src/config';
import { logger } from '../src/utils/logger';
import { FlashbotsBundleManager } from '../src/flashbots/bundle-provider';

/**
 * Test Aave Flashloan No-Wait Bundle Submission
 * 
 * This script tests the new Aave flashloan bundle submission that doesn't wait
 * for inclusion and returns immediately for the next block.
 */

async function main() {
    try {
        logger.system('🧪 TESTING AAVE FLASHLOAN NO-WAIT SUBMISSION');
        logger.system('=============================================');

        // Initialize provider and bundle manager
        const provider = new ethers.JsonRpcProvider(config.rpcUrl);
        const wallet = new ethers.Wallet(config.privateKey, provider);
        const bundleManager = new FlashbotsBundleManager(provider, wallet);

        // Get current block
        const currentBlock = await provider.getBlockNumber();
        const targetBlock = currentBlock + 1;

        logger.system(`Current Block: ${currentBlock}`);
        logger.system(`Target Block: ${targetBlock}`);
        logger.system('');

        // Create a mock Aave flashloan transaction
        const mockTransaction = {
            transaction: {
                to: '******************************************', // Aave Pool
                data: '0x', // Mock data
                value: '0x0',
                gasLimit: '0x493e0', // 300,000 gas
                maxFeePerGas: ethers.parseUnits('50', 'gwei'),
                maxPriorityFeePerGas: ethers.parseUnits('2', 'gwei'),
                type: 2
            },
            signer: wallet
        };

        // Test 1: Original bundle submission (with waiting)
        logger.system('📦 TEST 1: Original Bundle Submission (with waiting)');
        logger.system('===================================================');

        const startTime1 = Date.now();
        let executionTime1 = 0;

        try {
            const result1 = await bundleManager.submitBundle(
                [mockTransaction],
                targetBlock,
                {
                    isHighPriority: false,
                    minTimestamp: Math.floor(Date.now() / 1000),
                    maxTimestamp: Math.floor(Date.now() / 1000) + 60
                }
            );

            executionTime1 = Date.now() - startTime1;

            logger.system(`✅ Original submission completed`);
            logger.system(`   Success: ${result1.success}`);
            logger.system(`   Bundle Hash: ${result1.bundleHash}`);
            logger.system(`   Resolution: ${result1.resolution}`);
            logger.system(`   Execution Time: ${executionTime1}ms`);
            logger.system(`   Error: ${result1.error || 'None'}`);

        } catch (error) {
            executionTime1 = Date.now() - startTime1;
            logger.system(`❌ Original submission failed: ${(error as Error).message}`);
        }
        
        logger.system('');

        // Test 2: Aave flashloan bundle submission (no waiting)
        logger.system('🏦 TEST 2: Aave Flashloan Bundle Submission (no waiting)');
        logger.system('========================================================');
        
        const startTime2 = Date.now();
        
        try {
            const result2 = await bundleManager.submitBundleForAaveFlashloan(
                [mockTransaction],
                targetBlock + 1,
                {
                    isHighPriority: false,
                    minTimestamp: Math.floor(Date.now() / 1000),
                    maxTimestamp: Math.floor(Date.now() / 1000) + 60
                }
            );
            
            const executionTime2 = Date.now() - startTime2;
            
            logger.system(`🚀 Aave submission completed`);
            logger.system(`   Success: ${result2.success}`);
            logger.system(`   Bundle Hash: ${result2.bundleHash}`);
            logger.system(`   Resolution: ${result2.resolution || 'None (immediate return)'}`);
            logger.system(`   Execution Time: ${executionTime2}ms`);
            logger.system(`   Error: ${result2.error || 'None'}`);
            
            // Compare execution times
            logger.system('');
            logger.system('⚡ PERFORMANCE COMPARISON');
            logger.system('========================');
            logger.system(`Original method: ${executionTime1}ms`);
            logger.system(`Aave no-wait method: ${executionTime2}ms`);
            
            if (executionTime2 < executionTime1) {
                const improvement = ((executionTime1 - executionTime2) / executionTime1 * 100).toFixed(1);
                logger.system(`🎯 Aave method is ${improvement}% faster!`);
            } else {
                logger.system(`⚠️  Execution times are similar`);
            }
            
        } catch (error) {
            logger.system(`❌ Aave submission failed: ${(error as Error).message}`);
        }

        logger.system('');

        // Test 3: Configuration verification
        logger.system('⚙️  TEST 3: Configuration Verification');
        logger.system('=====================================');
        logger.system(`AAVE_NO_WAIT_SUBMISSION: ${(config as any).aaveNoWaitSubmission}`);
        logger.system(`AAVE_IMMEDIATE_RETURN: ${(config as any).aaveImmediateReturn}`);
        logger.system(`ENABLE_FLASHBOTS: ${config.enableFlashbots}`);
        logger.system(`SIMULATION_MODE: ${config.simulationMode}`);
        logger.system('');

        // Test 4: Bundle analyzer integration
        logger.system('📊 TEST 4: Bundle Analyzer Integration');
        logger.system('=====================================');
        
        const { bundleAnalyzer } = await import('../src/utils/bundle-analyzer');
        const stats = bundleAnalyzer.getInclusionStats();
        
        logger.system(`Total Bundle Submissions: ${stats.totalSubmissions}`);
        logger.system(`Successful Inclusions: ${stats.successfulInclusions}`);
        logger.system(`Inclusion Rate: ${stats.inclusionRate.toFixed(1)}%`);
        logger.system(`Average Priority Fee: ${stats.averagePriorityFee} gwei`);
        logger.system('');

        // Test 5: Simulation mode test
        logger.system('🎭 TEST 5: Simulation Mode Test');
        logger.system('===============================');
        
        // Temporarily enable simulation mode
        const originalSimulationMode = config.simulationMode;
        (config as any).simulationMode = true;
        
        const startTime3 = Date.now();
        
        try {
            const result3 = await bundleManager.submitBundleForAaveFlashloan(
                [mockTransaction],
                targetBlock + 2,
                {
                    isHighPriority: true,
                    minTimestamp: Math.floor(Date.now() / 1000),
                    maxTimestamp: Math.floor(Date.now() / 1000) + 60
                }
            );
            
            const executionTime3 = Date.now() - startTime3;
            
            logger.system(`🎭 Simulation completed`);
            logger.system(`   Success: ${result3.success}`);
            logger.system(`   Bundle Hash: ${result3.bundleHash}`);
            logger.system(`   Execution Time: ${executionTime3}ms`);
            logger.system(`   Is Simulation: ${result3.bundleHash?.includes('SIMULATION')}`);
            
        } catch (error) {
            logger.system(`❌ Simulation failed: ${(error as Error).message}`);
        }
        
        // Restore original simulation mode
        (config as any).simulationMode = originalSimulationMode;

        logger.system('');
        logger.system('✅ AAVE FLASHLOAN NO-WAIT TESTING COMPLETED');
        logger.system('===========================================');
        logger.system('');
        logger.system('🎯 KEY BENEFITS:');
        logger.system('• Immediate return without waiting for block inclusion');
        logger.system('• Faster execution for high-frequency Aave flashloan attacks');
        logger.system('• Reduced latency for next block targeting');
        logger.system('• Better MEV opportunity capture timing');
        logger.system('');
        logger.system('📋 USAGE:');
        logger.system('• Set AAVE_NO_WAIT_SUBMISSION=true in .env');
        logger.system('• Set AAVE_IMMEDIATE_RETURN=true in .env');
        logger.system('• Use executeAaveFlashloan() instead of executeFlashloan()');
        logger.system('• Bundle submissions return immediately');
        logger.system('');

    } catch (error) {
        logger.error('Aave no-wait test failed:', error);
        process.exit(1);
    }
}

// Run the test
if (require.main === module) {
    main().catch(error => {
        logger.error('Script failed:', error);
        process.exit(1);
    });
}

export { main as testAaveNoWait };
