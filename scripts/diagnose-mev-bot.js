#!/usr/bin/env node

const { spawn } = require('child_process');
const path = require('path');

console.log('🔍 MEV Bot Diagnostic Tool');
console.log('==========================');
console.log('');
console.log('This tool will run comprehensive diagnostics to identify why');
console.log('your MEV bot is not finding opportunities on mainnet.');
console.log('');

async function runDiagnostic() {
  console.log('📋 Step 1: Running Configuration and Pool Diagnostics...');
  console.log('');
  
  try {
    await runTest('test:diagnostic');
    console.log('');
    console.log('✅ Configuration diagnostics completed');
    console.log('');
    
    console.log('📋 Step 2: Running Real-Life Opportunity Analysis...');
    console.log('');
    
    await runTest('test:real-life');
    console.log('');
    console.log('✅ Real-life opportunity analysis completed');
    console.log('');
    
    console.log('🎉 DIAGNOSTIC COMPLETE!');
    console.log('');
    console.log('📊 Check the test output above for:');
    console.log('   • Configuration issues');
    console.log('   • Pool availability problems');
    console.log('   • Price calculation errors');
    console.log('   • Optimal threshold recommendations');
    console.log('');
    console.log('💡 Common fixes:');
    console.log('   • Lower MIN_PROFIT_WEI in .env');
    console.log('   • Lower MIN_ARBITRAGE_SPREAD in .env');
    console.log('   • Add more token pairs');
    console.log('   • Check RPC connectivity');
    
  } catch (error) {
    console.error('❌ Diagnostic failed:', error.message);
    process.exit(1);
  }
}

function runTest(testName) {
  return new Promise((resolve, reject) => {
    const child = spawn('npm', ['run', testName], {
      stdio: 'inherit',
      cwd: process.cwd()
    });
    
    child.on('close', (code) => {
      if (code === 0) {
        resolve();
      } else {
        reject(new Error(`Test ${testName} failed with code ${code}`));
      }
    });
    
    child.on('error', (error) => {
      reject(error);
    });
  });
}

// Run the diagnostic
runDiagnostic().catch(console.error);
