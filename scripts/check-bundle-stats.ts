#!/usr/bin/env ts-node

import { bundleAnalyzer } from '../src/utils/bundle-analyzer';
import { logger } from '../src/utils/logger';

/**
 * Quick Bundle Statistics Checker
 * 
 * This script provides a quick overview of current bundle inclusion statistics
 * without running the full analysis.
 */

function main() {
    try {
        logger.system('📊 QUICK BUNDLE STATISTICS');
        logger.system('==========================');

        const stats = bundleAnalyzer.getInclusionStats();
        const failures = bundleAnalyzer.analyzeRecentFailures();
        const feeRecommendation = bundleAnalyzer.getOptimalPriorityFeeRecommendation();

        if (stats.totalSubmissions === 0) {
            logger.system('❌ No bundle submission data available');
            logger.system('   Start the MEV bot to begin collecting bundle statistics');
            return;
        }

        // Basic stats
        logger.system(`Total Submissions: ${stats.totalSubmissions}`);
        logger.system(`Successful Inclusions: ${stats.successfulInclusions}`);
        
        // Inclusion rate with color coding
        const inclusionRate = stats.inclusionRate;
        let rateStatus = '';
        if (inclusionRate >= 80) {
            rateStatus = '🎯 EXCELLENT';
        } else if (inclusionRate >= 70) {
            rateStatus = '✅ GOOD';
        } else if (inclusionRate >= 50) {
            rateStatus = '📈 MODERATE';
        } else if (inclusionRate >= 30) {
            rateStatus = '⚠️  LOW';
        } else {
            rateStatus = '🚨 CRITICAL';
        }
        
        logger.system(`Inclusion Rate: ${inclusionRate.toFixed(1)}% (${rateStatus})`);
        logger.system(`Average Priority Fee: ${stats.averagePriorityFee} gwei`);
        logger.system(`Average Base Fee: ${stats.averageBaseFee} gwei`);
        logger.system('');

        // Recent failures
        logger.system('🔍 RECENT PERFORMANCE');
        logger.system(`Recent Failures (last 20): ${failures.recentFailures}/20`);
        if (failures.commonErrors.length > 0) {
            logger.system('Most Common Errors:');
            failures.commonErrors.slice(0, 2).forEach(error => {
                logger.system(`  • ${error}`);
            });
        }
        logger.system('');

        // Quick recommendations
        logger.system('💡 QUICK RECOMMENDATIONS');
        if (inclusionRate < 30) {
            logger.system('🚨 URGENT ACTION NEEDED:');
            logger.system(`  • Increase MAX_PRIORITY_FEE_GWEI to ${feeRecommendation.recommendedMaxGwei}+ gwei`);
            logger.system('  • Set BUNDLE_SUBMISSION_STRATEGY=aggressive');
            logger.system('  • Enable ENABLE_BUNDLE_MULTIPLEXING=true');
        } else if (inclusionRate < 50) {
            logger.system('⚠️  IMPROVEMENTS NEEDED:');
            logger.system(`  • Consider increasing MAX_PRIORITY_FEE_GWEI to ${feeRecommendation.recommendedMaxGwei} gwei`);
            logger.system('  • Monitor network congestion and adjust accordingly');
        } else if (inclusionRate < 70) {
            logger.system('📈 MODERATE PERFORMANCE:');
            logger.system('  • Current settings are working but can be improved');
            logger.system('  • Monitor and fine-tune based on market conditions');
        } else if (inclusionRate < 85) {
            logger.system('✅ GOOD PERFORMANCE:');
            logger.system('  • Current settings are working well');
            logger.system('  • Consider minor optimizations for cost efficiency');
        } else {
            logger.system('🎯 EXCELLENT PERFORMANCE:');
            logger.system('  • Your bundle inclusion rate is excellent');
            logger.system('  • Consider optimizing for cost efficiency');
            logger.system(`  • You may be able to reduce priority fees to ${feeRecommendation.recommendedMinGwei} gwei`);
        }
        
        logger.system('');
        logger.system('📋 NEXT STEPS:');
        logger.system('  • Run "npm run analyze:bundles" for detailed analysis');
        logger.system('  • Monitor inclusion rates during different network conditions');
        logger.system('  • Adjust priority fees based on competition levels');
        logger.system('');

    } catch (error) {
        logger.error('Failed to check bundle stats:', error);
        process.exit(1);
    }
}

// Run the check
if (require.main === module) {
    main();
}

export { main as checkBundleStats };
