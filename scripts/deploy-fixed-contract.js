const { ethers } = require("hardhat");

async function main() {
    console.log("🚀 Deploying HybridFlashloanArbitrageFixed to mainnet...");
    
    // Get the deployer account
    const [deployer] = await ethers.getSigners();
    console.log("📋 Deploying with account:", deployer.address);
    
    // Check balance
    const balance = await deployer.provider.getBalance(deployer.address);
    console.log("💰 Account balance:", ethers.formatEther(balance), "ETH");

    // Check current gas prices and estimate deployment cost
    console.log("\n💸 Checking gas prices...");
    const feeData = await ethers.provider.getFeeData();
    const gasPrice = feeData.gasPrice;
    const maxFeePerGas = feeData.maxFeePerGas;
    const maxPriorityFeePerGas = feeData.maxPriorityFeePerGas;

    console.log("   Gas Price:", ethers.formatUnits(gasPrice || 0n, "gwei"), "gwei");
    console.log("   Max Fee Per Gas:", ethers.formatUnits(maxFeePerGas || 0n, "gwei"), "gwei");
    console.log("   Max Priority Fee:", ethers.formatUnits(maxPriorityFeePerGas || 0n, "gwei"), "gwei");

    // Estimate deployment gas cost (contract is ~3M gas)
    const estimatedGasUnits = 3200000n; // Conservative estimate for large contract
    const effectiveGasPrice = maxFeePerGas || gasPrice || 20000000000n; // Use maxFeePerGas or fallback
    const estimatedCostWei = estimatedGasUnits * effectiveGasPrice;
    const estimatedCostEth = ethers.formatEther(estimatedCostWei);

    // Get current ETH price (fallback to conservative estimate)
    let ethPriceUsd = 3500; // Conservative fallback
    try {
        // Try to get real ETH price from a simple API
        const response = await fetch('https://api.coingecko.com/api/v3/simple/price?ids=ethereum&vs_currencies=usd');
        const data = await response.json();
        if (data.ethereum && data.ethereum.usd) {
            ethPriceUsd = data.ethereum.usd;
            console.log("   Current ETH Price: $" + ethPriceUsd);
        }
    } catch (error) {
        console.log("   ETH Price: $" + ethPriceUsd + " (fallback estimate)");
    }

    const estimatedCostUsd = parseFloat(estimatedCostEth) * ethPriceUsd;

    console.log("\n💰 Deployment Cost Estimate:");
    console.log("   Gas Units:", estimatedGasUnits.toLocaleString());
    console.log("   Effective Gas Price:", ethers.formatUnits(effectiveGasPrice, "gwei"), "gwei");
    console.log("   Estimated Cost:", estimatedCostEth, "ETH");
    console.log("   Estimated USD Cost: $" + estimatedCostUsd.toFixed(2));

    // Check if cost exceeds $2 limit
    const maxCostUsd = 2.0;
    if (estimatedCostUsd > maxCostUsd) {
        console.log("\n❌ DEPLOYMENT CANCELLED!");
        console.log("   Estimated cost ($" + estimatedCostUsd.toFixed(2) + ") exceeds maximum limit ($" + maxCostUsd + ")");
        console.log("   Current gas prices are too high for deployment.");
        console.log("\n💡 Suggestions:");
        console.log("   1. Wait for lower gas prices (check ethgasstation.info)");
        console.log("   2. Deploy during off-peak hours (weekends, early morning UTC)");
        console.log("   3. Use a gas tracker to monitor for optimal timing");
        console.log("\n⏰ Try again when gas prices are lower!");
        process.exit(1);
    }

    console.log("\n✅ Gas cost acceptable ($" + estimatedCostUsd.toFixed(2) + " < $" + maxCostUsd + ")");
    console.log("   Proceeding with deployment...");

    // Contract parameters for mainnet
    const aavePool = "******************************************"; // Aave V3 Pool
    const balancerVault = "******************************************"; // Balancer V2 Vault
    
    console.log("📋 Contract Parameters:");
    console.log("   Aave Pool:", aavePool);
    console.log("   Balancer Vault:", balancerVault);
    
    // Deploy the contract
    const HybridFlashloanArbitrageFixed = await ethers.getContractFactory("HybridFlashloanArbitrageFixed");
    
    console.log("⏳ Deploying contract...");
    const contract = await HybridFlashloanArbitrageFixed.deploy(
        aavePool,
        balancerVault
    );
    
    await contract.waitForDeployment();
    const contractAddress = await contract.getAddress();
    
    console.log("✅ Contract deployed successfully!");
    console.log("📍 Contract address:", contractAddress);
    
    // Verify contract state
    console.log("\n🔍 Verifying contract state...");
    const owner = await contract.owner();
    const chainId = await contract.CHAIN_ID();
    const v2Router = await contract.UNISWAP_V2_ROUTER();
    const v3Router = await contract.UNISWAP_V3_ROUTER();
    const aavePoolAddr = await contract.AAVE_POOL();
    const balancerVaultAddr = await contract.BALANCER_VAULT();
    
    console.log("   Owner:", owner);
    console.log("   Chain ID:", chainId.toString());
    console.log("   V2 Router:", v2Router);
    console.log("   V3 Router:", v3Router);
    console.log("   Aave Pool:", aavePoolAddr);
    console.log("   Balancer Vault:", balancerVaultAddr);
    
    // Calculate deployment cost
    const deploymentTx = contract.deploymentTransaction();
    if (deploymentTx) {
        const receipt = await deploymentTx.wait();
        const gasUsed = receipt.gasUsed;
        const gasPrice = deploymentTx.gasPrice;
        const cost = gasUsed * gasPrice;
        
        console.log("\n💸 Deployment Cost:");
        console.log("   Gas used:", gasUsed.toString());
        console.log("   Gas price:", ethers.formatUnits(gasPrice, "gwei"), "gwei");
        console.log("   Total cost:", ethers.formatEther(cost), "ETH");
        console.log("   USD cost: ~$" + (parseFloat(ethers.formatEther(cost)) * 4500).toFixed(2)); // Assuming $4500 ETH
    }
    
    console.log("\n🎯 Next Steps:");
    console.log("1. Update .env file with new contract address");
    console.log("2. Restart the MEV bot");
    console.log("3. Monitor for improved error messages");
    
    console.log("\n📝 Contract Address for .env:");
    console.log(`HYBRID_FLASHLOAN_CONTRACT=${contractAddress}`);
}

main()
    .then(() => process.exit(0))
    .catch((error) => {
        console.error("❌ Deployment failed:", error);
        process.exit(1);
    });
