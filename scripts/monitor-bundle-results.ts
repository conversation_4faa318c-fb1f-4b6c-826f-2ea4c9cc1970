#!/usr/bin/env ts-node

import { ethers } from 'ethers';
import { FlashbotsBundleResolution } from '@flashbots/ethers-provider-bundle';
import { config } from '../src/config';
import { logger } from '../src/utils/logger';
import { FlashbotsBundleManager } from '../src/flashbots/bundle-provider';
import { getBundleTracker } from '../src/flashbots/bundle-tracker';

/**
 * Bundle Result Monitoring Script
 * 
 * This script demonstrates how to monitor bundle results after submission
 * without waiting, showing the proper way to get results from Flashbots.
 */

async function main() {
    try {
        logger.system('📊 BUNDLE RESULT MONITORING DEMO');
        logger.system('=================================');

        // Initialize provider and bundle manager
        const provider = new ethers.JsonRpcProvider(config.rpcUrl);
        const wallet = new ethers.Wallet(config.private<PERSON>ey, provider);
        const bundleManager = new FlashbotsBundleManager(provider, wallet);

        // Initialize Flashbots
        await bundleManager.initialize();

        if (!bundleManager.isAvailable()) {
            logger.error('❌ Flashbots not available (mainnet required)');
            return;
        }

        logger.system('✅ Flashbots initialized and bundle tracker ready');
        logger.system('');

        // Get current block
        const currentBlock = await provider.getBlockNumber();
        const targetBlock = currentBlock + 1;

        logger.system('📦 SUBMITTING TEST BUNDLE');
        logger.system('=========================');
        logger.system(`Current Block: ${currentBlock}`);
        logger.system(`Target Block: ${targetBlock}`);

        // Create a mock Aave flashloan transaction
        const mockTransaction = {
            transaction: {
                to: config.aavePoolAddress,
                data: '0x', // Mock data
                value: '0x0',
                gasLimit: '0x493e0', // 300,000 gas
                maxFeePerGas: ethers.parseUnits('50', 'gwei'),
                maxPriorityFeePerGas: ethers.parseUnits('2', 'gwei'),
                type: 2
            },
            signer: wallet
        };

        // Submit bundle using Aave no-wait method
        const startTime = Date.now();
        const result = await bundleManager.submitBundleForAaveFlashloan(
            [mockTransaction],
            targetBlock,
            {
                isHighPriority: false,
                minTimestamp: Math.floor(Date.now() / 1000),
                maxTimestamp: Math.floor(Date.now() / 1000) + 60
            }
        );

        const submissionTime = Date.now() - startTime;

        logger.system(`✅ Bundle submitted in ${submissionTime}ms`);
        logger.system(`   Bundle Hash: ${result.bundleHash}`);
        logger.system(`   Success: ${result.success}`);
        logger.system(`   Error: ${result.error || 'None'}`);
        logger.system('');

        if (!result.success || !result.bundleHash) {
            logger.error('❌ Bundle submission failed');
            return;
        }

        // Now demonstrate result monitoring
        logger.system('🔍 MONITORING BUNDLE RESULTS');
        logger.system('============================');

        const tracker = getBundleTracker();
        if (!tracker) {
            logger.error('❌ Bundle tracker not available');
            return;
        }

        // Monitor for 2 minutes
        const monitoringDuration = 120000; // 2 minutes
        const checkInterval = 5000; // Check every 5 seconds
        const endTime = Date.now() + monitoringDuration;

        logger.system(`📊 Monitoring bundle for ${monitoringDuration / 1000} seconds...`);
        logger.system(`   Bundle Hash: ${result.bundleHash.substring(0, 10)}...`);
        logger.system(`   Check Interval: ${checkInterval / 1000} seconds`);
        logger.system('');

        let lastStatus = 'pending';
        let checkCount = 0;

        const monitoringInterval = setInterval(async () => {
            checkCount++;
            const now = Date.now();

            try {
                // Get bundle status from tracker
                const bundleStatus = tracker.getBundleStatus(result.bundleHash!);
                
                if (bundleStatus) {
                    const currentStatus = bundleStatus.resolved ? 
                        (bundleStatus.resolution ? 'resolved' : 'failed') : 'tracking';
                    
                    if (currentStatus !== lastStatus) {
                        logger.system(`📊 Status Update #${checkCount}:`);
                        logger.system(`   Status: ${currentStatus}`);
                        logger.system(`   Target Block: ${bundleStatus.targetBlock}`);
                        logger.system(`   Resolved: ${bundleStatus.resolved}`);
                        logger.system(`   Resolution: ${bundleStatus.resolution || 'None'}`);
                        logger.system(`   Error: ${bundleStatus.error || 'None'}`);
                        logger.system(`   Priority Fee: ${ethers.formatUnits(bundleStatus.priorityFee, 'gwei')} gwei`);
                        logger.system(`   Tracking Time: ${((now - bundleStatus.submissionTime) / 1000).toFixed(1)}s`);
                        logger.system('');
                        
                        lastStatus = currentStatus;
                    }

                    // If resolved, show final results
                    if (bundleStatus.resolved) {
                        logger.system('🎯 FINAL BUNDLE RESULT');
                        logger.system('=====================');
                        
                        if (bundleStatus.resolution === FlashbotsBundleResolution.BundleIncluded) {
                            logger.success('🎉 Bundle was included in blockchain!');
                            logger.success(`   Target Block: ${bundleStatus.targetBlock}`);
                            logger.success(`   Gas Used: ${bundleStatus.gasUsed?.toString() || 'Unknown'}`);
                            logger.success(`   Total Tracking Time: ${((now - bundleStatus.submissionTime) / 1000).toFixed(1)}s`);
                        } else {
                            logger.warn('⏭️ Bundle was not included');
                            logger.warn(`   Reason: ${bundleStatus.error}`);
                            logger.warn(`   Target Block: ${bundleStatus.targetBlock}`);
                        }
                        
                        clearInterval(monitoringInterval);
                        showTrackingStats(tracker);
                        return;
                    }
                } else {
                    logger.debug(`📊 Check #${checkCount}: Bundle not found in tracker`);
                }

                // Check if monitoring time expired
                if (now >= endTime) {
                    logger.system('⏰ Monitoring timeout reached');
                    clearInterval(monitoringInterval);
                    showTrackingStats(tracker);
                    return;
                }

                // Get current block for progress
                const currentBlockNow = await provider.getBlockNumber();
                if (checkCount % 3 === 0) { // Every 3rd check
                    logger.debug(`📊 Check #${checkCount}: Current block ${currentBlockNow}, Target block ${targetBlock}`);
                }

            } catch (error) {
                logger.debug(`❌ Error during monitoring check #${checkCount}: ${(error as Error).message}`);
            }
        }, checkInterval);

        // Handle graceful shutdown
        process.on('SIGINT', () => {
            logger.system('');
            logger.system('🛑 Monitoring interrupted by user');
            clearInterval(monitoringInterval);
            showTrackingStats(tracker);
            process.exit(0);
        });

        // Keep process alive during monitoring
        await new Promise(resolve => {
            setTimeout(resolve, monitoringDuration + 5000);
        });

    } catch (error) {
        logger.error('Bundle result monitoring failed:', error);
        process.exit(1);
    }
}

/**
 * Show tracking statistics
 */
function showTrackingStats(tracker: any): void {
    try {
        const stats = tracker.getTrackingStats();
        const allBundles = tracker.getAllTrackedBundles();

        logger.system('');
        logger.system('📈 TRACKING STATISTICS');
        logger.system('======================');
        logger.system(`Active Tracking: ${stats.activeTracking}`);
        logger.system(`Total Tracked: ${stats.totalTracked}`);
        logger.system(`Average Tracking Time: ${(stats.averageTrackingTime / 1000).toFixed(1)}s`);
        logger.system('');

        if (allBundles.length > 0) {
            logger.system('📋 TRACKED BUNDLES');
            logger.system('==================');
            allBundles.forEach((bundle: any, index: number) => {
                logger.system(`${index + 1}. ${bundle.bundleHash.substring(0, 10)}...`);
                logger.system(`   Target Block: ${bundle.targetBlock}`);
                logger.system(`   Resolved: ${bundle.resolved}`);
                logger.system(`   Resolution: ${bundle.resolution || 'None'}`);
                logger.system(`   Error: ${bundle.error || 'None'}`);
            });
        }

        logger.system('');
        logger.system('✅ Bundle result monitoring completed');
        logger.system('');
        logger.system('💡 KEY LEARNINGS:');
        logger.system('• Bundle submission returns immediately');
        logger.system('• Results are tracked asynchronously');
        logger.system('• Bundle inclusion is determined after target block');
        logger.system('• Tracking provides detailed success/failure information');
        logger.system('• This enables high-frequency MEV operations');

    } catch (error) {
        logger.debug(`Error showing tracking stats: ${(error as Error).message}`);
    }
}

// Run the monitoring demo
if (require.main === module) {
    main().catch(error => {
        logger.error('Script failed:', error);
        process.exit(1);
    });
}

export { main as monitorBundleResults };
