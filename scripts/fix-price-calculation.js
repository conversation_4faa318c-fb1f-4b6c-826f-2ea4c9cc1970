const { ethers } = require('ethers');

// Load configuration
require('dotenv').config();

/**
 * Fix Uniswap V3 price calculation
 */
function calculateV3PriceFromTick(tick, token0Decimals, token1Decimals) {
  // Convert tick to price using the correct formula
  const price = Math.pow(1.0001, tick);
  
  // Adjust for token decimals
  const decimalsAdjustment = Math.pow(10, token1Decimals - token0Decimals);
  
  return price * decimalsAdjustment;
}

/**
 * Calculate price from sqrtPriceX96 (CORRECTED)
 */
function calculateV3PriceFromSqrtPrice(sqrtPriceX96, token0Decimals, token1Decimals) {
  // Convert sqrtPriceX96 to actual price
  // sqrtPriceX96 = sqrt(price) * 2^96
  // price = (sqrtPriceX96 / 2^96)^2

  const Q96 = Math.pow(2, 96);
  const sqrtPrice = Number(sqrtPriceX96.toString()) / Q96;
  const price = sqrtPrice * sqrtPrice;

  // Adjust for token decimals
  // Price is token1/token0, so we need to adjust by 10^(token0Decimals - token1Decimals)
  const decimalsAdjustment = Math.pow(10, token0Decimals - token1Decimals);

  return price * decimalsAdjustment;
}

async function testPriceCalculations() {
  console.log('🔧 Testing Fixed Price Calculations');
  console.log('=' .repeat(50));

  try {
    const provider = new ethers.JsonRpcProvider(process.env.RPC_URL);

    // Test with WETH/USDC pool
    const wethUsdcV3Pool = '******************************************';
    const v3PoolContract = new ethers.Contract(
      wethUsdcV3Pool,
      [
        'function slot0() external view returns (uint160 sqrtPriceX96, int24 tick, uint16 observationIndex, uint16 observationCardinality, uint16 observationCardinalityNext, uint8 feeProtocol, bool unlocked)',
        'function token0() external view returns (address)',
        'function token1() external view returns (address)'
      ],
      provider
    );

    const slot0 = await v3PoolContract.slot0();
    const token0 = await v3PoolContract.token0();
    const token1 = await v3PoolContract.token1();
    
    console.log(`Pool: ${wethUsdcV3Pool}`);
    console.log(`Token0: ${token0}`);
    console.log(`Token1: ${token1}`);
    console.log(`Current tick: ${slot0.tick}`);
    console.log(`SqrtPriceX96: ${slot0.sqrtPriceX96.toString()}`);

    // Determine which token is which
    const USDC_ADDRESS = '******************************************';
    const WETH_ADDRESS = '******************************************';
    
    const isToken0USDC = token0.toLowerCase() === USDC_ADDRESS.toLowerCase();
    const isToken0WETH = token0.toLowerCase() === WETH_ADDRESS.toLowerCase();
    
    console.log(`\nToken identification:`);
    console.log(`Token0 is USDC: ${isToken0USDC}`);
    console.log(`Token0 is WETH: ${isToken0WETH}`);

    // Calculate price using sqrtPriceX96 (CORRECTED)
    // In V3, price is always token1/token0
    // If token0=USDC, token1=WETH, then price = WETH/USDC
    // If token0=WETH, token1=USDC, then price = USDC/WETH

    let v3PriceToken1PerToken0;
    if (isToken0USDC) {
      // Token0 = USDC, Token1 = WETH
      // Price = WETH per USDC
      v3PriceToken1PerToken0 = calculateV3PriceFromSqrtPrice(slot0.sqrtPriceX96, 6, 18);
      console.log(`\nV3 Price (WETH per USDC): ${v3PriceToken1PerToken0.toFixed(8)}`);
      console.log(`V3 Price (USDC per WETH): ${(1/v3PriceToken1PerToken0).toFixed(2)}`);
    } else if (isToken0WETH) {
      // Token0 = WETH, Token1 = USDC
      // Price = USDC per WETH
      v3PriceToken1PerToken0 = calculateV3PriceFromSqrtPrice(slot0.sqrtPriceX96, 18, 6);
      console.log(`\nV3 Price (USDC per WETH): ${v3PriceToken1PerToken0.toFixed(2)}`);
      console.log(`V3 Price (WETH per USDC): ${(1/v3PriceToken1PerToken0).toFixed(8)}`);
    }

    // Compare with V2 price
    const uniV2Pair = '******************************************';
    const pairContract = new ethers.Contract(
      uniV2Pair,
      [
        'function getReserves() external view returns (uint112 reserve0, uint112 reserve1, uint32 blockTimestampLast)',
        'function token0() external view returns (address)',
        'function token1() external view returns (address)'
      ],
      provider
    );

    const reserves = await pairContract.getReserves();
    const v2Token0 = await pairContract.token0();
    const v2Token1 = await pairContract.token1();
    
    console.log(`\nV2 Pool: ${uniV2Pair}`);
    console.log(`V2 Token0: ${v2Token0}`);
    console.log(`V2 Token1: ${v2Token1}`);

    const isV2Token0USDC = v2Token0.toLowerCase() === USDC_ADDRESS.toLowerCase();
    
    let v2Price;
    if (isV2Token0USDC) {
      // Token0 = USDC, Token1 = WETH
      const reserve0 = Number(ethers.formatUnits(reserves.reserve0, 6)); // USDC
      const reserve1 = Number(ethers.formatUnits(reserves.reserve1, 18)); // WETH
      v2Price = reserve0 / reserve1; // USDC per WETH
      console.log(`V2 Price (USDC per WETH): ${v2Price.toFixed(2)}`);
    } else {
      // Token0 = WETH, Token1 = USDC
      const reserve0 = Number(ethers.formatUnits(reserves.reserve0, 18)); // WETH
      const reserve1 = Number(ethers.formatUnits(reserves.reserve1, 6)); // USDC
      v2Price = reserve1 / reserve0; // USDC per WETH
      console.log(`V2 Price (USDC per WETH): ${v2Price.toFixed(2)}`);
    }

    // Calculate arbitrage opportunity
    let v3PriceUSDCPerWETH;
    if (isToken0USDC) {
      // Token0=USDC, Token1=WETH, so price is WETH/USDC, we want USDC/WETH
      v3PriceUSDCPerWETH = 1 / v3PriceToken1PerToken0;
    } else {
      // Token0=WETH, Token1=USDC, so price is already USDC/WETH
      v3PriceUSDCPerWETH = v3PriceToken1PerToken0;
    }

    console.log(`\n📊 Price Comparison:`);
    console.log(`V2 Price (USDC/WETH): ${v2Price.toFixed(2)}`);
    console.log(`V3 Price (USDC/WETH): ${v3PriceUSDCPerWETH.toFixed(2)}`);
    
    const priceDiff = Math.abs(v2Price - v3PriceUSDCPerWETH);
    const priceSpread = (priceDiff / Math.min(v2Price, v3PriceUSDCPerWETH)) * 100;
    
    console.log(`Price difference: ${priceDiff.toFixed(2)} USDC`);
    console.log(`Price spread: ${priceSpread.toFixed(4)}%`);
    
    if (priceSpread > 0.1) {
      console.log(`🎯 Arbitrage opportunity: ${priceSpread.toFixed(4)}% spread`);
      
      if (v2Price > v3PriceUSDCPerWETH) {
        console.log(`Strategy: Buy WETH on V3, sell on V2`);
      } else {
        console.log(`Strategy: Buy WETH on V2, sell on V3`);
      }
    } else {
      console.log(`📉 No significant arbitrage opportunity (${priceSpread.toFixed(4)}% spread)`);
    }

    console.log('\n✅ Price calculation test completed!');

  } catch (error) {
    console.error('❌ Price calculation test failed:', error.message);
    console.error(error.stack);
  }
}

// Run test
testPriceCalculations().catch(console.error);
