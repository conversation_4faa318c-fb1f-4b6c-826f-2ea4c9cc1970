# 🌐 MEV Bot Web Dashboard

A modern web-based dashboard that replicates the functionality of the terminal split-screen dashboard in a browser interface.

## Features

- **Split-screen layout**: Status dashboard on the left, live logs on the right
- **Real-time updates**: WebSocket connection for instant data updates
- **Modern web interface**: Clean, responsive design with dark theme
- **Live logging**: Real-time log streaming with level filtering
- **Interactive controls**: Refresh button, log filtering, and clear logs
- **Mobile responsive**: Works on desktop and mobile devices
- **No terminal artifacts**: Smooth rendering without terminal display issues

## Quick Start

### Method 1: Run with web dashboard (Recommended)
```bash
npm run dev:web
```

### Method 2: Run with watch mode (for development)
```bash
npm run dev:watch:web
```

### Method 3: Test the web dashboard with simulated data
```bash
npm run test:web-dashboard
```

## Accessing the Dashboard

Once started, the web dashboard will be available at:
- **URL**: http://localhost:3000
- **Default Port**: 3000

The console will display:
```
🌐 Web Dashboard running at http://localhost:3000
📊 Dashboard accessible in your browser
```

## Dashboard Layout

### Left Panel: Status Dashboard
- **Status Overview**: Bot status, network info, ETH balance, uptime
- **Strategy Status**: Flashloan, MEV Share, Arbitrage indicators
- **Statistics**: Transaction counts, opportunities, profit, gas prices
- **Configuration**: Token pairs, DEXes, profit thresholds
- **Successful Transactions**: Recent profitable trades

### Right Panel: Live Logs
- **Real-time logging**: All bot activities and events
- **Log level filtering**: Filter by Error, Warning, Info, Debug
- **Auto-scroll**: Automatically scrolls to newest logs
- **Clear logs**: Button to clear log history
- **Timestamps**: All logs include precise timestamps

## Features Comparison

| Feature | Terminal Dashboard | Web Dashboard |
|---------|-------------------|---------------|
| Split-screen layout | ✅ | ✅ |
| Real-time updates | ✅ | ✅ |
| Log filtering | ✅ | ✅ |
| Mouse scrolling | ⚠️ (artifacts) | ✅ |
| Mobile access | ❌ | ✅ |
| Multiple viewers | ❌ | ✅ |
| Copy/paste logs | ⚠️ (limited) | ✅ |
| Mouse mode toggle | ⚠️ (needed) | ❌ (not needed) |
| Browser bookmarks | ❌ | ✅ |
| Remote access | ❌ | ✅ |

## Environment Variables

Set the following environment variable to enable web dashboard:

```bash
export WEB_DASHBOARD=true
```

Or use the npm scripts which set it automatically:
- `npm run dev:web`
- `npm run dev:watch:web`

## Technical Implementation

### Backend
- **Express.js**: HTTP server for serving static files and API endpoints
- **Socket.IO**: WebSocket connection for real-time updates
- **TypeScript**: Full type safety and integration with existing codebase

### Frontend
- **Vanilla JavaScript**: No framework dependencies, lightweight
- **CSS Grid/Flexbox**: Responsive layout system
- **WebSocket client**: Real-time communication with backend
- **Dark theme**: Terminal-inspired color scheme

### Integration
- **Shared data structures**: Uses same `DashboardData` and `LogEntry` interfaces
- **Existing loggers**: Integrates with `logger` and `enhancedLogger`
- **Status dashboard**: Reuses existing status tracking logic

## API Endpoints

### REST API
- `GET /`: Main dashboard page
- `GET /api/dashboard`: Current dashboard data (JSON)
- `GET /api/logs?limit=100`: Recent log entries (JSON)

### WebSocket Events
- `dashboard-data`: Real-time dashboard updates
- `new-log`: Individual log entries as they occur
- `logs`: Bulk log data on connection
- `request-refresh`: Client can request data refresh

## Configuration

The web dashboard can be configured by modifying `src/server/webDashboard.ts`:

```typescript
const webDashboard = new WebDashboard({
  port: 3000,              // Server port
  title: 'MEV Bot Dashboard', // Page title
  maxLogLines: 500,        // Maximum log entries to keep
  refreshRate: 2000        // Update interval (ms)
});
```

## Development

### File Structure
```
src/server/
├── webDashboard.ts      # Main server and WebSocket handler
└── public/
    ├── index.html       # Dashboard HTML page
    ├── style.css        # Dashboard styling
    └── script.js        # Client-side JavaScript
```

### Adding Custom Features

1. **Backend**: Modify `src/server/webDashboard.ts`
2. **Frontend**: Update files in `src/server/public/`
3. **Styling**: Edit `src/server/public/style.css`
4. **Client logic**: Modify `src/server/public/script.js`

## Troubleshooting

### Port Already in Use
If port 3000 is busy, modify the port in `webDashboard.ts`:
```typescript
const webDashboard = new WebDashboard({ port: 3001 });
```

### WebSocket Connection Issues
- Check firewall settings
- Ensure port is accessible
- Verify no proxy blocking WebSocket connections

### Dashboard Not Updating
- Check browser console for JavaScript errors
- Verify WebSocket connection status
- Restart the dashboard server

## Security Considerations

- **Local access only**: Dashboard binds to localhost by default
- **No authentication**: Intended for local development use
- **CORS enabled**: Allows connections from any origin for development

For production use, consider:
- Adding authentication
- Restricting CORS origins
- Using HTTPS/WSS
- Implementing rate limiting

## Performance

- **Lightweight**: Minimal resource usage
- **Efficient updates**: Only sends changed data
- **Log rotation**: Automatically limits log history
- **Throttled rendering**: Prevents excessive DOM updates

## Browser Compatibility

- **Chrome/Chromium**: Full support
- **Firefox**: Full support
- **Safari**: Full support
- **Edge**: Full support
- **Mobile browsers**: Responsive design works on mobile

## Migration from Terminal Dashboard

To switch from terminal to web dashboard:

1. **Stop current bot**: `Ctrl+C`
2. **Start with web dashboard**: `npm run dev:web`
3. **Open browser**: Navigate to http://localhost:3000
4. **Enjoy improved experience**: No more terminal artifacts!

The web dashboard provides all the same information as the terminal version with improved usability and accessibility.
