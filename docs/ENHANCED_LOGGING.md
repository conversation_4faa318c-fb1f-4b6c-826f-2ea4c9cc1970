# Enhanced Logging with Wei Formatting and USD Values

The MEV bot now includes enhanced logging capabilities that automatically convert Wei values to readable ETH amounts **with current USD values** in error messages and logs.

## Features

### 🔄 Automatic Wei Detection and Formatting with USD Values

The logger now automatically detects large numbers (15+ digits) in error messages and formats them as readable ETH values with real-time USD conversion:

**Before:**
```
Error: insufficient funds for gas * price + value: have 3800073449395503 want 8989122000000000
```

**After:**
```
❌ ERROR: Transaction failed
Details: insufficient funds for gas * price + value: have 0.00380007 ETH ($12.31) [3800073449395503 wei] want 0.00898912 ETH ($29.11) [8989122000000000 wei]
```

### 💰 Specific Insufficient Funds Logging with USD Values

Use the dedicated method for clearer insufficient funds errors with real-time USD conversion:

```typescript
import { logger } from '../utils/logger';

await logger.insufficientFunds(
  '3800073449395503',    // available wei
  '8989122000000000',    // required wei
  'MEV arbitrage transaction'  // context
);
```

**Output:**
```
💰 INSUFFICIENT FUNDS:
Available: 0.00380007 ETH ($12.31) [3800073449395503 wei]
Required:  0.00898912 ETH ($29.11) [8989122000000000 wei]
Context:   MEV arbitrage transaction
```

**Synchronous Version (when async is not possible):**
```typescript
logger.insufficientFundsSync('3800073449395503', '8989122000000000', 'MEV transaction');
```

### ⛽ Gas Estimation Logging with USD Values

Log gas estimations with readable formatting including USD values:

```typescript
await logger.gasEstimation(
  '300000',                           // gas limit
  ethers.parseUnits('25', 'gwei'),   // gas price
  BigInt('7500000000000000')         // total cost in wei
);
```

**Output:**
```
⛽ GAS ESTIMATION:
Gas Limit:  300000
Gas Price:  25.0 gwei
Total Cost: 0.00750000 ETH ($24.29) [7500000000000000 wei]
```

**Synchronous Version:**
```typescript
logger.gasEstimationSync('300000', ethers.parseUnits('25', 'gwei'), BigInt('7500000000000000'));
```

### 💰 Profitability Analysis with USD Values

Log complete profitability analysis with Expected Profit, Gas Cost, and Net Profit including USD values:

```typescript
await logger.profitabilityAnalysis(
  ethers.parseEther('0.05'),    // expected profit
  ethers.parseEther('0.01'),    // gas cost
  ethers.parseEther('0.04'),    // net profit
  'Arbitrage Opportunity'       // context
);
```

**Output:**
```
🔍 PROFITABILITY ANALYSIS: (Arbitrage Opportunity)
Expected Profit: 0.05000000 ETH ($161.93) [50000000000000000 wei]
Gas Cost:        0.01000000 ETH ($32.39) [10000000000000000 wei]
Net Profit:      0.04000000 ETH ($129.54) [40000000000000000 wei]
```

**Individual Profit Components:**
```typescript
await logger.expectedProfit(ethers.parseEther('0.025'), 'Sandwich Attack');
await logger.gasCost(ethers.parseEther('0.008'), 'Sandwich Attack');
await logger.netProfit(ethers.parseEther('0.017'), 'Sandwich Attack');
```

**Synchronous Version:**
```typescript
logger.profitabilityAnalysisSync(expectedProfit, gasCost, netProfit, 'MEV Strategy');
```

### 📈 Enhanced Profit Calculation (Legacy Method)

The existing `profitCalculation` method has been enhanced with USD values:

```typescript
await logger.profitCalculation(ethers.parseEther('0.03'), true);  // profitable
await logger.profitCalculation(ethers.parseEther('-0.005'), false); // loss
```

**Output:**
```
Profit Analysis: PROFITABLE --> Expected profit: 0.03000000 ETH ($97.16) [30000000000000000 wei]
Profit Analysis: NOT PROFITABLE --> Loss: -0.00500000 ETH (-$16.19) [-5000000000000000 wei]
```

## Enhanced Error Handling

### 🛡️ ErrorHandler Class

The new `ErrorHandler` class provides intelligent error handling with automatic Wei formatting:

```typescript
import { ErrorHandler } from '../utils/error-handler';

// Automatically detect and format insufficient funds errors
try {
  await wallet.sendTransaction(tx);
} catch (error) {
  ErrorHandler.handleTransactionError(error, 'Flashloan execution');
}

// Handle balance checks
ErrorHandler.handleBalanceError(
  walletBalance,    // available wei
  requiredAmount,   // required wei
  'MEV strategy'    // context
);

// Wrap operations with retry logic and enhanced error handling
const result = await ErrorHandler.withErrorHandling(
  async () => await riskyOperation(),
  'MEV sandwich attack',
  { retryCount: 2, retryDelay: 1000 }
);
```

### 🔧 Utility Functions

Quick formatting utilities for custom use cases:

```typescript
import { WeiFormatter, formatWei, formatWeiWithBoth } from '../utils/formatting/wei-formatter';

// Convert Wei to ETH
const ethAmount = formatWei('1500000000000000000'); // "1.50000000"

// Format with ETH, USD, and Wei (async)
const fullFormat = await formatWeiWithBoth('1500000000000000000');
// "1.50000000 ETH ($4,858.31) [1500000000000000000 wei]"

// Synchronous version with cached USD price
const fullFormatSync = WeiFormatter.formatWeiWithBothSync('1500000000000000000');
// "1.50000000 ETH ($4,858.31) [1500000000000000000 wei]"

// Parse insufficient funds errors
const parsed = WeiFormatter.parseInsufficientFundsError(errorMessage);
if (parsed) {
  console.log(`Have: ${parsed.have}, Want: ${parsed.want}`);
}

// Format any text containing Wei values
const formatted = WeiFormatter.formatWeiInText(
  'Transaction requires 1500000000000000000 wei'
);
// "Transaction requires 1.50000000 ETH ($4,858.31) [1500000000000000000 wei] wei"
```

## Integration Examples

### 🔄 Updating Existing Code

**Before:**
```typescript
try {
  const tx = await wallet.sendTransaction(txRequest);
} catch (error) {
  logger.logError(error, 'Transaction failed');
}
```

**After:**
```typescript
import { ErrorHandler } from '../utils/error-handler';

try {
  const tx = await wallet.sendTransaction(txRequest);
} catch (error) {
  await ErrorHandler.handleTransactionError(error, 'Transaction execution');
}
```

### 📊 Balance Checking

```typescript
// Check if wallet has sufficient funds
const walletBalance = await provider.getBalance(wallet.address);
const requiredGas = gasLimit * gasPrice;

if (walletBalance < requiredGas) {
  await logger.insufficientFunds(
    walletBalance.toString(),
    requiredGas.toString(),
    'MEV strategy execution'
  );
  return false;
}
```

### 🎯 MEV Strategy Integration

```typescript
async function executeMEVStrategy() {
  // Log profitability analysis before execution
  const expectedProfit = ethers.parseEther('0.05');
  const gasCost = ethers.parseEther('0.01');
  const netProfit = expectedProfit - gasCost;

  await logger.profitabilityAnalysis(
    expectedProfit,
    gasCost,
    netProfit,
    'MEV Strategy Pre-check'
  );

  return await ErrorHandler.withErrorHandling(
    async () => {
      // Your MEV strategy logic here
      const result = await flashloanContract.executeArbitrage(params);
      return result;
    },
    'Flashloan arbitrage execution',
    {
      retryCount: 3,
      retryDelay: 2000,
      logSuccess: true
    }
  );
}
```

## Configuration

The enhanced logging respects your existing log level configuration. Wei formatting is automatically applied to:

- ✅ Error messages (`logger.error()`)
- ✅ Warning messages (`logger.warn()`)
- ✅ Info messages (`logger.info()`)
- ✅ Error handling (`logger.logError()`)

## Testing

Run the test scripts to see all formatting features in action:

**Basic Wei Formatting Test:**
```bash
npx ts-node src/test/logger-formatting-test.ts
```

**Profit Logging with USD Values Test:**
```bash
npx ts-node src/test/profit-logging-test.ts
```

**Comprehensive Examples:**
```bash
npx ts-node examples/enhanced-logging-usage.ts
```

## Real-Time ETH Price Integration

The logger automatically fetches current ETH prices from CoinGecko API:

- **🔄 Auto-refresh**: Price updates every 60 seconds
- **⚡ Cached**: Uses cached price for immediate responses
- **🛡️ Resilient**: Gracefully handles API failures
- **🚀 Fast**: Minimal impact on performance

## Benefits

1. **💰 Real-Time USD Values**: See exact dollar amounts for all ETH values
2. **📊 Comprehensive Profit Analysis**: Expected Profit, Gas Cost, and Net Profit with USD values
3. **🔍 Improved Readability**: Wei values are instantly readable as ETH and USD amounts
4. **🚀 Better Debugging**: Clear context for insufficient funds errors with dollar impact
5. **⚡ Easy Integration**: Drop-in replacement for existing error handling
6. **🎯 MEV-Specific**: Designed for common MEV bot error scenarios including profitability analysis
7. **🔄 Backward Compatible**: Existing code continues to work unchanged
8. **📈 Financial Context**: Understand the real-world value of transactions, profits, and errors
9. **🎨 Color-Coded Output**: Green for profits, red for losses, yellow for costs
10. **⚖️ Instant Profitability Assessment**: Quickly see if opportunities are worth pursuing

The enhanced logging makes debugging MEV bot issues much easier by providing clear, readable information about Wei values, gas costs, profit calculations, and balance requirements **with real-time USD values** for better financial context and decision-making.
