# Balancer Flashloan Configuration

## Overview

The Balancer flashloan strategy now supports configurable parameters through environment variables, allowing you to optimize the bot's behavior for different market conditions without code changes.

## Configuration Variables

### `BALANCER_MIN_PROFIT_THRESHOLD`
- **Type**: Number (ETH)
- **Default**: `0.003`
- **Description**: Minimum profit required to execute a Balancer flashloan
- **Range**: `0.001` - `0.01` (recommended)

**Examples:**
```bash
BALANCER_MIN_PROFIT_THRESHOLD=0.001  # Aggressive (more opportunities)
BALANCER_MIN_PROFIT_THRESHOLD=0.003  # Balanced (current default)
BALANCER_MIN_PROFIT_THRESHOLD=0.01   # Conservative (fewer, safer trades)
```

### `BALANCER_MAX_FLASHLOAN_AMOUNT`
- **Type**: Number (USDC)
- **Default**: `50000`
- **Description**: Maximum flashloan amount in USDC
- **Range**: `1000` - `100000` (recommended)

**Examples:**
```bash
BALANCER_MAX_FLASHLOAN_AMOUNT=25000   # Conservative (lower risk)
BALANCER_MAX_FLASHLOAN_AMOUNT=50000   # Balanced (current default)
BALANCER_MAX_FLASHLOAN_AMOUNT=100000  # Aggressive (higher potential profit)
```

## Market Condition Optimization

### Bull Market Settings
```bash
BALANCER_MIN_PROFIT_THRESHOLD=0.001   # Lower threshold for more opportunities
BALANCER_MAX_FLASHLOAN_AMOUNT=75000   # Higher amounts for bigger profits
```

### Bear Market Settings
```bash
BALANCER_MIN_PROFIT_THRESHOLD=0.005   # Higher threshold for safety
BALANCER_MAX_FLASHLOAN_AMOUNT=25000   # Lower amounts to reduce risk
```

### High Gas Environment
```bash
BALANCER_MIN_PROFIT_THRESHOLD=0.008   # Higher threshold to cover gas costs
BALANCER_MAX_FLASHLOAN_AMOUNT=50000   # Standard amounts
```

### Low Liquidity Periods
```bash
BALANCER_MIN_PROFIT_THRESHOLD=0.003   # Standard threshold
BALANCER_MAX_FLASHLOAN_AMOUNT=20000   # Lower amounts due to limited liquidity
```

## Profit Threshold Analysis

| Threshold | Min Spread Needed* | Risk Level | Opportunities |
|-----------|-------------------|------------|---------------|
| 0.001 ETH | ~0.02%           | High       | Many          |
| 0.003 ETH | ~0.06%           | Medium     | Moderate      |
| 0.005 ETH | ~0.10%           | Low        | Few           |
| 0.01 ETH  | ~0.20%           | Very Low   | Rare          |

*For a 5 ETH flashloan

## Max Amount Considerations

### Balancer Liquidity Limits
- **Typical Pool Size**: 10k - 100k USDC
- **Recommended Max**: 50k USDC (safe for most pools)
- **Aggressive Max**: 100k USDC (may hit liquidity limits)

### Risk vs Reward
- **Lower amounts**: Safer, more consistent execution
- **Higher amounts**: Bigger profits but higher failure risk

## Configuration Examples

### Day Trading Setup
```bash
# Quick, frequent trades
BALANCER_MIN_PROFIT_THRESHOLD=0.002
BALANCER_MAX_FLASHLOAN_AMOUNT=30000
```

### Swing Trading Setup
```bash
# Fewer, larger trades
BALANCER_MIN_PROFIT_THRESHOLD=0.008
BALANCER_MAX_FLASHLOAN_AMOUNT=75000
```

### Testing Setup
```bash
# Safe testing parameters
BALANCER_MIN_PROFIT_THRESHOLD=0.01
BALANCER_MAX_FLASHLOAN_AMOUNT=10000
```

## Monitoring and Adjustment

### Key Metrics to Watch
1. **Success Rate**: % of attempted flashloans that succeed
2. **Opportunity Frequency**: How often opportunities are found
3. **Average Profit**: Mean profit per successful trade
4. **Gas Efficiency**: Profit after gas costs

### Adjustment Guidelines

**If success rate < 70%:**
- Increase `BALANCER_MIN_PROFIT_THRESHOLD`
- Decrease `BALANCER_MAX_FLASHLOAN_AMOUNT`

**If opportunities < 1 per hour:**
- Decrease `BALANCER_MIN_PROFIT_THRESHOLD`
- Consider market conditions

**If average profit < threshold:**
- Check gas costs and market efficiency
- Adjust thresholds accordingly

## Implementation Details

### Code Changes
The configuration is loaded in `src/config/index.ts`:
```typescript
balancerMinProfitThreshold: parseFloat(process.env.BALANCER_MIN_PROFIT_THRESHOLD || '0.003'),
balancerMaxFlashloanAmount: parseInt(process.env.BALANCER_MAX_FLASHLOAN_AMOUNT || '50000'),
```

### Strategy Usage
The `BalancerFlashloanStrategy` uses these values:
```typescript
this.MIN_PROFIT_THRESHOLD = config.balancerMinProfitThreshold;
this.MAX_FLASHLOAN_AMOUNT = ethers.parseUnits(config.balancerMaxFlashloanAmount.toString(), 6);
```

## Best Practices

1. **Start Conservative**: Begin with higher thresholds and lower amounts
2. **Monitor Performance**: Track success rates and adjust accordingly
3. **Market Adaptation**: Change settings based on market volatility
4. **Gas Awareness**: Increase thresholds during high gas periods
5. **Liquidity Checks**: Ensure max amounts don't exceed pool liquidity

## Troubleshooting

### No Opportunities Found
- Lower `BALANCER_MIN_PROFIT_THRESHOLD`
- Check market conditions and volatility

### High Failure Rate
- Increase `BALANCER_MIN_PROFIT_THRESHOLD`
- Decrease `BALANCER_MAX_FLASHLOAN_AMOUNT`

### Low Profits
- Check gas costs vs profit threshold
- Consider market efficiency changes

## Quick Start

1. **Edit `.env` file** with desired values
2. **Rebuild project**: `npm run build`
3. **Start bot**: `npm run dev`
4. **Monitor performance** and adjust as needed

The bot will automatically use the new configuration values without requiring code changes.
