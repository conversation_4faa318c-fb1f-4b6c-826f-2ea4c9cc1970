# Pool Validation & Dynamic Liquidity Fetching

This document describes the new pool validation and dynamic liquidity fetching features added to the MEV bot.

## Overview

The MEV bot now includes two major improvements:

1. **Pool Validation on Startup**: Automatically validates that Uniswap V2/V3 pools exist for all configured token pairs
2. **Dynamic Balancer Liquidity Fetching**: Fetches real-time Balancer liquidity instead of using hardcoded parameters

## Features

### 1. Pool Validation

The bot now validates on startup that:
- Uniswap V2 pools exist for configured token pairs
- Uniswap V3 pools exist for configured token pairs (checks multiple fee tiers: 0.05%, 0.3%, 1%)
- Balancer pools exist for configured token pairs (if Balancer is available)

#### Configuration

```bash
# Enable/disable pool validation
ENABLE_POOL_VALIDATION=true

# Stop bot if validation fails (recommended for production)
FAIL_ON_POOL_VALIDATION_ERROR=false
```

#### Example Output

```
🔍 Validating configured token pairs and pools...
Primary token: USDC (******************************************)
Target tokens: WETH, USDT, DAI
Checking pools for USDC/WETH...
✅ Uniswap V2 pool found: ******************************************
✅ Uniswap V3 pool found (0.05%): ******************************************
✅ Uniswap V3 pool found (0.3%): ******************************************
✅ Balancer pool found: 0x5c6ee304399dbdb9c8ef030ab642b10820db8f56000200000000000000000014
✅ All configured token pairs have available pools
```

### 2. Dynamic Balancer Liquidity Fetching

Instead of using hardcoded liquidity estimates, the bot now:
- Queries the Balancer Vault contract directly
- Fetches real-time token balances from known Balancer pools
- Calculates total available liquidity across all pools
- Uses this data for more accurate flashloan opportunity detection

#### Configuration

```bash
# Enable/disable dynamic liquidity fetching
ENABLE_LIQUIDITY_FETCHING=true
```

#### Example Output

```
💧 Fetching Balancer liquidity...
   USDC: 1250000.0 (2 pools)
   WETH: 450.75 (2 pools)
   DAI: 890000.0 (1 pools)
   USDT: No liquidity found
✅ Pool validation and liquidity check completed
```

## Implementation Details

### Pool Manager Enhancements

The `PoolManager` class now includes:

- `validateConfiguredPools()`: Validates all configured token pairs have available pools
- `fetchBalancerLiquidity()`: Fetches real-time Balancer liquidity for a token
- `getBalancerPoolAddress()`: Resolves Balancer pool IDs for token pairs
- Support for Balancer pool queries via the Vault contract

### Balancer Pool Support

The bot now supports Balancer pools with:
- Known pool IDs for common token pairs on mainnet
- Real-time liquidity queries via the Balancer Vault
- Integration with the existing pool management system

### Startup Validation

The bot performs validation during startup:
1. Validates wallet balance
2. **NEW**: Validates pools and fetches liquidity
3. Initializes Flashbots
4. Starts mempool monitoring

## Configuration Options

| Variable | Default | Description |
|----------|---------|-------------|
| `ENABLE_POOL_VALIDATION` | `true` | Enable pool validation on startup |
| `ENABLE_LIQUIDITY_FETCHING` | `true` | Enable dynamic Balancer liquidity fetching |
| `FAIL_ON_POOL_VALIDATION_ERROR` | `false` | Stop bot if pool validation fails |

## Benefits

### 1. Improved Reliability
- Ensures all required pools exist before starting trading
- Prevents runtime errors due to missing pools
- Provides early warning of configuration issues

### 2. Better Accuracy
- Uses real-time liquidity data instead of estimates
- More accurate profit calculations
- Better opportunity detection

### 3. Enhanced Monitoring
- Clear startup validation logs
- Detailed liquidity information
- Better error reporting

## Error Handling

The validation system includes comprehensive error handling:

- **Warnings**: Missing pools that don't prevent operation
- **Errors**: Critical issues that may require attention
- **Graceful Degradation**: Continues operation with warnings unless configured otherwise

## Supported Networks

### Mainnet
- Full Balancer pool support with known pool IDs
- Complete Uniswap V2/V3 validation
- Real-time liquidity fetching

### Testnets (Sepolia)
- Uniswap V2/V3 validation
- Limited Balancer support (fewer known pools)
- Conservative liquidity estimates as fallback

## Future Enhancements

Planned improvements include:
- Balancer subgraph integration for automatic pool discovery
- Support for additional DEX protocols
- Dynamic pool discovery without hardcoded pool IDs
- Liquidity monitoring and alerting
- Pool performance analytics

## Troubleshooting

### Common Issues

1. **Pool validation warnings**: Some token pairs may not have pools on all DEXs - this is normal
2. **Balancer liquidity errors**: May occur on testnets where Balancer has limited presence
3. **RPC rate limits**: Use your own node or high-limit providers for best results

### Debug Mode

Enable debug logging to see detailed validation information:

```bash
LOG_LEVEL=debug
```

This will show detailed pool lookup attempts, liquidity queries, and error details.
