const { ethers } = require('ethers');

async function debugExecutionFlow() {
    console.log('🔍 Debugging contract execution flow...');
    
    // Connect to mainnet
    const provider = new ethers.JsonRpcProvider('http://************:8545');
    const wallet = new ethers.Wallet('0xac0974bec39a17e36ba4a6b4d238ff944bacb478cbed5efcae784d7bf4f2ff80', provider);
    
    // Current mainnet contract address
    const contractAddress = '******************************************';
    
    console.log(`📋 Debug Configuration:`);
    console.log(`   Contract: ${contractAddress}`);
    console.log(`   Network: Mainnet`);
    
    // Token addresses
    const wethAddress = '******************************************';
    const daiAddress = '******************************************';
    
    // Get router addresses from contract
    const contract = new ethers.Contract(contractAddress, [
        'function UNISWAP_V2_ROUTER() external view returns (address)',
        'function UNISWAP_V3_ROUTER() external view returns (address)',
        'function executeOptimalFlashloan(address asset, uint256 amount, bytes calldata params) external'
    ], wallet);
    
    const v2Router = await contract.UNISWAP_V2_ROUTER();
    const v3Router = await contract.UNISWAP_V3_ROUTER();
    
    console.log(`   V2 Router: ${v2Router}`);
    console.log(`   V3 Router: ${v3Router}`);
    
    // Create the failing parameters
    const failingParams = ethers.AbiCoder.defaultAbiCoder().encode(
        ['address[]', 'address[]', 'address', 'address', 'uint24[]', 'uint256', 'uint8', 'uint256', 'uint256'],
        [
            [wethAddress, daiAddress],               // buyPath: WETH → DAI
            [daiAddress, wethAddress],               // sellPath: DAI → WETH
            v3Router,                                // buyDex (V3)
            v2Router,                                // sellDex (V2)
            [3000],                                  // v3Fees (0.3%)
            ethers.parseEther('0.00001'),            // minProfit
            0,                                       // provider (AAVE)
            1000,                                    // slippageToleranceBps (10%)
            ethers.parseUnits('200', 'gwei')         // maxGasCostWei
        ]
    );
    
    console.log('\n🧪 Testing execution with detailed error analysis...');
    
    // Test with different amounts to see where it fails
    const testAmounts = [
        { name: '0.001 ETH (minimum)', amount: ethers.parseUnits('1', 'finney') },
        { name: '0.1 ETH (small)', amount: ethers.parseEther('0.1') },
        { name: '1 ETH (medium)', amount: ethers.parseEther('1.0') },
        { name: '2 ETH (original failing)', amount: ethers.parseEther('2.0') }
    ];
    
    for (const test of testAmounts) {
        console.log(`\n   Testing with ${test.name}:`);
        
        try {
            // Try to call the function and catch the detailed error
            await contract.executeOptimalFlashloan.staticCall(
                wethAddress,
                test.amount,
                failingParams
            );
            
            console.log(`      ✅ SUCCESS: ${test.name} worked!`);
            
        } catch (error) {
            // Parse the error to get more details
            const errorMsg = error.message;
            console.log(`      ❌ FAILED: ${errorMsg.split('(')[0]}`);
            
            // Try to decode the error data if available
            if (error.data) {
                try {
                    // Try to decode common error signatures
                    const errorSignatures = [
                        'Error(string)',
                        'Panic(uint256)',
                        'ArbitrageResultedInLoss()',
                        'BuyExecutionFailed()',
                        'SellExecutionFailed()'
                    ];
                    
                    for (const sig of errorSignatures) {
                        try {
                            const iface = new ethers.Interface([`error ${sig}`]);
                            const decoded = iface.parseError(error.data);
                            if (decoded) {
                                console.log(`      🔍 Decoded error: ${decoded.name}(${decoded.args.join(', ')})`);
                                break;
                            }
                        } catch (e) {
                            // Continue to next signature
                        }
                    }
                } catch (decodeError) {
                    console.log(`      🔍 Raw error data: ${error.data}`);
                }
            }
            
            // Analyze the error type
            if (errorMsg.includes('execution reverted') && !errorMsg.includes('reason')) {
                console.log(`      💡 Generic execution revert - likely in external DEX call`);
            } else if (errorMsg.includes('Arbitrage resulted in loss')) {
                console.log(`      ✅ Expected error - arbitrage logic working`);
            } else if (errorMsg.includes('Buy execution failed')) {
                console.log(`      💡 Issue in buy trade execution`);
            } else if (errorMsg.includes('Sell execution failed')) {
                console.log(`      💡 Issue in sell trade execution`);
            } else if (errorMsg.includes('insufficient funds')) {
                console.log(`      💡 Contract doesn't have tokens to trade`);
            } else if (errorMsg.includes('transfer amount exceeds balance')) {
                console.log(`      💡 Token balance issue`);
            }
        }
    }
    
    console.log('\n🔍 Checking contract token balances...');
    
    // Check if contract has any tokens
    const wethContract = new ethers.Contract(wethAddress, [
        'function balanceOf(address) external view returns (uint256)',
        'function symbol() external view returns (string)'
    ], provider);
    
    const daiContract = new ethers.Contract(daiAddress, [
        'function balanceOf(address) external view returns (uint256)',
        'function symbol() external view returns (string)'
    ], provider);
    
    try {
        const wethBalance = await wethContract.balanceOf(contractAddress);
        const daiBalance = await daiContract.balanceOf(contractAddress);
        const ethBalance = await provider.getBalance(contractAddress);
        
        console.log(`   ETH Balance: ${ethers.formatEther(ethBalance)} ETH`);
        console.log(`   WETH Balance: ${ethers.formatEther(wethBalance)} WETH`);
        console.log(`   DAI Balance: ${ethers.formatUnits(daiBalance, 18)} DAI`);
        
        if (wethBalance === 0n && daiBalance === 0n && ethBalance === 0n) {
            console.log(`   💡 Contract has no tokens - this explains execution failures`);
            console.log(`   💡 Flashloan should provide tokens, but initial validation might be failing`);
        }
        
    } catch (error) {
        console.log(`   ❌ Error checking balances: ${error.message}`);
    }
    
    console.log('\n💡 ANALYSIS SUMMARY:');
    console.log('   The issue is likely in the execution phase, not validation.');
    console.log('   The contract validation passes, but execution fails.');
    console.log('   This suggests the problem is in the DEX interaction logic.');
    console.log('   ');
    console.log('   Possible causes:');
    console.log('   1. Multi-hop trade execution logic has bugs');
    console.log('   2. Token approval issues in DEX calls');
    console.log('   3. Slippage protection too strict');
    console.log('   4. V3 fee handling in multi-hop trades');
}

debugExecutionFlow().catch(console.error);
