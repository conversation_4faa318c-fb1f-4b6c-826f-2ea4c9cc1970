import { ethers } from 'ethers';

// Token addresses on mainnet
export const TOKENS = {
    WETH: '******************************************',
    USDC: '******************************************', // USDC
    DAI: '******************************************',
    USDT: '******************************************'
} as const;

// DEX router addresses
export const DEX_ROUTERS = {
    UNISWAP_V2: '******************************************',
    UNISWAP_V3: '******************************************',
    SUSHISWAP: '******************************************',
    CURVE_3POOL: '******************************************', // DAI/USDC/USDT
    CURVE_TRICRYPTO2: '******************************************', // USDT/WBTC/WETH
    CURVE_STETH: '******************************************' // stETH/ETH
} as const;

// DEX types for our contract
export enum DEX_TYPE {
    UNSUPPORTED = 0,
    V2 = 1,
    V3 = 2,
    CURVE = 3
}

// Trading pairs to monitor
export const TRADING_PAIRS = [
    {
        name: 'WETH/USDC',
        tokenA: TOKENS.WETH,
        tokenB: TOKENS.USDC,
        decimalsA: 18,
        decimalsB: 6,
        minProfitUSD: 10, // $10 minimum profit
        dexes: [
            {
                name: 'Uniswap V3',
                router: DEX_ROUTERS.UNISWAP_V3,
                type: DEX_TYPE.V3,
                fees: [3000], // 0.3%
                priority: 1
            },
            {
                name: 'SushiSwap',
                router: DEX_ROUTERS.SUSHISWAP,
                type: DEX_TYPE.V2,
                fees: [],
                priority: 2
            }
            // Note: WETH/USDC not directly available on major Curve pools
        ]
    },
    {
        name: 'WETH/DAI',
        tokenA: TOKENS.WETH,
        tokenB: TOKENS.DAI,
        decimalsA: 18,
        decimalsB: 18,
        minProfitUSD: 10,
        dexes: [
            {
                name: 'Uniswap V3',
                router: DEX_ROUTERS.UNISWAP_V3,
                type: DEX_TYPE.V3,
                fees: [3000], // 0.3%
                priority: 1
            },
            {
                name: 'SushiSwap',
                router: DEX_ROUTERS.SUSHISWAP,
                type: DEX_TYPE.V2,
                fees: [],
                priority: 2
            }
            // Note: WETH/DAI not directly available on major Curve pools
        ]
    },
    {
        name: 'USDC/DAI',
        tokenA: TOKENS.USDC,
        tokenB: TOKENS.DAI,
        decimalsA: 6,
        decimalsB: 18,
        minProfitUSD: 5, // Lower minimum for stablecoin pairs
        dexes: [
            {
                name: 'Uniswap V3',
                router: DEX_ROUTERS.UNISWAP_V3,
                type: DEX_TYPE.V3,
                fees: [100, 500], // 0.01% and 0.05% pools
                priority: 1
            },
            {
                name: 'SushiSwap',
                router: DEX_ROUTERS.SUSHISWAP,
                type: DEX_TYPE.V2,
                fees: [],
                priority: 2
            },
            {
                name: 'Curve 3Pool',
                router: DEX_ROUTERS.CURVE_3POOL,
                type: DEX_TYPE.CURVE,
                fees: [],
                priority: 3,
                curveIndices: { USDC: 1, DAI: 0 } // Token indices in 3Pool
            }
        ]
    }
] as const;

// Arbitrage configuration
export const ARBITRAGE_CONFIG = {
    // Flashloan amounts to test (in ETH equivalent)
    FLASHLOAN_AMOUNTS: [
        ethers.parseEther('1'),     // 1 ETH
        ethers.parseEther('5'),     // 5 ETH
        ethers.parseEther('10'),    // 10 ETH
        ethers.parseEther('25'),    // 25 ETH
        ethers.parseEther('50')     // 50 ETH
    ],
    
    // Slippage tolerance (basis points)
    SLIPPAGE_TOLERANCE_BPS: 100, // 1%
    
    // Gas settings
    MAX_GAS_COST_GWEI: 100, // 100 gwei max
    
    // Minimum profit thresholds
    MIN_PROFIT_ETH: ethers.parseEther('0.01'), // 0.01 ETH minimum
    
    // Scan intervals
    PRICE_SCAN_INTERVAL_MS: 5000, // 5 seconds
    ARBITRAGE_SCAN_INTERVAL_MS: 10000, // 10 seconds
    
    // Flashloan provider preference
    FLASHLOAN_PROVIDER: 0, // 0 = AAVE, 1 = BALANCER
    
    // DEX priority for execution
    DEX_PRIORITY: {
        [DEX_TYPE.V3]: 1,     // Uniswap V3 highest priority
        [DEX_TYPE.CURVE]: 2,  // Curve second (low slippage for stablecoins)
        [DEX_TYPE.V2]: 3      // V2 forks third
    }
} as const;

// Price monitoring configuration
export const PRICE_MONITORING = {
    // WebSocket endpoints for real-time price feeds
    WEBSOCKET_ENDPOINTS: {
        UNISWAP_V3: 'wss://api.thegraph.com/subgraphs/name/uniswap/uniswap-v3',
        CURVE: 'wss://api.curve.fi/api/getPools'
    },
    
    // Price deviation threshold to trigger arbitrage scan
    PRICE_DEVIATION_THRESHOLD_BPS: 50, // 0.5% price difference
    
    // Maximum age of price data (milliseconds)
    MAX_PRICE_AGE_MS: 30000, // 30 seconds
    
    // Price sources priority
    PRICE_SOURCES: [
        'UNISWAP_V3',
        'CURVE',
        'SUSHISWAP'
    ]
} as const;

// Risk management
export const RISK_MANAGEMENT = {
    // Maximum number of concurrent arbitrage attempts
    MAX_CONCURRENT_ARBITRAGES: 3,
    
    // Cooldown period between arbitrages (milliseconds)
    ARBITRAGE_COOLDOWN_MS: 30000, // 30 seconds
    
    // Maximum daily loss threshold
    MAX_DAILY_LOSS_ETH: ethers.parseEther('0.1'), // 0.1 ETH
    
    // Circuit breaker - stop trading if too many failures
    MAX_CONSECUTIVE_FAILURES: 5,
    
    // Gas price limits
    MAX_GAS_PRICE_GWEI: 200, // 200 gwei max
    
    // Profit validation
    REQUIRE_POSITIVE_PROFIT: true,
    PROFIT_SAFETY_MARGIN_BPS: 200 // 2% safety margin
} as const;

// Logging and monitoring
export const MONITORING_CONFIG = {
    // Log levels
    LOG_LEVEL: 'info', // 'debug', 'info', 'warn', 'error'
    
    // Performance metrics
    TRACK_PERFORMANCE: true,
    PERFORMANCE_LOG_INTERVAL_MS: 60000, // 1 minute
    
    // Alert thresholds
    ALERT_ON_LARGE_PROFIT: ethers.parseEther('0.1'), // Alert if profit > 0.1 ETH
    ALERT_ON_LOSS: true,
    
    // Dashboard settings
    DASHBOARD_UPDATE_INTERVAL_MS: 2000, // 2 seconds
    DASHBOARD_PORT: 3001
} as const;

export default {
    TOKENS,
    DEX_ROUTERS,
    DEX_TYPE,
    TRADING_PAIRS,
    ARBITRAGE_CONFIG,
    PRICE_MONITORING,
    RISK_MANAGEMENT,
    MONITORING_CONFIG
};
