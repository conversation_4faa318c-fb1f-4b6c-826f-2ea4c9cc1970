import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class ArbitrageConfigService {
  constructor(private readonly configService: ConfigService) {}

  get enableFlashloanAttacks(): boolean {
    return this.configService.get<boolean>('ENABLE_FLASHLOAN_ATTACKS', true);
  }

  get enableArbitrage(): boolean {
    return this.configService.get<boolean>('ENABLE_ARBITRAGE', true);
  }

  get enableMevShare(): boolean {
    return this.configService.get<boolean>('ENABLE_MEV_SHARE', false);
  }

  get flashloanContractAddress(): string {
    const address = this.configService.get<string>('FLASHLOAN_CONTRACT_ADDRESS');
    if (!address) {
      throw new Error('FLASHLOAN_CONTRACT_ADDRESS environment variable is required');
    }
    return address;
  }

  get minProfitThreshold(): string {
    return this.configService.get<string>('MIN_PROFIT_THRESHOLD', '0.001');
  }

  get maxSlippageBps(): number {
    return this.configService.get<number>('MAX_SLIPPAGE_BPS', 100);
  }

  get maxTradeAmount(): string {
    return this.configService.get<string>('MAX_TRADE_AMOUNT', '10');
  }

  get minTradeAmount(): string {
    return this.configService.get<string>('MIN_TRADE_AMOUNT', '0.1');
  }

  get maxConcurrentTrades(): number {
    return this.configService.get<number>('MAX_CONCURRENT_TRADES', 3);
  }

  get tradeTimeoutMs(): number {
    return this.configService.get<number>('TRADE_TIMEOUT_MS', 30000);
  }

  get maxConsecutiveFailures(): number {
    return this.configService.get<number>('MAX_CONSECUTIVE_FAILURES', 5);
  }

  get dailyLossLimit(): string {
    return this.configService.get<string>('DAILY_LOSS_LIMIT', '1');
  }

  get enableRiskManagement(): boolean {
    return this.configService.get<boolean>('ENABLE_RISK_MANAGEMENT', true);
  }

  get monitoredTokens(): string[] {
    const tokens = this.configService.get<string>('MONITORED_TOKENS', 'WETH,USDC,DAI,USDT');
    return tokens.split(',').map(token => token.trim());
  }

  get supportedDexes(): string[] {
    const dexes = this.configService.get<string>('SUPPORTED_DEXES', 'UNISWAP_V2,UNISWAP_V3,SUSHISWAP,CURVE,BALANCER');
    return dexes.split(',').map(dex => dex.trim());
  }
}
