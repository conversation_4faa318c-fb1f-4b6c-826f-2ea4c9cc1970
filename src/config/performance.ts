/**
 * Performance configuration for MEV Bot
 * Centralized settings for optimizing CPU usage, memory consumption, and responsiveness
 */

export interface PerformanceConfig {
  // Worker thread settings
  workerThreads: {
    enabled: boolean;
    maxWorkers: number;
    taskTimeout: number; // milliseconds
    maxQueueSize: number;
    workerIdleTimeout: number; // milliseconds
    enableLoadBalancing: boolean;
  };

  // Dashboard and UI settings
  dashboard: {
    refreshRate: number; // milliseconds
    logRenderThrottle: number; // milliseconds
    statusRenderThrottle: number; // milliseconds
    maxLogLines: number;
    batchLogUpdates: boolean;
    maxBatchSize: number;
    enableVirtualScrolling: boolean;
    forceLogRerender: boolean;
  };

  // Transaction processing settings
  mempool: {
    processThrottle: number; // milliseconds between batch processing
    maxQueueSize: number; // maximum transactions in queue
    batchSize: number; // transactions to process per batch
  };

  // Performance monitoring settings
  monitoring: {
    enabled: boolean;
    intervalMs: number;
    maxMetricsHistory: number;
    cpuWarningThreshold: number; // percentage
    memoryWarningThreshold: number; // percentage
    eventLoopLagThreshold: number; // milliseconds
  };

  // Memory management settings
  memory: {
    maxLogBufferSize: number;
    maxTransactionHistory: number;
    maxOpportunityHistory: number;
    gcInterval: number; // milliseconds
  };

  // Network and RPC settings
  network: {
    maxConcurrentRequests: number;
    requestTimeout: number; // milliseconds
    retryAttempts: number;
    retryDelay: number; // milliseconds
  };
}

// Default performance configuration
export const defaultPerformanceConfig: PerformanceConfig = {
  workerThreads: {
    enabled: false, // Disabled by default until issues are resolved
    maxWorkers: Math.min(4, require('os').cpus().length), // Reduced worker count
    taskTimeout: 30000, // Increased timeout to 30 seconds
    maxQueueSize: 100, // Reduced queue size
    workerIdleTimeout: 300000, // 5 minutes
    enableLoadBalancing: true
  },

  dashboard: {
    refreshRate: 3000, // 3 seconds
    logRenderThrottle: 200, // 200ms
    statusRenderThrottle: 1000, // 1 second
    maxLogLines: 500,
    batchLogUpdates: true,
    maxBatchSize: 10,
    enableVirtualScrolling: true,
    forceLogRerender: false
  },

  mempool: {
    processThrottle: 50, // 50ms
    maxQueueSize: 100,
    batchSize: 5
  },

  monitoring: {
    enabled: true,
    intervalMs: 2000, // 2 seconds
    maxMetricsHistory: 100,
    cpuWarningThreshold: 80,
    memoryWarningThreshold: 85,
    eventLoopLagThreshold: 100
  },

  memory: {
    maxLogBufferSize: 1000,
    maxTransactionHistory: 500,
    maxOpportunityHistory: 100,
    gcInterval: 30000 // 30 seconds
  },

  network: {
    maxConcurrentRequests: 10,
    requestTimeout: 5000,
    retryAttempts: 3,
    retryDelay: 1000
  }
};

// Performance profiles for different use cases
export const performanceProfiles = {
  // High performance mode - maximum speed, higher resource usage
  highPerformance: {
    ...defaultPerformanceConfig,
    workerThreads: {
      enabled: true,
      maxWorkers: require('os').cpus().length, // Use all available cores
      taskTimeout: 10000, // 10 second timeout for high performance
      maxQueueSize: 1000,
      workerIdleTimeout: 180000, // 3 minutes
      enableLoadBalancing: true
    },
    dashboard: {
      ...defaultPerformanceConfig.dashboard,
      refreshRate: 1000,
      logRenderThrottle: 100,
      statusRenderThrottle: 500
    },
    mempool: {
      processThrottle: 25,
      maxQueueSize: 200,
      batchSize: 10
    },
    monitoring: {
      ...defaultPerformanceConfig.monitoring,
      intervalMs: 1000
    }
  },

  // Balanced mode - good performance with reasonable resource usage
  balanced: defaultPerformanceConfig,

  // Low resource mode - minimal CPU and memory usage
  lowResource: {
    ...defaultPerformanceConfig,
    workerThreads: {
      enabled: true,
      maxWorkers: 2, // Minimal workers for low resource usage
      taskTimeout: 20000, // Longer timeout for low resource mode
      maxQueueSize: 100,
      workerIdleTimeout: 600000, // 10 minutes
      enableLoadBalancing: false // Disable load balancing to reduce overhead
    },
    dashboard: {
      ...defaultPerformanceConfig.dashboard,
      refreshRate: 5000,
      logRenderThrottle: 500,
      statusRenderThrottle: 2000,
      maxLogLines: 200,
      maxBatchSize: 5
    },
    mempool: {
      processThrottle: 100,
      maxQueueSize: 50,
      batchSize: 3
    },
    monitoring: {
      ...defaultPerformanceConfig.monitoring,
      intervalMs: 5000,
      maxMetricsHistory: 50
    },
    memory: {
      ...defaultPerformanceConfig.memory,
      maxLogBufferSize: 500,
      maxTransactionHistory: 200,
      maxOpportunityHistory: 50
    }
  },

  // Development mode - optimized for debugging and development
  development: {
    ...defaultPerformanceConfig,
    dashboard: {
      ...defaultPerformanceConfig.dashboard,
      refreshRate: 2000,
      maxLogLines: 1000,
      batchLogUpdates: false, // Immediate logging for debugging
      forceLogRerender: true
    },
    monitoring: {
      ...defaultPerformanceConfig.monitoring,
      enabled: true,
      intervalMs: 1000
    }
  }
};

// Get performance configuration based on environment
export function getPerformanceConfig(): PerformanceConfig {
  const mode = process.env.PERFORMANCE_MODE || 'balanced';
  
  switch (mode.toLowerCase()) {
    case 'high':
    case 'highperformance':
      return performanceProfiles.highPerformance;
    case 'low':
    case 'lowresource':
      return performanceProfiles.lowResource;
    case 'dev':
    case 'development':
      return performanceProfiles.development;
    case 'balanced':
    default:
      return performanceProfiles.balanced;
  }
}

// Apply performance optimizations based on current system
export function optimizeForSystem(): PerformanceConfig {
  const config = getPerformanceConfig();
  const cpuCount = require('os').cpus().length;
  const totalMemory = require('os').totalmem();
  const freeMemory = require('os').freemem();
  const memoryUsage = (totalMemory - freeMemory) / totalMemory;

  // Adjust worker threads based on CPU count
  if (cpuCount <= 2) {
    config.workerThreads.maxWorkers = 1;
  } else if (cpuCount <= 4) {
    config.workerThreads.maxWorkers = 2;
  } else {
    config.workerThreads.maxWorkers = Math.min(4, cpuCount - 1);
  }

  // Adjust memory settings based on available memory
  if (memoryUsage > 0.8) {
    // High memory usage - reduce buffers
    config.dashboard.maxLogLines = Math.floor(config.dashboard.maxLogLines * 0.5);
    config.memory.maxLogBufferSize = Math.floor(config.memory.maxLogBufferSize * 0.5);
    config.memory.maxTransactionHistory = Math.floor(config.memory.maxTransactionHistory * 0.5);
  }

  // Adjust refresh rates based on system load
  const loadAverage = require('os').loadavg()[0];
  if (loadAverage > cpuCount * 0.8) {
    // High CPU load - reduce refresh rates
    config.dashboard.refreshRate *= 2;
    config.dashboard.logRenderThrottle *= 2;
    config.monitoring.intervalMs *= 2;
  }

  return config;
}

// Export the current performance configuration
export const performanceConfig = optimizeForSystem();
