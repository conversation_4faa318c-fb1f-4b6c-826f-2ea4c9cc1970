import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { AppConfigService } from './app-config.service';
import { BlockchainConfigService } from './blockchain-config.service';
import { ArbitrageConfigService } from './arbitrage-config.service';

@Module({
  imports: [ConfigModule],
  providers: [
    AppConfigService,
    BlockchainConfigService,
    ArbitrageConfigService,
  ],
  exports: [
    AppConfigService,
    BlockchainConfigService,
    ArbitrageConfigService,
  ],
})
export class ConfigurationModule {}
