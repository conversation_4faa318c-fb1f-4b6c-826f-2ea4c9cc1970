import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class AppConfigService {
  constructor(private readonly configService: ConfigService) {}

  get port(): number {
    return this.configService.get<number>('PORT', 3000);
  }

  get nodeEnv(): string {
    return this.configService.get<string>('NODE_ENV', 'development');
  }

  get isDevelopment(): boolean {
    return this.nodeEnv === 'development';
  }

  get isProduction(): boolean {
    return this.nodeEnv === 'production';
  }

  get logLevel(): string {
    return this.configService.get<string>('LOG_LEVEL', 'info');
  }

  get enableWebDashboard(): boolean {
    return this.configService.get<boolean>('WEB_DASHBOARD', true);
  }

  get enableSplitScreenDashboard(): boolean {
    return this.configService.get<boolean>('SPLIT_SCREEN_DASHBOARD', false);
  }

  get dashboardUpdateInterval(): number {
    return this.configService.get<number>('DASHBOARD_UPDATE_INTERVAL', 5000);
  }
}
