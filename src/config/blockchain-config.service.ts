import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class BlockchainConfigService {
  constructor(private readonly configService: ConfigService) {}

  get chainId(): number {
    return this.configService.get<number>('CHAIN_ID', 1);
  }

  get privateKey(): string {
    const key = this.configService.get<string>('PRIVATE_KEY');
    if (!key) {
      throw new Error('PRIVATE_KEY environment variable is required');
    }
    return key;
  }

  get rpcUrls(): string[] {
    const urls = this.configService.get<string>('RPC_URLS', '');
    return urls.split(',').filter(url => url.trim().length > 0);
  }

  get primaryRpcUrl(): string {
    const urls = this.rpcUrls;
    if (urls.length === 0) {
      throw new Error('At least one RPC_URL is required');
    }
    return urls[0];
  }

  get flashbotsRelayUrl(): string {
    return this.configService.get<string>(
      'FLASHBOTS_RELAY_URL',
      'https://relay.flashbots.net'
    );
  }

  get maxGasPrice(): string {
    return this.configService.get<string>('MAX_GAS_PRICE', '100');
  }

  get maxPriorityFeePerGas(): string {
    return this.configService.get<string>('MAX_PRIORITY_FEE_PER_GAS', '2');
  }

  get gasLimitMultiplier(): number {
    return this.configService.get<number>('GAS_LIMIT_MULTIPLIER', 1.2);
  }

  get confirmationBlocks(): number {
    return this.configService.get<number>('CONFIRMATION_BLOCKS', 1);
  }

  get blockTimeMs(): number {
    return this.configService.get<number>('BLOCK_TIME_MS', 12000);
  }
}
