import express from 'express';
import {createServer} from 'http';
import {Server as SocketIOServer} from 'socket.io';
import path from 'path';
import {LogEntry, LogLevel} from '../types';
import {config} from '../config';

export interface DashboardData {
    // Network status
    currentBlock: number;
    networkName: string;

    // Wallet status
    ethBalance: bigint;
    lastBalanceUpdate: number;

    // Bot status
    isRunning: boolean;
    uptime: number;
    lastActivity: number;

    // Strategy status
    flashloanEnabled: boolean;
    mevShareEnabled: boolean;
    arbitrageEnabled: boolean;

    // Statistics
    totalTransactions: number;
    relevantTransactions: number;
    opportunitiesFound: number;
    opportunitiesExecuted: number;
    totalProfit: bigint;
    avgGasPrice: bigint;

    // Bundle inclusion statistics
    bundleSubmissions: number;
    bundleInclusions: number;
    bundleInclusionRate: number;
    avgPriorityFee: string;

    // Configuration
    configuration: {
        tokenPairs: string[];
        dexes: string[];
        minProfitThreshold: string;
        maxGasPrice: string;
    };

    // Successful transactions
    successfulTransactions: Array<{
        timestamp: number;
        type: string;
        profit: bigint;
        gasUsed: bigint;
        txHash?: string;
        confidence?: number;
        details?: string;
    }>;

    // Error tracking
    errors: number;
    lastError?: string;
}

export interface WebDashboardConfig {
    port: number;
    title: string;
    maxLogLines: number;
    // refreshRate removed - using real-time WebSocket updates instead
}

export class WebDashboard {
    private app: express.Application;
    private server: any;
    private io: SocketIOServer;
    private config: WebDashboardConfig;
    private dashboardData: DashboardData;
    private logEntries: LogEntry[] = [];
    private isRunning: boolean = false;

    constructor(config: Partial<WebDashboardConfig> = {}) {
        this.config = {
            port: parseInt(process.env.WEB_DASHBOARD_PORT || '3001'), // Use env var or default to 3001
            title: 'MEV Bot Web Dashboard',
            maxLogLines: 1000, // Increased to match client-side expectations
            // No refreshRate needed - using real-time WebSocket updates
            ...config
        };

        this.dashboardData = this.getDefaultDashboardData();
        this.app = express();
        this.server = createServer(this.app);
        this.io = new SocketIOServer(this.server, {
            cors: {
                origin: "*",
                methods: ["GET", "POST"]
            }
        });

        this.setupRoutes();
        this.setupSocketHandlers();
    }

    private getDefaultDashboardData(): DashboardData {
        return {
            currentBlock: 0,
            networkName: 'Unknown',
            ethBalance: BigInt(0),
            lastBalanceUpdate: 0,
            isRunning: false,
            uptime: 0,
            lastActivity: 0,
            flashloanEnabled: false,
            mevShareEnabled: false,
            arbitrageEnabled: false,
            totalTransactions: 0,
            relevantTransactions: 0,
            opportunitiesFound: 0,
            opportunitiesExecuted: 0,
            totalProfit: BigInt(0),
            avgGasPrice: BigInt(0),
            bundleSubmissions: 0,
            bundleInclusions: 0,
            bundleInclusionRate: 0,
            avgPriorityFee: '0',
            configuration: {
                tokenPairs: [],
                dexes: [],
                minProfitThreshold: '0',
                maxGasPrice: '0'
            },
            successfulTransactions: [],
            errors: 0,
            lastError: undefined
        };
    }

    private setupRoutes(): void {
        // Serve static files from public directory
        this.app.use(express.static(path.join(__dirname, 'public')));

        // Main dashboard route
        this.app.get('/', (req, res) => {
            res.sendFile(path.join(__dirname, 'public', 'index.html'));
        });

        // API endpoint for dashboard data
        this.app.get('/api/dashboard', (req, res) => {
            res.json(this.serializeDashboardData());
        });

        // API endpoint for logs
        this.app.get('/api/logs', (req, res) => {
            const limit = parseInt(req.query.limit as string) || this.config.maxLogLines;
            const recentLogs = this.logEntries.slice(-limit);
            res.json(recentLogs);
        });
    }

    private setupSocketHandlers(): void {
        this.io.on('connection', (socket) => {
            console.log('Client connected to web dashboard');

            // Send initial data
            socket.emit('dashboard-data', this.serializeDashboardData());
            // Send all available logs (up to maxLogLines) instead of just 50
            socket.emit('logs', this.logEntries.slice(-this.config.maxLogLines));

            socket.on('disconnect', () => {
                console.log('Client disconnected from web dashboard');
            });

            socket.on('request-refresh', () => {
                socket.emit('dashboard-data', this.serializeDashboardData());
                // Send all available logs (up to maxLogLines) instead of just 50
                socket.emit('logs', this.logEntries.slice(-this.config.maxLogLines));
            });
        });
    }

    private serializeDashboardData(): any {
        return {
            ...this.dashboardData,
            ethBalance: this.dashboardData.ethBalance.toString(),
            totalProfit: this.dashboardData.totalProfit.toString(),
            avgGasPrice: this.dashboardData.avgGasPrice.toString(),
            successfulTransactions: this.dashboardData.successfulTransactions.map(tx => ({
                ...tx,
                profit: tx.profit.toString(),
                gasUsed: tx.gasUsed.toString()
            }))
        };
    }

    public start(): void {
        if (this.isRunning) {
            return;
        }

        this.server.listen(this.config.port, () => {
            console.log(`🌐 Web Dashboard running at http://192.168.0.16:${this.config.port}`);
            console.log(`📊 Dashboard accessible in your browser`);
            console.log(`⚡ Real-time updates via WebSocket - no polling needed`);
        });

        this.isRunning = true;

        // No periodic updates needed - WebSocket provides real-time communication
        // Updates are sent immediately when data changes via broadcastUpdate()
    }

    public stop(): void {
        if (!this.isRunning) {
            return;
        }

        // No timer to clear - using real-time WebSocket updates
        this.server.close();
        this.isRunning = false;
        console.log('🛑 Web Dashboard stopped');
    }

    public updateDashboardData(data: Partial<DashboardData>): void {
        this.dashboardData = {...this.dashboardData, ...data};
        if (this.isRunning) {
            // Immediate update via WebSocket - no throttling needed
            this.broadcastUpdate();
        }
    }

    public addLogEntry(entry: LogEntry): void {
        // Filter logs based on configured log level from environment
        if (!this.shouldShowLogLevel(entry.level)) {
            return;
        }

        this.logEntries.push(entry);

        // Keep only recent entries
        if (this.logEntries.length > this.config.maxLogLines) {
            this.logEntries = this.logEntries.slice(-this.config.maxLogLines);
        }

        if (this.isRunning) {
            // Immediate log broadcast via WebSocket - real-time performance
            this.io.emit('new-log', entry);
        }
    }

    public addLog(level: LogLevel, message: string, data?: any): void {
        // Filter logs based on configured log level from environment
        if (!this.shouldShowLogLevel(level)) {
            return;
        }

        const entry: LogEntry = {
            level,
            message,
            timestamp: Date.now(),
            data
        };
        this.addLogEntry(entry);
    }

    private shouldShowLogLevel(level: LogLevel): boolean {
        const configLevel = config.logLevel.toLowerCase();
        const levelHierarchy = {
            'error': 0,
            'warn': 1,
            'info': 2,
            'system': 3,
            'debug': 4
        };

        const configLevelValue = levelHierarchy[configLevel as keyof typeof levelHierarchy];
        const entryLevelValue = levelHierarchy[level.toLowerCase() as keyof typeof levelHierarchy];

        // If levels are not recognized, show the log
        if (configLevelValue === undefined || entryLevelValue === undefined) {
            return true;
        }

        // Show log if entry level is at or above (lower number = higher priority) the configured level
        return entryLevelValue <= configLevelValue;
    }

    private broadcastUpdate(): void {
        this.io.emit('dashboard-data', this.serializeDashboardData());
    }

    public isActive(): boolean {
        return this.isRunning;
    }

    public getPort(): number {
        return this.config.port;
    }
}

// Create singleton instance
export const webDashboard = new WebDashboard();
