<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🤖 MEV Bot Web Dashboard</title>
    <link rel="stylesheet" href="style.css">
    <script src="/socket.io/socket.io.js"></script>
</head>
<body>
    <div class="container">
        <!-- Title Bar -->
        <div class="title-bar">
            <h1>MEV Bot Dashboard - <span id="status-indicator" class="status-stopped">STOPPED</span> | <span id="current-time"></span></h1>
            <div class="title-info">
                <button id="refresh-btn" class="refresh-btn">Refresh</button>
            </div>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <!-- Status Dashboard (Left Panel) -->
            <div class="status-panel">
                <div class="panel-header">
                    <h2>Status Dashboard</h2>
                </div>
                <div class="panel-content" id="status-content">
                    <!-- Status Overview -->
                    <div class="section">
                        <h3>Status Overview</h3>
                        <div class="status-grid">
                            <div class="status-item">
                                <span class="label">Status:</span>
                                <span id="bot-status" class="value status-stopped">STOPPED</span>
                            </div>
                            <div class="status-item">
                                <span class="label">Network:</span>
                                <span id="network-name" class="value">Unknown | Block: <span id="current-block">0</span></span>
                            </div>
                            <div class="status-item">
                                <span class="label">ETH Balance:</span>
                                <span id="eth-balance" class="value">0.0 ETH (Never ago)</span>
                            </div>
                            <div class="status-item">
                                <span class="label">Uptime:</span>
                                <span id="uptime" class="value">0s</span>
                            </div>
                            <div class="status-item">
                                <span class="label">Last Activity:</span>
                                <span id="last-activity" class="value">Never</span>
                            </div>
                        </div>
                    </div>

                    <!-- Strategy Status -->
                    <div class="section">
                        <h3>Strategy Status</h3>
                        <div class="strategy-grid">
                            <div class="strategy-item">
                                <span class="label">Flashloan:</span>
                                <span id="flashloan-status" class="value">DISABLED</span>
                            </div>
                            <div class="strategy-item">
                                <span class="label">MEV-Share:</span>
                                <span id="mevshare-status" class="value">DISABLED</span>
                            </div>
                            <div class="strategy-item">
                                <span class="label">Arbitrage:</span>
                                <span id="arbitrage-status" class="value">DISABLED</span>
                            </div>
                        </div>
                    </div>

                    <!-- Statistics -->
                    <div class="section">
                        <h3>Statistics</h3>
                        <div class="stats-grid">
                            <div class="stat-item">
                                <span class="label">Total Transactions:</span>
                                <span id="total-transactions" class="value">0</span>
                            </div>
                            <div class="stat-item">
                                <span class="label">Relevant:</span>
                                <span id="relevant-transactions" class="value">0</span>
                            </div>
                            <div class="stat-item">
                                <span class="label">Opportunities Found:</span>
                                <span id="opportunities-found" class="value">0</span>
                            </div>
                            <div class="stat-item">
                                <span class="label">Opportunities Executed:</span>
                                <span id="opportunities-executed" class="value">0</span>
                            </div>
                            <div class="stat-item">
                                <span class="label">Total Profit:</span>
                                <span id="total-profit" class="value profit">0.0 ETH</span>
                            </div>
                        </div>
                    </div>

                    <!-- Bundle Statistics -->
                    <div class="section">
                        <h3>Bundle Inclusion</h3>
                        <div class="stats-grid">
                            <div class="stat-item">
                                <span class="label">Bundle Submissions:</span>
                                <span id="bundle-submissions" class="value">0</span>
                            </div>
                            <div class="stat-item">
                                <span class="label">Successful Inclusions:</span>
                                <span id="bundle-inclusions" class="value">0</span>
                            </div>
                            <div class="stat-item">
                                <span class="label">Inclusion Rate:</span>
                                <span id="bundle-inclusion-rate" class="value">0.0%</span>
                            </div>
                            <div class="stat-item">
                                <span class="label">Avg Priority Fee:</span>
                                <span id="avg-priority-fee" class="value">0 gwei</span>
                            </div>
                            <div class="stat-item">
                                <span class="label">Avg Gas Price:</span>
                                <span id="avg-gas-price" class="value">0.0 gwei</span>
                            </div>
                            <div class="stat-item">
                                <span class="label">Errors:</span>
                                <span id="error-count" class="value">0</span>
                            </div>
                        </div>
                    </div>

                    <!-- Configuration -->
                    <div class="section">
                        <h3>Configuration</h3>
                        <div class="config-section">
                            <div class="config-item">
                                <span class="label">Token Pairs:</span>
                                <div id="token-pairs" class="config-list">None</div>
                            </div>
                            <div class="config-item">
                                <span class="label">DEXes:</span>
                                <div id="dexes" class="config-list">None</div>
                            </div>
                            <div class="config-item">
                                <span class="label">Min Profit:</span>
                                <span id="min-profit" class="config-list">0 ETH</span>
                            </div>
                            <div class="config-item">
                                <span class="label">Max Gas:</span>
                                <span id="max-gas" class="config-list">0 ETH</span>
                            </div>
                        </div>
                    </div>

                    <!-- Successful Transactions -->
                    <div class="section">
                        <h3>Successful Transactions</h3>
                        <div id="successful-transactions" class="transactions-list">
                            <div class="no-transactions">No successful transactions yet</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Log Panel (Right Panel) -->
            <div class="log-panel">
                <div class="panel-header">
                    <h2>Live Logs</h2>
                    <div class="log-controls">
                        <select id="log-level-filter">
                            <option value="all">All</option>
                            <option value="error">Error</option>
                            <option value="warn">Warn</option>
                            <option value="info">Info</option>
                            <option value="system">System</option>
                            <option value="debug">Debug</option>
                        </select>
                        <button id="clear-logs-btn">Clear</button>
                        <button id="scroll-to-bottom-btn" style="display: none;">↓ New logs</button>
                    </div>
                </div>
                <div class="panel-content">
                    <div id="log-container" class="log-container">
                        <div class="log-entry info">
                            <span class="log-time">--:--:--</span>
                            <span class="log-level">INFO</span>
                            <span class="log-message">Web dashboard initialized. Waiting for data...</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
