// WebSocket connection
const socket = io();

// DOM elements
const elements = {
    statusIndicator: document.getElementById('status-indicator'),
    currentTime: document.getElementById('current-time'),
    refreshBtn: document.getElementById('refresh-btn'),
    botStatus: document.getElementById('bot-status'),
    networkName: document.getElementById('network-name'),
    currentBlock: document.getElementById('current-block'),
    ethBalance: document.getElementById('eth-balance'),
    uptime: document.getElementById('uptime'),
    lastActivity: document.getElementById('last-activity'),
    flashloanStatus: document.getElementById('flashloan-status'),
    mevshareStatus: document.getElementById('mevshare-status'),
    arbitrageStatus: document.getElementById('arbitrage-status'),
    totalTransactions: document.getElementById('total-transactions'),
    relevantTransactions: document.getElementById('relevant-transactions'),
    opportunitiesFound: document.getElementById('opportunities-found'),
    opportunitiesExecuted: document.getElementById('opportunities-executed'),
    totalProfit: document.getElementById('total-profit'),
    avgGasPrice: document.getElementById('avg-gas-price'),
    bundleSubmissions: document.getElementById('bundle-submissions'),
    bundleInclusions: document.getElementById('bundle-inclusions'),
    bundleInclusionRate: document.getElementById('bundle-inclusion-rate'),
    avgPriorityFee: document.getElementById('avg-priority-fee'),
    errorCount: document.getElementById('error-count'),
    tokenPairs: document.getElementById('token-pairs'),
    dexes: document.getElementById('dexes'),
    minProfit: document.getElementById('min-profit'),
    maxGas: document.getElementById('max-gas'),
    successfulTransactions: document.getElementById('successful-transactions'),
    logContainer: document.getElementById('log-container'),
    logLevelFilter: document.getElementById('log-level-filter'),
    clearLogsBtn: document.getElementById('clear-logs-btn'),
    scrollToBottomBtn: document.getElementById('scroll-to-bottom-btn')
};

// State
let currentLogLevel = 'all';
let logs = [];
let isUserScrolledUp = false;

// Initialize
document.addEventListener('DOMContentLoaded', () => {
    updateCurrentTime();
    setInterval(updateCurrentTime, 1000);
    
    // Event listeners
    elements.refreshBtn.addEventListener('click', () => {
        socket.emit('request-refresh');
    });
    
    elements.logLevelFilter.addEventListener('change', (e) => {
        currentLogLevel = e.target.value;
        filterAndDisplayLogs();
    });
    
    elements.clearLogsBtn.addEventListener('click', () => {
        logs = [];
        elements.logContainer.innerHTML = '<div class="log-entry info"><span class="log-time">--:--:--</span><span class="log-level info">INFO</span><span class="log-message">Logs cleared</span></div>';
        isUserScrolledUp = false;
        elements.scrollToBottomBtn.style.display = 'none';
    });

    elements.scrollToBottomBtn.addEventListener('click', () => {
        elements.logContainer.scrollTop = elements.logContainer.scrollHeight;
        isUserScrolledUp = false;
        elements.scrollToBottomBtn.style.display = 'none';
    });

    // Track user scroll position to determine auto-scroll behavior
    elements.logContainer.addEventListener('scroll', () => {
        const container = elements.logContainer;
        const isAtBottom = container.scrollTop + container.clientHeight >= container.scrollHeight - 5; // 5px tolerance
        isUserScrolledUp = !isAtBottom;

        // Hide/show scroll to bottom button
        if (isUserScrolledUp) {
            elements.scrollToBottomBtn.style.display = 'inline-block';
        } else {
            elements.scrollToBottomBtn.style.display = 'none';
        }
    });
});

// Socket event handlers
socket.on('connect', () => {
    console.log('Connected to MEV Bot dashboard');
    addSystemLog('Connected to MEV Bot dashboard', 'info');
});

socket.on('disconnect', () => {
    console.log('Disconnected from MEV Bot dashboard');
    addSystemLog('Disconnected from MEV Bot dashboard', 'warn');
});

socket.on('dashboard-data', (data) => {
    // Immediate dashboard update - no throttling needed with WebSockets
    updateDashboard(data);
});

socket.on('new-log', (logEntry) => {
    // Immediate log display - real-time performance
    addLogEntry(logEntry);
});

socket.on('logs', (logEntries) => {
    // Initial log load
    logs = logEntries;
    filterAndDisplayLogs();
});

// Update functions
function updateCurrentTime() {
    const now = new Date();
    elements.currentTime.textContent = now.toLocaleTimeString();
}

function updateDashboard(data) {
    // Status indicators
    const isRunning = data.isRunning;
    elements.statusIndicator.textContent = isRunning ? 'RUNNING' : 'STOPPED';
    elements.statusIndicator.className = isRunning ? 'status-running' : 'status-stopped';

    elements.botStatus.textContent = isRunning ? 'RUNNING' : 'STOPPED';
    elements.botStatus.className = isRunning ? 'value status-running' : 'value status-stopped';

    // Network info with block
    const networkText = `${data.networkName || 'Unknown'} | Block: ${data.currentBlock || '0'}`;
    elements.networkName.textContent = networkText;

    // ETH Balance with age
    const ethBalance = parseFloat(data.ethBalance) / 1e18;
    const balanceAge = data.lastBalanceUpdate > 0 ?
        Math.floor((Date.now() - data.lastBalanceUpdate) / 1000) + 's ago' : 'Never ago';
    elements.ethBalance.textContent = `${ethBalance.toFixed(4)} ETH (${balanceAge})`;
    elements.ethBalance.className = ethBalance > 0.1 ? 'value profit' : ethBalance > 0.01 ? 'value' : 'value status-stopped';
    
    // Timing
    elements.uptime.textContent = formatUptime(data.uptime || 0);
    elements.lastActivity.textContent = formatTimeSince(data.lastActivity || 0);
    
    // Strategy status
    elements.flashloanStatus.textContent = data.flashloanEnabled ? 'ENABLED' : 'DISABLED';
    elements.flashloanStatus.className = data.flashloanEnabled ? 'value profit' : 'value';
    elements.mevshareStatus.textContent = data.mevShareEnabled ? 'ENABLED' : 'DISABLED';
    elements.mevshareStatus.className = data.mevShareEnabled ? 'value profit' : 'value';
    elements.arbitrageStatus.textContent = data.arbitrageEnabled ? 'ENABLED' : 'DISABLED';
    elements.arbitrageStatus.className = data.arbitrageEnabled ? 'value profit' : 'value';
    
    // Statistics
    elements.totalTransactions.textContent = data.totalTransactions || '0';
    elements.relevantTransactions.textContent = data.relevantTransactions || '0';
    elements.opportunitiesFound.textContent = data.opportunitiesFound || '0';
    elements.opportunitiesExecuted.textContent = data.opportunitiesExecuted || '0';
    
    const totalProfit = parseFloat(data.totalProfit) / 1e18;
    elements.totalProfit.textContent = `${totalProfit.toFixed(6)} ETH`;
    
    const avgGasPrice = parseFloat(data.avgGasPrice) / 1e9;
    elements.avgGasPrice.textContent = `${avgGasPrice.toFixed(2)} gwei`;

    // Bundle statistics
    elements.bundleSubmissions.textContent = data.bundleSubmissions || '0';
    elements.bundleInclusions.textContent = data.bundleInclusions || '0';

    const inclusionRate = data.bundleInclusionRate || 0;
    elements.bundleInclusionRate.textContent = `${inclusionRate.toFixed(1)}%`;

    // Color code inclusion rate
    if (inclusionRate >= 70) {
        elements.bundleInclusionRate.className = 'value profit'; // Green
    } else if (inclusionRate >= 50) {
        elements.bundleInclusionRate.className = 'value'; // Default
    } else {
        elements.bundleInclusionRate.className = 'value status-stopped'; // Red
    }

    const priorityFee = parseFloat(data.avgPriorityFee) || 0;
    elements.avgPriorityFee.textContent = `${priorityFee.toFixed(1)} gwei`;

    elements.errorCount.textContent = data.errors || '0';
    elements.errorCount.className = (data.errors || 0) > 0 ? 'value status-stopped' : 'value profit';
    
    // Configuration
    const config = data.configuration || {};
    elements.tokenPairs.textContent = config.tokenPairs?.length ? config.tokenPairs.join(', ') : 'None';
    elements.dexes.textContent = config.dexes?.length ? config.dexes.join(', ') : 'None';

    // Convert Wei to ETH for Min Profit display
    const minProfitWei = config.minProfitThreshold || '0';
    const minProfitEth = minProfitWei !== '0' ? (parseFloat(minProfitWei) / 1e18).toFixed(6) : '0';
    elements.minProfit.textContent = `${minProfitEth} ETH`;

    elements.maxGas.textContent = `${config.maxGasPrice || '0'} ETH`;
    
    // Successful transactions
    updateSuccessfulTransactions(data.successfulTransactions || []);
}

function updateSuccessfulTransactions(transactions) {
    if (!transactions.length) {
        elements.successfulTransactions.innerHTML = '<div class="no-transactions">No successful transactions yet</div>';
        return;
    }
    
    const html = transactions.slice(-5).map(tx => {
        const profit = parseFloat(tx.profit) / 1e18;
        const gasUsed = parseFloat(tx.gasUsed) / 1e18;
        const time = new Date(tx.timestamp).toLocaleTimeString();

        return `
            <div class="transaction-item">
                <div style="color: #28a745; font-weight: 500;">${tx.type.toUpperCase()}: Profit ${profit.toFixed(6)} ETH</div>
                <div style="color: #888888; font-size: 11px;">${time}${tx.txHash ? ` | ${tx.txHash.substring(0, 12)}...` : ''}</div>
            </div>
        `;
    }).join('');
    
    elements.successfulTransactions.innerHTML = html;
}

function addLogEntry(logEntry) {
    logs.push(logEntry);

    // Keep only recent logs (increased to 1000)
    if (logs.length > 1000) {
        logs = logs.slice(-1000);
    }

    // Immediate display if it matches current filter - no batching needed
    if (shouldShowLog(logEntry)) {
        appendLogToDisplay(logEntry);
    }
}

function addSystemLog(message, level) {
    const logEntry = {
        level: level,
        message: message,
        timestamp: Date.now()
    };
    addLogEntry(logEntry);
}

function shouldShowLog(logEntry) {
    if (currentLogLevel === 'all') return true;

    // Hierarchical log level filtering
    const levelHierarchy = {
        'error': 0,
        'warn': 1,
        'info': 2,
        'system': 3,
        'debug': 4,
    };

    const selectedLevelValue = levelHierarchy[currentLogLevel.toLowerCase()];
    const entryLevelValue = levelHierarchy[logEntry.level.toLowerCase()];

    // If levels are not recognized, show the log
    if (selectedLevelValue === undefined || entryLevelValue === undefined) {
        return true;
    }

    // Show log if entry level is at or above (lower number = higher priority) the selected level
    return entryLevelValue <= selectedLevelValue;
}

function filterAndDisplayLogs() {
    const filteredLogs = currentLogLevel === 'all' ? logs : logs.filter(log => shouldShowLog(log));

    elements.logContainer.innerHTML = '';

    if (filteredLogs.length === 0) {
        elements.logContainer.innerHTML = '<div class="log-entry info"><span class="log-time">--:--:--</span><span class="log-level info">INFO</span><span class="log-message">No logs to display</span></div>';
        return;
    }

    // Show last 1000 logs (increased from 100)
    const recentLogs = filteredLogs.slice(-1000);
    recentLogs.forEach(log => appendLogToDisplay(log, false)); // Don't auto-scroll for bulk display

    // Auto-scroll to bottom only if user was at bottom or this is initial load
    if (!isUserScrolledUp) {
        elements.logContainer.scrollTop = elements.logContainer.scrollHeight;
    }
}

function appendLogToDisplay(logEntry, shouldAutoScroll = true) {
    const time = new Date(logEntry.timestamp).toLocaleTimeString();
    const levelClass = logEntry.level.toLowerCase();
    const levelText = logEntry.level.toUpperCase().padEnd(5);

    const logElement = document.createElement('div');
    logElement.className = 'log-entry';
    // Safe JSON stringify with BigInt handling
    const dataStr = logEntry.data ? JSON.stringify(logEntry.data, (key, value) => {
        if (typeof value === 'bigint') {
            return value.toString() + 'n';
        }
        return value;
    }) : '';

    logElement.innerHTML = `
        <span class="log-time">${time}</span>
        <span class="log-level ${levelClass}">${levelText}</span>
        <span class="log-message ${levelClass}">${escapeHtml(logEntry.message)}${dataStr ? ` ${dataStr}` : ''}</span>
    `;

    elements.logContainer.appendChild(logElement);

    // Smart auto-scroll: only scroll if user is at bottom or if explicitly requested
    if (shouldAutoScroll && !isUserScrolledUp) {
        elements.logContainer.scrollTop = elements.logContainer.scrollHeight;
    } else if (shouldAutoScroll && isUserScrolledUp) {
        // Show scroll-to-bottom button when new logs arrive and user is scrolled up
        elements.scrollToBottomBtn.style.display = 'inline-block';
    }

    // Remove old entries if too many (performance optimization - increased to 1200)
    const logEntries = elements.logContainer.querySelectorAll('.log-entry');
    if (logEntries.length > 1200) {
        logEntries[0].remove();
    }
}

// Utility functions
function formatUptime(uptime) {
    if (!uptime) return '0s';
    
    const seconds = Math.floor(uptime / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);
    
    if (days > 0) return `${days}d ${hours % 24}h`;
    if (hours > 0) return `${hours}h ${minutes % 60}m`;
    if (minutes > 0) return `${minutes}m ${seconds % 60}s`;
    return `${seconds}s`;
}

function formatTimeSince(timestamp) {
    if (!timestamp) return 'Never';
    
    const now = Date.now();
    const diff = now - timestamp;
    const seconds = Math.floor(diff / 1000);
    
    if (seconds < 60) return `${seconds}s ago`;
    if (seconds < 3600) return `${Math.floor(seconds / 60)}m ago`;
    if (seconds < 86400) return `${Math.floor(seconds / 3600)}h ago`;
    return `${Math.floor(seconds / 86400)}d ago`;
}

function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}
