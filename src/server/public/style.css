/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', 'Source Code Pro', 'Consolas', monospace;
    background: linear-gradient(135deg, #1a1a1a 0%, #1e1e1e 100%);
    color: #e8e8e8;
    line-height: 1.3;
    overflow: hidden;
    font-size: 13px;
    font-weight: 400;
}

.container {
    height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Title Bar */
.title-bar {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    padding: 8px 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #3a4a5c;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    height: 36px;
}

.title-bar h1 {
    font-size: 13px;
    font-weight: 500;
    color: #ecf0f1;
    letter-spacing: 0.3px;
    font-family: inherit;
}

.title-info {
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 12px;
}

.status-indicator {
    padding: 2px 6px;
    border-radius: 2px;
    font-weight: 500;
    font-size: 11px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-running {
    color: #28a745;
    background-color: transparent;
}

.status-stopped {
    color: #dc3545;
    background-color: transparent;
}

.refresh-btn {
    background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
    color: #ffffff;
    border: none;
    padding: 4px 10px;
    border-radius: 3px;
    cursor: pointer;
    font-weight: 500;
    font-size: 11px;
    transition: all 0.2s ease;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.refresh-btn:hover {
    background: linear-gradient(135deg, #2980b9 0%, #1f5f8b 100%);
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* Main Content */
.main-content {
    flex: 1;
    display: flex;
    height: calc(100vh - 36px);
}

/* Panels */
.status-panel {
    width: 400px;
    min-width: 400px;
    max-width: 400px;
    display: flex;
    flex-direction: column;
    background: linear-gradient(135deg, #1e1e1e 0%, #1a1a1a 100%);
    border-right: 1px solid #3a4a5c;
    box-shadow: 2px 0 4px rgba(0, 0, 0, 0.1);
}

.log-panel {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: linear-gradient(135deg, #1a1a1a 0%, #1e1e1e 100%);
    border-right: none;
}

.panel-header {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    padding: 8px 12px;
    border-bottom: 1px solid #3a4a5c;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 32px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.panel-header h2 {
    font-size: 12px;
    color: #3498db;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.8px;
}

.panel-content {
    flex: 1;
    padding: 12px;
    overflow-y: auto;
    background: linear-gradient(135deg, #1a1a1a 0%, #1e1e1e 100%);
}

/* Status Panel Sections */
.section {
    margin-bottom: 16px;
    padding: 8px;
    background: linear-gradient(135deg, rgba(52, 73, 94, 0.1) 0%, rgba(44, 62, 80, 0.1) 100%);
    border-radius: 4px;
    border: 1px solid rgba(58, 74, 92, 0.3);
    transition: all 0.3s ease;
}

.section:hover {
    background: linear-gradient(135deg, rgba(52, 73, 94, 0.15) 0%, rgba(44, 62, 80, 0.15) 100%);
    border-color: rgba(52, 152, 219, 0.4);
    box-shadow: 0 2px 8px rgba(52, 152, 219, 0.1);
}

.section h3 {
    color: #3498db;
    margin-bottom: 8px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.8px;
    border-bottom: 1px solid rgba(52, 152, 219, 0.3);
    padding-bottom: 4px;
    text-shadow: 0 0 4px rgba(52, 152, 219, 0.2);
}

/* Status Grid */
.status-grid, .strategy-grid, .stats-grid {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.status-item, .strategy-item, .stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 3px 4px;
    font-size: 12px;
    border-radius: 2px;
    transition: background-color 0.2s ease;
}

.status-item:hover, .strategy-item:hover, .stat-item:hover {
    background-color: rgba(52, 152, 219, 0.05);
}

.label {
    color: #888888;
    font-size: 12px;
    font-weight: 400;
}

.value {
    color: #e8e8e8;
    font-weight: 500;
    font-size: 12px;
    font-family: inherit;
}

.profit {
    color: #27ae60;
    text-shadow: 0 0 2px rgba(39, 174, 96, 0.3);
}

.error-text {
    color: #e74c3c;
}

.warning-text {
    color: #f39c12;
}

/* Configuration */
.config-section {
    display: flex;
    flex-direction: column;
    gap: 6px;
}

.config-item {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: 2px 0;
    font-size: 12px;
}

.config-list {
    background-color: transparent;
    padding: 0;
    font-size: 12px;
    color: #e0e0e0;
    border: none;
    text-align: right;
    max-width: 60%;
    word-wrap: break-word;
}

/* Transactions */
.transactions-list {
    max-height: 120px;
    overflow-y: auto;
    background-color: transparent;
    border: none;
}

.transaction-item {
    padding: 4px 0;
    border-bottom: 1px solid #2a2a2a;
    font-size: 11px;
    line-height: 1.3;
}

.transaction-item:last-child {
    border-bottom: none;
}

.no-transactions {
    padding: 8px 0;
    text-align: left;
    color: #666666;
    font-style: italic;
    font-size: 11px;
}

/* Log Panel */
.log-controls {
    display: flex;
    gap: 8px;
    align-items: center;
}

.log-controls select {
    background-color: #2a2a2a;
    color: #e0e0e0;
    border: 1px solid #444444;
    padding: 2px 6px;
    border-radius: 2px;
    font-size: 11px;
    font-family: inherit;
}

.log-controls button {
    background-color: #dc3545;
    color: #ffffff;
    border: none;
    padding: 2px 6px;
    border-radius: 2px;
    cursor: pointer;
    font-size: 11px;
    font-family: inherit;
}

#scroll-to-bottom-btn {
    background-color: #007bff;
    animation: pulse 2s infinite;
}

#scroll-to-bottom-btn:hover {
    background-color: #0056b3;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.7; }
    100% { opacity: 1; }
}

.log-container {
    height: 100%;
    overflow-y: auto;
    background: linear-gradient(135deg, #1a1a1a 0%, #1e1e1e 100%);
    border: none;
    padding: 8px;
    font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', 'Source Code Pro', 'Consolas', monospace;
    border-radius: 4px;
}

/* Log Entries */
.log-entry {
    display: flex;
    gap: 8px;
    padding: 1px 0;
    font-size: 12px;
    line-height: 1.3;
    border-bottom: none;
}

.log-entry:last-child {
    border-bottom: none;
}

.log-time {
    color: #666666;
    min-width: 65px;
    font-size: 11px;
    font-weight: 400;
}

.log-level {
    min-width: 45px;
    font-weight: 500;
    text-align: left;
    padding: 0;
    border-radius: 0;
    font-size: 11px;
    text-transform: uppercase;
}

.log-level.error {
    color: #ff6b6b;
    background-color: transparent;
}

.log-level.warn {
    color: #ffa726;
    background-color: transparent;
}

.log-level.info {
    color: #42a5f5;
    background-color: transparent;
}

.log-level.system {
    color: #888888;
    background-color: transparent;
}

.log-level.debug {
    color: #888888;
    background-color: transparent;
}

.log-message.system {
    opacity: 0.6;
}

.log-message {
    flex: 1;
    color: #e0e0e0;
    word-wrap: break-word;
    font-weight: 400;
}

/* Scrollbars */
::-webkit-scrollbar {
    width: 6px;
}

::-webkit-scrollbar-track {
    background: #1a1a1a;
}

::-webkit-scrollbar-thumb {
    background: #444444;
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: #555555;
}

/* Responsive */
@media (max-width: 1200px) {
    .main-content {
        flex-direction: column;
    }
    
    .status-panel, .log-panel {
        flex: none;
        height: 50%;
    }
    
    .status-panel {
        border-right: 1px solid #00ffff;
        border-bottom: none;
    }
}
