import { UtilsService } from './shared/services/utils.service';

describe('UtilsService', () => {
  let service: UtilsService;

  beforeEach(() => {
    service = new UtilsService();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it('should format ETH amounts correctly', () => {
    const result = service.formatEther('1000000000000000000');
    expect(result).toBe('1.0');
  });

  it('should validate addresses correctly', () => {
    expect(service.isValidAddress('******************************************')).toBe(true);
    expect(service.isValidAddress('invalid')).toBe(false);
  });

  it('should calculate percentage change', () => {
    const result = service.calculatePercentageChange(100, 150);
    expect(result).toBe(50);
  });

  it('should format uptime correctly', () => {
    const result = service.formatUptime(3661000); // 1 hour, 1 minute, 1 second
    expect(result).toBe('1h 1m');
  });
});
