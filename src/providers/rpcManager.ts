import { ethers } from 'ethers';
import { logger } from '../utils/logger';


export interface RPCProvider {
  name: string;
  http: string;
  ws: string;
  priority: number;
}

export class RPCManager {
  private providers: RPCProvider[] = [];
  private currentProvider: RPCProvider | null = null;
  private httpProvider: ethers.JsonRpcProvider | null = null;
  private wsProvider: ethers.WebSocketProvider | null = null;
  private failedProviders: Set<string> = new Set();

  constructor(chainId: number) {
    this.initializeProviders(chainId);
    this.selectBestProvider();
  }

  private initializeProviders(chainId: number): void {
    const isMainnet = chainId === 1;
    const isTestnet = chainId === 11155111; // Sepolia
    const isHardhat = chainId === 31337; // Hardhat local network

    // Alchemy (Best for MEV - High rate limits)
    if (process.env.ALCHEMY_API_KEY) {
      this.providers.push({
        name: 'Alchemy',
        http: isMainnet 
          ? `https://eth-mainnet.g.alchemy.com/v2/${process.env.ALCHEMY_API_KEY}`
          : `https://eth-sepolia.g.alchemy.com/v2/${process.env.ALCHEMY_API_KEY}`,
        ws: isMainnet
          ? `wss://eth-mainnet.g.alchemy.com/v2/${process.env.ALCHEMY_API_KEY}`
          : `wss://eth-sepolia.g.alchemy.com/v2/${process.env.ALCHEMY_API_KEY}`,
        priority: 1
      });
    }

    // QuickNode (MEV Optimized - No rate limits on paid plans)
    if (process.env.QUICKNODE_ENDPOINT) {
      this.providers.push({
        name: 'QuickNode',
        http: process.env.QUICKNODE_ENDPOINT,
        ws: process.env.QUICKNODE_ENDPOINT.replace('https://', 'wss://'),
        priority: 2
      });
    }

    // Ankr (Good free tier)
    if (process.env.ANKR_API_KEY) {
      this.providers.push({
        name: 'Ankr',
        http: isMainnet
          ? `https://rpc.ankr.com/eth/${process.env.ANKR_API_KEY}`
          : `https://rpc.ankr.com/eth_sepolia/${process.env.ANKR_API_KEY}`,
        ws: isMainnet
          ? `wss://rpc.ankr.com/eth/ws/${process.env.ANKR_API_KEY}`
          : `wss://rpc.ankr.com/eth_sepolia/ws/${process.env.ANKR_API_KEY}`,
        priority: 3
      });
    }

    // Hardhat Local Network (Best for testing)
    if (isHardhat) {
      this.providers.push({
        name: 'Hardhat Local',
        http: process.env.RPC_URL || 'http://localhost:8545',
        ws: process.env.MEMPOOL_WEBSOCKET_URL || 'ws://localhost:8545',
        priority: 0 // Highest priority for testing
      });
    }

    // Local Node (Best option - no limits)
    if (process.env.LOCAL_NODE_URL && !isHardhat) {
      this.providers.push({
        name: 'Local Node',
        http: process.env.LOCAL_NODE_URL,
        ws: process.env.LOCAL_NODE_WS || process.env.LOCAL_NODE_URL.replace('http', 'ws'),
        priority: 0 // Highest priority
      });
    }

    // Fallback public RPCs (rate limited but free) - Skip for Hardhat
    const publicRpcs = isHardhat ? [] : isMainnet ? [
      { name: 'Ethereum Public Node', http: 'https://ethereum.publicnode.com', ws: 'wss://ethereum.publicnode.com' },
      { name: 'Ankr Public', http: 'https://rpc.ankr.com/eth', ws: 'wss://rpc.ankr.com/eth/ws' },
      { name: 'LlamaRPC', http: 'https://eth.llamarpc.com', ws: 'wss://eth.llamarpc.com' },
      { name: 'Cloudflare', http: 'https://cloudflare-eth.com', ws: 'wss://cloudflare-eth.com/ws' }
    ] : [
      { name: 'Ethereum Public Node Sepolia', http: 'https://ethereum-sepolia.publicnode.com', ws: 'wss://ethereum-sepolia.publicnode.com' },
      { name: 'Ankr Public Sepolia', http: 'https://rpc.ankr.com/eth_sepolia', ws: 'wss://rpc.ankr.com/eth_sepolia/ws' }
    ];

    publicRpcs.forEach((rpc, index) => {
      this.providers.push({
        name: rpc.name,
        http: rpc.http,
        ws: rpc.ws,
        priority: 10 + index // Lower priority
      });
    });

    // Sort by priority
    this.providers.sort((a, b) => a.priority - b.priority);
    
    logger.system(`Initialized ${this.providers.length} RPC providers`);
    this.providers.forEach(p => {
      logger.system(`- ${p.name}: No Rate Limits`);
    });
  }

  private selectBestProvider(): void {
    // Find the best available provider
    const availableProviders = this.providers.filter(p => !this.failedProviders.has(p.name));
    
    if (availableProviders.length === 0) {
      // Reset failed providers if all have failed
      this.failedProviders.clear();
      logger.warn('All providers failed, resetting...');
      this.selectBestProvider();
      return;
    }

    const bestProvider = availableProviders[0];
    
    if (this.currentProvider?.name !== bestProvider.name) {
      this.currentProvider = bestProvider;
      this.initializeConnections();
      logger.system(`Switched to RPC provider: ${bestProvider.name}`);
    }
  }

  private async initializeConnections(): Promise<void> {
    if (!this.currentProvider) return;

    try {
      // Initialize HTTP provider
      this.httpProvider = new ethers.JsonRpcProvider(this.currentProvider.http);
      
      // Test the connection
      await this.httpProvider.getBlockNumber();
      
      // Initialize WebSocket provider
      try {
        this.wsProvider = new ethers.WebSocketProvider(this.currentProvider.ws);
        logger.system(`WebSocket connection established: ${this.currentProvider.name}`);
      } catch (wsError) {
        logger.error(`WebSocket failed for ${this.currentProvider.name}, using HTTP only`);
        this.wsProvider = null;
      }

    } catch (error) {
      logger.error(`Failed to connect to ${this.currentProvider.name}`, error);
      this.failedProviders.add(this.currentProvider.name);
      this.selectBestProvider();
    }
  }



  async makeRequest(method: string, params: any[] = []): Promise<any> {
    if (!this.httpProvider || !this.currentProvider) {
      throw new Error('No RPC provider available');
    }

    try {
      return await this.httpProvider.send(method, params);
    } catch (error) {
      logger.error(`Request failed for ${this.currentProvider.name}`, error);
      this.failedProviders.add(this.currentProvider.name);
      this.selectBestProvider();

      // Retry with new provider
      if (this.httpProvider && this.currentProvider) {
        return this.makeRequest(method, params);
      }
      throw error;
    }
  }

  getHttpProvider(): ethers.JsonRpcProvider {
    if (!this.httpProvider) {
      throw new Error('No HTTP provider available');
    }
    return this.httpProvider;
  }

  getWebSocketProvider(): ethers.WebSocketProvider | null {
    return this.wsProvider;
  }

  getCurrentProviderInfo(): { name: string } | null {
    if (!this.currentProvider) return null;
    return {
      name: this.currentProvider.name
    };
  }

  async switchToNextProvider(): Promise<void> {
    if (this.currentProvider) {
      this.failedProviders.add(this.currentProvider.name);
    }
    this.selectBestProvider();
  }

  getProviderStats(): { total: number; available: number; failed: number } {
    return {
      total: this.providers.length,
      available: this.providers.length - this.failedProviders.size,
      failed: this.failedProviders.size
    };
  }
}
