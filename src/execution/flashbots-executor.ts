import {ethers} from 'ethers';
import {FlashbotsBundleTransaction} from '@flashbots/ethers-provider-bundle';
import {FlashbotsBundleManager} from '../flashbots/bundle-provider';
import {FlashbotsProtectProvider, FlashbotsProtectTransaction} from '../flashbots/protect-provider';
import {AdvancedGasEstimator} from '../gas/advanced-estimator';
import {GasOptimizer} from '../gas/optimizer';
import {SwapDataBuilder} from './swap-data-builder';
import {PriceCalculator} from './price-calculator';
import {config, ADDRESSES} from '../config';
import {logger} from '../utils/logger';
import {ErrorHandler} from '../utils/error-handler';
import {transactionMonitor, TransactionStatus} from '../utils/transaction-monitor';

import {ArbitrageRoute, FlashloanRoute} from '../types';

export interface ExecutionResult {
    success: boolean;
    txHash?: string;
    bundleHash?: string;
    gasUsed?: bigint;
    gasPrice?: bigint;
    profit?: bigint;
    error?: string;
    executionTime?: number;
    resolution?: any; // Flashbots bundle resolution
    isTracked?: boolean; // Whether bundle is being tracked for results
    trackingId?: string; // Bundle hash for tracking
}

export interface ExecutionOptions {
    useFlashbots: boolean;
    urgency: 'slow' | 'standard' | 'fast' | 'instant';
    maxGasCostEth: number;
    slippageTolerance: number;
    deadline?: number;
}

/**
 * Enhanced MEV Executor with Flashbots integration
 * Handles both regular mempool and Flashbots bundle execution
 */
export class FlashbotsExecutor {
    private provider: ethers.JsonRpcProvider;
    private wallet: ethers.Wallet;
    private flashbotsManager: FlashbotsBundleManager;
    private protectProvider: FlashbotsProtectProvider;
    private gasEstimator: AdvancedGasEstimator;
    private gasOptimizer: GasOptimizer;
    private swapDataBuilder: SwapDataBuilder;
    private priceCalculator: PriceCalculator;

    constructor(
        provider: ethers.JsonRpcProvider,
        wallet: ethers.Wallet,
        flashbotsManager: FlashbotsBundleManager,
        gasEstimator: AdvancedGasEstimator,
        gasOptimizer: GasOptimizer
    ) {
        this.provider = provider;
        this.wallet = wallet;
        this.flashbotsManager = flashbotsManager;
        this.protectProvider = new FlashbotsProtectProvider(provider, wallet);
        this.gasEstimator = gasEstimator;
        this.gasOptimizer = gasOptimizer;
        this.swapDataBuilder = new SwapDataBuilder(provider);
        this.priceCalculator = new PriceCalculator(provider);

        // Initialize Flashbots Protect if enabled
        if (config.enableFlashbotsProtect) {
            this.protectProvider.initialize().catch(error => {
                logger.logError(error, 'FlashbotsExecutor.constructor - Protect initialization failed');
            });
        }
    }
    private async toWei(amount: bigint, token: any): Promise<bigint> {
        const { tokenAmountToWei } = await import('../utils/denomination');
        return tokenAmountToWei(token, amount);
    }


    /**
     * Execute arbitrage opportunity with enhanced validation
     */
    async executeArbitrage(
        route: ArbitrageRoute,
        options: ExecutionOptions = {
            useFlashbots: config.enableFlashbots,
            urgency: config.gasUrgency,
            maxGasCostEth: config.maxGasCostEth,
            slippageTolerance: config.slippageTolerance
        }
    ): Promise<ExecutionResult> {
        const startTime = Date.now();

        try {
            logger.system('🚀 Executing arbitrage opportunity...');
            logger.system(`   Expected Profit: ${ethers.formatEther(route.expectedProfit)} ETH`);
            logger.system(`   Use Flashbots: ${options.useFlashbots}`);

            // Validate and enhance the route with accurate calculations
            const validatedRoute = await this.validateArbitrageRoute(route);
            if (!validatedRoute) {
                logger.error('❌ Route validation failed');
                return {success: false, error: 'Route validation failed'};
            }

            logger.system(`   Validated Profit: ${ethers.formatEther(validatedRoute.expectedProfit)} ETH`);
            logger.system(`   Confidence: ${validatedRoute.confidence}%`);

            // Check if gas conditions are favorable
            const gasFavorable = await this.gasEstimator.isGasFavorable(options.maxGasCostEth);
            if (!gasFavorable) {
                logger.warn('⚠️  Gas prices too high, skipping execution');
                return {success: false, error: 'Gas prices unfavorable'};
            }

            // Build transaction with validated route
            const transaction = await this.buildArbitrageTransaction(validatedRoute, options);
            if (!transaction) {
                return {success: false, error: 'Failed to build transaction'};
            }

            logger.info('✅ Transaction built successfully');

            // Check for simulation mode
            if (config.simulationMode) {
                return await this.simulateArbitrageExecution(validatedRoute, transaction, options);
            }

            // Execute based on strategy - prioritize Flashbots Protect for private submission
            if (options.useFlashbots) {
                // Check wallet balance before Flashbots execution
                const hasBalance = await this.checkWalletBalanceForExecution(transaction, options);
                if (!hasBalance) {
                    logger.warn('⚠️  Insufficient wallet balance for gas costs, skipping Flashbots execution');
                    return {success: false, error: 'Insufficient balance for gas costs'};
                }

                // Prioritize Flashbots Protect for private transaction submission
                if (config.enableFlashbotsProtect && this.protectProvider.isAvailable()) {
                    logger.system('🛡️  Using Flashbots Protect for private transaction submission...');

                    const protectResult = await this.executeViaFlashbotsProtect(transaction, options);

                    if (protectResult.success) {
                        return protectResult;
                    } else {
                        logger.warn('⚠️  Flashbots Protect failed, falling back to bundle submission', {
                            error: protectResult.error
                        });
                    }
                }

                // Fallback to standard Flashbots bundle submission
                if (this.flashbotsManager.isAvailable()) {
                    return await this.executeViaFlashbots([transaction], options);
                } else {
                    logger.warn('⚠️  Flashbots not available, using regular mempool');
                    // Extract transaction request from bundle transaction
                    const txRequest = 'transaction' in transaction ? transaction.transaction : transaction;
                    return await this.executeViaMempool(txRequest, options);
                }
            } else {
                // Extract transaction request from bundle transaction
                const txRequest = 'transaction' in transaction ? transaction.transaction : transaction;
                return await this.executeViaMempool(txRequest, options);
            }

        } catch (error) {
            logger.logError(error as Error, 'FlashbotsExecutor.executeArbitrage');
            return {
                success: false,
                error: (error as Error).message,
                executionTime: Date.now() - startTime
            };
        }
    }

    /**
     * Execute Aave flashloan opportunity with immediate bundle submission (no waiting)
     */
    async executeAaveFlashloan(
        route: FlashloanRoute,
        options: ExecutionOptions = {
            useFlashbots: config.enableFlashbots,
            urgency: config.gasUrgency,
            maxGasCostEth: config.maxGasCostEth,
            slippageTolerance: config.slippageTolerance
        }
    ): Promise<ExecutionResult> {
        const startTime = Date.now();

        try {
            logger.system('🏦 Executing Aave flashloan with immediate submission...');
            logger.system(`   Route: ${route.flashloanToken.symbol} → ${route.arbitrageRoute.tokens[1]?.symbol || 'Unknown'} → ${route.flashloanToken.symbol}`);
            logger.system(`   Expected Profit: ${ethers.formatEther(route.expectedProfit)} ETH`);

            // Basic route validation
            if (!route.flashloanToken || !route.arbitrageRoute) {
                logger.warn('⚠️  Invalid route structure for Aave flashloan');
                return {
                    success: false,
                    error: 'Invalid route structure',
                    executionTime: Date.now() - startTime
                };
            }

            logger.system(`   Route Profit: ${ethers.formatEther(route.expectedProfit)} ETH`);
            logger.system(`   Confidence: ${route.confidence}%`);

            // Check if gas conditions are favorable
            const gasFavorable = await this.gasEstimator.isGasFavorable(options.maxGasCostEth);
            if (!gasFavorable) {
                logger.warn('⚠️  Gas prices too high, skipping Aave flashloan execution');
                return {success: false, error: 'Gas prices unfavorable', executionTime: Date.now() - startTime};
            }

            // Build transaction with flashloan route (Aave provider)
            const transaction = await this.buildFlashloanTransaction(route, options, 'aave');
            if (!transaction) {
                return {
                    success: false,
                    error: 'Failed to build Aave flashloan transaction',
                    executionTime: Date.now() - startTime
                };
            }

            // Execute based on strategy
            if (options.useFlashbots && this.flashbotsManager.isAvailable()) {
                // Check wallet balance before Flashbots execution
                const hasBalance = await this.checkWalletBalanceForExecution(transaction, options);
                if (!hasBalance) {
                    logger.warn('⚠️  Insufficient wallet balance for gas costs, skipping Aave flashloan execution');
                    return {success: false, error: 'Insufficient balance for gas costs', executionTime: Date.now() - startTime};
                }

                return await this.executeAaveViaFlashbots([transaction], options, startTime);
            } else {
                // Extract transaction request from bundle transaction
                const txRequest = 'transaction' in transaction ? transaction.transaction : transaction;
                return await this.executeViaMempool(txRequest, options);
            }

        } catch (error) {
            logger.logError(error as Error, 'FlashbotsExecutor.executeAaveFlashloan');
            return {
                success: false,
                error: (error as Error).message,
                executionTime: Date.now() - startTime
            };
        }
    }

    /**
     * Execute flashloan opportunity (original method with waiting)
     */
    async executeFlashloan(
        route: FlashloanRoute,
        options: ExecutionOptions = {
            useFlashbots: config.enableFlashbots,
            urgency: config.gasUrgency,
            maxGasCostEth: config.maxGasCostEth,
            slippageTolerance: config.slippageTolerance
        }
    ): Promise<ExecutionResult> {
        const startTime = Date.now();

        try {
            logger.system('💰 Executing flashloan opportunity...');
            logger.system(`   Flashloan Amount: ${ethers.formatUnits(route.flashloanAmount, route.flashloanToken.decimals)} ${route.flashloanToken.symbol}`);
            logger.system(`   Expected Profit: ${ethers.formatEther(route.expectedProfit)} ETH`);

            // Get current gas strategy for accurate cost calculation
            const gasStrategy = await this.gasOptimizer.getCurrentGasStrategy();

            // Estimate gas limit for flashloan transaction (more accurate estimate)
            const estimatedGasLimit = 300000n; // Conservative estimate for flashloan

            // Calculate actual gas cost using current gas strategy
            const actualGasCost = estimatedGasLimit * BigInt(gasStrategy.maxFeePerGas.toString());

            // Calculate net profit after gas costs using wei-normalized expectedProfit
            const expectedProfitWei = await this.toWei(BigInt(route.expectedProfit.toString()), route.flashloanToken);
            const netProfit = expectedProfitWei - actualGasCost;

            await logger.profitabilityAnalysis(
                expectedProfitWei,
                actualGasCost,
                netProfit,
                'Flashloan Execution'
            );

            // Check if profitable after gas costs
            if (netProfit <= BigInt(config.minProfitWei)) {
                logger.system('⚠️  Opportunity not profitable after gas costs');
                logger.system(`   Required minimum: ${ethers.formatEther(config.minProfitWei)} ETH`);
                return {success: false, error: 'Not profitable after gas costs'};
            }

            logger.system(`✅ Opportunity is profitable! Net profit: ${ethers.formatEther(netProfit)} ETH`);

            // Build flashloan transaction (default to balancer for generic execution)
            const transaction = await this.buildFlashloanTransaction(route, options, 'balancer');
            if (!transaction) {
                return {success: false, error: 'Failed to build flashloan transaction'};
            }

            // Check for simulation mode
            if (config.simulationMode) {
                return await this.simulateFlashloanExecution(route, transaction, options);
            }

            // Execute based on strategy
            if (options.useFlashbots && this.flashbotsManager.isAvailable()) {
                // Check wallet balance before Flashbots execution
                const hasBalance = await this.checkWalletBalanceForExecution(transaction, options);
                if (!hasBalance) {
                    logger.warn('⚠️  Insufficient wallet balance for gas costs, skipping Flashbots execution');
                    return {success: false, error: 'Insufficient balance for gas costs'};
                }

                return await this.executeViaFlashbots([transaction], options);
            } else {
                // Extract transaction request from bundle transaction
                const txRequest = 'transaction' in transaction ? transaction.transaction : transaction;
                return await this.executeViaMempool(txRequest, options);
            }

        } catch (error) {
            logger.logError(error as Error, 'FlashbotsExecutor.executeFlashloan');
            return {
                success: false,
                error: (error as Error).message,
                executionTime: Date.now() - startTime
            };
        }
    }

    /**
     * Execute Aave flashloan via Flashbots bundle (no waiting, immediate return)
     */
    private async executeAaveViaFlashbots(
        transactions: FlashbotsBundleTransaction[],
        options: ExecutionOptions,
        startTime: number
    ): Promise<ExecutionResult> {
        try {
            logger.system('🏦 Executing Aave flashloan via Flashbots bundle (no wait)...');

            const targetBlock = await this.calculateOptimalTargetBlock();

            // Simulate bundle first
            const simulation = await this.flashbotsManager.simulateBundle(transactions, targetBlock);
            if (!simulation.success) {
                logger.warn('⚠️  Aave flashloan bundle simulation failed');
                logger.warn(`   Error: ${simulation.error}`);
                return {
                    success: false,
                    error: `Bundle simulation failed: ${simulation.error}`,
                    executionTime: Date.now() - startTime
                };
            }

            logger.success('✅ Aave flashloan bundle simulation successful');
            logger.system(`   Simulated Gas Used: ${simulation.simulation?.gasUsed?.toString() || 'N/A'}`);

            // Use Aave-specific bundle submission (no waiting)
            const submission = await this.flashbotsManager.submitBundleForAaveFlashloan(
                transactions,
                targetBlock,
                {
                    isHighPriority: options.urgency === 'instant',
                    minTimestamp: Math.floor(Date.now() / 1000),
                    maxTimestamp: Math.floor(Date.now() / 1000) + 60 // 1 minute timeout
                }
            );

            if (!submission.success) {
                logger.error('❌ Aave flashloan bundle submission failed');
                logger.error(`   Error: ${submission.error}`);
                return {
                    success: false,
                    error: submission.error || 'Bundle submission failed',
                    executionTime: Date.now() - startTime
                };
            }

            logger.success('🚀 Aave flashloan bundle submitted successfully (no wait)');
            logger.system(`   Bundle Hash: ${submission.bundleHash}`);
            logger.system(`   Target Block: ${targetBlock}`);
            logger.system(`   Execution Time: ${Date.now() - startTime}ms`);

            // Return immediately without waiting for inclusion
            return {
                success: true,
                txHash: submission.bundleHash, // Use bundle hash as identifier
                bundleHash: submission.bundleHash,
                executionTime: Date.now() - startTime,
                gasUsed: simulation.simulation?.gasUsed,
                resolution: undefined, // No resolution since we're not waiting
                profit: BigInt(0), // Will be calculated later if needed
                isTracked: true, // Bundle is being tracked
                trackingId: submission.bundleHash // Bundle hash for tracking
            };

        } catch (error) {
            logger.logError(error as Error, 'FlashbotsExecutor.executeAaveViaFlashbots');
            return {
                success: false,
                error: (error as Error).message,
                executionTime: Date.now() - startTime
            };
        }
    }

    /**
     * Execute transaction via Flashbots bundle (original method with waiting)
     */
    private async executeViaFlashbots(
        transactions: FlashbotsBundleTransaction[],
        options: ExecutionOptions
    ): Promise<ExecutionResult> {
        try {
            logger.system('📦 Executing via Flashbots bundle...');

            const targetBlock = await this.calculateOptimalTargetBlock();

            // Simulate bundle first
            const simulation = await this.flashbotsManager.simulateBundle(transactions, targetBlock);
            if (!simulation.success) {
                logger.warn('❌ Bundle simulation failed');
                return {success: false, error: simulation.error};
            }

            logger.system('✅ Bundle simulation successful');

            // Check for simulation mode
            if (config.simulationMode) {
                logger.info('🎭 SIMULATION MODE: Bundle would be submitted to Flashbots');
                logger.info(`   Target Block: ${targetBlock}`);
                logger.info(`   Transactions: ${transactions.length}`);
                logger.info(`   Estimated Gas: ${simulation.simulation?.gasUsed || 'unknown'}`);
                return {
                    success: true,
                    bundleHash: 'SIMULATION_BUNDLE_HASH',
                    executionTime: Date.now() - Date.now()
                };
            }

            // Check network congestion to determine submission strategy
            const congestion = await this.flashbotsManager.getNetworkCongestion();
            logger.system(`🚦 Network congestion: ${congestion.congestionLevel} (${(congestion.gasUsageRatio * 100).toFixed(1)}%)`);

            // Use multi-block submission for better inclusion chances during high congestion
            const useMultiBlock = congestion.congestionLevel === 'high' || congestion.congestionLevel === 'extreme' || config.enableBundleMultiplexing;
            const isHighPriority = options.urgency === 'instant' || congestion.congestionLevel === 'extreme';
            const startTime = Date.now();

            // Apply early submission offset if enabled
            if (config.enablePreemptiveSubmission && config.earlySubmissionOffsetMs > 0) {
                await new Promise(resolve => setTimeout(resolve, config.earlySubmissionOffsetMs));
                logger.debug(`⏰ Applied early submission offset: ${config.earlySubmissionOffsetMs}ms`);
            }

            // Implement advanced bundle submission with retry logic
            let submission = await this.executeAdvancedBundleSubmission(
                transactions,
                targetBlock,
                isHighPriority,
                useMultiBlock
            );
            const executionTime = Date.now() - startTime;

            if (!submission.success) {
                logger.system('❌ Bundle submission/resolution failed');
                logger.system(`   Error: ${submission.error}`);
                logger.system(`   Bundle Hash: ${submission.bundleHash || 'N/A'}`);
                logger.system(`   Resolution: ${submission.resolution || 'N/A'}`);
                return {
                    success: false,
                    error: submission.error,
                    bundleHash: submission.bundleHash,
                    executionTime,
                    resolution: submission.resolution
                };
            }

            // Bundle was successfully included!
            logger.info('🎉 Bundle included in block!');
            logger.info(`   Bundle Hash: ${submission.bundleHash}`);
            logger.info(`   Resolution: ${submission.resolution}`);
            logger.info(`   Execution Time: ${executionTime}ms`);

            // Calculate actual profit from transaction receipts
            let actualProfit: bigint | undefined;
            try {
                actualProfit = await this.calculateActualProfitFromBundle(
                    submission.bundleHash,
                    transactions,
                    targetBlock
                );

                if (actualProfit !== undefined) {
                    logger.info(`💰 Actual profit calculated: ${ethers.formatEther(actualProfit)} ETH`);
                } else {
                    logger.info('Could not calculate actual profit - using estimated value');
                }
            } catch (error) {
                logger.error(`Error calculating actual profit: ${(error as Error).message}`);
            }

            return {
                success: true,
                bundleHash: submission.bundleHash,
                executionTime,
                resolution: submission.resolution,
                profit: actualProfit
            };

        } catch (error) {
            logger.logError(error as Error, 'FlashbotsExecutor.executeViaFlashbots');
            return {success: false, error: (error as Error).message};
        }
    }

    /**
     * Execute transaction via regular mempool
     */
    private async executeViaMempool(
        transaction: ethers.TransactionRequest,
        options: ExecutionOptions
    ): Promise<ExecutionResult> {
        try {
            logger.info('🌐 Executing via mempool...');

            // Get optimal gas pricing
            const gasPrice = await this.gasEstimator.getOptimalGasPrice(options.urgency);

            // Validate and prepare transaction (use EIP-1559 if available, otherwise legacy)
            let txRequest: ethers.TransactionRequest;

            if (gasPrice.maxFeePerGas && gasPrice.maxPriorityFeePerGas) {
                // EIP-1559 transaction - validate gas parameters
                const validatedGas = this.validateEIP1559GasParams(gasPrice.maxFeePerGas, gasPrice.maxPriorityFeePerGas);

                txRequest = {
                    ...transaction,
                    maxFeePerGas: validatedGas.maxFeePerGas,
                    maxPriorityFeePerGas: validatedGas.maxPriorityFeePerGas
                };
            } else {
                // Legacy transaction
                txRequest = {
                    ...transaction,
                    gasPrice: gasPrice.gasPrice
                };
            }

            if (config.dryRun || config.simulationMode) {
                const mode = config.simulationMode ? 'SIMULATION' : 'DRY RUN';
                logger.info(`🎭 ${mode}: Transaction would be sent to mempool`);
                logger.info(`   Gas Price: ${ethers.formatUnits(gasPrice.gasPrice, 'gwei')} gwei`);
                logger.info(`   Gas Limit: ${transaction.gasLimit || 'estimated'}`);
                logger.info(`   To: ${transaction.to}`);
                logger.info(`   Value: ${ethers.formatEther(transaction.value || 0)} ETH`);
                return {success: true, txHash: `${mode.replace(' ', '_')}_TX_HASH`};
            }

            // Use sendRawTransaction for better mempool performance
            const useRawTransaction = config.useRawTransactionExecution !== false; // Default to true

            if (useRawTransaction) {
                return await this.executeViaRawTransaction(txRequest, options);
            } else {
                // Fallback to traditional sendTransaction with await
                return await this.executeViaTraditionalMethod(txRequest, options);
            }

        } catch (error) {
            ErrorHandler.handleTransactionError(error as Error, 'FlashbotsExecutor.executeViaMempool');
            return {success: false, error: (error as Error).message};
        }
    }

    /**
     * Execute transaction using sendRawTransaction for better mempool performance
     */
    private async executeViaRawTransaction(
        txRequest: ethers.TransactionRequest,
        options: ExecutionOptions
    ): Promise<ExecutionResult> {
        try {
            // Sign the transaction
            const signedTx = await this.wallet.signTransaction(txRequest);

            // Send raw transaction to mempool without waiting
            const provider = this.wallet.provider as ethers.JsonRpcProvider;
            const txHash = await provider.send('eth_sendRawTransaction', [signedTx]);
            logger.info(`🚀 Raw transaction sent to mempool: ${txHash}`);

            // Optionally start monitoring the transaction (non-blocking)
            const enableMonitoring = config.enableTransactionMonitoring !== false; // Default to true
            if (enableMonitoring) {
                transactionMonitor.monitorTransaction(txHash, {
                    timeout: 300000, // 5 minutes
                    maxConfirmations: 1,
                    onStatusChange: (status: TransactionStatus) => {
                        this.handleTransactionStatusChange(status);
                    }
                });
            }

            // Return immediately without waiting for confirmation
            // This allows for faster execution and better MEV performance
            logger.info('⚡ Transaction submitted to mempool (non-blocking)');
            logger.info(`   Gas Price: ${ethers.formatUnits(txRequest.gasPrice || txRequest.maxFeePerGas || 0, 'gwei')} gwei`);
            logger.info(`   Gas Limit: ${txRequest.gasLimit || 'estimated'}`);
            if (enableMonitoring) {
                logger.info('   📊 Transaction monitoring enabled');
            }

            return {
                success: true,
                txHash: txHash,
                executionTime: Date.now()
            };

        } catch (error) {
            logger.error(`❌ Raw transaction execution failed: ${(error as Error).message}`);
            throw error;
        }
    }

    /**
     * Handle transaction status changes from the monitor
     */
    private handleTransactionStatusChange(status: TransactionStatus): void {
        switch (status.status) {
            case 'confirmed':
                logger.success(`✅ Transaction confirmed: ${status.hash}`);
                if (status.gasUsed && status.gasPrice) {
                    logger.info(`   Gas Used: ${status.gasUsed}`);
                    logger.info(`   Gas Price: ${ethers.formatUnits(status.gasPrice, 'gwei')} gwei`);
                }
                if (status.blockNumber) {
                    logger.info(`   Block: ${status.blockNumber}`);
                }
                break;

            case 'failed':
                logger.error(`❌ Transaction failed: ${status.hash}`);
                if (status.error) {
                    logger.error(`   Error: ${status.error}`);
                }
                break;

            case 'timeout':
                logger.warn(`⏰ Transaction monitoring timed out: ${status.hash}`);
                break;

            default:
                logger.debug(`📊 Transaction status update: ${status.status} - ${status.hash}`);
        }
    }

    /**
     * Traditional transaction execution method (with await)
     * Used as fallback when raw transaction execution is disabled
     */
    private async executeViaTraditionalMethod(
        txRequest: ethers.TransactionRequest,
        options: ExecutionOptions
    ): Promise<ExecutionResult> {
        try {
            // Send transaction
            const tx = await this.wallet.sendTransaction(txRequest);
            logger.info(`📤 Transaction sent: ${tx.hash}`);

            // Wait for confirmation
            const receipt = await tx.wait();
            if (!receipt) {
                return {success: false, error: 'Transaction failed'};
            }

            logger.info('✅ Transaction confirmed!');
            logger.info(`   Gas Used: ${receipt.gasUsed}`);
            logger.info(`   Gas Price: ${ethers.formatUnits(receipt.gasPrice || 0, 'gwei')} gwei`);

            return {
                success: true,
                txHash: receipt.hash,
                gasUsed: receipt.gasUsed,
                gasPrice: receipt.gasPrice || BigInt(0)
            };

        } catch (error) {
            logger.error(`❌ Traditional transaction execution failed: ${(error as Error).message}`);
            throw error;
        }
    }

    /**
     * Simulate arbitrage execution without sending transactions
     */
    private async simulateArbitrageExecution(
        route: ArbitrageRoute,
        transaction: FlashbotsBundleTransaction,
        options: ExecutionOptions
    ): Promise<ExecutionResult> {
        try {
            logger.info('🎭 SIMULATION: Arbitrage opportunity detected');
            logger.info(`   Expected Profit: ${ethers.formatEther(route.expectedProfit)} ETH`);
            logger.info(`   Route: ${route.pools.map(p => p.protocol || 'Unknown').join(' → ')}`);
            logger.info(`   Tokens: ${route.tokens.map(t => t.symbol).join(' → ')}`);

            // Get current gas strategy for accurate cost calculation
            const gasStrategy = await this.gasOptimizer.getCurrentGasStrategy();

            // Estimate gas limit for arbitrage transaction
            const estimatedGasLimit = BigInt(250000); // Conservative estimate for arbitrage

            // Calculate actual gas cost using current gas strategy
            const actualGasCost = estimatedGasLimit * BigInt(gasStrategy.maxFeePerGas.toString());

            // Convert expectedProfit to BigInt for calculation
            const expectedProfitBigInt = BigInt(route.expectedProfit.toString());
            const netProfit = expectedProfitBigInt - actualGasCost;

            await logger.profitabilityAnalysis(
                expectedProfitBigInt,
                actualGasCost,
                netProfit,
                'Arbitrage Simulation'
            );

            // Check if profitable after gas costs
            if (netProfit <= BigInt(config.minProfitWei)) {
                logger.system('⚠️  Arbitrage not profitable after gas costs');
                logger.system(`   Required minimum: ${ethers.formatEther(config.minProfitWei)} ETH`);
                return {success: false, error: 'Not profitable after gas costs'};
            }

            logger.info(`✅ Arbitrage is profitable! Net profit: ${ethers.formatEther(netProfit)} ETH`);

            logger.info('✅ SIMULATION: Arbitrage would be profitable and executed');

            return {
                success: true,
                txHash: 'SIMULATION_ARBITRAGE_TX',
                gasUsed: BigInt(300000),
                gasPrice: BigInt(gasStrategy.maxFeePerGas.toString()),
                profit: netProfit,
                executionTime: Date.now() - Date.now()
            };

        } catch (error) {
            logger.logError(error as Error, 'FlashbotsExecutor.simulateArbitrageExecution');
            return {success: false, error: (error as Error).message};
        }
    }

    /**
     * Simulate flashloan execution without sending transactions
     */
    private async simulateFlashloanExecution(
        route: FlashloanRoute,
        transaction: FlashbotsBundleTransaction,
        options: ExecutionOptions
    ): Promise<ExecutionResult> {
        try {
            logger.system('🎭 SIMULATION: Flashloan opportunity detected');
            logger.system(`   Flashloan Amount: ${ethers.formatEther(route.flashloanAmount)} ${route.flashloanToken.symbol}`);
            logger.system(`   Expected Profit: ${ethers.formatEther(route.expectedProfit)} ETH`);
            logger.system(`   Arbitrage Route: ${route.arbitrageRoute.tokens.map(t => t.symbol).join(' → ')}`);

            // Get current gas strategy for accurate cost calculation
            const gasStrategy = await this.gasOptimizer.getCurrentGasStrategy();

            // Estimate gas limit for enhanced flashloan transaction
            const estimatedGasLimit = BigInt(400000); // Higher estimate for enhanced flashloan

            // Calculate actual gas cost using current gas strategy
            const actualGasCost = estimatedGasLimit * BigInt(gasStrategy.maxFeePerGas.toString());

            // Convert expectedProfit to BigInt for calculation
            const expectedProfitBigInt = BigInt(route.expectedProfit.toString());
            const netProfit = expectedProfitBigInt - actualGasCost;

            await logger.profitabilityAnalysis(
                expectedProfitBigInt,
                actualGasCost,
                netProfit,
                'Enhanced Flashloan Simulation'
            );

            // Check if profitable after gas costs
            if (netProfit <= BigInt(config.minProfitWei)) {
                logger.system('⚠️  Enhanced flashloan not profitable after gas costs');
                logger.system(`   Required minimum: ${ethers.formatEther(config.minProfitWei)} ETH`);
                return {success: false, error: 'Not profitable after gas costs'};
            }

            logger.info(`✅ Enhanced flashloan is profitable! Net profit: ${ethers.formatEther(netProfit)} ETH`);

            logger.info('✅ SIMULATION: Flashloan would be profitable and executed');

            return {
                success: true,
                txHash: 'SIMULATION_FLASHLOAN_TX',
                gasUsed: BigInt(400000),
                gasPrice: BigInt(gasStrategy.maxFeePerGas.toString()),
                profit: netProfit,
                executionTime: Date.now() - Date.now()
            };

        } catch (error) {
            logger.logError(error as Error, 'FlashbotsExecutor.simulateFlashloanExecution');
            return {success: false, error: (error as Error).message};
        }
    }



    /**
     * Build arbitrage transaction with proper swap data
     */
    private async buildArbitrageTransaction(
        route: ArbitrageRoute,
        options: ExecutionOptions
    ): Promise<FlashbotsBundleTransaction | null> {
        try {
            // Validate route has valid pools and tokens
            if (!route.pools || route.pools.length === 0) {
                logger.error('Invalid arbitrage route: no pools');
                return null;
            }

            if (!route.tokens || route.tokens.length < 2) {
                logger.error('Invalid arbitrage route: insufficient tokens');
                return null;
            }

            const targetPool = route.pools[0];
            if (!targetPool.address || !ethers.isAddress(targetPool.address)) {
                logger.error('Invalid pool address in arbitrage route', {address: targetPool.address});
                return null;
            }

            // Build swap data using the new swap data builder
            const swapData = await this.swapDataBuilder.buildArbitrageSwapData(
                route,
                options,
                this.wallet.address
            );

            if (!swapData) {
                logger.error('Failed to build swap data for arbitrage');
                return null;
            }

            // Validate swap data
            if (!this.swapDataBuilder.validateSwapData(swapData)) {
                logger.error('Invalid swap data generated', {swapData});
                return null;
            }

            // Estimate gas for the transaction
            const gasLimit = await this.gasOptimizer.estimateGasForTransaction(
                swapData.to,
                swapData.data,
                swapData.value
            );

            // Get optimal gas pricing for the transaction
            const gasStrategy = await this.gasOptimizer.getCurrentGasStrategy();

            // Get current nonce for the wallet
            const nonce = await this.provider.getTransactionCount(this.wallet.address, 'pending');

            // Build the transaction
            const transaction: ethers.TransactionRequest = {
                to: swapData.to,
                data: swapData.data,
                value: swapData.value,
                gasLimit: BigInt(gasLimit.toString()),
                nonce,
                // Use EIP-1559 gas pricing for better Flashbots compatibility - ensure proper BigInt conversion
                maxFeePerGas: BigInt(gasStrategy.maxFeePerGas.toString()),
                maxPriorityFeePerGas: BigInt(gasStrategy.priorityFee.toString()),
                type: 2, // EIP-1559 transaction type
                chainId: config.chainId // Required for EIP-1559 transactions
            };

            logger.debug('Built arbitrage transaction', {
                to: transaction.to,
                dataLength: swapData.data.length,
                gasLimit: gasLimit.toString(),
                nonce,
                maxFeePerGas: gasStrategy.maxFeePerGas.toString(),
                maxPriorityFeePerGas: gasStrategy.priorityFee.toString()
            });

            return this.flashbotsManager.createBundleTransaction(transaction, this.wallet);

        } catch (error) {
            logger.logError(error as Error, 'FlashbotsExecutor.buildArbitrageTransaction');
            return null;
        }
    }

    /**
     * Validate and enhance arbitrage route with accurate profit calculations
     */
    async validateArbitrageRoute(route: ArbitrageRoute): Promise<ArbitrageRoute | null> {
        try {
            if (!route.pools || route.pools.length < 1 || !route.tokens || route.tokens.length < 2) {
                logger.debug('Route validation failed: insufficient pools or tokens', {
                    pools: route.pools?.length || 0,
                    tokens: route.tokens?.length || 0
                });
                return null;
            }

            // Basic validation - check if pools have valid addresses
            for (let i = 0; i < route.pools.length; i++) {
                const pool = route.pools[i];
                if (!pool.address || !ethers.isAddress(pool.address)) {
                    logger.debug(`Route validation failed: invalid pool address at index ${i}`, {
                        address: pool.address
                    });
                    return null;
                }
            }

            // Basic validation - check if tokens have valid addresses
            for (let i = 0; i < route.tokens.length; i++) {
                const token = route.tokens[i];
                if (!token.address || !ethers.isAddress(token.address)) {
                    logger.debug(`Route validation failed: invalid token address at index ${i}`, {
                        address: token.address,
                        symbol: token.symbol
                    });
                    return null;
                }
            }

            // Normalize expected profit to wei for consistent validation
            const denomToken = route.tokens?.[0];
            const expectedProfitWei = denomToken
                ? await this.toWei(BigInt(route.expectedProfit.toString()), denomToken)
                : BigInt(route.expectedProfit.toString());

            // Only require minimum profit threshold (much more lenient)
            const minProfitWei = ethers.parseEther('0.0001'); // 0.0001 ETH minimum
            if (expectedProfitWei <= minProfitWei) {
                logger.debug('Route validation failed: profit below minimum threshold', {
                    expectedProfit: ethers.formatEther(expectedProfitWei),
                    minProfit: ethers.formatEther(minProfitWei)
                });
                return null;
            }

            // Calculate gas estimate for the arbitrage
            const gasEstimate = await this.estimateArbitrageGas(route);

            // More reasonable gas cost check - ensure profit > gas cost with small buffer
            const currentGasPrice = await this.gasEstimator.getOptimalGasPrice('fast');
            const maxFeePerGas = currentGasPrice.maxFeePerGas || ethers.parseUnits('100', 'gwei'); // Reasonable fallback
            const gasCost = gasEstimate * maxFeePerGas;

            // Only require profit to be 20% more than gas cost (not 100% more)
            const minProfitAfterGas = gasCost + (gasCost / 5n); // 20% buffer

            if (expectedProfitWei <= minProfitAfterGas) {
                logger.debug('Route validation failed: insufficient profit margin over gas cost', {
                    profit: ethers.formatEther(expectedProfitWei),
                    gasCost: ethers.formatEther(gasCost),
                    minRequired: ethers.formatEther(minProfitAfterGas),
                    gasPrice: ethers.formatUnits(maxFeePerGas, 'gwei') + ' gwei'
                });
                return null;
            }

            logger.debug('Route validation passed', {
                pools: route.pools.length,
                tokens: route.tokens.map(t => t.symbol).join(' → '),
                expectedProfit: ethers.formatEther(expectedProfitWei),
                gasCost: ethers.formatEther(gasCost),
                netProfit: ethers.formatEther(expectedProfitWei - gasCost)
            });

            // Return route with updated gas estimate
            return {
                ...route,
                gasEstimate,
                confidence: Math.min(route.confidence + 10, 95) // Boost confidence slightly for validated routes
            };

        } catch (error) {
            logger.error('Error validating arbitrage route:', error);
            return null;
        }
    }

    /**
     * Estimate gas for arbitrage transaction
     */
    private async estimateArbitrageGas(route: ArbitrageRoute): Promise<bigint> {
        try {
            // Base gas for simple swap
            let gasEstimate = 150000n; // Conservative base estimate

            // Add gas for each additional pool/hop
            if (route.pools.length > 1) {
                gasEstimate += BigInt(route.pools.length - 1) * 50000n;
            }

            // Add gas for complex protocols
            for (const pool of route.pools) {
                switch (pool.protocol) {
                    case 'uniswap-v3':
                        gasEstimate += 30000n; // V3 is more gas intensive
                        break;
                    case 'curve':
                        gasEstimate += 20000n; // Curve calculations
                        break;
                    default:
                        gasEstimate += 10000n; // V2 style DEXs
                }
            }

            return gasEstimate;
        } catch (error) {
            logger.error('Error estimating arbitrage gas:', error);
            return 200000n; // Conservative fallback
        }
    }

    /**
     * Build multi-step arbitrage transaction for complex routes
     */
    async buildMultiStepArbitrage(
        route: ArbitrageRoute,
        options: ExecutionOptions
    ): Promise<FlashbotsBundleTransaction[]> {
        try {
            const transactions: FlashbotsBundleTransaction[] = [];

            if (route.pools.length === 1) {
                // Single pool arbitrage - not typical but handle it
                const singleTx = await this.buildArbitrageTransaction(route, options);
                if (singleTx) {
                    transactions.push(singleTx);
                }
                return transactions;
            }

            // Multi-step arbitrage: buy on first DEX, sell on second DEX
            for (let i = 0; i < route.pools.length; i++) {
                const pool = route.pools[i];
                const tokenIn = route.tokens[i];
                const tokenOut = route.tokens[i + 1];

                if (!tokenIn || !tokenOut) {
                    logger.debug(`Missing tokens for step ${i}`);
                    continue;
                }

                // Create a single-step route for this hop
                const stepRoute: ArbitrageRoute = {
                    pools: [pool],
                    tokens: [tokenIn, tokenOut],
                    expectedProfit: 0n, // Will be calculated
                    gasEstimate: 0n,
                    confidence: route.confidence
                };

                // Build swap data for this step
                const swapData = await this.swapDataBuilder.buildArbitrageSwapData(
                    stepRoute,
                    options,
                    this.wallet.address
                );

                if (!swapData || !this.swapDataBuilder.validateSwapData(swapData)) {
                    logger.debug(`Failed to build swap data for step ${i}`);
                    continue;
                }

                // Estimate gas
                const gasLimit = await this.gasOptimizer.estimateGasForTransaction(
                    swapData.to,
                    swapData.data,
                    swapData.value
                );

                // Get optimal gas pricing for the transaction
                const gasStrategy = await this.gasOptimizer.getCurrentGasStrategy();

                // Get current nonce for the wallet (incremented for each transaction in bundle)
                const nonce = await this.provider.getTransactionCount(this.wallet.address, 'pending') + i;

                // Build transaction
                const transaction: ethers.TransactionRequest = {
                    to: swapData.to,
                    data: swapData.data,
                    value: swapData.value,
                    gasLimit: BigInt(gasLimit.toString()),
                    nonce,
                    // Use EIP-1559 gas pricing for better Flashbots compatibility - ensure proper BigInt conversion
                    maxFeePerGas: BigInt(gasStrategy.maxFeePerGas.toString()),
                    maxPriorityFeePerGas: BigInt(gasStrategy.priorityFee.toString()),
                    type: 2, // EIP-1559 transaction type
                    chainId: config.chainId // Required for EIP-1559 transactions
                };

                const bundleTx = this.flashbotsManager.createBundleTransaction(transaction, this.wallet);
                transactions.push(bundleTx);
            }

            logger.debug(`Built ${transactions.length} transactions for multi-step arbitrage`);
            return transactions;

        } catch (error) {
            logger.logError(error as Error, 'FlashbotsExecutor.buildMultiStepArbitrage');
            return [];
        }
    }

    /**
     * Build flashloan transaction
     */
    private async buildFlashloanTransaction(
        route: FlashloanRoute,
        options: ExecutionOptions,
        provider: 'aave' | 'balancer' = 'aave'
    ): Promise<FlashbotsBundleTransaction | null> {
        try {
            // Build the actual flashloan transaction data
            const hybridContractInterface = new ethers.Interface([
                'function executeOptimalFlashloan(address asset, uint256 amount, bytes calldata params) external'
            ]);

            // Get target token from arbitrage route
            const targetToken = route.arbitrageRoute.tokens.find(token =>
                token.address !== route.flashloanToken.address
            );

            if (!targetToken) {
                throw new Error('No target token found in arbitrage route');
            }

            // Get DEX routers from pools
            const buyPool = route.arbitrageRoute.pools[0];
            const sellPool = route.arbitrageRoute.pools[1] || buyPool;

            // Map protocol to router addresses (use network-appropriate addresses)
            const getRouterAddress = (protocol: string) => {
                switch (protocol) {
                    case 'uniswap-v2':
                        return ADDRESSES.UNISWAP_V2_ROUTER; // Network-appropriate V2 router
                    case 'uniswap-v3':
                        return ADDRESSES.UNISWAP_V3_ROUTER; // Network-appropriate V3 router
                    default:
                        return ADDRESSES.UNISWAP_V2_ROUTER; // Default to V2
                }
            };

            // Encode arbitrage parameters for the advanced multi-hop contract
            // ArbitrageParams struct: buyPath[], sellPath[], buyDex, sellDex, v3Fees[], minProfit, provider, slippageToleranceBps, maxGasCostWei
            const buyPath = [route.flashloanToken.address, targetToken.address];
            const sellPath = [targetToken.address, route.flashloanToken.address];
            const v3Fees = [3000]; // 0.3% fee for single hop

            const arbitrageParams = ethers.AbiCoder.defaultAbiCoder().encode(
                ['address[]', 'address[]', 'address', 'address', 'uint24[]', 'uint256', 'uint8', 'uint256', 'uint256'],
                [
                    buyPath,                             // buyPath: [flashloanToken, targetToken]
                    sellPath,                            // sellPath: [targetToken, flashloanToken]
                    getRouterAddress(buyPool.protocol),  // buyDex
                    getRouterAddress(sellPool.protocol), // sellDex
                    v3Fees,                              // v3Fees array
                    ethers.parseEther('0.00001'),        // minProfit (reduced for better success rate)
                    provider === 'aave' ? 0 : 1,         // FlashloanProvider: AAVE = 0, BALANCER = 1
                    1000,                                // slippageToleranceBps (10% slippage tolerance)
                    ethers.parseUnits('200', 'gwei')     // maxGasCostWei (200 gwei gas cost limit)
                ]
            );

            // Encode the executeOptimalFlashloan function call
            const flashloanData = hybridContractInterface.encodeFunctionData('executeOptimalFlashloan', [
                route.flashloanToken.address,
                route.flashloanAmount,
                arbitrageParams
            ]);

            // Validate contract address before gas estimation
            if (!config.hybridFlashloanContract || !ethers.isAddress(config.hybridFlashloanContract)) {
                logger.error('Invalid hybrid flashloan contract address', {
                    address: config.hybridFlashloanContract
                });
                return null;
            }

            // Validate flashloan data
            if (!flashloanData || flashloanData === '0x') {
                logger.error('Invalid flashloan transaction data');
                return null;
            }

            // Validate transaction before gas estimation to prevent reverts
            const isValid = await this.validateFlashloanTransaction(
                config.hybridFlashloanContract,
                flashloanData
            );

            if (!isValid) {
                logger.error('❌ Flashloan transaction validation failed - would revert');
                return null;
            }

            const gasLimit = await this.gasOptimizer.estimateGasForTransaction(
                config.hybridFlashloanContract,
                flashloanData,
                0
            );

            // Get optimal gas pricing for the transaction
            const gasStrategy = await this.gasOptimizer.getCurrentGasStrategy();

            // Get current nonce for the wallet
            const nonce = await this.provider.getTransactionCount(this.wallet.address, 'pending');

            const transaction: ethers.TransactionRequest = {
                to: config.hybridFlashloanContract,
                data: flashloanData,
                value: 0,
                gasLimit: BigInt(gasLimit.toString()),
                nonce,
                // Use EIP-1559 gas pricing for better Flashbots compatibility - ensure proper BigInt conversion
                maxFeePerGas: BigInt(gasStrategy.maxFeePerGas.toString()),
                maxPriorityFeePerGas: BigInt(gasStrategy.priorityFee.toString()),
                type: 2, // EIP-1559 transaction type
                chainId: config.chainId // Required for EIP-1559 transactions
            };

            // Debug transaction structure for Flashbots compatibility
            logger.debug(`🔍 Transaction structure for Flashbots:`);
            logger.debug(`   to: ${transaction.to}`);
            logger.debug(`   value: ${transaction.value}`);
            logger.debug(`   gasLimit: ${transaction.gasLimit?.toString()}`);
            logger.debug(`   maxFeePerGas: ${transaction.maxFeePerGas?.toString()}`);
            logger.debug(`   maxPriorityFeePerGas: ${transaction.maxPriorityFeePerGas?.toString()}`);
            logger.debug(`   nonce: ${transaction.nonce}`);
            logger.debug(`   type: ${transaction.type}`);
            logger.debug(`   chainId: ${transaction.chainId}`);
            logger.debug(`   dataLength: ${transaction.data?.length || 0}`);

            return this.flashbotsManager.createBundleTransaction(transaction, this.wallet);

        } catch (error) {
            logger.logError(error as Error, 'FlashbotsExecutor.buildFlashloanTransaction');
            return null;
        }
    }

    /**
     * Check if wallet has sufficient balance for gas costs
     */
    async checkWalletBalanceForExecution(
        transaction: FlashbotsBundleTransaction | ethers.TransactionRequest,
        options: ExecutionOptions
    ): Promise<boolean> {
        try {
            // Get current wallet balance
            const balance = await this.provider.getBalance(this.wallet.address);

            // Extract transaction request if it's a bundle transaction
            const txRequest = 'transaction' in transaction ? transaction.transaction : transaction;

            // Estimate gas cost for the transaction
            const gasLimit = BigInt(txRequest.gasLimit?.toString() || '300000'); // Default gas limit
            const gasPrice = await this.gasEstimator.getOptimalGasPrice(options.urgency);
            const estimatedGasCost = gasLimit * BigInt(gasPrice.gasPrice.toString());

            // Add 10% buffer for gas price fluctuations
            const gasCostWithBuffer = estimatedGasCost + (estimatedGasCost / BigInt(10));

            logger.system(`💰 Wallet Balance Check:`);
            logger.system(`   Current Balance: ${ethers.formatEther(balance)} ETH`);
            logger.system(`   Estimated Gas Cost: ${ethers.formatEther(estimatedGasCost)} ETH`);
            logger.system(`   Gas Cost + Buffer: ${ethers.formatEther(gasCostWithBuffer)} ETH`);

            const hasBalance = balance >= gasCostWithBuffer;

            if (!hasBalance) {
                logger.warn(`⚠️  Insufficient balance for gas costs!`);
                logger.warn(`   Need: ${ethers.formatEther(gasCostWithBuffer)} ETH`);
                logger.warn(`   Have: ${ethers.formatEther(balance)} ETH`);
                logger.warn(`   Deficit: ${ethers.formatEther(gasCostWithBuffer - balance)} ETH`);
            }

            return hasBalance;
        } catch (error) {
            logger.logError(error as Error, 'FlashbotsExecutor.checkWalletBalanceForExecution');
            logger.warn('⚠️  Could not verify wallet balance, proceeding with caution');
            return true; // Default to true if balance check fails to avoid blocking execution
        }
    }

    /**
     * Check if execution conditions are favorable
     */
    async isExecutionFavorable(options: ExecutionOptions): Promise<boolean> {
        try {
            // Check gas conditions
            const gasFavorable = await this.gasEstimator.isGasFavorable(options.maxGasCostEth);

            // Check network congestion
            const currentBlock = await this.provider.getBlockNumber();
            const block = await this.provider.getBlock(currentBlock);
            const gasUsageRatio = Number(block?.gasUsed || 0) / Number(block?.gasLimit || 1);

            // Allow execution during higher congestion for MEV opportunities
            const congestionOk = gasUsageRatio < 0.95; // Increased from 90% to 95%

            logger.system(`⛽ Gas Favorable: ${gasFavorable}`);
            logger.system(`🚦 Network Congestion: ${(gasUsageRatio * 100).toFixed(1)}%`);

            // For MEV, we're more aggressive - only block execution if gas is extremely unfavorable
            return gasFavorable || congestionOk; // Changed from AND to OR

        } catch (error) {
            logger.logError(error as Error, 'FlashbotsExecutor.isExecutionFavorable');
            return false;
        }
    }

    /**
     * Get execution statistics
     */
    async getExecutionStats(): Promise<{
        flashbotsAvailable: boolean;
        gasEstimates: any;
        networkCongestion: number;
        recommendedStrategy: 'flashbots' | 'mempool';
    }> {
        try {
            const gasEstimates = await this.gasEstimator.getGasEstimates();
            const currentBlock = await this.provider.getBlockNumber();
            const block = await this.provider.getBlock(currentBlock);
            const networkCongestion = Number(block?.gasUsed || 0) / Number(block?.gasLimit || 1);

            const recommendedStrategy = this.flashbotsManager.isAvailable() && networkCongestion > 0.7
                ? 'flashbots'
                : 'mempool';

            return {
                flashbotsAvailable: this.flashbotsManager.isAvailable(),
                gasEstimates,
                networkCongestion,
                recommendedStrategy
            };

        } catch (error) {
            logger.logError(error as Error, 'FlashbotsExecutor.getExecutionStats');
            return {
                flashbotsAvailable: false,
                gasEstimates: null,
                networkCongestion: 0,
                recommendedStrategy: 'mempool'
            };
        }
    }

    /**
     * Validate EIP-1559 gas parameters to ensure maxPriorityFeePerGas <= maxFeePerGas
     */
    private validateEIP1559GasParams(maxFeePerGas: bigint, maxPriorityFeePerGas: bigint): {
        maxFeePerGas: bigint;
        maxPriorityFeePerGas: bigint;
    } {
        // If priority fee is higher than max fee, adjust them
        if (maxPriorityFeePerGas > maxFeePerGas) {
            logger.warn(`⚠️  Priority fee (${ethers.formatUnits(maxPriorityFeePerGas, 'gwei')} gwei) > Max fee (${ethers.formatUnits(maxFeePerGas, 'gwei')} gwei)`);

            // Option 1: Set priority fee to max fee (most conservative)
            const adjustedPriorityFee = maxFeePerGas;

            // Option 2: Increase max fee to accommodate priority fee (more aggressive)
            // const adjustedMaxFee = maxPriorityFeePerGas + ethers.parseUnits('5', 'gwei'); // Add 5 gwei buffer

            logger.info(`   Adjusted priority fee to: ${ethers.formatUnits(adjustedPriorityFee, 'gwei')} gwei`);

            return {
                maxFeePerGas: maxFeePerGas,
                maxPriorityFeePerGas: adjustedPriorityFee
            };
        }

        // Parameters are valid
        return {
            maxFeePerGas,
            maxPriorityFeePerGas
        };
    }

    /**
     * Execute advanced bundle submission with retry logic and optimization
     */
    private async executeAdvancedBundleSubmission(
        transactions: any[],
        targetBlock: number,
        isHighPriority: boolean,
        useMultiBlock: boolean
    ): Promise<any> {
        let lastError: string | undefined;

        // Implement retry logic based on configuration
        for (let retryCount = 0; retryCount <= config.bundleRetryCount; retryCount++) {
            try {
                logger.debug(`🚀 Bundle submission attempt ${retryCount + 1}/${config.bundleRetryCount + 1}`);

                let submission;
                if (useMultiBlock) {
                    logger.debug('🎯 Using multi-block submission strategy for better inclusion');
                    const blockCount = Math.min(config.maxBlocksAhead, 7);
                    submission = await this.flashbotsManager.submitBundleMultiBlock(
                        transactions,
                        targetBlock,
                        blockCount,
                        {
                            isHighPriority,
                            minTimestamp: Math.floor(Date.now() / 1000),
                            maxTimestamp: Math.floor(Date.now() / 1000) + config.bundleTimeoutMs / 1000
                        }
                    );
                } else {
                    submission = await this.flashbotsManager.submitBundle(
                        transactions,
                        targetBlock,
                        {
                            isHighPriority,
                            minTimestamp: Math.floor(Date.now() / 1000),
                            maxTimestamp: Math.floor(Date.now() / 1000) + config.bundleTimeoutMs / 1000
                        }
                    );
                }

                // If submission was successful, return immediately
                if (submission.success) {
                    if (retryCount > 0) {
                        logger.success(`✅ Bundle submission succeeded on retry ${retryCount + 1}`);
                    }
                    return submission;
                }

                // Store error for potential retry
                lastError = submission.error;

                // If this is not the last attempt, wait before retrying
                if (retryCount < config.bundleRetryCount) {
                    const retryDelay = Math.min(1000 * Math.pow(2, retryCount), 5000); // Exponential backoff, max 5s
                    logger.warn(`⚠️  Bundle submission failed (attempt ${retryCount + 1}), retrying in ${retryDelay}ms...`);
                    logger.debug(`   Error: ${submission.error}`);

                    await new Promise(resolve => setTimeout(resolve, retryDelay));

                    // Adjust target block for next attempt with timing considerations
                    const currentBlock = await this.provider.getBlockNumber();
                    if (targetBlock <= currentBlock) {
                        const newTargetBlock = await this.calculateOptimalTargetBlock();
                        targetBlock = newTargetBlock;
                        logger.debug(`   Adjusted target block to ${targetBlock} with timing buffer`);
                    }
                }

            } catch (error) {
                lastError = (error as Error).message;
                logger.error(`❌ Bundle submission attempt ${retryCount + 1} failed: ${lastError}`);

                if (retryCount < config.bundleRetryCount) {
                    const retryDelay = Math.min(1000 * Math.pow(2, retryCount), 5000);
                    await new Promise(resolve => setTimeout(resolve, retryDelay));
                }
            }
        }

        // All retries failed
        logger.error(`❌ All ${config.bundleRetryCount + 1} bundle submission attempts failed`);
        return {
            success: false,
            error: lastError || 'All retry attempts failed',
            bundleHash: undefined,
            resolution: 'retry_exhausted'
        };
    }

    /**
     * Calculate optimal target block with timing buffer considerations
     */
    private async calculateOptimalTargetBlock(): Promise<number> {
        try {
            const currentBlock = await this.provider.getBlockNumber();
            const currentTime = Date.now();

            // Get the current block to analyze timing
            const block = await this.provider.getBlock(currentBlock);
            if (!block) {
                // Fallback to simple next block if we can't get current block info
                return currentBlock + 1;
            }

            const blockTimestamp = Number(block.timestamp) * 1000; // Convert to milliseconds
            const timeSinceBlock = currentTime - blockTimestamp;

            // Ethereum average block time is ~12 seconds
            const averageBlockTime = 12000; // 12 seconds in milliseconds
            const timeToNextBlock = averageBlockTime - timeSinceBlock;

            logger.debug(`⏰ Block timing analysis:`);
            logger.debug(`   Current block: ${currentBlock}`);
            logger.debug(`   Time since block: ${timeSinceBlock}ms`);
            logger.debug(`   Estimated time to next block: ${timeToNextBlock}ms`);
            logger.debug(`   Block timing buffer: ${config.blockTimingBufferMs}ms`);

            // Determine optimal target block based on timing
            let targetBlock = currentBlock + 1;

            // If we're very close to the next block (within timing buffer), target the block after
            if (timeToNextBlock < config.blockTimingBufferMs) {
                targetBlock = currentBlock + 2;
                logger.debug(`   ⚡ Close to next block, targeting block ${targetBlock} for better timing`);
            }

            // If we're very early in the current block cycle, we might target current block + 1
            if (timeSinceBlock < config.blockTimingBufferMs / 2) {
                targetBlock = currentBlock + 1;
                logger.debug(`   🎯 Early in block cycle, targeting block ${targetBlock}`);
            }

            // Additional logic for preemptive submission
            if (config.enablePreemptiveSubmission) {
                // For preemptive submission, we might want to target even further ahead
                if (timeToNextBlock < config.blockTimingBufferMs * 2) {
                    targetBlock = Math.max(targetBlock, currentBlock + 2);
                    logger.debug(`   🚀 Preemptive mode: targeting block ${targetBlock}`);
                }
            }

            // Ensure we never target a block that's already passed or current
            targetBlock = Math.max(targetBlock, currentBlock + 1);

            logger.system(`🎯 Optimal target block calculated: ${targetBlock} (current: ${currentBlock})`);
            logger.debug(`   Timing buffer applied: ${config.blockTimingBufferMs}ms`);
            logger.debug(`   Blocks ahead: ${targetBlock - currentBlock}`);

            return targetBlock;

        } catch (error) {
            logger.warn(`Block timing calculation failed: ${(error as Error).message}`);
            // Fallback to simple next block calculation
            const currentBlock = await this.provider.getBlockNumber();
            return currentBlock + 1;
        }
    }

    /**
     * Calculate actual profit from executed bundle by analyzing transaction receipts
     */
    private async calculateActualProfitFromBundle(
        bundleHash: string,
        transactions: FlashbotsBundleTransaction[],
        targetBlock: number
    ): Promise<bigint | undefined> {
        try {
            logger.info(`🔍 Calculating actual profit for bundle ${bundleHash.substring(0, 10)}...`);

            // Get the block where the bundle was included
            const block = await this.provider.getBlock(targetBlock);
            if (!block) {
                logger.info('Could not fetch target block for profit calculation');
                return undefined;
            }

            // Find our transaction hashes in the block
            const ourTxHashes: string[] = [];
            for (const blockTxHash of block.transactions) {
                try {
                    const tx = await this.provider.getTransaction(blockTxHash);
                    if (tx && await this.isOurTransaction(tx, transactions)) {
                        ourTxHashes.push(blockTxHash);
                    }
                } catch (error) {
                    // Skip transactions we can't fetch
                    continue;
                }
            }

            if (ourTxHashes.length === 0) {
                logger.info('No matching transactions found in target block');
                return undefined;
            }

            logger.info(`Found ${ourTxHashes.length} matching transactions in block`);

            // Get transaction receipts for profit analysis
            const receipts = await Promise.all(
                ourTxHashes.map(async (txHash) => {
                    try {
                        return await this.provider.getTransactionReceipt(txHash);
                    } catch {
                        return null;
                    }
                })
            );

            const validReceipts = receipts.filter(receipt => receipt !== null);
            if (validReceipts.length === 0) {
                logger.info('No valid transaction receipts found');
                return undefined;
            }

            // Calculate profit based on the type of transaction
            return await this.analyzeTransactionProfitability(validReceipts, ourTxHashes);

        } catch (error) {
            logger.error(`Error in calculateActualProfitFromBundle: ${(error as Error).message}`);
            return undefined;
        }
    }

    /**
     * Check if a transaction belongs to our bundle
     */
    private async isOurTransaction(
        tx: ethers.TransactionResponse,
        bundleTransactions: FlashbotsBundleTransaction[]
    ): Promise<boolean> {
        for (const bundleTx of bundleTransactions) {
            if ('transaction' in bundleTx && bundleTx.transaction) {
                const txData = bundleTx.transaction;

                // Get signer address
                let signerAddress: string | undefined;
                try {
                    signerAddress = await bundleTx.signer?.getAddress();
                } catch {
                    signerAddress = undefined;
                }

                // Match by to address, value, and data
                if (tx.to === txData.to &&
                    tx.value === (txData.value || 0n) &&
                    tx.data === (txData.data || '0x') &&
                    (signerAddress ? tx.from === signerAddress : true)) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * Analyze transaction receipts to calculate actual profit
     */
    private async analyzeTransactionProfitability(
        receipts: ethers.TransactionReceipt[],
        txHashes: string[]
    ): Promise<bigint | undefined> {
        try {
            let totalProfit = 0n;
            let totalGasCost = 0n;

            for (let i = 0; i < receipts.length; i++) {
                const receipt = receipts[i];
                const txHash = txHashes[i];

                // Calculate gas cost
                const gasCost = receipt.gasUsed * (receipt.gasPrice || 0n);
                totalGasCost += gasCost;

                logger.debug(`Transaction ${txHash.substring(0, 10)}:`, {
                    gasUsed: receipt.gasUsed.toString(),
                    gasPrice: ethers.formatUnits(receipt.gasPrice || 0n, 'gwei') + ' gwei',
                    gasCost: ethers.formatEther(gasCost) + ' ETH',
                    status: receipt.status === 1 ? 'Success' : 'Failed'
                });

                // Analyze profit from transaction logs and balance changes
                const txProfit = await this.calculateTransactionProfit(receipt, txHash);
                if (txProfit !== undefined) {
                    totalProfit += txProfit;
                }
            }

            // Net profit = total profit - total gas costs
            const netProfit = totalProfit - totalGasCost;

            logger.debug('Bundle profit analysis:', {
                totalGrossProfit: ethers.formatEther(totalProfit) + ' ETH',
                totalGasCost: ethers.formatEther(totalGasCost) + ' ETH',
                netProfit: ethers.formatEther(netProfit) + ' ETH',
                transactionCount: receipts.length
            });

            return netProfit;

        } catch (error) {
            logger.debug(`Error analyzing transaction profitability: ${(error as Error).message}`);
            return undefined;
        }
    }

    /**
     * Calculate profit from a single transaction by analyzing logs and balance changes
     */
    private async calculateTransactionProfit(
        receipt: ethers.TransactionReceipt,
        txHash: string
    ): Promise<bigint | undefined> {
        try {
            // Method 1: Analyze Transfer events for token movements
            const transferProfit = this.analyzeTransferEvents(receipt);
            if (transferProfit !== undefined) {
                return transferProfit;
            }

            // Method 2: Analyze balance changes (for ETH transactions)
            const balanceProfit = await this.analyzeBalanceChanges(receipt, txHash);
            if (balanceProfit !== undefined) {
                return balanceProfit;
            }

            // Method 3: Analyze flashloan-specific events
            const flashloanProfit = this.analyzeFlashloanEvents(receipt);
            if (flashloanProfit !== undefined) {
                return flashloanProfit;
            }

            logger.debug(`Could not determine profit for transaction ${txHash.substring(0, 10)}`);
            return undefined;

        } catch (error) {
            logger.debug(`Error calculating transaction profit: ${(error as Error).message}`);
            return undefined;
        }
    }

    /**
     * Analyze Transfer events to calculate profit from token movements
     */
    private analyzeTransferEvents(receipt: ethers.TransactionReceipt): bigint | undefined {
        try {
            // ERC20 Transfer event signature: Transfer(address,address,uint256)
            const transferEventSignature = '0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef';

            const transferLogs = receipt.logs.filter(log =>
                log.topics[0] === transferEventSignature && log.topics.length >= 3
            );

            if (transferLogs.length === 0) {
                return undefined;
            }

            let netTokenFlow = 0n;
            const walletAddress = this.wallet.address.toLowerCase();

            for (const log of transferLogs) {
                try {
                    const from = '0x' + log.topics[1].slice(26); // Remove padding
                    const to = '0x' + log.topics[2].slice(26);   // Remove padding
                    const amount = BigInt(log.data);

                    // Track tokens flowing to/from our wallet
                    if (to.toLowerCase() === walletAddress) {
                        netTokenFlow += amount; // Tokens received
                    }
                    if (from.toLowerCase() === walletAddress) {
                        netTokenFlow -= amount; // Tokens sent
                    }
                } catch {
                    continue; // Skip malformed logs
                }
            }

            // Convert token flow to ETH equivalent (simplified)
            // In practice, you'd need to know the token price
            if (netTokenFlow > 0n) {
                logger.debug(`Net token inflow detected: ${netTokenFlow.toString()}`);
                // This is a simplified conversion - in practice you'd use price oracles
                return netTokenFlow / 1000n; // Rough conversion for demonstration
            }

            return undefined;

        } catch (error) {
            logger.debug(`Error analyzing transfer events: ${(error as Error).message}`);
            return undefined;
        }
    }

    /**
     * Analyze balance changes for ETH profit calculation
     */
    private async analyzeBalanceChanges(
        receipt: ethers.TransactionReceipt,
        txHash: string
    ): Promise<bigint | undefined> {
        try {
            // Get transaction details
            const tx = await this.provider.getTransaction(txHash);
            if (!tx) return undefined;

            // For ETH transactions, profit = value received - value sent
            // This is simplified - in practice you'd need before/after balance snapshots
            const ethValue = tx.value || 0n;

            if (ethValue > 0n) {
                logger.debug(`ETH value in transaction: ${ethers.formatEther(ethValue)} ETH`);
                // This is a placeholder - real implementation would compare pre/post balances
                return ethValue;
            }

            return undefined;

        } catch (error) {
            logger.debug(`Error analyzing balance changes: ${(error as Error).message}`);
            return undefined;
        }
    }

    /**
     * Analyze flashloan-specific events for profit calculation
     */
    private analyzeFlashloanEvents(receipt: ethers.TransactionReceipt): bigint | undefined {
        try {
            // Look for flashloan completion events that might indicate profit
            // Different protocols have different event signatures

            // Aave V3 FlashLoan event: FlashLoan(address,address,address,uint256,uint8,uint256,uint16)
            const aaveFlashLoanSig = '0x631042c832b07452973831137f2d73e395028b44b250dedc5abb0ee766e168ac';

            // Balancer FlashLoan event: FlashLoan(address,address,uint256,uint256)
            const balancerFlashLoanSig = '0x0d7d75e01ab95780d3cd1c8ec0dd6c2ce19e3a20427eec8bf53283b6fb8e95f0';

            const flashloanLogs = receipt.logs.filter(log =>
                log.topics[0] === aaveFlashLoanSig || log.topics[0] === balancerFlashLoanSig
            );

            if (flashloanLogs.length > 0) {
                logger.debug(`Found ${flashloanLogs.length} flashloan events`);

                // For flashloans, profit is typically the difference between
                // the amount borrowed and amount repaid (minus fees)
                // This would require parsing the specific event data

                // Placeholder implementation
                return 0n; // Would need protocol-specific parsing
            }

            return undefined;

        } catch (error) {
            logger.debug(`Error analyzing flashloan events: ${(error as Error).message}`);
            return undefined;
        }
    }

    /**
     * Validate flashloan transaction to prevent reverts
     */
    private async validateFlashloanTransaction(
        contractAddress: string,
        data: string
    ): Promise<boolean> {
        try {
            // Check if contract exists
            const code = await this.provider.getCode(contractAddress);
            if (code === '0x') {
                logger.error('❌ Flashloan contract not found at address', { contractAddress });
                return false;
            }

            // Check wallet balance for gas
            const balance = await this.provider.getBalance(this.wallet.address);
            const minGasCost = ethers.parseEther('0.01'); // Minimum 0.01 ETH for gas

            if (balance < minGasCost) {
                logger.error('❌ Insufficient wallet balance for gas', {
                    balance: ethers.formatEther(balance),
                    required: ethers.formatEther(minGasCost)
                });
                return false;
            }

            // Try to use the new profitability check if available
            try {
                const contract = new ethers.Contract(
                    contractAddress,
                    ['function checkProfitability(address,uint256,bytes) external view returns (bool,uint256,uint256)'],
                    this.provider
                );

                // Decode the transaction data to extract parameters
                const iface = new ethers.Interface(['function executeOptimalFlashloan(address,uint256,bytes)']);
                const decoded = iface.parseTransaction({ data });

                if (decoded && decoded.name === 'executeOptimalFlashloan') {
                    const [asset, amount, params] = decoded.args;

                    // Use the advanced profitability check
                    const [profitable, expectedProfit, gasEstimate] = await contract.checkProfitability(
                        asset,
                        amount,
                        params
                    );

                    if (!profitable) {
                        logger.warn('⚠️ Advanced profitability check indicates unprofitable trade', {
                            profitable,
                            expectedProfit: ethers.formatEther(expectedProfit),
                            gasEstimate: ethers.formatUnits(gasEstimate, 'gwei')
                        });
                        return false;
                    }

                    logger.info('✅ Advanced profitability check passed', {
                        profitable,
                        expectedProfit: ethers.formatEther(expectedProfit),
                        gasEstimate: ethers.formatUnits(gasEstimate, 'gwei')
                    });
                    return true;
                }

            } catch (profitabilityError) {
                logger.debug('⚠️ Advanced profitability check not available, falling back to static call', {
                    error: (profitabilityError as Error).message.split('(')[0]
                });
            }

            // Fallback to static call validation
            try {
                await this.provider.call({
                    to: contractAddress,
                    data: data,
                    from: this.wallet.address
                });

                logger.debug('✅ Flashloan transaction validation passed (static call)');
                return true;

            } catch (callError) {
                const errorMessage = (callError as Error).message;
                logger.error('❌ Flashloan transaction would revert', {
                    error: errorMessage,
                    contract: contractAddress,
                    data: data.substring(0, 100) + '...'
                });

                // Check for specific revert reasons
                if (errorMessage.includes('insufficient balance')) {
                    logger.error('💰 Insufficient token balance for flashloan');
                } else if (errorMessage.includes('slippage')) {
                    logger.error('📉 Slippage too high for profitable execution');
                } else if (errorMessage.includes('deadline')) {
                    logger.error('⏰ Transaction deadline exceeded');
                }

                return false;
            }

        } catch (error) {
            logger.error('❌ Error validating flashloan transaction', {
                error: (error as Error).message
            });
            return false;
        }
    }

    /**
     * Execute transaction via Flashbots Protect RPC (Private Transaction Submission)
     */
    private async executeViaFlashbotsProtect(
        transaction: FlashbotsBundleTransaction,
        options: ExecutionOptions
    ): Promise<ExecutionResult> {
        const startTime = Date.now();

        try {
            if (!this.protectProvider.isAvailable()) {
                return { success: false, error: 'Flashbots Protect not available' };
            }

            logger.system('🛡️  Executing via Flashbots Protect RPC...');

            // Extract transaction data
            const txRequest = 'transaction' in transaction ? transaction.transaction : transaction;

            // Convert to Protect transaction format
            const protectTx: FlashbotsProtectTransaction = {
                to: txRequest.to!.toString(),
                data: txRequest.data!.toString(),
                value: txRequest.value?.toString() || '0',
                gasLimit: txRequest.gasLimit?.toString(),
                maxFeePerGas: txRequest.maxFeePerGas?.toString(),
                maxPriorityFeePerGas: txRequest.maxPriorityFeePerGas?.toString()
            };

            // Determine urgency level
            const urgency = options.urgency === 'instant' ? 'urgent' :
                           options.urgency === 'fast' ? 'high' :
                           options.urgency === 'standard' ? 'medium' : 'low';

            logger.system(`   Urgency: ${urgency}`);
            logger.system(`   To: ${protectTx.to}`);
            logger.system(`   Gas Limit: ${protectTx.gasLimit}`);

            // Submit via Protect RPC
            const result = await this.protectProvider.submitMEVProtectedTransaction(
                protectTx,
                { urgency }
            );

            if (!result.success) {
                logger.error('❌ Flashbots Protect submission failed', {
                    error: result.error
                });
                return {
                    success: false,
                    error: result.error || 'Protect submission failed',
                    executionTime: Date.now() - startTime
                };
            }

            logger.success('✅ Transaction submitted via Flashbots Protect');
            logger.system(`   TX Hash: ${result.txHash}`);
            logger.system(`   Fast Mode: ${result.fastMode}`);
            logger.system(`   Max Block Number: ${result.maxBlockNumber}`);
            logger.system('   🛡️  Private submission - no gas fees if failed!');

            return {
                success: true,
                txHash: result.txHash,
                gasUsed: BigInt(0), // Will be updated when transaction is included
                gasPrice: BigInt(0),
                profit: BigInt(0), // Will be calculated when transaction is confirmed
                executionTime: Date.now() - startTime
            };

        } catch (error) {
            logger.logError(error as Error, 'FlashbotsExecutor.executeViaFlashbotsProtect');
            return {
                success: false,
                error: (error as Error).message,
                executionTime: Date.now() - startTime
            };
        }
    }
}
