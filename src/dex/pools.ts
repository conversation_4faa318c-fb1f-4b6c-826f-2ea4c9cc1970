import { ethers } from 'ethers';
import { Pool, Token, LiquidityData } from '../types';
import { config, ADDRESSES, MAINNET_ADDRESSES, SEPOLIA_ADDRESSES } from '../config';
import { logger } from '../utils/logger';

export class PoolManager {
  private provider: ethers.JsonRpcProvider;
  private pools: Map<string, Pool> = new Map();
  private lastUpdate: Map<string, number> = new Map();
  private readonly UPDATE_INTERVAL = 300000; // 5 minutes

  private static readonly UNISWAP_V2_PAIR_ABI = [
    'function getReserves() view returns (uint112 reserve0, uint112 reserve1, uint32 blockTimestampLast)',
    'function token0() view returns (address)',
    'function token1() view returns (address)',
    'function totalSupply() view returns (uint256)'
  ];

  private static readonly UNISWAP_V3_POOL_ABI = [
    'function slot0() view returns (uint160 sqrtPriceX96, int24 tick, uint16 observationIndex, uint16 observationCardinality, uint16 observationCardinalityNext, uint8 feeProtocol, bool unlocked)',
    'function liquidity() view returns (uint128)',
    'function token0() view returns (address)',
    'function token1() view returns (address)',
    'function fee() view returns (uint24)'
  ];

  private static readonly UNISWAP_V2_FACTORY_ABI = [
    'function getPair(address tokenA, address tokenB) view returns (address pair)'
  ];

  private static readonly UNISWAP_V3_FACTORY_ABI = [
    'function getPool(address tokenA, address tokenB, uint24 fee) view returns (address pool)'
  ];

  private static readonly BALANCER_VAULT_ABI = [
    'function getPoolTokens(bytes32 poolId) view returns (address[] tokens, uint256[] balances, uint256 lastChangeBlock)',
    'function getPool(bytes32 poolId) view returns (address, uint8)',
    'function getPools() view returns (bytes32[])'
  ];

  constructor() {
    this.provider = new ethers.JsonRpcProvider(config.rpcUrl);
  }

  /**
   * Check if a protocol is available on the current network
   */
  private isProtocolAvailable(protocol: string): boolean {
    switch (protocol) {
      case 'uniswap-v2':
        return !!ADDRESSES.UNISWAP_V2_FACTORY && ADDRESSES.UNISWAP_V2_FACTORY !== '';
      case 'uniswap-v3':
        return !!ADDRESSES.UNISWAP_V3_FACTORY && ADDRESSES.UNISWAP_V3_FACTORY !== '';
      case 'sushiswap':
        return !!ADDRESSES.SUSHISWAP_ROUTER && ADDRESSES.SUSHISWAP_ROUTER !== '';
      case 'curve':
        return !!ADDRESSES.CURVE_3POOL && ADDRESSES.CURVE_3POOL !== '';
      case 'balancer':
        return !!ADDRESSES.BALANCER_VAULT && ADDRESSES.BALANCER_VAULT !== '';
      default:
        return false;
    }
  }



  async getPool(token0: string, token1: string, protocol: 'uniswap-v2' | 'uniswap-v3' | 'sushiswap' | 'balancer' | 'curve', fee?: number): Promise<Pool | null> {
    const poolKey = this.getPoolKey(token0, token1, protocol, fee);

    // Check if we have cached pool data that's still fresh
    const cachedPool = this.pools.get(poolKey);
    const lastUpdate = this.lastUpdate.get(poolKey) || 0;

    if (cachedPool && Date.now() - lastUpdate < this.UPDATE_INTERVAL) {
      return cachedPool;
    }

    try {
      // Validate input addresses
      if (!ethers.isAddress(token0) || !ethers.isAddress(token1)) {
        logger.debug('Invalid token addresses provided', { token0, token1, protocol });
        return null;
      }

      // Check if protocol is available on current network
      if (!this.isProtocolAvailable(protocol)) {
        logger.debug(`Protocol ${protocol} not available on chain ${config.chainId}`);
        return null;
      }

      let poolAddress: string;

      if (protocol === 'uniswap-v2') {
        poolAddress = await this.getUniswapV2PoolAddress(token0, token1);
      } else if (protocol === 'uniswap-v3') {
        poolAddress = await this.getUniswapV3PoolAddress(token0, token1, fee || 3000);
      } else if (protocol === 'sushiswap') {
        poolAddress = await this.getSushiswapPoolAddress(token0, token1);
      } else if (protocol === 'curve') {
        poolAddress = await this.getCurvePoolAddress(token0, token1);
      } else if (protocol === 'balancer') {
        poolAddress = await this.getBalancerPoolAddress(token0, token1);
      } else {
        logger.debug(`Unknown protocol: ${protocol}`);
        return null;
      }

      if (!poolAddress || poolAddress === ethers.ZeroAddress) {
        logger.debug('Pool not found', { token0, token1, protocol, fee });
        return null;
      }

      const pool = await this.loadPoolData(poolAddress, token0, token1, protocol, fee);

      if (pool) {
        this.pools.set(poolKey, pool);
        this.lastUpdate.set(poolKey, Date.now());
        logger.debug('Pool loaded successfully', {
          poolAddress,
          protocol,
          token0Symbol: pool.token0.symbol,
          token1Symbol: pool.token1.symbol
        });
      }

      return pool;
    } catch (error) {
      logger.logError(error as Error, `PoolManager.getPool(${token0}, ${token1}, ${protocol}, ${fee})`);
      return null;
    }
  }

  private async getUniswapV2PoolAddress(token0: string, token1: string): Promise<string> {
    try {
      if (!ADDRESSES.UNISWAP_V2_FACTORY || ADDRESSES.UNISWAP_V2_FACTORY === '') {
        logger.debug('Uniswap V2 factory address not available on this network');
        return ethers.ZeroAddress;
      }

      const factory = new ethers.Contract(
        ADDRESSES.UNISWAP_V2_FACTORY,
        PoolManager.UNISWAP_V2_FACTORY_ABI,
        this.provider
      );

      const pairAddress = await factory.getPair(token0, token1);

      logger.debug('Uniswap V2 pair lookup', {
        token0,
        token1,
        pairAddress,
        factory: ADDRESSES.UNISWAP_V2_FACTORY
      });

      return pairAddress;
    } catch (error) {
      logger.logError(error as Error, `PoolManager.getUniswapV2PoolAddress(${token0}, ${token1})`);
      return ethers.ZeroAddress;
    }
  }

  private async getUniswapV3PoolAddress(token0: string, token1: string, fee: number): Promise<string> {
    try {
      if (!ADDRESSES.UNISWAP_V3_FACTORY || ADDRESSES.UNISWAP_V3_FACTORY === '') {
        logger.debug('Uniswap V3 factory address not available on this network');
        return ethers.ZeroAddress;
      }

      const factory = new ethers.Contract(
        ADDRESSES.UNISWAP_V3_FACTORY,
        PoolManager.UNISWAP_V3_FACTORY_ABI,
        this.provider
      );

      const poolAddress = await factory.getPool(token0, token1, fee);

      logger.debug('Uniswap V3 pool lookup', {
        token0,
        token1,
        fee,
        poolAddress,
        factory: ADDRESSES.UNISWAP_V3_FACTORY
      });

      return poolAddress;
    } catch (error) {
      logger.logError(error as Error, `PoolManager.getUniswapV3PoolAddress(${token0}, ${token1}, ${fee})`);
      return ethers.ZeroAddress;
    }
  }

  private async getSushiswapPoolAddress(token0: string, token1: string): Promise<string> {
    // SushiSwap uses the same factory interface as Uniswap V2
    if (!ADDRESSES.SUSHISWAP_ROUTER || ADDRESSES.SUSHISWAP_ROUTER === '') {
      return ethers.ZeroAddress; // Not available on this network
    }

    // SushiSwap factory address (same on mainnet, different on other networks)
    const sushiFactoryAddress = config.chainId === 1
      ? '******************************************' // Mainnet
      : ethers.ZeroAddress; // Not available on Sepolia

    if (sushiFactoryAddress === ethers.ZeroAddress) {
      return ethers.ZeroAddress;
    }

    const factory = new ethers.Contract(
      sushiFactoryAddress,
      PoolManager.UNISWAP_V2_FACTORY_ABI, // Same interface as Uniswap V2
      this.provider
    );

    return await factory.getPair(token0, token1);
  }

  private async getCurvePoolAddress(token0: string, token1: string): Promise<string> {
    // For Curve 3pool (USDC/DAI/USDT), we use the hardcoded pool address
    if (!ADDRESSES.CURVE_3POOL || ADDRESSES.CURVE_3POOL === '') {
      return ethers.ZeroAddress; // Not available on this network
    }

    // Check if both tokens are in the 3pool (USDC, DAI, USDT)
    const token0Symbol = await this.getTokenSymbol(token0);
    const token1Symbol = await this.getTokenSymbol(token1);

    const supportedTokens = ['USDC', 'DAI', 'USDT'];

    if (supportedTokens.includes(token0Symbol) && supportedTokens.includes(token1Symbol)) {
      return ADDRESSES.CURVE_3POOL;
    }

    return ethers.ZeroAddress;
  }

  private async getBalancerPoolAddress(token0: string, token1: string): Promise<string> {
    try {
      // For now, use known Balancer pool IDs for common pairs
      // In a full implementation, we'd query the Balancer subgraph or vault
      const knownPools = this.getKnownBalancerPools();

      const poolKey = this.getBalancerPoolKey(token0, token1);
      const poolId = knownPools[poolKey];

      if (!poolId) {
        throw new Error(`No known Balancer pool for ${token0}/${token1}`);
      }

      logger.debug('Found Balancer pool', {
        token0,
        token1,
        poolId,
        poolKey
      });

      return poolId; // For Balancer, we return the pool ID as the "address"
    } catch (error) {
      logger.debug('Error finding Balancer pool', { error: (error as Error).message });
      throw error;
    }
  }

  private getKnownBalancerPools(): Record<string, string> {
    // Known Balancer pool IDs on mainnet
    if (config.chainId === 1) {
      return {
        // WETH pairs
        'usdc-weth': '0x5c6ee304399dbdb9c8ef030ab642b10820db8f56000200000000000000000014', // USDC/WETH 80/20
        'weth-usdc': '0x5c6ee304399dbdb9c8ef030ab642b10820db8f56000200000000000000000014', // USDC/WETH 80/20
        'weth-wbtc': '0xa6f548df93de924d73be7d25dc02554c6bd66db500020000000000000000000e', // WETH/WBTC 50/50
        'wbtc-weth': '0xa6f548df93de924d73be7d25dc02554c6bd66db500020000000000000000000e', // WETH/WBTC 50/50

        // Stablecoin pairs
        'dai-usdc': '0x06df3b2bbb68adc8b0e302443692037ed9f91b42000000000000000000000063', // DAI/USDC
        'usdc-dai': '0x06df3b2bbb68adc8b0e302443692037ed9f91b42000000000000000000000063', // DAI/USDC
        'usdt-usdc': '0x3e5fa9518ea95c3e533eb377c001702a9aacaa32000000000000000000000052', // USDT/USDC
        'usdc-usdt': '0x3e5fa9518ea95c3e533eb377c001702a9aacaa32000000000000000000000052', // USDT/USDC
        'dai-usdt': '0x0b09dea16768f0799065c475be02919503cb2a3500020000000000000000001a', // DAI/USDT
        'usdt-dai': '0x0b09dea16768f0799065c475be02919503cb2a3500020000000000000000001a', // DAI/USDT

        // Additional major pairs
        'weth-dai': '0x0b09dea16768f0799065c475be02919503cb2a3500020000000000000000001a', // WETH/DAI
        'dai-weth': '0x0b09dea16768f0799065c475be02919503cb2a3500020000000000000000001a', // WETH/DAI
        'weth-usdt': '0x3e5fa9518ea95c3e533eb377c001702a9aacaa32000000000000000000000052', // WETH/USDT
        'usdt-weth': '0x3e5fa9518ea95c3e533eb377c001702a9aacaa32000000000000000000000052', // WETH/USDT
      };
    }

    // For testnets, return empty (would need testnet pool IDs)
    return {};
  }

  private getBalancerPoolKey(token0: string, token1: string): string {
    // Create a consistent key for token pairs
    const tokens = [token0.toLowerCase(), token1.toLowerCase()].sort();

    // Map addresses to symbols for known tokens
    const addressToSymbol: Record<string, string> = {};
    if (config.chainId === 1) {
      addressToSymbol[ADDRESSES.USDC.toLowerCase()] = 'usdc';
      addressToSymbol[ADDRESSES.WETH.toLowerCase()] = 'weth';
      addressToSymbol[ADDRESSES.DAI.toLowerCase()] = 'dai';
      addressToSymbol[ADDRESSES.USDT.toLowerCase()] = 'usdt';
      addressToSymbol[ADDRESSES.WBTC.toLowerCase()] = 'wbtc';
    }

    const symbol0 = addressToSymbol[tokens[0]] || tokens[0];
    const symbol1 = addressToSymbol[tokens[1]] || tokens[1];

    return `${symbol0}-${symbol1}`;
  }

  private async getTokenSymbol(tokenAddress: string): Promise<string> {
    try {
      const tokenContract = new ethers.Contract(
        tokenAddress,
        ['function symbol() view returns (string)'],
        this.provider
      );
      return await tokenContract.symbol();
    } catch (error) {
      // Fallback to checking against known addresses
      const knownTokens: Record<string, string> = {
        [MAINNET_ADDRESSES.USDC]: 'USDC',
        [MAINNET_ADDRESSES.DAI]: 'DAI',
        [MAINNET_ADDRESSES.USDT]: 'USDT',
        [SEPOLIA_ADDRESSES.USDC]: 'USDC'
      };
      return knownTokens[tokenAddress] || 'UNKNOWN';
    }
  }

  private async loadPoolData(
    poolAddress: string,
    token0Address: string,
    token1Address: string,
    protocol: 'uniswap-v2' | 'uniswap-v3' | 'sushiswap' | 'balancer' | 'curve',
    fee?: number
  ): Promise<Pool | null> {
    try {
      const token0 = await this.getTokenInfo(token0Address);
      const token1 = await this.getTokenInfo(token1Address);

      if (protocol === 'uniswap-v2') {
        return await this.loadUniswapV2Pool(poolAddress, token0, token1);
      } else if (protocol === 'uniswap-v3') {
        return await this.loadUniswapV3Pool(poolAddress, token0, token1, fee || 3000);
      } else if (protocol === 'sushiswap') {
        return await this.loadSushiswapPool(poolAddress, token0, token1);
      } else if (protocol === 'curve') {
        return await this.loadCurvePool(poolAddress, token0, token1);
      } else {
        return null;
      }
    } catch (error) {
      logger.logError(error as Error, `PoolManager.loadPoolData(${poolAddress})`);
      return null;
    }
  }

  private async loadUniswapV2Pool(poolAddress: string, token0: Token, token1: Token): Promise<Pool> {
    const contract = new ethers.Contract(
      poolAddress,
      PoolManager.UNISWAP_V2_PAIR_ABI,
      this.provider
    );

    // Get the actual token addresses from the pool contract
    const [reserves, actualToken0Address, actualToken1Address] = await Promise.all([
      contract.getReserves(),
      contract.token0(),
      contract.token1()
    ]);

    // Get the correct token info based on actual pool ordering
    const actualToken0 = await this.getTokenInfo(actualToken0Address);
    const actualToken1 = await this.getTokenInfo(actualToken1Address);

    return {
      address: poolAddress,
      token0: actualToken0,
      token1: actualToken1,
      fee: 3000, // 0.3% for Uniswap V2
      protocol: 'uniswap-v2',
      reserves: {
        reserve0: reserves[0],
        reserve1: reserves[1]
      }
    };
  }

  private async loadUniswapV3Pool(poolAddress: string, token0: Token, token1: Token, fee: number): Promise<Pool> {
    try {
      const contract = new ethers.Contract(
        poolAddress,
        PoolManager.UNISWAP_V3_POOL_ABI,
        this.provider
      );

      // Get the actual token addresses from the pool contract
      const [slot0, liquidity, actualToken0Address, actualToken1Address] = await Promise.all([
        contract.slot0(),
        contract.liquidity(),
        contract.token0(),
        contract.token1()
      ]);

      // Validate that we got valid data
      if (!slot0 || liquidity === undefined || !actualToken0Address || !actualToken1Address) {
        throw new Error('Invalid pool data received from contract');
      }

      // Get the correct token info based on actual pool ordering
      const actualToken0 = await this.getTokenInfo(actualToken0Address);
      const actualToken1 = await this.getTokenInfo(actualToken1Address);

      return {
        address: poolAddress,
        token0: actualToken0,
        token1: actualToken1,
        fee,
        protocol: 'uniswap-v3',
        liquidity,
        tick: slot0.tick,
        sqrtPriceX96: slot0.sqrtPriceX96
      };
    } catch (error) {
      logger.logError(error as Error, `PoolManager.loadUniswapV3Pool(${poolAddress})`);
      throw error;
    }
  }

  private async loadSushiswapPool(poolAddress: string, token0: Token, token1: Token): Promise<Pool> {
    // SushiSwap uses the same pool interface as Uniswap V2
    const contract = new ethers.Contract(
      poolAddress,
      PoolManager.UNISWAP_V2_PAIR_ABI,
      this.provider
    );

    // Get the actual token addresses from the pool contract
    const [reserves, actualToken0Address, actualToken1Address] = await Promise.all([
      contract.getReserves(),
      contract.token0(),
      contract.token1()
    ]);

    // Get the correct token info based on actual pool ordering
    const actualToken0 = await this.getTokenInfo(actualToken0Address);
    const actualToken1 = await this.getTokenInfo(actualToken1Address);

    return {
      address: poolAddress,
      token0: actualToken0,
      token1: actualToken1,
      fee: 3000, // 0.3% for SushiSwap (same as Uniswap V2)
      protocol: 'uniswap-v2', // Use same protocol type since interface is identical
      reserves: {
        reserve0: reserves[0],
        reserve1: reserves[1]
      }
    };
  }

  private async loadCurvePool(poolAddress: string, token0: Token, token1: Token): Promise<Pool> {
    // Curve 3pool ABI for basic functions
    const curvePoolAbi = [
      'function balances(uint256) view returns (uint256)',
      'function get_dy(int128 i, int128 j, uint256 dx) view returns (uint256)',
      'function exchange(int128 i, int128 j, uint256 dx, uint256 min_dy) returns (uint256)'
    ];

    const contract = new ethers.Contract(
      poolAddress,
      curvePoolAbi,
      this.provider
    );

    // Get token indices in Curve 3pool
    const tokenIndices = this.getCurveTokenIndices(token0.symbol, token1.symbol);

    if (!tokenIndices) {
      throw new Error(`Unsupported token pair for Curve: ${token0.symbol}/${token1.symbol}`);
    }

    // Get balances for both tokens
    const balance0 = await contract.balances(tokenIndices.i);
    const balance1 = await contract.balances(tokenIndices.j);

    return {
      address: poolAddress,
      token0,
      token1,
      fee: 4, // 0.04% for Curve 3pool
      protocol: 'curve',
      reserves: {
        reserve0: balance0,
        reserve1: balance1
      },
      curveTokenIndices: tokenIndices
    };
  }

  private getCurveTokenIndices(symbol0: string, symbol1: string): { i: number; j: number } | null {
    // Curve 3pool token indices: DAI=0, USDC=1, USDT=2
    const tokenIndices: Record<string, number> = {
      'DAI': 0,
      'USDC': 1,
      'USDT': 2
    };

    const i = tokenIndices[symbol0];
    const j = tokenIndices[symbol1];

    if (i !== undefined && j !== undefined && i !== j) {
      return { i, j };
    }

    return null;
  }

  private async getTokenInfo(address: string): Promise<Token> {
    // This would typically fetch from a cache or contract
    // For now, return basic info based on known addresses
    const addr = address.toLowerCase();

    if (addr === ADDRESSES.WETH.toLowerCase()) {
      return { address: addr, symbol: 'WETH', decimals: 18, name: 'Wrapped Ether' };
    } else if (addr === ADDRESSES.USDC.toLowerCase()) {
      return { address: addr, symbol: 'USDC', decimals: 6, name: 'USD Coin' };
    } else if (addr === ADDRESSES.USDT.toLowerCase()) {
      return { address: addr, symbol: 'USDT', decimals: 6, name: 'Tether USD' };
    } else if (addr === ADDRESSES.DAI.toLowerCase()) {
      return { address: addr, symbol: 'DAI', decimals: 18, name: 'Dai Stablecoin' };
    } else if (addr === ADDRESSES.WBTC.toLowerCase()) {
      return { address: addr, symbol: 'WBTC', decimals: 8, name: 'Wrapped Bitcoin' };
    }

    // For unknown tokens, try to fetch from contract
    try {
      const tokenContract = new ethers.Contract(
        address,
        ['function symbol() view returns (string)', 'function decimals() view returns (uint8)', 'function name() view returns (string)'],
        this.provider
      );

      const [symbol, decimals, name] = await Promise.all([
        tokenContract.symbol(),
        tokenContract.decimals(),
        tokenContract.name()
      ]);

      return { address: addr, symbol, decimals, name };
    } catch (error) {
      return { address: addr, symbol: 'UNKNOWN', decimals: 18, name: 'Unknown Token' };
    }
  }

  calculatePriceImpact(pool: Pool, amountIn: ethers.BigNumberish): number {
    if (pool.protocol === 'uniswap-v2' && pool.reserves) {
      return this.calculateV2PriceImpact(pool, amountIn);
    } else if (pool.protocol === 'uniswap-v3' && pool.liquidity && pool.sqrtPriceX96) {
      return this.calculateV3PriceImpact(pool, amountIn);
    }

    return 0;
  }

  private calculateV2PriceImpact(pool: Pool, amountIn: ethers.BigNumberish): number {
    if (!pool.reserves) return 0;

    const amountInBN = typeof amountIn === 'string' ? ethers.parseUnits(amountIn, 18) : BigInt(amountIn.toString());
    const reserve0 = BigInt(pool.reserves.reserve0.toString());
    const reserve1 = BigInt(pool.reserves.reserve1.toString());

    // Simplified price impact calculation for Uniswap V2
    // Price impact = (amountIn / (reserveIn + amountIn)) * 100
    const priceImpact = Number(amountInBN * BigInt(10000) / (reserve0 + amountInBN)) / 100;

    return Math.min(priceImpact, 100); // Cap at 100%
  }

  private calculateV3PriceImpact(pool: Pool, amountIn: ethers.BigNumberish): number {
    // Simplified V3 price impact calculation
    // In reality, this would use the tick math and liquidity distribution
    const amountInBN = typeof amountIn === 'string' ? ethers.parseUnits(amountIn, 18) : BigInt(amountIn.toString());
    const liquidity = BigInt(pool.liquidity?.toString() || '0');

    if (liquidity === BigInt(0)) return 100;

    // Very simplified calculation
    const priceImpact = Number(amountInBN * BigInt(10000) / liquidity) / 100;

    return Math.min(priceImpact, 100);
  }

  calculateOptimalAmountIn(pool: Pool, targetPriceImpact: number): ethers.BigNumberish {
    if (pool.protocol === 'uniswap-v2' && pool.reserves) {
      // For V2: optimal amount = reserve * targetImpact / (1 - targetImpact)
      const reserve = BigInt(pool.reserves.reserve0.toString());
      const impact = targetPriceImpact / 100;
      return (reserve * BigInt(Math.floor(impact * 10000))) / BigInt(10000 - Math.floor(impact * 10000));
    } else if (pool.protocol === 'uniswap-v3' && pool.liquidity) {
      // Simplified V3 calculation
      const liquidity = BigInt(pool.liquidity.toString());
      const impact = targetPriceImpact / 100;
      return (liquidity * BigInt(Math.floor(impact * 10000))) / BigInt(10000);
    }

    return ethers.parseUnits('0', 18);
  }

  async getLiquidityData(pool: Pool, amountIn: ethers.BigNumberish): Promise<LiquidityData> {
    const priceImpact = this.calculatePriceImpact(pool, amountIn);
    const optimalAmountIn = this.calculateOptimalAmountIn(pool, 1); // 1% price impact

    // Calculate expected amount out (simplified)
    const expectedAmountOut = await this.calculateAmountOut(pool, amountIn);

    return {
      totalLiquidity: BigInt(pool.liquidity?.toString() || pool.reserves?.reserve0?.toString() || '0'),
      priceImpact,
      optimalAmountIn,
      expectedAmountOut
    };
  }

  private async calculateAmountOut(pool: Pool, amountIn: ethers.BigNumberish): Promise<ethers.BigNumberish> {
    // This would use the actual AMM formulas
    // For now, return a simplified calculation
    if (pool.protocol === 'uniswap-v2' && pool.reserves) {
      const amountInBN = typeof amountIn === 'string' ? ethers.parseUnits(amountIn, 18) : BigInt(amountIn.toString());
      const reserve0 = BigInt(pool.reserves.reserve0.toString());
      const reserve1 = BigInt(pool.reserves.reserve1.toString());

      // Uniswap V2 formula: amountOut = (amountIn * 997 * reserve1) / (reserve0 * 1000 + amountIn * 997)
      const amountInWithFee = amountInBN * BigInt(997);
      const numerator = amountInWithFee * reserve1;
      const denominator = reserve0 * BigInt(1000) + amountInWithFee;

      return numerator / denominator;
    }

    // Simplified for V3
    return typeof amountIn === 'string' ? ethers.parseUnits(amountIn, 18) : BigInt(amountIn.toString());
  }

  private getPoolKey(token0: string, token1: string, protocol: string, fee?: number): string {
    const sortedTokens = [token0.toLowerCase(), token1.toLowerCase()].sort();
    return `${protocol}:${sortedTokens[0]}:${sortedTokens[1]}${fee ? `:${fee}` : ''}`;
  }

  clearCache(): void {
    this.pools.clear();
    this.lastUpdate.clear();
    logger.info('Pool cache cleared');
  }

  /**
   * Validate that V2 and V3 pools exist for configured token pairs
   */
  async validateConfiguredPools(): Promise<{ valid: boolean; errors: string[]; warnings: string[] }> {
    const errors: string[] = [];
    const warnings: string[] = [];

    try {
      logger.system('🔍 Validating configured token pairs and pools...');

      // Get configured tokens from config
      const { getConfiguredTokens, getPrimaryFlashloanToken, getTargetTokens } = require('../config');
      const configuredTokens = getConfiguredTokens();
      const primaryToken = getPrimaryFlashloanToken();
      const targetTokens = getTargetTokens();

      if (!primaryToken) {
        errors.push('No primary flashloan token configured');
        return { valid: false, errors, warnings };
      }

      logger.system(`Primary token: ${primaryToken.symbol} (${primaryToken.address})`);
      logger.system(`Target tokens: ${targetTokens.map((t: any) => t.symbol).join(', ')}`);

      // Check each target token pair with primary token
      for (const targetToken of targetTokens) {
        if (targetToken.address === primaryToken.address) continue;

        logger.system(`Checking pools for ${primaryToken.symbol}/${targetToken.symbol}...`);

        // Check Uniswap V2 pool
        const v2Pool = await this.getPool(primaryToken.address, targetToken.address, 'uniswap-v2');
        if (v2Pool) {
          logger.system(`✅ Uniswap V2 pool found: ${v2Pool.address}`);
        } else {
          warnings.push(`Uniswap V2 pool not found for ${primaryToken.symbol}/${targetToken.symbol}`);
        }

        // Check Uniswap V3 pools (multiple fee tiers)
        const v3FeeTiers = [500, 3000, 10000];
        let v3PoolFound = false;

        for (const fee of v3FeeTiers) {
          const v3Pool = await this.getPool(primaryToken.address, targetToken.address, 'uniswap-v3', fee);
          if (v3Pool) {
            logger.system(`✅ Uniswap V3 pool found (${fee/10000}%): ${v3Pool.address}`);
            v3PoolFound = true;
          }
        }

        if (!v3PoolFound) {
          warnings.push(`No Uniswap V3 pools found for ${primaryToken.symbol}/${targetToken.symbol}`);
        }

        // Check Balancer pool if available
        if (this.isProtocolAvailable('balancer')) {
          try {
            const balancerPool = await this.getPool(primaryToken.address, targetToken.address, 'balancer');
            if (balancerPool) {
              logger.info(`✅ Balancer pool found: ${balancerPool.address}`);
            } else {
              warnings.push(`Balancer pool not found for ${primaryToken.symbol}/${targetToken.symbol}`);
            }
          } catch (error) {
            warnings.push(`Balancer pool check failed for ${primaryToken.symbol}/${targetToken.symbol}: ${(error as Error).message}`);
          }
        }
      }

      // Summary
      const hasErrors = errors.length > 0;
      const hasWarnings = warnings.length > 0;

      if (!hasErrors && !hasWarnings) {
        logger.info('✅ All configured token pairs have available pools');
      } else if (!hasErrors) {
        logger.warn(`⚠️  Pool validation completed with ${warnings.length} warnings`);
      } else {
        logger.error(`❌ Pool validation failed with ${errors.length} errors`);
      }

      return { valid: !hasErrors, errors, warnings };

    } catch (error) {
      const errorMsg = `Pool validation failed: ${(error as Error).message}`;
      errors.push(errorMsg);
      logger.error(errorMsg);
      return { valid: false, errors, warnings };
    }
  }

  /**
   * Fetch real-time Balancer liquidity for a token
   */
  async fetchBalancerLiquidity(tokenAddress: string): Promise<{ totalLiquidity: bigint; pools: Array<{ poolId: string; balance: bigint }> }> {
    try {
      if (!this.isProtocolAvailable('balancer')) {
        throw new Error('Balancer not available on this network');
      }

      // Get token info for better logging
      const tokenInfo = await this.getTokenInfo(tokenAddress);

      const vault = new ethers.Contract(
        ADDRESSES.BALANCER_VAULT,
        PoolManager.BALANCER_VAULT_ABI,
        this.provider
      );

      const knownPools = this.getKnownBalancerPools();
      const pools: Array<{ poolId: string; balance: bigint }> = [];
      let totalLiquidity = BigInt(0);

      logger.debug(`Checking Balancer liquidity for ${tokenInfo.symbol} (${tokenAddress})`);
      logger.debug(`Known Balancer pools: ${Object.keys(knownPools).length}`);

      // Check each known pool for the token
      for (const [pairKey, poolId] of Object.entries(knownPools)) {
        try {
          const poolTokens = await vault.getPoolTokens(poolId);
          const tokenIndex = poolTokens.tokens.findIndex((addr: string) =>
            addr.toLowerCase() === tokenAddress.toLowerCase()
          );

          if (tokenIndex >= 0) {
            const balance = BigInt(poolTokens.balances[tokenIndex].toString());
            pools.push({ poolId, balance });
            totalLiquidity += balance;

            logger.debug(`Balancer pool ${pairKey} (${poolId.slice(0, 10)}...) has ${ethers.formatUnits(balance, tokenInfo.decimals)} ${tokenInfo.symbol}`);
          }
        } catch (poolError) {
          logger.debug(`Error checking Balancer pool ${pairKey}:`, (poolError as Error).message);
        }
      }

      // Only log if this is a significant amount or first time checking
      if (totalLiquidity > BigInt(0)) {
        logger.debug(`Total Balancer liquidity for ${tokenInfo.symbol}: ${ethers.formatUnits(totalLiquidity, tokenInfo.decimals)}`);
      } else {
        logger.debug(`No Balancer liquidity found for ${tokenInfo.symbol}`);
      }

      return { totalLiquidity, pools };

    } catch (error) {
      logger.error(`Error fetching Balancer liquidity for ${tokenAddress}:`, (error as Error).message);
      throw error;
    }
  }

  getCacheStats(): { poolCount: number; oldestUpdate: number; newestUpdate: number } {
    const updates = Array.from(this.lastUpdate.values());
    return {
      poolCount: this.pools.size,
      oldestUpdate: updates.length > 0 ? Math.min(...updates) : 0,
      newestUpdate: updates.length > 0 ? Math.max(...updates) : 0
    };
  }
}
