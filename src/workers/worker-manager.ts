import { Worker } from 'worker_threads';
import { EventEmitter } from 'events';
import * as path from 'path';
import * as os from 'os';
import { 
  WorkerTask, 
  WorkerResult, 
  WorkerStats, 
  WorkerPoolConfig, 
  WorkerMessage,
  ArbitrageScanTask,
  ArbitrageScanResult
} from './types';
import { Token } from '../types';
import { logger } from '../utils/logger';

export class WorkerManager extends EventEmitter {
  private workers: Map<number, Worker> = new Map();
  private workerStats: Map<number, WorkerStats> = new Map();
  private taskQueue: WorkerTask[] = [];
  private pendingTasks: Map<string, { resolve: Function; reject: Function; timeout: NodeJS.Timeout }> = new Map();
  private config: WorkerPoolConfig;
  private nextWorkerId = 1;
  private isShuttingDown = false;

  constructor(config?: Partial<WorkerPoolConfig>) {
    super();
    
    this.config = {
      maxWorkers: config?.maxWorkers || Math.min(os.cpus().length, 8),
      taskTimeout: config?.taskTimeout || 30000,
      maxQueueSize: config?.maxQueueSize || 1000,
      workerIdleTimeout: config?.workerIdleTimeout || 300000, // 5 minutes
      enableLoadBalancing: config?.enableLoadBalancing ?? true,
      priorityLevels: config?.priorityLevels || 3
    };

    logger.info(`WorkerManager initialized with ${this.config.maxWorkers} workers`);
  }

  async initialize(): Promise<void> {
    try {
      // Create initial worker pool
      for (let i = 0; i < this.config.maxWorkers; i++) {
        await this.createWorker();
      }

      // Start task processing
      this.startTaskProcessor();

      // Start health monitoring
      this.startHealthMonitoring();

      logger.info(`Worker pool initialized with ${this.workers.size} workers`);
    } catch (error) {
      logger.error('Failed to initialize worker pool:', error);
      throw error;
    }
  }

  async processArbitrageScan(tokenPairs: Array<{ token0: Token; token1: Token }>, minProfitThreshold: number, gasPrice: string): Promise<ArbitrageScanResult['data']> {
    if (this.isShuttingDown) {
      throw new Error('Worker manager is shutting down');
    }

    // Split token pairs across workers for parallel processing
    const chunkSize = Math.ceil(tokenPairs.length / this.workers.size);
    const chunks = this.chunkArray(tokenPairs, chunkSize);
    
    const tasks: ArbitrageScanTask[] = chunks.map((chunk, index) => ({
      id: `arbitrage-scan-${Date.now()}-${index}`,
      type: 'arbitrage-scan',
      data: {
        tokenPairs: chunk.map((pair, pairIndex) => ({
          ...pair,
          pairIndex: index * chunkSize + pairIndex
        })),
        minProfitThreshold,
        gasPrice
      },
      priority: 1,
      timestamp: Date.now()
    }));

    try {
      // Execute all tasks in parallel with explicit worker assignment
      const results = await Promise.all(
        tasks.map((task, index) => {
          // Assign tasks to workers in round-robin fashion
          const workerIds = Array.from(this.workers.keys());
          const assignedWorkerId = workerIds[index % workerIds.length];
          return this.executeTaskOnWorker(task, assignedWorkerId);
        })
      );

      // Combine results from all workers
      const combinedResult: ArbitrageScanResult['data'] = {
        opportunities: [],
        processedPairs: 0,
        profitableCount: 0
      };

      for (const result of results) {
        if (result.success && result.data) {
          combinedResult.opportunities.push(...result.data.opportunities);
          combinedResult.processedPairs += result.data.processedPairs;
          combinedResult.profitableCount += result.data.profitableCount;
        }
      }

      // Sort combined opportunities by profit
      combinedResult.opportunities.sort((a: any, b: any) =>
        Number(BigInt(b.expectedProfit.toString()) - BigInt(a.expectedProfit.toString()))
      );

      // Return top 10 opportunities
      combinedResult.opportunities = combinedResult.opportunities.slice(0, 10);

      logger.debug(`Arbitrage scan completed: ${combinedResult.processedPairs} pairs processed, ${combinedResult.profitableCount} profitable found`);

      return combinedResult;
    } catch (error) {
      logger.error('Error in parallel arbitrage scan:', error);
      throw error;
    }
  }

  private async executeTask(task: WorkerTask): Promise<WorkerResult> {
    return new Promise((resolve, reject) => {
      // Find best available worker
      const workerId = this.selectWorker(task);
      const worker = this.workers.get(workerId);

      if (!worker) {
        reject(new Error('No available workers'));
        return;
      }

      // Set up timeout
      const timeout = setTimeout(() => {
        this.pendingTasks.delete(task.id);
        reject(new Error(`Task ${task.id} timed out after ${this.config.taskTimeout}ms`));
      }, this.config.taskTimeout);

      // Store pending task
      this.pendingTasks.set(task.id, { resolve, reject, timeout });

      // Send task to worker
      worker.postMessage({
        type: 'task',
        data: task,
        taskId: task.id,
        timestamp: Date.now()
      } as WorkerMessage);

      // Update worker stats
      const stats = this.workerStats.get(workerId);
      if (stats) {
        stats.lastTaskTime = Date.now();
      }
    });
  }

  private async executeTaskOnWorker(task: WorkerTask, workerId: number): Promise<WorkerResult> {
    return new Promise((resolve, reject) => {
      const worker = this.workers.get(workerId);

      if (!worker) {
        reject(new Error(`Worker ${workerId} not available`));
        return;
      }

      // Set up timeout
      const timeout = setTimeout(() => {
        this.pendingTasks.delete(task.id);
        reject(new Error(`Task ${task.id} timed out after ${this.config.taskTimeout}ms on worker ${workerId}`));
      }, this.config.taskTimeout);

      // Store pending task
      this.pendingTasks.set(task.id, { resolve, reject, timeout });

      // Send task to specific worker
      worker.postMessage({
        type: 'task',
        data: task,
        taskId: task.id,
        timestamp: Date.now()
      } as WorkerMessage);

      // Update worker stats
      const stats = this.workerStats.get(workerId);
      if (stats) {
        stats.lastTaskTime = Date.now();
      }
    });
  }

  private selectWorker(task: WorkerTask): number {
    const workerIds = Array.from(this.workers.keys());

    if (!this.config.enableLoadBalancing || workerIds.length === 0) {
      // Simple round-robin selection
      return workerIds[0] || 1;
    }

    // Load balancing: select worker with least active tasks
    let bestWorkerId = workerIds[0];
    let minLoad = Infinity;

    for (const workerId of workerIds) {
      const stats = this.workerStats.get(workerId);
      if (stats && stats.isActive) {
        const currentLoad = this.calculateWorkerLoad(workerId);
        if (currentLoad < minLoad) {
          minLoad = currentLoad;
          bestWorkerId = workerId;
        }
      }
    }

    return bestWorkerId;
  }

  private calculateWorkerLoad(workerId: number): number {
    // Calculate current load based on pending tasks for this worker
    let pendingCount = 0;
    for (const [taskId, pending] of this.pendingTasks) {
      // This is a simplified load calculation
      pendingCount++;
    }
    return pendingCount;
  }

  private async createWorker(): Promise<number> {
    const workerId = this.nextWorkerId++;
    // Use .js extension for compiled output
    const workerPath = path.join(__dirname, 'arbitrage-worker.js');

    try {
      const worker = new Worker(workerPath, {
        workerData: { workerId }
      });

      // Set up worker event handlers
      worker.on('message', (message: WorkerMessage) => {
        this.handleWorkerMessage(workerId, message);
      });

      worker.on('error', (error) => {
        logger.error(`Worker ${workerId} error:`, error);
        this.handleWorkerError(workerId, error);
      });

      worker.on('exit', (code) => {
        logger.warn(`Worker ${workerId} exited with code ${code}`);
        this.handleWorkerExit(workerId, code);
      });

      // Store worker and initialize stats
      this.workers.set(workerId, worker);
      this.workerStats.set(workerId, {
        workerId,
        tasksProcessed: 0,
        totalProcessingTime: 0,
        averageProcessingTime: 0,
        errorCount: 0,
        isActive: true,
        lastTaskTime: Date.now()
      });

      logger.debug(`Worker ${workerId} created successfully`);
      return workerId;
    } catch (error) {
      logger.error(`Failed to create worker ${workerId}:`, error);
      throw error;
    }
  }

  private handleWorkerMessage(workerId: number, message: WorkerMessage): void {
    switch (message.type) {
      case 'result':
        this.handleTaskResult(workerId, message);
        break;
      case 'error':
        this.handleTaskError(workerId, message);
        break;
      default:
        logger.debug(`Unknown message type from worker ${workerId}:`, message.type);
    }
  }

  private handleTaskResult(workerId: number, message: WorkerMessage): void {
    if (!message.taskId) return;

    const pending = this.pendingTasks.get(message.taskId);
    if (pending) {
      clearTimeout(pending.timeout);
      this.pendingTasks.delete(message.taskId);

      // Update worker stats
      const stats = this.workerStats.get(workerId);
      if (stats) {
        stats.tasksProcessed++;
        stats.totalProcessingTime += Date.now() - (message.timestamp || Date.now());
        stats.averageProcessingTime = stats.totalProcessingTime / stats.tasksProcessed;
      }

      pending.resolve({
        taskId: message.taskId,
        success: true,
        data: message.data,
        processingTime: Date.now() - (message.timestamp || Date.now()),
        workerId
      });
    }
  }

  private handleTaskError(workerId: number, message: WorkerMessage): void {
    if (!message.taskId) return;

    const pending = this.pendingTasks.get(message.taskId);
    if (pending) {
      clearTimeout(pending.timeout);
      this.pendingTasks.delete(message.taskId);

      // Update worker stats
      const stats = this.workerStats.get(workerId);
      if (stats) {
        stats.errorCount++;
      }

      pending.reject(new Error(message.data?.error || 'Worker task failed'));
    }
  }

  private handleWorkerError(workerId: number, error: Error): void {
    logger.error(`Worker ${workerId} encountered an error:`, error);
    
    // Mark worker as inactive
    const stats = this.workerStats.get(workerId);
    if (stats) {
      stats.isActive = false;
      stats.errorCount++;
    }

    // Restart worker if not shutting down
    if (!this.isShuttingDown) {
      this.restartWorker(workerId);
    }
  }

  private handleWorkerExit(workerId: number, code: number): void {
    logger.warn(`Worker ${workerId} exited with code ${code}`);
    
    // Clean up worker references
    this.workers.delete(workerId);
    
    // Mark worker as inactive
    const stats = this.workerStats.get(workerId);
    if (stats) {
      stats.isActive = false;
    }

    // Restart worker if not shutting down and exit was unexpected
    if (!this.isShuttingDown && code !== 0) {
      this.restartWorker(workerId);
    }
  }

  private async restartWorker(workerId: number): Promise<void> {
    try {
      logger.info(`Restarting worker ${workerId}...`);
      
      // Remove old worker references
      this.workers.delete(workerId);
      
      // Create new worker with same ID
      await this.createWorker();
      
      logger.system(`Worker ${workerId} restarted successfully`);
    } catch (error) {
      logger.error(`Failed to restart worker ${workerId}:`, error);
    }
  }

  private startTaskProcessor(): void {
    // Task processing is handled by executeTask method
    // This could be extended for queue-based processing if needed
  }

  private startHealthMonitoring(): void {
    setInterval(() => {
      this.performHealthCheck();
    }, 60000); // Check every minute
  }

  private performHealthCheck(): void {
    const now = Date.now();
    
    for (const [workerId, stats] of this.workerStats) {
      // Check if worker has been idle too long
      if (now - stats.lastTaskTime > this.config.workerIdleTimeout) {
        logger.debug(`Worker ${workerId} has been idle for ${now - stats.lastTaskTime}ms`);
      }

      // Check if worker is responsive
      const worker = this.workers.get(workerId);
      if (worker && stats.isActive) {
        worker.postMessage({
          type: 'ping',
          timestamp: now
        } as WorkerMessage);
      }
    }
  }

  private chunkArray<T>(array: T[], chunkSize: number): T[][] {
    const chunks: T[][] = [];
    for (let i = 0; i < array.length; i += chunkSize) {
      chunks.push(array.slice(i, i + chunkSize));
    }
    return chunks;
  }

  getWorkerStats(): WorkerStats[] {
    return Array.from(this.workerStats.values());
  }

  async shutdown(): Promise<void> {
    this.isShuttingDown = true;
    
    logger.info('Shutting down worker pool...');

    // Cancel all pending tasks
    for (const [taskId, pending] of this.pendingTasks) {
      clearTimeout(pending.timeout);
      pending.reject(new Error('Worker pool shutting down'));
    }
    this.pendingTasks.clear();

    // Terminate all workers
    const shutdownPromises = Array.from(this.workers.entries()).map(([workerId, worker]) => {
      return new Promise<void>((resolve) => {
        worker.postMessage({ type: 'shutdown', timestamp: Date.now() } as WorkerMessage);
        
        // Force terminate after timeout
        setTimeout(() => {
          worker.terminate();
          resolve();
        }, 5000);
        
        worker.on('exit', () => resolve());
      });
    });

    await Promise.all(shutdownPromises);
    
    this.workers.clear();
    this.workerStats.clear();
    
    logger.info('Worker pool shutdown complete');
  }
}
