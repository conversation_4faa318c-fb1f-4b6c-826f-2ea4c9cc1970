import { ethers } from 'ethers';
import { logger } from './logger';

export interface TransactionStatus {
    hash: string;
    status: 'pending' | 'confirmed' | 'failed' | 'timeout';
    gasUsed?: bigint;
    gasPrice?: bigint;
    blockNumber?: number;
    confirmations?: number;
    error?: string;
}

export interface MonitoringOptions {
    timeout?: number; // milliseconds
    maxConfirmations?: number;
    onStatusChange?: (status: TransactionStatus) => void;
}

/**
 * Non-blocking transaction monitor utility
 * Allows tracking transaction status without blocking execution
 */
export class TransactionMonitor {
    private provider: ethers.JsonRpcProvider;
    private monitoredTransactions: Map<string, {
        options: MonitoringOptions;
        startTime: number;
        timeoutId?: NodeJS.Timeout;
    }> = new Map();

    constructor(provider: ethers.JsonRpcProvider) {
        this.provider = provider;
    }

    /**
     * Start monitoring a transaction without blocking
     */
    public monitorTransaction(
        txHash: string, 
        options: MonitoringOptions = {}
    ): void {
        const defaultOptions: MonitoringOptions = {
            timeout: 300000, // 5 minutes default
            maxConfirmations: 1,
            ...options
        };

        // Store monitoring info
        this.monitoredTransactions.set(txHash, {
            options: defaultOptions,
            startTime: Date.now()
        });

        // Start monitoring
        this.startMonitoring(txHash, defaultOptions);

        logger.debug(`📊 Started monitoring transaction: ${txHash}`);
    }

    /**
     * Get current status of a monitored transaction
     */
    public async getTransactionStatus(txHash: string): Promise<TransactionStatus | null> {
        try {
            const tx = await this.provider.getTransaction(txHash);
            if (!tx) {
                return {
                    hash: txHash,
                    status: 'pending'
                };
            }

            const receipt = await this.provider.getTransactionReceipt(txHash);
            if (!receipt) {
                return {
                    hash: txHash,
                    status: 'pending'
                };
            }

            const currentBlock = await this.provider.getBlockNumber();
            const confirmations = currentBlock - receipt.blockNumber + 1;

            return {
                hash: txHash,
                status: receipt.status === 1 ? 'confirmed' : 'failed',
                gasUsed: receipt.gasUsed,
                gasPrice: receipt.gasPrice || BigInt(0),
                blockNumber: receipt.blockNumber,
                confirmations
            };

        } catch (error) {
            logger.debug(`Error getting transaction status for ${txHash}: ${(error as Error).message}`);
            return {
                hash: txHash,
                status: 'failed',
                error: (error as Error).message
            };
        }
    }

    /**
     * Stop monitoring a specific transaction
     */
    public stopMonitoring(txHash: string): void {
        const monitoring = this.monitoredTransactions.get(txHash);
        if (monitoring?.timeoutId) {
            clearTimeout(monitoring.timeoutId);
        }
        this.monitoredTransactions.delete(txHash);
        logger.debug(`🛑 Stopped monitoring transaction: ${txHash}`);
    }

    /**
     * Stop monitoring all transactions
     */
    public stopAllMonitoring(): void {
        for (const [txHash] of this.monitoredTransactions) {
            this.stopMonitoring(txHash);
        }
        logger.debug('🛑 Stopped monitoring all transactions');
    }

    /**
     * Get list of currently monitored transactions
     */
    public getMonitoredTransactions(): string[] {
        return Array.from(this.monitoredTransactions.keys());
    }

    /**
     * Internal method to start monitoring a transaction
     */
    private async startMonitoring(txHash: string, options: MonitoringOptions): Promise<void> {
        const monitoring = this.monitoredTransactions.get(txHash);
        if (!monitoring) return;

        // Set up timeout
        if (options.timeout) {
            monitoring.timeoutId = setTimeout(() => {
                this.handleTimeout(txHash);
            }, options.timeout);
        }

        // Start polling for status
        this.pollTransactionStatus(txHash, options);
    }

    /**
     * Poll transaction status periodically
     */
    private async pollTransactionStatus(txHash: string, options: MonitoringOptions): Promise<void> {
        const monitoring = this.monitoredTransactions.get(txHash);
        if (!monitoring) return;

        try {
            const status = await this.getTransactionStatus(txHash);
            if (!status) return;

            // Call status change callback if provided
            if (options.onStatusChange) {
                options.onStatusChange(status);
            }

            // Check if monitoring should continue
            if (status.status === 'confirmed' || status.status === 'failed') {
                // Check if we have enough confirmations
                if (status.status === 'confirmed' && 
                    options.maxConfirmations && 
                    status.confirmations && 
                    status.confirmations >= options.maxConfirmations) {
                    
                    logger.debug(`✅ Transaction confirmed with ${status.confirmations} confirmations: ${txHash}`);
                    this.stopMonitoring(txHash);
                    return;
                }

                if (status.status === 'failed') {
                    logger.debug(`❌ Transaction failed: ${txHash}`);
                    this.stopMonitoring(txHash);
                    return;
                }
            }

            // Continue polling if still pending or need more confirmations
            setTimeout(() => {
                this.pollTransactionStatus(txHash, options);
            }, 5000); // Poll every 5 seconds

        } catch (error) {
            logger.debug(`Error polling transaction ${txHash}: ${(error as Error).message}`);
            
            // Continue polling on error (might be temporary network issue)
            setTimeout(() => {
                this.pollTransactionStatus(txHash, options);
            }, 10000); // Wait longer on error
        }
    }

    /**
     * Handle transaction timeout
     */
    private handleTimeout(txHash: string): void {
        const monitoring = this.monitoredTransactions.get(txHash);
        if (!monitoring) return;

        const timeoutStatus: TransactionStatus = {
            hash: txHash,
            status: 'timeout',
            error: `Transaction monitoring timed out after ${monitoring.options.timeout}ms`
        };

        // Call status change callback if provided
        if (monitoring.options.onStatusChange) {
            monitoring.options.onStatusChange(timeoutStatus);
        }

        logger.warn(`⏰ Transaction monitoring timed out: ${txHash}`);
        this.stopMonitoring(txHash);
    }

    /**
     * Get monitoring statistics
     */
    public getMonitoringStats(): {
        totalMonitored: number;
        averageMonitoringTime: number;
        oldestTransaction?: string;
    } {
        const now = Date.now();
        const transactions = Array.from(this.monitoredTransactions.entries());
        
        if (transactions.length === 0) {
            return {
                totalMonitored: 0,
                averageMonitoringTime: 0
            };
        }

        const totalTime = transactions.reduce((sum, [, monitoring]) => {
            return sum + (now - monitoring.startTime);
        }, 0);

        const averageTime = totalTime / transactions.length;
        
        const oldest = transactions.reduce((oldest, [txHash, monitoring]) => {
            if (!oldest || monitoring.startTime < oldest.startTime) {
                return { txHash, startTime: monitoring.startTime };
            }
            return oldest;
        }, null as { txHash: string; startTime: number } | null);

        return {
            totalMonitored: transactions.length,
            averageMonitoringTime: averageTime,
            oldestTransaction: oldest?.txHash
        };
    }
}

// Export a singleton instance
export const transactionMonitor = new TransactionMonitor(
    new ethers.JsonRpcProvider(process.env.RPC_URL || 'http://localhost:8545')
);
