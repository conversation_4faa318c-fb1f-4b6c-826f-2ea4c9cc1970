import { ethers } from 'ethers';
import axios from 'axios';

/**
 * Utility functions for formatting Wei values to readable ETH amounts with USD values
 */

export class WeiFormatter {
  private static ethPrice: number = 0;
  private static lastPriceUpdate: number = 0;
  private static priceUpdateInterval: number = 60000; // 1 minute
  /**
   * Fetch current ETH price from CoinGecko API
   */
  private static async updateEthPrice(): Promise<void> {
    try {
      const now = Date.now();

      // Only update if cache is expired
      if (now - this.lastPriceUpdate < this.priceUpdateInterval) {
        return;
      }

      const response = await axios.get(
        'https://api.coingecko.com/api/v3/simple/price?ids=ethereum&vs_currencies=usd',
        { timeout: 5000 }
      );

      if (response.data?.ethereum?.usd) {
        this.ethPrice = response.data.ethereum.usd;
        this.lastPriceUpdate = now;
      }
    } catch (error) {
      // Silently handle price fetch errors
    }
  }

  /**
   * Get current ETH price (with automatic updates)
   */
  private static async getEthPrice(): Promise<number> {
    await this.updateEthPrice();
    return this.ethPrice;
  }

  /**
   * Convert ETH amount to USD
   */
  private static async ethToUsd(ethAmount: number): Promise<string> {
    const price = await this.getEthPrice();
    if (price === 0) return 'N/A';

    const usdValue = ethAmount * price;
    return usdValue.toLocaleString('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    });
  }

  /**
   * Convert Wei to ETH with specified decimal places
   */
  static weiToEth(wei: string | bigint, decimals: number = 8): string {
    try {
      const weiValue = typeof wei === 'string' ? BigInt(wei) : wei;
      const eth = ethers.formatEther(weiValue);
      return parseFloat(eth).toFixed(decimals);
    } catch (error) {
      return '0.00000000';
    }
  }

  /**
   * Format Wei value with ETH, USD, and Wei display
   */
  static async formatWeiWithBoth(wei: string | bigint): Promise<string> {
    try {
      const weiValue = typeof wei === 'string' ? BigInt(wei) : wei;
      const eth = this.weiToEth(weiValue);
      const ethNum = parseFloat(eth);
      const usd = await this.ethToUsd(ethNum);

      return `${eth} ETH (${usd}) [${weiValue.toString()} wei]`;
    } catch (error) {
      return wei.toString();
    }
  }

  /**
   * Synchronous format Wei value with ETH, USD (cached), and Wei display
   */
  static formatWeiWithBothSync(wei: string | bigint): string {
    try {
      const weiValue = typeof wei === 'string' ? BigInt(wei) : wei;
      const eth = this.weiToEth(weiValue);
      const ethNum = parseFloat(eth);

      // Use cached price if available
      const usdValue = this.ethPrice > 0 ?
        (ethNum * this.ethPrice).toLocaleString('en-US', {
          style: 'currency',
          currency: 'USD',
          minimumFractionDigits: 2,
          maximumFractionDigits: 2
        }) : 'N/A';

      return `${eth} ETH (${usdValue}) [${weiValue.toString()} wei]`;
    } catch (error) {
      return wei.toString();
    }
  }

  /**
   * Parse insufficient funds error and extract Wei values
   */
  static parseInsufficientFundsError(errorMessage: string): {
    have?: string;
    want?: string;
    formatted?: string;
  } | null {
    // Pattern to match: "have XXXXXX want YYYYYY"
    const pattern = /have\s+(\d+)\s+want\s+(\d+)/i;
    const match = errorMessage.match(pattern);
    
    if (match) {
      const have = match[1];
      const want = match[2];
      
      return {
        have,
        want,
        formatted: `Insufficient funds - Have: ${this.formatWeiWithBoth(have)}, Want: ${this.formatWeiWithBoth(want)}`
      };
    }
    
    return null;
  }

  /**
   * Format gas cost calculation
   */
  static formatGasCost(gasLimit: string | bigint, gasPrice: string | bigint): {
    gasLimitFormatted: string;
    gasPriceGwei: string;
    totalCostWei: string;
    totalCostEth: string;
  } {
    try {
      const gasLimitBig = typeof gasLimit === 'string' ? BigInt(gasLimit) : gasLimit;
      const gasPriceBig = typeof gasPrice === 'string' ? BigInt(gasPrice) : gasPrice;
      
      const totalCostWei = gasLimitBig * gasPriceBig;
      
      return {
        gasLimitFormatted: gasLimitBig.toString(),
        gasPriceGwei: ethers.formatUnits(gasPriceBig, 'gwei'),
        totalCostWei: totalCostWei.toString(),
        totalCostEth: this.weiToEth(totalCostWei)
      };
    } catch (error) {
      return {
        gasLimitFormatted: gasLimit.toString(),
        gasPriceGwei: '0',
        totalCostWei: '0',
        totalCostEth: '0.00000000'
      };
    }
  }

  /**
   * Check if a string contains Wei values and format them
   */
  static formatWeiInText(text: string): string {
    // Pattern to match large numbers that could be Wei values (15+ digits)
    const weiPattern = /\b(\d{15,})\b/g;
    
    return text.replace(weiPattern, (match) => {
      try {
        const weiValue = BigInt(match);
        // Only format if it's a reasonable Wei value (> 0.001 ETH)
        if (weiValue > BigInt('1000000000000000')) {
          const eth = this.weiToEth(weiValue);
          return `${eth} ETH (${match} wei)`;
        }
        return match;
      } catch {
        return match;
      }
    });
  }

  /**
   * Format balance comparison (async version)
   */
  static async formatBalanceComparison(available: string | bigint, required: string | bigint): Promise<{
    available: string;
    required: string;
    deficit: string;
    sufficient: boolean;
  }> {
    try {
      const availableBig = typeof available === 'string' ? BigInt(available) : available;
      const requiredBig = typeof required === 'string' ? BigInt(required) : required;

      const sufficient = availableBig >= requiredBig;
      const deficit = sufficient ? BigInt(0) : requiredBig - availableBig;

      return {
        available: await this.formatWeiWithBoth(availableBig),
        required: await this.formatWeiWithBoth(requiredBig),
        deficit: await this.formatWeiWithBoth(deficit),
        sufficient
      };
    } catch (error) {
      return {
        available: available.toString(),
        required: required.toString(),
        deficit: '0',
        sufficient: false
      };
    }
  }

  /**
   * Format balance comparison (sync version)
   */
  static formatBalanceComparisonSync(available: string | bigint, required: string | bigint): {
    available: string;
    required: string;
    deficit: string;
    sufficient: boolean;
  } {
    try {
      const availableBig = typeof available === 'string' ? BigInt(available) : available;
      const requiredBig = typeof required === 'string' ? BigInt(required) : required;

      const sufficient = availableBig >= requiredBig;
      const deficit = sufficient ? BigInt(0) : requiredBig - availableBig;

      return {
        available: this.formatWeiWithBothSync(availableBig),
        required: this.formatWeiWithBothSync(requiredBig),
        deficit: this.formatWeiWithBothSync(deficit),
        sufficient
      };
    } catch (error) {
      return {
        available: available.toString(),
        required: required.toString(),
        deficit: '0',
        sufficient: false
      };
    }
  }
}

/**
 * Convenience functions for common formatting tasks
 */
export const formatWei = WeiFormatter.weiToEth;
export const formatWeiWithBoth = WeiFormatter.formatWeiWithBoth;
export const parseInsufficientFunds = WeiFormatter.parseInsufficientFundsError;
export const formatGasCost = WeiFormatter.formatGasCost;
export const formatWeiInText = WeiFormatter.formatWeiInText;
