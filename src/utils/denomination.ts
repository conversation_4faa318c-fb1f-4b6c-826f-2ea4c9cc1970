import { ethers } from 'ethers';
import { Token } from '../types';
import { ADDRESSES, config } from '../config';
import { PoolManager } from '../dex/pools';
import { PriceCalculator } from '../execution/price-calculator';
import { logger } from '../utils/logger';

export type TokenToWeiDeps = {
  poolManager: PoolManager;
  priceCalculator: PriceCalculator;
  provider: ethers.JsonRpcProvider;
};

function makeDefaultDeps(): TokenToWeiDeps {
  const provider = new ethers.JsonRpcProvider(config.rpcUrl);
  return {
    poolManager: new PoolManager(),
    priceCalculator: new PriceCalculator(provider as any),
    provider,
  };
}

/**
 * Convert an amount of a token to wei using best-available on-chain price vs WETH.
 * - If token is WETH, returns amount unchanged.
 * - Tries Uniswap V3, then V2. Applies a 1% safety haircut.
 * - Returns 0n if pricing cannot be established.
 */
export async function tokenAmountToWei(
  token: Token,
  amount: bigint,
  deps: TokenToWeiDeps = makeDefaultDeps()
): Promise<bigint> {
  try {
    if (token.address.toLowerCase() === ADDRESSES.WETH.toLowerCase()) {
      return amount; // already in wei (18 decimals)
    }

    const oneUnit = 10n ** BigInt(token.decimals);

    // Prefer Uniswap V3 0.3% pool
    const v3Pool = await deps.poolManager.getPool(
      token.address,
      ADDRESSES.WETH,
      'uniswap-v3',
      3000
    );
    if (v3Pool) {
      const out = await deps.priceCalculator.getAmountOut(
        v3Pool,
        token,
        { address: ADDRESSES.WETH, symbol: 'WETH', decimals: 18, name: 'Wrapped Ether' },
        oneUnit
      );
      if (out > 0n) {
        const wei = (amount * out) / oneUnit;
        return (wei * 9900n) / 10000n; // 1% haircut
      }
    }

    // Fallback Uniswap V2 pool using reserves ratio
    const v2Pool = await deps.poolManager.getPool(
      token.address,
      ADDRESSES.WETH,
      'uniswap-v2'
    );
    if (v2Pool && v2Pool.reserves) {
      const isToken0 = token.address.toLowerCase() === v2Pool.token0.address.toLowerCase();
      const reserveToken = BigInt((isToken0 ? v2Pool.reserves.reserve0 : v2Pool.reserves.reserve1).toString());
      const reserveWeth = BigInt((isToken0 ? v2Pool.reserves.reserve1 : v2Pool.reserves.reserve0).toString());
      if (reserveToken > 0n && reserveWeth > 0n) {
        const wei = (amount * reserveWeth) / reserveToken;
        return (wei * 9900n) / 10000n; // 1% haircut
      }
    }

    logger.debug(`tokenAmountToWei: unable to price ${token.symbol}→WETH, returning 0`);
    return 0n;
  } catch (err) {
    logger.debug('tokenAmountToWei error', err);
    return 0n;
  }
}

