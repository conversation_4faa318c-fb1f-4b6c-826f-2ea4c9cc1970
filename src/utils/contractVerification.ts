import { ethers } from 'ethers';
import { config } from '../config';
import { logger } from './logger';

interface ContractInfo {
  name: string;
  address: string;
  required: boolean;
  strategy: string;
  description: string;
}

interface VerificationResult {
  address: string;
  deployed: boolean;
  codeSize: number;
  error?: string;
}

export class ContractVerificationService {
  private provider: ethers.Provider;

  constructor(provider: ethers.Provider) {
    this.provider = provider;
  }

  /**
   * Verify all flashloan contracts at bot startup
   */
  async verifyAllContracts(): Promise<void> {
    logger.separator();
    logger.system('🔍 VERIFYING FLASHLOAN CONTRACT DEPLOYMENTS');
    logger.separator();

    const contracts = this.getContractList();
    const results: Map<string, VerificationResult> = new Map();

    // Verify each contract
    for (const contract of contracts) {
      const result = await this.verifyContract(contract.address);
      results.set(contract.name, result);
    }

    // Display results
    this.displayVerificationResults(contracts, results);
    
    // Check if we have enough contracts for dynamic flashloan attacks
    this.analyzeFlashloanCapabilities(contracts, results);
  }

  /**
   * Get list of all flashloan-related contracts
   */
  private getContractList(): ContractInfo[] {
    return [
      // Balancer Flashloan Strategy
      {
        name: 'Balancer Flashloan Contract',
        address: config.balancerFlashloanContract || '',
        required: true,
        strategy: 'Balancer (0% fees)',
        description: 'Custom contract for Balancer V2 flashloan arbitrage'
      },
      
      // Aave Flashloan Strategy  
      {
        name: 'Aave Flashloan Contract',
        address: config.aaveFlashloanContract || '',
        required: true,
        strategy: 'Aave (0.09% fees)',
        description: 'Custom contract for Aave V3 flashloan arbitrage'
      },

      // Hybrid Strategy
      {
        name: 'Hybrid Flashloan Contract',
        address: config.hybridFlashloanContract || '',
        required: false,
        strategy: 'Hybrid (Aave + Balancer)',
        description: 'Combined contract supporting both Aave and Balancer'
      },

      // Core Protocol Contracts (should always be available)
      {
        name: 'Balancer V2 Vault',
        address: config.balancerVaultAddress || '0xBA12222222228d8Ba445958a75a0704d566BF2C8',
        required: true,
        strategy: 'Core Protocol',
        description: 'Official Balancer V2 Vault (universal address)'
      },

      {
        name: 'Aave V3 Pool',
        address: config.aavePoolAddress || '******************************************',
        required: true,
        strategy: 'Core Protocol', 
        description: 'Official Aave V3 Pool contract'
      }
    ];
  }

  /**
   * Verify if a contract is deployed at the given address
   */
  private async verifyContract(address: string): Promise<VerificationResult> {
    try {
      if (!address || !ethers.isAddress(address)) {
        return {
          address: address || 'Not configured',
          deployed: false,
          codeSize: 0,
          error: 'Invalid or missing address'
        };
      }

      const code = await this.provider.getCode(address);
      const deployed = code !== '0x';
      
      return {
        address,
        deployed,
        codeSize: code.length,
        error: deployed ? undefined : 'No contract code found'
      };

    } catch (error) {
      return {
        address,
        deployed: false,
        codeSize: 0,
        error: `Verification failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  /**
   * Display verification results in a formatted table
   */
  private displayVerificationResults(contracts: ContractInfo[], results: Map<string, VerificationResult>): void {
    logger.system('📊 CONTRACT VERIFICATION RESULTS:');
    logger.system('');

    for (const contract of contracts) {
      const result = results.get(contract.name);
      if (!result) continue;

      const status = result.deployed ? '✅ DEPLOYED' : '❌ NOT FOUND';
      const statusColor = result.deployed ? 'success' : 'error';
      
      logger.system(`${status} ${contract.name}`);
      logger.system(`   Strategy: ${contract.strategy}`);
      logger.system(`   Address: ${result.address}`);
      
      if (result.deployed) {
        logger.system(`   Code Size: ${result.codeSize} bytes`);
        logger.system(`   Description: ${contract.description}`);
      } else {
        logger.system(`   Error: ${result.error}`);
        if (contract.required) {
          logger.system(`   ⚠️  This contract is REQUIRED for ${contract.strategy} strategy`);
        }
      }
      logger.system('');
    }
  }

  /**
   * Analyze what flashloan capabilities are available
   */
  private analyzeFlashloanCapabilities(contracts: ContractInfo[], results: Map<string, VerificationResult>): void {
    logger.separator();
    logger.system('🚀 DYNAMIC FLASHLOAN ATTACK CAPABILITIES:');
    logger.separator();

    const balancerContract = results.get('Balancer Flashloan Contract');
    const aaveContract = results.get('Aave Flashloan Contract');
    const hybridContract = results.get('Hybrid Flashloan Contract');
    const balancerVault = results.get('Balancer V2 Vault');
    const aavePool = results.get('Aave V3 Pool');

    // Check individual strategies
    const capabilities: string[] = [];
    
    if (balancerContract?.deployed && balancerVault?.deployed) {
      capabilities.push('✅ Balancer Flashloan (0% fees) - READY');
      logger.system('🔵 Balancer Strategy: OPERATIONAL');
      logger.system('   💰 Fee: 0% (maximum profit retention)');
      logger.system('   🏦 Liquidity: Access to Balancer V2 pools');
      logger.system('   ⚡ Gas: Lower than Aave');
    } else {
      logger.system('❌ Balancer Strategy: NOT AVAILABLE');
      if (!balancerContract?.deployed) {
        logger.system('   Missing: Custom Balancer flashloan contract');
        logger.system('   Deploy: npx hardhat run scripts/deploy-balancer-flashloan.js');
      }
    }

    if (aaveContract?.deployed && aavePool?.deployed) {
      capabilities.push('✅ Aave Flashloan (0.09% fees) - READY');
      logger.system('🏦 Aave Strategy: OPERATIONAL');
      logger.system('   💰 Fee: 0.09% (deducted from profit)');
      logger.system('   🏦 Liquidity: Access to Aave V3 pools');
      logger.system('   🔒 Reliability: Battle-tested protocol');
    } else {
      logger.system('❌ Aave Strategy: NOT AVAILABLE');
      if (!aaveContract?.deployed) {
        logger.system('   Missing: Custom Aave flashloan contract');
        logger.system('   Deploy: npx hardhat run scripts/deploy-aave-flashloan.js');
      }
    }

    if (hybridContract?.deployed) {
      capabilities.push('✅ Hybrid Strategy (Dynamic Selection) - READY');
      logger.system('🔄 Hybrid Strategy: OPERATIONAL');
      logger.system('   🧠 Auto-selects optimal provider (Aave vs Balancer)');
      logger.system('   💡 Maximizes profit by choosing best fees/liquidity');
    } else {
      logger.system('⚠️  Hybrid Strategy: NOT AVAILABLE');
      logger.system('   Missing: Hybrid flashloan contract');
      logger.system('   Deploy: npx hardhat run scripts/deploy-hybrid-flashloan.js');
    }

    // Uniswap V3 Flash Swaps (always available if on supported network)
    if (config.chainId === 1 || config.chainId === 11155111) {
      capabilities.push('✅ Uniswap V3 Flash Swaps - READY');
      logger.system('🦄 Uniswap V3 Flash Swaps: OPERATIONAL');
      logger.system('   💰 Fee: 0.05% - 1% (depends on pool)');
      logger.system('   🏦 Liquidity: Access to Uniswap V3 pools');
      logger.system('   ⚡ No custom contract needed');
    }

    logger.separator();
    
    // Summary
    const readyStrategies = capabilities.length;
    if (readyStrategies === 0) {
      logger.system('❌ NO FLASHLOAN STRATEGIES AVAILABLE');
      logger.system('   Deploy contracts to enable dynamic flashloan attacks');
    } else if (readyStrategies === 1) {
      logger.system(`✅ ${readyStrategies} FLASHLOAN STRATEGY READY`);
      logger.system('   Limited to single strategy - deploy more for optimization');
    } else {
      logger.system(`🚀 ${readyStrategies} FLASHLOAN STRATEGIES READY`);
      logger.system('   Dynamic strategy selection ENABLED');
      logger.system('   Bot will auto-select optimal flashloan provider');
    }

    logger.separator();
  }
}
