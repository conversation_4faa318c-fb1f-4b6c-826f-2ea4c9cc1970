import { ethers } from 'ethers';
import { logger } from './logger';
import { config } from '../config';

/**
 * Bundle inclusion analysis and optimization recommendations
 */
export class BundleAnalyzer {
    private inclusionHistory: Array<{
        timestamp: number;
        targetBlock: number;
        included: boolean;
        priorityFee: bigint;
        baseFee: bigint;
        error?: string;
    }> = [];

    private readonly MAX_HISTORY = 100;

    /**
     * Record bundle submission result
     */
    recordBundleSubmission(
        targetBlock: number,
        included: boolean,
        priorityFee: bigint,
        baseFee: bigint,
        error?: string
    ): void {
        this.inclusionHistory.push({
            timestamp: Date.now(),
            targetBlock,
            included,
            priorityFee,
            baseFee,
            error
        });

        // Keep only recent history
        if (this.inclusionHistory.length > this.MAX_HISTORY) {
            this.inclusionHistory.shift();
        }
    }

    /**
     * Get bundle inclusion statistics
     */
    getInclusionStats(): {
        totalSubmissions: number;
        successfulInclusions: number;
        inclusionRate: number;
        averagePriorityFee: string;
        averageBaseFee: string;
        recommendations: string[];
    } {
        if (this.inclusionHistory.length === 0) {
            return {
                totalSubmissions: 0,
                successfulInclusions: 0,
                inclusionRate: 0,
                averagePriorityFee: '0',
                averageBaseFee: '0',
                recommendations: ['No bundle submission history available']
            };
        }

        const totalSubmissions = this.inclusionHistory.length;
        const successfulInclusions = this.inclusionHistory.filter(h => h.included).length;
        const inclusionRate = (successfulInclusions / totalSubmissions) * 100;

        // Calculate averages
        const totalPriorityFee = this.inclusionHistory.reduce((sum, h) => sum + h.priorityFee, BigInt(0));
        const totalBaseFee = this.inclusionHistory.reduce((sum, h) => sum + h.baseFee, BigInt(0));
        
        const averagePriorityFee = ethers.formatUnits(totalPriorityFee / BigInt(totalSubmissions), 'gwei');
        const averageBaseFee = ethers.formatUnits(totalBaseFee / BigInt(totalSubmissions), 'gwei');

        const recommendations = this.generateRecommendations(inclusionRate);

        return {
            totalSubmissions,
            successfulInclusions,
            inclusionRate,
            averagePriorityFee,
            averageBaseFee,
            recommendations
        };
    }

    /**
     * Generate optimization recommendations based on inclusion rate
     */
    private generateRecommendations(inclusionRate: number): string[] {
        const recommendations: string[] = [];

        if (inclusionRate < 30) {
            recommendations.push('🚨 CRITICAL: Very low inclusion rate (<30%)');
            recommendations.push('• Increase MAX_PRIORITY_FEE_GWEI to 1000+ gwei');
            recommendations.push('• Set GAS_URGENCY=instant');
            recommendations.push('• Enable BUNDLE_SUBMISSION_STRATEGY=aggressive');
            recommendations.push('• Consider increasing MAX_BLOCKS_AHEAD to 7');
        } else if (inclusionRate < 50) {
            recommendations.push('⚠️  LOW: Poor inclusion rate (<50%)');
            recommendations.push('• Increase MAX_PRIORITY_FEE_GWEI to 500+ gwei');
            recommendations.push('• Set BUNDLE_SUBMISSION_STRATEGY=aggressive');
            recommendations.push('• Enable ENABLE_BUNDLE_MULTIPLEXING=true');
        } else if (inclusionRate < 70) {
            recommendations.push('📈 MODERATE: Acceptable inclusion rate (50-70%)');
            recommendations.push('• Consider increasing priority fees during high congestion');
            recommendations.push('• Monitor network conditions and adjust accordingly');
        } else if (inclusionRate < 85) {
            recommendations.push('✅ GOOD: Good inclusion rate (70-85%)');
            recommendations.push('• Current settings are working well');
            recommendations.push('• Fine-tune based on profit vs gas cost analysis');
        } else {
            recommendations.push('🎯 EXCELLENT: High inclusion rate (>85%)');
            recommendations.push('• Consider optimizing for cost efficiency');
            recommendations.push('• You may be able to reduce priority fees slightly');
        }

        // Add general recommendations
        recommendations.push('');
        recommendations.push('💡 General Tips:');
        recommendations.push('• Submit bundles earlier in block cycle');
        recommendations.push('• Monitor mempool congestion');
        recommendations.push('• Use multi-block submission strategy');
        recommendations.push('• Ensure profitable opportunities justify gas costs');

        return recommendations;
    }

    /**
     * Analyze recent failures and suggest improvements
     */
    analyzeRecentFailures(): {
        recentFailures: number;
        commonErrors: string[];
        suggestedActions: string[];
    } {
        const recentHistory = this.inclusionHistory.slice(-20); // Last 20 submissions
        const recentFailures = recentHistory.filter(h => !h.included).length;
        
        // Count error types
        const errorCounts: Record<string, number> = {};
        recentHistory.filter(h => !h.included && h.error).forEach(h => {
            const error = h.error!;
            errorCounts[error] = (errorCounts[error] || 0) + 1;
        });

        const commonErrors = Object.entries(errorCounts)
            .sort(([,a], [,b]) => b - a)
            .slice(0, 3)
            .map(([error, count]) => `${error} (${count} times)`);

        const suggestedActions: string[] = [];

        if (recentFailures > 15) {
            suggestedActions.push('🚨 High failure rate detected');
            suggestedActions.push('• Immediately increase priority fees');
            suggestedActions.push('• Check network congestion');
            suggestedActions.push('• Consider pausing until conditions improve');
        } else if (recentFailures > 10) {
            suggestedActions.push('⚠️  Elevated failure rate');
            suggestedActions.push('• Increase priority fees by 50%');
            suggestedActions.push('• Enable aggressive submission strategy');
        } else if (recentFailures > 5) {
            suggestedActions.push('📊 Some recent failures detected');
            suggestedActions.push('• Monitor next few submissions');
            suggestedActions.push('• Consider slight priority fee increase');
        } else {
            suggestedActions.push('✅ Recent performance looks good');
        }

        return {
            recentFailures,
            commonErrors,
            suggestedActions
        };
    }

    /**
     * Get optimal priority fee recommendation based on recent data
     */
    getOptimalPriorityFeeRecommendation(): {
        recommendedMinGwei: number;
        recommendedMaxGwei: number;
        reasoning: string;
    } {
        const stats = this.getInclusionStats();
        const currentAvgPriority = parseFloat(stats.averagePriorityFee);
        
        let recommendedMinGwei: number;
        let recommendedMaxGwei: number;
        let reasoning: string;

        if (stats.inclusionRate < 30) {
            recommendedMinGwei = Math.max(100, currentAvgPriority * 3);
            recommendedMaxGwei = 1000;
            reasoning = 'Very low inclusion rate requires aggressive priority fees';
        } else if (stats.inclusionRate < 50) {
            recommendedMinGwei = Math.max(50, currentAvgPriority * 2);
            recommendedMaxGwei = 500;
            reasoning = 'Low inclusion rate needs higher priority fees';
        } else if (stats.inclusionRate < 70) {
            recommendedMinGwei = Math.max(25, currentAvgPriority * 1.5);
            recommendedMaxGwei = 300;
            reasoning = 'Moderate inclusion rate, slight increase recommended';
        } else if (stats.inclusionRate < 85) {
            recommendedMinGwei = Math.max(10, currentAvgPriority);
            recommendedMaxGwei = 200;
            reasoning = 'Good inclusion rate, maintain current levels';
        } else {
            recommendedMinGwei = Math.max(5, currentAvgPriority * 0.8);
            recommendedMaxGwei = 100;
            reasoning = 'Excellent inclusion rate, can optimize for cost';
        }

        return {
            recommendedMinGwei: Math.round(recommendedMinGwei),
            recommendedMaxGwei: Math.round(recommendedMaxGwei),
            reasoning
        };
    }

    /**
     * Print comprehensive bundle analysis report
     */
    printAnalysisReport(): void {
        const stats = this.getInclusionStats();
        const failures = this.analyzeRecentFailures();
        const feeRecommendation = this.getOptimalPriorityFeeRecommendation();

        logger.system('');
        logger.system('📊 BUNDLE INCLUSION ANALYSIS REPORT');
        logger.system('=====================================');
        logger.system(`Total Submissions: ${stats.totalSubmissions}`);
        logger.system(`Successful Inclusions: ${stats.successfulInclusions}`);
        logger.system(`Inclusion Rate: ${stats.inclusionRate.toFixed(1)}%`);
        logger.system(`Average Priority Fee: ${stats.averagePriorityFee} gwei`);
        logger.system(`Average Base Fee: ${stats.averageBaseFee} gwei`);
        logger.system('');

        logger.system('🔍 RECENT FAILURES ANALYSIS');
        logger.system(`Recent Failures (last 20): ${failures.recentFailures}`);
        if (failures.commonErrors.length > 0) {
            logger.system('Common Errors:');
            failures.commonErrors.forEach(error => logger.system(`  • ${error}`));
        }
        logger.system('');

        logger.system('💰 PRIORITY FEE RECOMMENDATIONS');
        logger.system(`Recommended Min: ${feeRecommendation.recommendedMinGwei} gwei`);
        logger.system(`Recommended Max: ${feeRecommendation.recommendedMaxGwei} gwei`);
        logger.system(`Reasoning: ${feeRecommendation.reasoning}`);
        logger.system('');

        logger.system('📋 OPTIMIZATION RECOMMENDATIONS');
        stats.recommendations.forEach(rec => logger.system(rec));
        logger.system('');

        logger.system('🎯 IMMEDIATE ACTIONS');
        failures.suggestedActions.forEach(action => logger.system(action));
        logger.system('');
    }
}

// Export singleton instance
export const bundleAnalyzer = new BundleAnalyzer();
