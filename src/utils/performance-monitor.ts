import * as os from 'os';
import { performance } from 'perf_hooks';
import { logger } from './logger';

export interface PerformanceMetrics {
  cpu: {
    usage: number;
    loadAverage: number[];
  };
  memory: {
    used: number;
    total: number;
    percentage: number;
    heapUsed: number;
    heapTotal: number;
  };
  eventLoop: {
    lag: number;
  };
  workers?: {
    active: number;
    total: number;
    tasksProcessed: number;
    averageProcessingTime: number;
  };
  arbitrage?: {
    scanTime: number;
    pairsProcessed: number;
    opportunitiesFound: number;
    useWorkers: boolean;
  };
}

export class PerformanceMonitor {
  private metrics: PerformanceMetrics[] = [];
  private maxHistorySize = 100;
  private monitoringInterval?: NodeJS.Timeout;
  private lastCpuUsage = process.cpuUsage();
  private lastEventLoopCheck = performance.now();

  start(intervalMs: number = 2000): void {
    this.monitoringInterval = setInterval(() => {
      this.collectMetrics();
    }, intervalMs);

    logger.system(`Performance monitoring started (interval: ${intervalMs}ms)`);
  }

  stop(): void {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = undefined;
    }
    logger.info('Performance monitoring stopped');
  }

  private collectMetrics(): void {
    const now = performance.now();
    
    // CPU metrics
    const currentCpuUsage = process.cpuUsage(this.lastCpuUsage);
    const cpuPercent = (currentCpuUsage.user + currentCpuUsage.system) / 1000 / (now - this.lastEventLoopCheck) * 100;
    this.lastCpuUsage = process.cpuUsage();

    // Memory metrics
    const memUsage = process.memoryUsage();
    const totalMem = os.totalmem();
    const freeMem = os.freemem();
    const usedMem = totalMem - freeMem;

    // Validate memory values to prevent incorrect readings
    const memPercentage = Math.min((usedMem / totalMem) * 100, 100);
    const heapUsedMB = memUsage.heapUsed / 1024 / 1024;
    const heapTotalMB = memUsage.heapTotal / 1024 / 1024;

    // Event loop lag
    const eventLoopLag = this.measureEventLoopLag();

    const metrics: PerformanceMetrics = {
      cpu: {
        usage: Math.min(cpuPercent, 100),
        loadAverage: os.loadavg()
      },
      memory: {
        used: usedMem,
        total: totalMem,
        percentage: memPercentage,
        heapUsed: memUsage.heapUsed,
        heapTotal: memUsage.heapTotal
      },
      eventLoop: {
        lag: eventLoopLag
      }
    };

    this.addMetrics(metrics);
    this.lastEventLoopCheck = now;
  }

  private measureEventLoopLag(): number {
    // Simplified synchronous event loop lag measurement
    const start = performance.now();
    let lag = 0;
    setImmediate(() => {
      lag = performance.now() - start;
    });
    return lag || 0; // Return 0 if measurement not ready
  }

  addMetrics(metrics: PerformanceMetrics): void {
    this.metrics.push(metrics);
    
    // Keep only recent metrics
    if (this.metrics.length > this.maxHistorySize) {
      this.metrics = this.metrics.slice(-this.maxHistorySize);
    }
  }

  getLatestMetrics(): PerformanceMetrics | null {
    return this.metrics.length > 0 ? this.metrics[this.metrics.length - 1] : null;
  }

  getAverageMetrics(samples: number = 10): PerformanceMetrics | null {
    if (this.metrics.length === 0) return null;

    const recentMetrics = this.metrics.slice(-samples);
    const count = recentMetrics.length;

    return {
      cpu: {
        usage: recentMetrics.reduce((sum, m) => sum + m.cpu.usage, 0) / count,
        loadAverage: recentMetrics[recentMetrics.length - 1].cpu.loadAverage // Use latest
      },
      memory: {
        used: recentMetrics.reduce((sum, m) => sum + m.memory.used, 0) / count,
        total: recentMetrics[recentMetrics.length - 1].memory.total, // Use latest
        percentage: recentMetrics.reduce((sum, m) => sum + m.memory.percentage, 0) / count,
        heapUsed: recentMetrics.reduce((sum, m) => sum + m.memory.heapUsed, 0) / count,
        heapTotal: recentMetrics.reduce((sum, m) => sum + m.memory.heapTotal, 0) / count
      },
      eventLoop: {
        lag: recentMetrics.reduce((sum, m) => sum + m.eventLoop.lag, 0) / count
      }
    };
  }

  updateWorkerMetrics(workerStats: any[]): void {
    const latest = this.getLatestMetrics();
    if (latest) {
      latest.workers = {
        active: workerStats.filter(w => w.isActive).length,
        total: workerStats.length,
        tasksProcessed: workerStats.reduce((sum, w) => sum + w.tasksProcessed, 0),
        averageProcessingTime: workerStats.reduce((sum, w) => sum + w.averageProcessingTime, 0) / workerStats.length
      };
    }
  }

  updateArbitrageMetrics(scanTime: number, pairsProcessed: number, opportunitiesFound: number, useWorkers: boolean): void {
    const latest = this.getLatestMetrics();
    if (latest) {
      latest.arbitrage = {
        scanTime,
        pairsProcessed,
        opportunitiesFound,
        useWorkers
      };
    }
  }

  getPerformanceReport(): string {
    const latest = this.getLatestMetrics();
    const average = this.getAverageMetrics();

    if (!latest || !average) {
      return 'No performance data available';
    }

    const report = [
      '📊 Performance Report',
      '==================',
      '',
      '🖥️  System Resources:',
      `   CPU Usage: ${latest.cpu.usage.toFixed(1)}% (avg: ${average.cpu.usage.toFixed(1)}%)`,
      `   Memory: ${(latest.memory.percentage).toFixed(1)}% (${(latest.memory.used / 1024 / 1024 / 1024).toFixed(2)} GB)`,
      `   Heap: ${(latest.memory.heapUsed / 1024 / 1024).toFixed(1)} MB / ${(latest.memory.heapTotal / 1024 / 1024).toFixed(1)} MB`,
      `   Event Loop Lag: ${latest.eventLoop.lag.toFixed(2)}ms`,
      ''
    ];

    if (latest.workers) {
      report.push(
        '⚡ Worker Pool:',
        `   Active Workers: ${latest.workers.active}/${latest.workers.total}`,
        `   Tasks Processed: ${latest.workers.tasksProcessed}`,
        `   Avg Processing Time: ${latest.workers.averageProcessingTime.toFixed(2)}ms`,
        ''
      );
    }

    if (latest.arbitrage) {
      report.push(
        '🔍 Arbitrage Performance:',
        `   Scan Mode: ${latest.arbitrage.useWorkers ? 'Multi-threaded' : 'Single-threaded'}`,
        `   Last Scan Time: ${latest.arbitrage.scanTime}ms`,
        `   Pairs Processed: ${latest.arbitrage.pairsProcessed}`,
        `   Opportunities Found: ${latest.arbitrage.opportunitiesFound}`,
        `   Avg Time per Pair: ${(latest.arbitrage.scanTime / latest.arbitrage.pairsProcessed).toFixed(2)}ms`,
        ''
      );
    }

    return report.join('\n');
  }

  getHealthStatus(): 'healthy' | 'warning' | 'critical' {
    const latest = this.getLatestMetrics();
    if (!latest) return 'warning';

    if (latest.cpu.usage > 90 || latest.memory.percentage > 95 || latest.eventLoop.lag > 1000) {
      return 'critical';
    }

    if (latest.cpu.usage > 70 || latest.memory.percentage > 80 || latest.eventLoop.lag > 100) {
      return 'warning';
    }

    return 'healthy';
  }

  logPerformanceWarnings(): void {
    const latest = this.getLatestMetrics();
    if (!latest) return;

    if (latest.cpu.usage > 80) {
      logger.warn(`High CPU usage detected: ${latest.cpu.usage.toFixed(1)}%`);
    }

    if (latest.memory.percentage > 85) {
      logger.warn(`High memory usage detected: ${latest.memory.percentage.toFixed(1)}%`);
    }

    if (latest.eventLoop.lag > 100) {
      logger.warn(`High event loop lag detected: ${latest.eventLoop.lag.toFixed(2)}ms`);
    }
  }

  clear(): void {
    this.metrics = [];
  }
}

// Global performance monitor instance
export const performanceMonitor = new PerformanceMonitor();
