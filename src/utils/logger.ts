import winston from 'winston';
import chalk from 'chalk';
import {ethers} from 'ethers';
import {config} from '../config';
import axios from 'axios';
import {LogEntry, LogLevel} from '../types';

// Global BigInt serialization fix
(BigInt.prototype as any).toJSON = function() {
  return this.toString() + 'n';
};

// Lazy import for web dashboard to avoid circular dependencies
let webDashboard: any = null;
async function getWebDashboard() {
  if (!webDashboard && process.env.WEB_DASHBOARD === 'true') {
    try {
      const module = await import('../server/webDashboard');
      webDashboard = module.webDashboard;
    } catch (error) {
      // Silently handle import errors
    }
  }
  return webDashboard;
}

class Logger {
  private winston: winston.Logger;
  private logEntries: LogEntry[] = [];
  private startTime: number;
  private ethPrice: number = 0;
  private lastPriceUpdate: number = 0;
  private priceUpdateInterval: number = 60000; // 1 minute

  constructor() {
    this.startTime = Date.now();
    const transports: winston.transport[] = [];

    // Initialize ETH price on startup
    this.updateEthPrice().catch(() => {
      // Silently handle price fetch errors on startup
    });

    // Add console transport with enhanced formatting
    transports.push(
      new winston.transports.Console({
        format: winston.format.combine(
          winston.format.colorize(),
          winston.format.timestamp(),
          winston.format.printf(({ timestamp, level, message, ...meta }) => {
            const metaStr = Object.keys(meta).length ? JSON.stringify(meta, (key, value) => {
              if (typeof value === 'bigint') {
                return value.toString() + 'n';
              }
              return value;
            }, 2) : '';
            return `${timestamp} [${level}]: ${message} ${metaStr}`;
          })
        )
      })
    );

    if (config.logToFile) {
      transports.push(
        new winston.transports.File({
          filename: 'logs/error.log',
          level: 'error',
          format: winston.format.combine(
            winston.format.timestamp(),
            winston.format.json({
              replacer: (key, value) => {
                if (typeof value === 'bigint') {
                  return value.toString() + 'n';
                }
                return value;
              }
            })
          )
        }),
        new winston.transports.File({
          filename: 'logs/combined.log',
          level: 'info',
          format: winston.format.combine(
            winston.format.timestamp(),
            winston.format.json({
              replacer: (key, value) => {
                if (typeof value === 'bigint') {
                  return value.toString() + 'n';
                }
                return value;
              }
            })
          )
        })
      );
    }

    this.winston = winston.createLogger({
      level: config.logLevel,
      transports
    });
  }

  private async addLogEntry(level: LogLevel, message: string, data?: any): Promise<void> {
    // Safely serialize data with BigInt handling
    const safeData = data ? JSON.parse(JSON.stringify(data, (key, value) => {
      if (typeof value === 'bigint') {
        return value.toString() + 'n';
      }
      return value;
    })) : undefined;

    const entry: LogEntry = {
      level,
      message,
      timestamp: Date.now(),
      data: safeData
    };

    this.logEntries.push(entry);

    // Send to web dashboard if active (web dashboard handles its own filtering)
    const webDash = await getWebDashboard();
    if (webDash?.isActive()) {
      try {
        webDash.addLogEntry(entry);
      } catch (error) {
        // Silently handle dashboard errors to prevent log loops
        console.error('Dashboard log error:', error instanceof Error ? error.message : String(error));
      }
    }

    // Keep only last 500 entries to reduce memory usage
    if (this.logEntries.length > 5000) {
      this.logEntries = this.logEntries.slice(-5000);
    }
  }

  error(message: string, error?: any): void {
    if (!this.shouldShowLogLevel(LogLevel.ERROR)) {
      return;
    }

    const timestamp = chalk.gray(this.getTimestamp());
    const prefix = chalk.red('❌ ERROR:');
    const formattedMessage = this.formatErrorMessage(message);
    const errorMsg = chalk.red(formattedMessage);

    console.log(`${timestamp} ${prefix} ${errorMsg}`);
    if (error) {
      const errorDetails = error.message || error;
      const formattedDetails = this.formatErrorMessage(errorDetails.toString());
      console.log(`${timestamp} ${chalk.gray('Details:')} ${chalk.red(formattedDetails)}`);
    }

    this.winston.error(formattedMessage, error);
    this.addLogEntry(LogLevel.ERROR, formattedMessage, error);
  }

  warn(message: string, data?: any): void {
    if (!this.shouldShowLogLevel(LogLevel.WARN)) {
      return;
    }

    const timestamp = chalk.gray(this.getTimestamp());
    const prefix = chalk.yellow('ℹ️  WARN:');
    const formattedMessage = this.formatErrorMessage(message);
    const infoMsg = chalk.white(formattedMessage);

    console.log(`${timestamp} ${prefix} ${infoMsg}`);
    if (data) {
      console.log(`${timestamp} ${chalk.gray('Data:')} ${chalk.cyan(JSON.stringify(data, (key, value) => {
        if (typeof value === 'bigint') {
          return value.toString() + 'n';
        }
        return value;
      }, 2))}`);
    }

    this.winston.info(formattedMessage, data);
    this.addLogEntry(LogLevel.WARN, formattedMessage, data);
  }

  info(message: string, data?: any): void {
    if (!this.shouldShowLogLevel(LogLevel.INFO)) {
      return;
    }

    const timestamp = chalk.gray(this.getTimestamp());
    const prefix = chalk.blue('ℹ️  INFO:');
    const formattedMessage = this.formatErrorMessage(message);
    const infoMsg = chalk.white(formattedMessage);

    console.log(`${timestamp} ${prefix} ${infoMsg}`);
    if (data) {
      console.log(`${timestamp} ${chalk.gray('Data:')} ${chalk.cyan(JSON.stringify(data, (key, value) => {
        if (typeof value === 'bigint') {
          return value.toString() + 'n';
        }
        return value;
      }, 2))}`);
    }

    this.winston.info(formattedMessage, data);
    this.addLogEntry(LogLevel.INFO, formattedMessage, data);
  }

  debug(message: string, data?: any): void {
    this.winston.debug(message, data);
    this.addLogEntry(LogLevel.DEBUG, message, data);
  }

  logBundle(bundleHash: string, transactions: number, profit?: string): void {
    this.info('Bundle Submitted', {
      bundleHash,
      transactions,
      profit,
      timestamp: new Date().toISOString()
    });
  }

  logError(error: Error, context?: string): void {
    const formattedMessage = this.formatErrorMessage(error.message);
    this.error(`${context ? `[${context}] ` : ''}${formattedMessage}`, {
      stack: error.stack,
      context
    });
  }

  getRecentLogs(count: number = 100): LogEntry[] {
    return this.logEntries.slice(-count);
  }

  getLogsByLevel(level: LogLevel, count: number = 100): LogEntry[] {
    return this.logEntries
      .filter(entry => entry.level === level)
      .slice(-count);
  }

  // Enhanced logging methods from enhancedLogger
  private getTimestamp(): string {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    const hours = String(now.getHours()).padStart(2, '0');
    const minutes = String(now.getMinutes()).padStart(2, '0');
    const seconds = String(now.getSeconds()).padStart(2, '0');
    const ms = String(now.getMilliseconds()).padStart(6, '0');

    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}.${ms}`;
  }

  private formatCurrency(amount: string | number | bigint, symbol: string = '$'): string {
    const num = typeof amount === 'bigint' ? Number(ethers.formatEther(amount)) : Number(amount);
    return `${num.toFixed(8)} ${symbol}`;
  }

  /**
   * Fetch current ETH price from CoinGecko API
   */
  private async updateEthPrice(): Promise<void> {
    try {
      const now = Date.now();

      // Only update if cache is expired
      if (now - this.lastPriceUpdate < this.priceUpdateInterval) {
        return;
      }

      const response = await axios.get(
        'https://api.coingecko.com/api/v3/simple/price?ids=ethereum&vs_currencies=usd',
        { timeout: 5000 }
      );

      if (response.data?.ethereum?.usd) {
        this.ethPrice = response.data.ethereum.usd;
        this.lastPriceUpdate = now;
      }
    } catch (error) {
      // Silently handle price fetch errors to avoid log spam
      // Keep using the last known price
    }
  }

  /**
   * Get current ETH price (with automatic updates)
   */
  private async getEthPrice(): Promise<number> {
    await this.updateEthPrice();
    return this.ethPrice;
  }

  /**
   * Convert ETH amount to USD
   */
  private async ethToUsd(ethAmount: number): Promise<string> {
    const price = await this.getEthPrice();
    if (price === 0) return 'N/A';

    const usdValue = ethAmount * price;
    return usdValue.toLocaleString('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    });
  }

  /**
   * Convert Wei values to readable ETH format with USD value
   */
  private async formatWeiToEth(weiValue: string | bigint): Promise<string> {
    try {
      const wei = typeof weiValue === 'string' ? BigInt(weiValue) : weiValue;
      const eth = ethers.formatEther(wei);
      const ethNum = parseFloat(eth);
      const usd = await this.ethToUsd(ethNum);

      return `${ethNum.toFixed(8)} ETH (${usd})`;
    } catch (error) {
      return weiValue.toString();
    }
  }

  /**
   * Synchronous version for cases where async is not possible
   */
  private formatWeiToEthSync(weiValue: string | bigint): string {
    try {
      const wei = typeof weiValue === 'string' ? BigInt(weiValue) : weiValue;
      const eth = ethers.formatEther(wei);
      const ethNum = parseFloat(eth);

      // Use cached price if available
      const usdValue = this.ethPrice > 0 ?
        (ethNum * this.ethPrice).toLocaleString('en-US', {
          style: 'currency',
          currency: 'USD',
          minimumFractionDigits: 2,
          maximumFractionDigits: 2
        }) : 'N/A';

      return `${ethNum.toFixed(8)} ETH (${usdValue})`;
    } catch (error) {
      return weiValue.toString();
    }
  }

  /**
   * Automatically detect and format Wei values in error messages
   */
  private formatErrorMessage(message: string): string {
    // Pattern to match large numbers that could be Wei values (typically 15+ digits)
    // Only matches standalone numbers, not those that are part of decimal notation
    const weiPattern = /\b(\d{15,})\b/g;

    return message.replace(weiPattern, (match, capture, offset, string) => {
      try {
        // Check if this number is part of a decimal (has a dot before or after)
        const beforeChar = offset > 0 ? string[offset - 1] : '';
        const afterChar = offset + match.length < string.length ? string[offset + match.length] : '';

        // Skip if this number is part of a decimal value
        if (beforeChar === '.' || afterChar === '.') {
          return match;
        }

        const weiValue = BigInt(match);
        // Only format if it's a reasonable Wei value (not just any large number)
        if (weiValue > BigInt('1000000000000000')) { // > 0.001 ETH
          return this.formatWeiToEthSync(weiValue);
        }
        return match;
      } catch {
        return match;
      }
    });
  }

  private shouldShowLogLevel(level: LogLevel): boolean {
    const configuredLevel = config.logLevel.toLowerCase();
    const levelHierarchy = {
      'error': 0,
      'warn': 1,
      'info': 2,
      'system': 3,
      'debug': 4
    };

    const currentLevelValue = levelHierarchy[level.toLowerCase() as keyof typeof levelHierarchy];
    const configuredLevelValue = levelHierarchy[configuredLevel as keyof typeof levelHierarchy];

    if (currentLevelValue === undefined || configuredLevelValue === undefined) {
      return true;
    }

    return currentLevelValue <= configuredLevelValue;
  }

  // System status logs
  system(message: string, data?: any): void {
    if (!this.shouldShowLogLevel(LogLevel.SYSTEM)) {
      return;
    }

    const timestamp = chalk.gray(this.getTimestamp());
    const prefix = chalk.white('ℹ️  SYSTEM:');
    const infoMsg = chalk.white(message);

    console.log(`${timestamp} ${prefix} ${infoMsg}`);
    if (data) {
      console.log(`${timestamp} ${chalk.gray('Data:')} ${chalk.cyan(JSON.stringify(data, (key, value) => {
        if (typeof value === 'bigint') {
          return value.toString() + 'n';
        }
        return value;
      }, 2))}`);
    }

    this.winston.info(message, data);
    this.addLogEntry(LogLevel.SYSTEM, message, data);
  }

  // Bot status and configuration
  botStatus(message: string, data?: any): void {
    if (!this.shouldShowLogLevel(LogLevel.INFO)) {
      return;
    }

    const timestamp = chalk.gray(this.getTimestamp());
    const prefix = chalk.cyan('Bot');

    const logMessage = data?.seconds
      ? `${timestamp} ${prefix} ${chalk.yellow(`will wait ${data.seconds} seconds before buy`)} ${chalk.white(message)}`
      : `${timestamp} ${prefix} ${chalk.white(message)}`;

    console.log(logMessage);
    this.addLogEntry(LogLevel.INFO, `BOT: ${message}`, data);
  }

  // Transaction monitoring
  transactionHash(hash: string, description?: string): void {
    if (!this.shouldShowLogLevel(LogLevel.INFO)) {
      return;
    }

    const timestamp = chalk.gray(this.getTimestamp());
    const prefix = chalk.magenta('Transaction Hash');
    const hashFormatted = chalk.cyan(hash);
    const desc = description ? chalk.gray(description) : '';
    const logMessage = `${timestamp} ${prefix} = ${hashFormatted} ${desc}`;

    console.log(logMessage);
    this.addLogEntry(LogLevel.INFO, `TRANSACTION: ${hash} ${description || ''}`);
  }

  // Transaction confirmation
  transactionConfirm(message: string, waitTime?: number): void {
    const timestamp = chalk.gray(this.getTimestamp());
    const prefix = chalk.yellow('Checking for Transaction confirmation');
    const wait = waitTime ? chalk.gray(`(waiting ${waitTime} seconds)...`) : '';
    const logMessage = `${timestamp} ${prefix} ${wait}`;

    console.log(logMessage);
    this.addLogEntry(LogLevel.INFO, `CONFIRMATION: ${message} ${waitTime ? `(${waitTime}s)` : ''}`);
  }

  // Success messages
  success(message: string, data?: any): void {
    const timestamp = chalk.gray(this.getTimestamp());
    const prefix = chalk.green('SUCCESS');
    const logMessage = `${timestamp} ${prefix} --> ${chalk.green(message)}`;

    console.log(`${timestamp} ${prefix} ${logMessage}`);
    if (data) {
      console.log(`${timestamp} ${chalk.gray('Data:')} ${chalk.cyan(JSON.stringify(data, (key, value) => {
        if (typeof value === 'bigint') {
          return value.toString() + 'n';
        }
        return value;
      }, 2))}`);
    }

    this.winston.info(message, data);
    this.addLogEntry(LogLevel.INFO, `SUCCESS: ${message}`, data);
  }

  // Wallet balance
  walletBalance(balance: string | number, currency: string = 'ETH'): void {
    const timestamp = chalk.gray(this.getTimestamp());
    const prefix = chalk.blue('Current Wallet Balance is:');
    const amount = chalk.yellow(this.formatCurrency(balance, currency));
    const logMessage = `${timestamp} ${prefix} ${amount}`;

    console.log(logMessage);
    this.addLogEntry(LogLevel.INFO, `WALLET: Balance ${this.formatCurrency(balance, currency)}`);
  }

  // Token purchase
  tokenPurchase(amount: string | number, tokenSymbol: string): void {
    const timestamp = chalk.gray(this.getTimestamp());
    const message = chalk.green(`You bought ${this.formatCurrency(amount)} ${tokenSymbol} tokens`);

    console.log(`${timestamp} ${message}`);
    this.addLogEntry(LogLevel.INFO, message, { amount, tokenSymbol });
  }

  // Approval status
  approvalStatus(tokenAddress: string, status: string): void {
    const timestamp = chalk.gray(this.getTimestamp());
    const prefix = chalk.yellow('Checking Approval Status');
    const address = chalk.cyan(tokenAddress);
    const statusMsg = status === 'approved' ?
      chalk.green('Token is already approved --> You can use this token') :
      chalk.red('Token needs approval');

    console.log(`${timestamp} ${prefix} ${address}`);
    console.log(`${timestamp} ${statusMsg}`);
    this.addLogEntry(LogLevel.INFO, `APPROVAL: ${tokenAddress} - ${status}`, { tokenAddress, status });
  }

  // Sell signals
  sellSignal(type: string = 'Sell Signal Found'): void {
    const timestamp = chalk.gray(this.getTimestamp());
    const signal = chalk.red(`${type} --> ${type} --> ${type}`);

    console.log(`${timestamp} ${signal}`);
    this.addLogEntry(LogLevel.INFO, `SELL SIGNAL: ${type}`, { type });
  }

  // Price information
  sellPrice(price: string | number): void {
    const timestamp = chalk.gray(this.getTimestamp());
    const prefix = chalk.yellow('Sell price in');
    const priceFormatted = chalk.white(`: ${price}`);
    const dots = chalk.gray('................................................................');

    console.log(`${timestamp} ${prefix} ${priceFormatted}`);
    console.log(`${timestamp} ${dots}`);
    this.addLogEntry(LogLevel.INFO, `SELL PRICE: ${price}`, { price });
  }

  // Liquidity detection
  liquidityDetected(amount: string | number): void {
    const timestamp = chalk.gray(this.getTimestamp());
    const prefix = chalk.blue('Current');
    const liquidity = chalk.green(`Liquidity = ${this.formatCurrency(amount)}`);
    const detection = chalk.green('1 --> Enough liquidity detected : let\'s go!');

    console.log(`${timestamp} ${prefix} ${liquidity}`);
    console.log(`${timestamp} ${detection}`);
    this.addLogEntry(LogLevel.INFO, `LIQUIDITY: ${this.formatCurrency(amount)}`, { amount });
  }

  // Order placement
  placingOrder(type: string = 'Sell'): void {
    const timestamp = chalk.gray(this.getTimestamp());
    const order = chalk.yellow(`Placing ${type} Order`);

    console.log(`${timestamp} ${order}`);
    this.addLogEntry(LogLevel.INFO, `ORDER: Placing ${type}`, { type });
  }

  // MEV specific logs
  victimTransaction(method: string, tokenIn: string, tokenOut: string, amount: string): void {
    const timestamp = chalk.gray(this.getTimestamp());
    const prefix = chalk.red('🔍 Victim Transaction Detected:');
    const methodFormatted = chalk.yellow(method);
    const tokens = chalk.cyan(`${tokenIn} -> ${tokenOut}`);
    const amountFormatted = chalk.green(this.formatCurrency(amount));

    console.log(`${timestamp} ${prefix} ${methodFormatted}`);
    console.log(`${timestamp} ${chalk.gray('Tokens:')} ${tokens} ${chalk.gray('Amount:')} ${amountFormatted}`);
    this.addLogEntry(LogLevel.INFO, `VICTIM TX: ${method} ${tokenIn}->${tokenOut} ${amount}`, { method, tokenIn, tokenOut, amount });
  }

  // Bundle simulation
  bundleSimulation(result: 'success' | 'error', details?: string): void {
    const timestamp = chalk.gray(this.getTimestamp());

    if (result === 'success') {
      const prefix = chalk.green('✅ Bundle Simulation:');
      const message = chalk.green('SUCCESS --> Bundle is profitable');
      console.log(`${timestamp} ${prefix} ${message}`);
      this.addLogEntry(LogLevel.SYSTEM, `BUNDLE SIM: SUCCESS - ${details || 'profitable'}`, { result, details });
    } else {
      const prefix = chalk.red('❌ Bundle Simulation:');
      const message = chalk.red(`ERROR --> ${details || 'Simulation failed'}`);
      console.log(`${timestamp} ${prefix} ${message}`);
      this.addLogEntry(LogLevel.WARN, `BUNDLE SIM: ERROR - ${details || 'failed'}`, { result, details });
    }
  }

  // Profit calculation (legacy method - enhanced)
  async profitCalculation(profit: string | number | bigint, profitable: boolean): Promise<void> {
    const timestamp = chalk.gray(this.getTimestamp());
    const prefix = chalk.blue('Profit Analysis:');

    // Convert to Wei if it's a number (assuming ETH)
    const profitWei = typeof profit === 'bigint' ? profit :
                     typeof profit === 'string' ? BigInt(profit) :
                     ethers.parseEther(profit.toString());

    const profitFormatted = await this.formatWeiToEth(profitWei);

    if (profitable) {
      const message = chalk.green(`PROFITABLE --> Expected profit: ${profitFormatted}`);
      console.log(`${timestamp} ${prefix} ${message}`);
      this.addLogEntry(LogLevel.INFO, `PROFIT: ${profitFormatted} (profitable)`, { profit: profitWei.toString(), profitable });
    } else {
      const message = chalk.red(`NOT PROFITABLE --> Loss: ${profitFormatted}`);
      console.log(`${timestamp} ${prefix} ${message}`);
      this.addLogEntry(LogLevel.WARN, `PROFIT: ${profitFormatted} (not profitable)`, { profit: profitWei.toString(), profitable });
    }
  }

  /**
   * Log individual profit components with USD values
   */
  async expectedProfit(profit: string | bigint, context?: string): Promise<void> {
    const timestamp = chalk.gray(this.getTimestamp());
    const profitFormatted = await this.formatWeiToEth(profit);
    const message = `${timestamp} ${chalk.gray('Expected Profit:')} ${chalk.green(profitFormatted)}${context ? ` (${context})` : ''}`;

    console.log(message);
    this.addLogEntry(LogLevel.SYSTEM, `Expected Profit: ${profitFormatted}${context ? ` (${context})` : ''}`, { profit: profit.toString(), context });
  }

  /**
   * Log gas cost with USD values
   */
  async gasCost(cost: string | bigint, context?: string): Promise<void> {
    const timestamp = chalk.gray(this.getTimestamp());
    const costFormatted = await this.formatWeiToEth(cost);
    const message = `${timestamp} ${chalk.gray('Gas Cost:')} ${chalk.yellow(costFormatted)}${context ? ` (${context})` : ''}`;

    console.log(message);
    this.addLogEntry(LogLevel.INFO, `Gas Cost: ${costFormatted}${context ? ` (${context})` : ''}`, { cost: cost.toString(), context });
  }

  /**
   * Log net profit with USD values
   */
  async netProfit(profit: string | bigint, context?: string): Promise<void> {
    const timestamp = chalk.gray(this.getTimestamp());
    const profitFormatted = await this.formatWeiToEth(profit);
    const isPositive = BigInt(profit.toString()) > BigInt(0);
    const color = isPositive ? chalk.green : chalk.red;
    const message = `${timestamp} ${chalk.gray('Net Profit:')} ${color(profitFormatted)}${context ? ` (${context})` : ''}`;

    console.log(message);
    this.addLogEntry(LogLevel.INFO, `Net Profit: ${profitFormatted}${context ? ` (${context})` : ''}`, { profit: profit.toString(), context, isPositive });
  }

  // Bundle submission
  bundleSubmission(blockNumber: number, txCount: number): void {
    const timestamp = chalk.gray(this.getTimestamp());
    const prefix = chalk.blue('🚀 Bundle Submitted:');
    const details = chalk.yellow(`Block ${blockNumber}, ${txCount} transactions`);

    console.log(`${timestamp} ${prefix} ${details}`);
    this.addLogEntry(LogLevel.INFO, `BUNDLE: Block ${blockNumber}, ${txCount} txs`, { blockNumber, txCount });
  }

  // Separator line
  separator(): void {
    const line = chalk.gray('═'.repeat(80));
    console.log(line);
    this.addLogEntry(LogLevel.DEBUG, '═'.repeat(80));
  }

  // Clear screen
  clear(): void {
    console.clear();
  }

  /**
   * Log insufficient funds error with readable ETH formatting
   */
  async insufficientFunds(have: string | bigint, want: string | bigint, context?: string): Promise<void> {
    const timestamp = chalk.gray(this.getTimestamp());
    const prefix = chalk.red('💰 INSUFFICIENT FUNDS:');

    const haveEth = await this.formatWeiToEth(have);
    const wantEth = await this.formatWeiToEth(want);

    console.log(`${timestamp} ${prefix}`);
    console.log(`${timestamp} ${chalk.gray('Available:')} ${chalk.yellow(haveEth)}`);
    console.log(`${timestamp} ${chalk.gray('Required: ')} ${chalk.red(wantEth)}`);

    if (context) {
      console.log(`${timestamp} ${chalk.gray('Context:  ')} ${chalk.white(context)}`);
    }

    const message = `Insufficient funds - Have: ${haveEth}, Want: ${wantEth}${context ? ` (${context})` : ''}`;
    this.winston.error(message);
    this.addLogEntry(LogLevel.ERROR, message, { have: have.toString(), want: want.toString(), context });
  }

  /**
   * Synchronous version of insufficientFunds for cases where async is not possible
   */
  insufficientFundsSync(have: string | bigint, want: string | bigint, context?: string): void {
    const timestamp = chalk.gray(this.getTimestamp());
    const prefix = chalk.red('💰 INSUFFICIENT FUNDS:');

    const haveEth = this.formatWeiToEthSync(have);
    const wantEth = this.formatWeiToEthSync(want);

    console.log(`${timestamp} ${prefix}`);
    console.log(`${timestamp} ${chalk.gray('Available:')} ${chalk.yellow(haveEth)}`);
    console.log(`${timestamp} ${chalk.gray('Required: ')} ${chalk.red(wantEth)}`);

    if (context) {
      console.log(`${timestamp} ${chalk.gray('Context:  ')} ${chalk.white(context)}`);
    }

    const message = `Insufficient funds - Have: ${haveEth}, Want: ${wantEth}${context ? ` (${context})` : ''}`;
    this.winston.error(message);
    this.addLogEntry(LogLevel.ERROR, message, { have: have.toString(), want: want.toString(), context });
  }

  /**
   * Log gas estimation with readable formatting
   */
  async gasEstimation(gasLimit: string | bigint, gasPrice: string | bigint, totalCost: string | bigint): Promise<void> {
    const timestamp = chalk.gray(this.getTimestamp());
    const prefix = chalk.blue('⛽ GAS ESTIMATION:');

    const costFormatted = await this.formatWeiToEth(totalCost);

    console.log(`${timestamp} ${prefix}`);
    console.log(`${timestamp} ${chalk.gray('Gas Limit: ')} ${chalk.cyan(gasLimit.toString())}`);
    console.log(`${timestamp} ${chalk.gray('Gas Price: ')} ${chalk.cyan(ethers.formatUnits(gasPrice, 'gwei'))} gwei`);
    console.log(`${timestamp} ${chalk.gray('Total Cost:')} ${chalk.yellow(costFormatted)}`);

    const message = `Gas estimation - Limit: ${gasLimit}, Price: ${ethers.formatUnits(gasPrice, 'gwei')} gwei, Cost: ${costFormatted}`;
    this.addLogEntry(LogLevel.INFO, message, { gasLimit: gasLimit.toString(), gasPrice: gasPrice.toString(), totalCost: totalCost.toString() });
  }

  /**
   * Synchronous version of gasEstimation
   */
  gasEstimationSync(gasLimit: string | bigint, gasPrice: string | bigint, totalCost: string | bigint): void {
    const timestamp = chalk.gray(this.getTimestamp());
    const prefix = chalk.blue('⛽ GAS ESTIMATION:');

    const costFormatted = this.formatWeiToEthSync(totalCost);

    console.log(`${timestamp} ${prefix}`);
    console.log(`${timestamp} ${chalk.gray('Gas Limit: ')} ${chalk.cyan(gasLimit.toString())}`);
    console.log(`${timestamp} ${chalk.gray('Gas Price: ')} ${chalk.cyan(ethers.formatUnits(gasPrice, 'gwei'))} gwei`);
    console.log(`${timestamp} ${chalk.gray('Total Cost:')} ${chalk.yellow(costFormatted)}`);

    const message = `Gas estimation - Limit: ${gasLimit}, Price: ${ethers.formatUnits(gasPrice, 'gwei')} gwei, Cost: ${costFormatted}`;
    this.addLogEntry(LogLevel.INFO, message, { gasLimit: gasLimit.toString(), gasPrice: gasPrice.toString(), totalCost: totalCost.toString() });
  }

  /**
   * Log profitability analysis with USD values
   */
  async profitabilityAnalysis(
    expectedProfit: string | bigint,
    gasCost: string | bigint,
    netProfit: string | bigint,
    context?: string
  ): Promise<void> {
    const timestamp = chalk.gray(this.getTimestamp());
    const prefix = chalk.blue('🔍 PROFITABILITY ANALYSIS:');

    const expectedProfitFormatted = await this.formatWeiToEth(expectedProfit);
    const gasCostFormatted = await this.formatWeiToEth(gasCost);
    const netProfitFormatted = await this.formatWeiToEth(netProfit);

    console.log(`${timestamp} ${prefix}${context ? ` (${context})` : ''}`);
    console.log(`${timestamp} ${chalk.gray('Expected Profit:')} ${chalk.green(expectedProfitFormatted)}`);
    console.log(`${timestamp} ${chalk.gray('Gas Cost:      ')} ${chalk.yellow(gasCostFormatted)}`);
    console.log(`${timestamp} ${chalk.gray('Net Profit:     ')} ${chalk.cyan(netProfitFormatted)}`);

    const message = `Profitability Analysis${context ? ` (${context})` : ''} - Expected: ${expectedProfitFormatted}, Gas: ${gasCostFormatted}, Net: ${netProfitFormatted}`;
    this.addLogEntry(LogLevel.SYSTEM, message, {
      expectedProfit: expectedProfit.toString(),
      gasCost: gasCost.toString(),
      netProfit: netProfit.toString(),
      context
    });
  }

  /**
   * Synchronous version of profitabilityAnalysis
   */
  profitabilityAnalysisSync(
    expectedProfit: string | bigint,
    gasCost: string | bigint,
    netProfit: string | bigint,
    context?: string
  ): void {
    const timestamp = chalk.gray(this.getTimestamp());
    const prefix = chalk.blue('🔍 PROFITABILITY ANALYSIS:');

    const expectedProfitFormatted = this.formatWeiToEthSync(expectedProfit);
    const gasCostFormatted = this.formatWeiToEthSync(gasCost);
    const netProfitFormatted = this.formatWeiToEthSync(netProfit);

    console.log(`${timestamp} ${prefix}${context ? ` (${context})` : ''}`);
    console.log(`${timestamp} ${chalk.gray('Expected Profit:')} ${chalk.green(expectedProfitFormatted)}`);
    console.log(`${timestamp} ${chalk.gray('Gas Cost:      ')} ${chalk.yellow(gasCostFormatted)}`);
    console.log(`${timestamp} ${chalk.gray('Net Profit:     ')} ${chalk.cyan(netProfitFormatted)}`);

    const message = `Profitability Analysis${context ? ` (${context})` : ''} - Expected: ${expectedProfitFormatted}, Gas: ${gasCostFormatted}, Net: ${netProfitFormatted}`;
    this.addLogEntry(LogLevel.SYSTEM, message, {
      expectedProfit: expectedProfit.toString(),
      gasCost: gasCost.toString(),
      netProfit: netProfit.toString(),
      context
    });
  }


}

export const logger = new Logger();
export { Logger };
