import { logger } from './logger';
import { WeiFormatter } from './formatting/wei-formatter';

/**
 * Enhanced error handler for MEV bot operations
 */
export class ErrorHandler {
  /**
   * Handle insufficient funds errors with enhanced formatting
   */
  static async handleInsufficientFunds(error: Error, context?: string): Promise<void> {
    const parsed = WeiFormatter.parseInsufficientFundsError(error.message);

    if (parsed && parsed.have && parsed.want) {
      await logger.insufficientFunds(parsed.have, parsed.want, context);
    } else {
      logger.error(`Insufficient funds error: ${error.message}`, { context });
    }
  }

  /**
   * Synchronous version of handleInsufficientFunds
   */
  static handleInsufficientFundsSync(error: Error, context?: string): void {
    const parsed = WeiFormatter.parseInsufficientFundsError(error.message);

    if (parsed && parsed.have && parsed.want) {
      logger.insufficientFundsSync(parsed.have, parsed.want, context);
    } else {
      logger.error(`Insufficient funds error: ${error.message}`, { context });
    }
  }

  /**
   * Handle gas estimation errors
   */
  static handleGasError(error: Error, gasLimit?: string | bigint, gasPrice?: string | bigint, context?: string): void {
    if (gasLimit && gasPrice) {
      const gasCost = WeiFormatter.formatGasCost(gasLimit, gasPrice);
      logger.error(`Gas error in ${context || 'transaction'}: ${error.message}`, {
        gasLimit: gasCost.gasLimitFormatted,
        gasPrice: gasCost.gasPriceGwei + ' gwei',
        estimatedCost: gasCost.totalCostEth + ' ETH'
      });
    } else {
      logger.error(`Gas error in ${context || 'transaction'}: ${error.message}`);
    }
  }

  /**
   * Handle transaction errors with automatic Wei formatting
   */
  static async handleTransactionError(error: Error, context?: string): Promise<void> {
    const message = error.message.toLowerCase();

    if (message.includes('insufficient funds')) {
      await this.handleInsufficientFunds(error, context);
    } else if (message.includes('gas')) {
      this.handleGasError(error, undefined, undefined, context);
    } else {
      logger.logError(error, context);
    }
  }

  /**
   * Synchronous version of handleTransactionError
   */
  static handleTransactionErrorSync(error: Error, context?: string): void {
    const message = error.message.toLowerCase();

    if (message.includes('insufficient funds')) {
      this.handleInsufficientFundsSync(error, context);
    } else if (message.includes('gas')) {
      this.handleGasError(error, undefined, undefined, context);
    } else {
      logger.logError(error, context);
    }
  }

  /**
   * Handle balance check errors
   */
  static handleBalanceError(available: string | bigint, required: string | bigint, context?: string): void {
    const comparison = WeiFormatter.formatBalanceComparisonSync(available, required);

    if (!comparison.sufficient) {
      logger.error(`Insufficient balance for ${context || 'operation'}`, {
        available: comparison.available,
        required: comparison.required,
        deficit: comparison.deficit
      });
    }
  }

  /**
   * Wrap async function with enhanced error handling
   */
  static async withErrorHandling<T>(
    operation: () => Promise<T>,
    context: string,
    options?: {
      logSuccess?: boolean;
      retryCount?: number;
      retryDelay?: number;
    }
  ): Promise<T | null> {
    const { logSuccess = false, retryCount = 0, retryDelay = 1000 } = options || {};
    
    for (let attempt = 0; attempt <= retryCount; attempt++) {
      try {
        const result = await operation();
        
        if (logSuccess) {
          logger.info(`✅ ${context} completed successfully`);
        }
        
        return result;
      } catch (error) {
        const isLastAttempt = attempt === retryCount;
        
        if (isLastAttempt) {
          this.handleTransactionError(error as Error, context);
          return null;
        } else {
          logger.warn(`⚠️ ${context} failed (attempt ${attempt + 1}/${retryCount + 1}), retrying in ${retryDelay}ms...`);
          await new Promise(resolve => setTimeout(resolve, retryDelay));
        }
      }
    }
    
    return null;
  }
}

/**
 * Convenience functions for common error handling scenarios
 */
export const handleInsufficientFunds = ErrorHandler.handleInsufficientFunds;
export const handleGasError = ErrorHandler.handleGasError;
export const handleTransactionError = ErrorHandler.handleTransactionError;
export const handleBalanceError = ErrorHandler.handleBalanceError;
export const withErrorHandling = ErrorHandler.withErrorHandling;
