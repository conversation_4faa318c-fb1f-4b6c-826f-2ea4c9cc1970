import { BigNumberish } from 'ethers';

export interface Config {
  rpcUrl: string;
  flashbotsRpcUrl: string;
  chainId: number;
  privateKey: string;
  flashbotsSignerKey: string;
  minProfitWei: string;
  maxGasPriceGwei: number;
  maxPriorityFeeGwei: number;
  gasUrgency: 'slow' | 'standard' | 'fast' | 'instant';
  slippageTolerance: number;
  mempoolWebsocketUrl: string;
  enableFlashbotsMempool: boolean;
  enableEthersMempool: boolean;
  enableSandwichAttacks: boolean;
  enableFrontRunning: boolean;
  enableArbitrage: boolean;
  enableFlashloanAttacks: boolean;
  enableMultiDexArbitrage: boolean;
  enableUniswapV3FlashSwaps: boolean;
  enableMultiBlockAttacks: boolean;
  maxBlocksAhead: number;
  maxPositionSizeEth: number;
  emergencyStop: boolean;
  dryRun: boolean;
  simulationMode: boolean;
  useRawTransactionExecution: boolean;
  enableTransactionMonitoring: boolean;

  // Light Contract (Off-chain architecture)
  LIGHT_CONTRACT_ADDRESS?: string;
  enableLightContract: boolean;

  // Advanced MEV optimization parameters
  bundleSubmissionStrategy: 'conservative' | 'balanced' | 'aggressive';
  enableBundleMultiplexing: boolean;
  bundleRetryCount: number;
  bundleTimeoutMs: number;

  // Gas optimization
  dynamicGasPricing: boolean;
  gasEscalationFactor: number;
  competitiveGasBuffer: number;
  baseFeeBufferMultiplier: number;
  allowDynamicBaseFeeBuffer: boolean;
  estimatedBlockTimeSec: number;

  // Profit optimization
  profitMarginMultiplier: number;
  enableProfitMaximization: boolean;
  minProfitMarginPercent: number;

  // Timing optimization
  earlySubmissionOffsetMs: number;
  blockTimingBufferMs: number;
  enablePreemptiveSubmission: boolean;

  // Aave flashloan optimization
  aaveNoWaitSubmission: boolean;
  aaveImmediateReturn: boolean;
  aavePoolAddress: string;

  logLevel: string;
  logToFile: boolean;
  // Flashloan contract addresses
  hybridFlashloanContract: string;
  balancerFlashloanContract: string;
  aaveFlashloanContract: string;
  // Balancer flashloan configuration
  balancerVaultAddress: string;
  balancerMinProfitThreshold: number;
  balancerMaxFlashloanAmount: number;
  balancerMinPriceDifference: number;
  balancerMaxPriceDifference: number;
  balancerSafetyMargin: number;
  // Flashloan DEX configuration
  flashloanDexPairs: string[];
  flashloanBuyDex: string;
  flashloanSellDex: string;
  enableCrossDexArbitrage: boolean;
  minArbitrageSpread: number;
  // Flashloan token configuration
  flashloanTokens: string[];
  flashloanPrimaryToken: string;
  flashloanTargetTokens: string[];
  enableAllTokenPairs: boolean;
  minTokenLiquidityUsd: number;
  // Flashloan amount configuration
  flashloanBaseAmountWeth: number;
  flashloanBaseAmountUsdc: number;
  flashloanMaxMultiplier: number;
  // Flashbots configuration
  enableFlashbots: boolean;
  flashbotsRelayUrl: string;
  flashbotsAuthKey: string;
  // Flashbots Protect RPC configuration
  enableFlashbotsProtect: boolean;
  flashbotsProtectRpcUrl: string;
  flashbotsProtectFastMode: boolean;
  flashbotsProtectMaxBlockNumber: number;
  // Uniswap V3 Flash Swap configuration
  uniswapV3TradingPairs: string[];
  uniswapV3FeeTiers: number[];
  // MEV-Share configuration
  enableMevShare: boolean;
  mevShareStreamUrl: string;
  enableBackrunStrategy: boolean;
  minBackrunProfitEth: number;
  maxGasCostEth: number;
  // Scanning intervals
  arbitrageScanIntervalMs: number;
  flashloanScanIntervalMs: number;
  // Advanced gas estimation
  blocknativeApiKey: string;
  enableBlocknativeGas: boolean;
  enable0xApiGas: boolean;
  enableEthGasStation: boolean;
  fallbackGasPriceGwei: number;
  // Pool validation configuration
  enablePoolValidation: boolean;
  enableLiquidityFetching: boolean;
  failOnPoolValidationError: boolean;
  pureProfitMode: boolean;
  debugMode: boolean;

  // Multi-DEX Arbitrage Configuration
  multiDexPairs: string[];
  multiDexSources: string[];
  multiDexMinProfitUSD: number;
  multiDexScanIntervalMs: number;
  multiDexPriceDeviationThresholdBps: number;
  multiDexMaxPriceAgeMs: number;
  multiDexFlashloanAmounts: number[];
  multiDexSlippageToleranceBps: number;
  multiDexMaxGasCostGwei: number;
  multiDexConfidenceThreshold: number;
  multiDexExecuteOnlyProfitable: boolean;

  // Addresses (for backward compatibility)
  addresses?: {
    WETH: string;
    USDC: string;
    DAI: string;
    USDT: string;
    UNISWAP_V3_ROUTER: string;
    SUSHISWAP_ROUTER: string;
    CURVE_3POOL: string;
  };
}

export interface Token {
  address: string;
  symbol: string;
  decimals: number;
  name: string;
}

export interface Pool {
  address: string;
  token0: Token;
  token1: Token;
  fee: number;
  protocol: 'uniswap-v2' | 'uniswap-v3' | 'balancer' | 'curve' | 'sushiswap';
  reserves?: {
    reserve0: BigNumberish;
    reserve1: BigNumberish;
  };
  liquidity?: BigNumberish;
  tick?: number;
  sqrtPriceX96?: BigNumberish;
  // Curve-specific properties
  curveTokenIndices?: {
    i: number; // Index of token0 in Curve pool
    j: number; // Index of token1 in Curve pool
  };
}

export interface Transaction {
  hash: string;
  from: string;
  to: string;
  value: BigNumberish;
  gasPrice: BigNumberish;
  gasLimit: BigNumberish;
  data: string;
  nonce: number;
  maxFeePerGas?: BigNumberish;
  maxPriorityFeePerGas?: BigNumberish;
}

export interface DecodedSwap {
  method: string;
  protocol: string;
  tokenIn: Token;
  tokenOut: Token;
  amountIn: BigNumberish;
  amountOutMin: BigNumberish;
  recipient: string;
  deadline: number;
  path: string[];
  fee?: number;
}

export interface MEVOpportunity {
  type: 'sandwich' | 'frontrun' | 'arbitrage' | 'flashloan' | 'mev-share' | 'multi-block';
  victimTx?: Transaction;
  decodedSwap?: DecodedSwap | null;
  pool?: Pool | null;
  estimatedProfit: BigNumberish;
  gasEstimate: BigNumberish;
  frontRunTx?: Transaction;
  backRunTx?: Transaction;
  frontRunAmount?: BigNumberish;
  backRunAmount?: BigNumberish;
  confidence: number;
  timestamp: number;
  // Additional properties for different strategy types
  transactions?: Transaction[];
  blocks?: number;
  strategy?: string;
  // Arbitrage-specific properties
  arbitrageRoute?: ArbitrageRoute;
  triggerTx?: string;
}

export interface Bundle {
  transactions: Transaction[];
  blockNumber: number;
  minTimestamp?: number;
  maxTimestamp?: number;
  revertingTxHashes?: string[];
}

export interface SimulationResult {
  success: boolean;
  gasUsed: BigNumberish;
  profit: BigNumberish;
  error?: string;
  bundleHash?: string;
}

export interface GasStrategy {
  baseFee: BigNumberish;
  priorityFee: BigNumberish;
  maxFeePerGas: BigNumberish;
  gasLimit: BigNumberish;
}

export interface ArbitrageRoute {
  pools: Pool[];
  tokens: Token[];
  expectedProfit: BigNumberish;
  gasEstimate: BigNumberish;
  confidence: number;
}

export interface FlashloanRoute {
  flashloanToken: Token;
  flashloanAmount: BigNumberish;
  flashloanPremium: BigNumberish;
  arbitrageRoute: ArbitrageRoute;
  expectedProfit: BigNumberish;
  gasEstimate: BigNumberish;
  confidence: number;
}

export interface FlashloanParams {
  asset: string;
  amount: BigNumberish;
  premium: BigNumberish;
  initiator: string;
  params: string;
}

export interface LiquidityData {
  totalLiquidity: BigNumberish;
  priceImpact: number;
  optimalAmountIn: BigNumberish;
  expectedAmountOut: BigNumberish;
}

export interface MempoolFilter {
  minValue: BigNumberish;
  maxGasPrice: BigNumberish;
  targetTokens: string[];
  targetPools: string[];
  excludeAddresses: string[];
}

export interface BotState {
  isRunning: boolean;
  totalProfit: BigNumberish;
  successfulTrades: number;
  failedTrades: number;
  lastActivity: number;
  emergencyStop: boolean;
}

export interface RiskMetrics {
  maxDrawdown: BigNumberish;
  winRate: number;
  averageProfit: BigNumberish;
  totalGasSpent: BigNumberish;
  profitFactor: number;
}

export enum LogLevel {
  ERROR = 'error',
  WARN = 'warn',
  INFO = 'info',
  SYSTEM = 'system',
  DEBUG = 'debug'
}

export interface LogEntry {
  level: LogLevel;
  message: string;
  timestamp: number;
  data?: any;
}
