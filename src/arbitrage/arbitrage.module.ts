import { Module } from '@nestjs/common';
import { ConfigurationModule } from '@config/configuration.module';
import { ContractsModule } from '@contracts/contracts.module';
import { ArbitrageService } from './services/arbitrage.service';
import { OpportunityDetectorService } from './services/opportunity-detector.service';
import { PriceService } from './services/price.service';
import { DexService } from './services/dex.service';
import { FlashloanStrategyService } from './services/flashloan-strategy.service';
import { RiskManagerService } from './services/risk-manager.service';

@Module({
  imports: [ConfigurationModule, ContractsModule],
  providers: [
    ArbitrageService,
    OpportunityDetectorService,
    PriceService,
    DexService,
    FlashloanStrategyService,
    RiskManagerService,
  ],
  exports: [
    ArbitrageService,
    OpportunityDetectorService,
    PriceService,
    DexService,
    FlashloanStrategyService,
    RiskManagerService,
  ],
})
export class ArbitrageModule {}
