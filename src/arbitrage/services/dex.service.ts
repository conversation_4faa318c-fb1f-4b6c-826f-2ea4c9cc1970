import { Injectable } from '@nestjs/common';
import { LoggerService } from '@shared/services/logger.service';
import { DEXType, PoolInfo } from '@shared/types';

@Injectable()
export class DexService {
  private readonly dexConfigs = new Map<string, { type: DEXType; router: string }>();

  constructor(private readonly logger: LoggerService) {
    this.initializeDexConfigs();
  }

  private initializeDexConfigs() {
    // Mock DEX configurations - in real implementation, these would be from config
    this.dexConfigs.set('UNISWAP_V2', {
      type: DEXType.V2,
      router: '0x7a250d5630B4cF539739dF2C5dAcb4c659F2488D',
    });

    this.dexConfigs.set('UNISWAP_V3', {
      type: DEXType.V3,
      router: '0xE592427A0AEce92De3Edee1F18E0157C05861564',
    });

    this.dexConfigs.set('SUSHISWAP', {
      type: DEXType.V2,
      router: '0xd9e1cE17f2641f24aE83637ab66a2cca9C378B9F',
    });
  }

  getDexConfig(dexName: string) {
    return this.dexConfigs.get(dexName);
  }

  getAllDexes(): string[] {
    return Array.from(this.dexConfigs.keys());
  }

  getDexType(dexName: string): DEXType | null {
    const config = this.dexConfigs.get(dexName);
    return config ? config.type : null;
  }

  getDexRouter(dexName: string): string | null {
    const config = this.dexConfigs.get(dexName);
    return config ? config.router : null;
  }
}
