import { Injectable } from '@nestjs/common';
import { LoggerService } from '@shared/services/logger.service';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { PriceData } from '@shared/types';

@Injectable()
export class PriceService {
  private priceCache = new Map<string, PriceData>();
  private isMonitoring = false;

  constructor(
    private readonly logger: LoggerService,
    private readonly eventEmitter: EventEmitter2,
  ) {}

  async startPriceMonitoring() {
    if (this.isMonitoring) return;
    
    this.isMonitoring = true;
    this.logger.info('Price monitoring started');
  }

  async stopPriceMonitoring() {
    this.isMonitoring = false;
    this.logger.info('Price monitoring stopped');
  }

  async getPrice(tokenA: string, tokenB: string, dex: string): Promise<PriceData> {
    const key = `${tokenA}-${tokenB}-${dex}`;
    const cached = this.priceCache.get(key);
    
    if (cached && Date.now() - cached.timestamp < 30000) {
      return cached;
    }

    // Mock price data for now - in real implementation, this would fetch from DEX
    const mockPrice: PriceData = {
      token: `${tokenA}/${tokenB}`,
      price: (Math.random() * 2000 + 1000).toString(),
      dex,
      timestamp: Date.now(),
      blockNumber: 0,
    };

    this.priceCache.set(key, mockPrice);
    return mockPrice;
  }

  getPriceCache(): Map<string, PriceData> {
    return new Map(this.priceCache);
  }
}
