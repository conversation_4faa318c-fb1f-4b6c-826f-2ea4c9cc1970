import { Injectable } from '@nestjs/common';
import { LoggerService } from '@shared/services/logger.service';
import { ArbitrageOpportunity, FlashloanProvider, TradeStep, DEXType } from '@shared/types';
import { DexService } from './dex.service';

@Injectable()
export class FlashloanStrategyService {
  constructor(
    private readonly logger: LoggerService,
    private readonly dexService: DexService,
  ) {}

  async createTradeSteps(opportunity: ArbitrageOpportunity): Promise<TradeStep[]> {
    const buyDexType = this.dexService.getDexType(opportunity.buyDex);
    const sellDexType = this.dexService.getDexType(opportunity.sellDex);
    
    if (!buyDexType || !sellDexType) {
      throw new Error('Unknown DEX type');
    }

    const tradeSteps: TradeStep[] = [
      {
        dex: this.dexService.getDexRouter(opportunity.buyDex)!,
        dexType: buyDexType,
        tokenIn: opportunity.tokenA,
        tokenOut: opportunity.tokenB,
        slippageToleranceBps: 100, // 1%
        v3Fee: buyDexType === DEXType.V3 ? 3000 : undefined,
      },
      {
        dex: this.dexService.getDexRouter(opportunity.sellDex)!,
        dexType: sellDexType,
        tokenIn: opportunity.tokenB,
        tokenOut: opportunity.tokenA,
        slippageToleranceBps: 100, // 1%
        v3Fee: sellDexType === DEXType.V3 ? 3000 : undefined,
      },
    ];

    return tradeSteps;
  }

  async createManualTradeSteps(
    tokenA: string,
    tokenB: string,
    buyDex: string,
    sellDex: string
  ): Promise<TradeStep[]> {
    const buyDexType = this.dexService.getDexType(buyDex);
    const sellDexType = this.dexService.getDexType(sellDex);
    
    if (!buyDexType || !sellDexType) {
      throw new Error('Unknown DEX type');
    }

    return [
      {
        dex: this.dexService.getDexRouter(buyDex)!,
        dexType: buyDexType,
        tokenIn: tokenA,
        tokenOut: tokenB,
        slippageToleranceBps: 100,
        v3Fee: buyDexType === DEXType.V3 ? 3000 : undefined,
      },
      {
        dex: this.dexService.getDexRouter(sellDex)!,
        dexType: sellDexType,
        tokenIn: tokenB,
        tokenOut: tokenA,
        slippageToleranceBps: 100,
        v3Fee: sellDexType === DEXType.V3 ? 3000 : undefined,
      },
    ];
  }

  async selectOptimalProvider(
    asset: string,
    profitEstimate: string
  ): Promise<FlashloanProvider> {
    // Simple strategy: use AAVE for larger amounts, Balancer for smaller
    const profit = parseFloat(profitEstimate);
    return profit > 0.5 ? FlashloanProvider.AAVE : FlashloanProvider.BALANCER;
  }
}
