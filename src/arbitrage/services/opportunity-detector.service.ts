import { Injectable } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { LoggerService } from '@shared/services/logger.service';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { ArbitrageConfigService } from '@config/arbitrage-config.service';
import { PriceService } from './price.service';
import { DexService } from './dex.service';
import { UtilsService } from '@shared/services/utils.service';
import { ArbitrageOpportunity, PriceData, PoolInfo } from '@shared/types';

@Injectable()
export class OpportunityDetectorService {
  private isRunning = false;
  private monitoredPairs: Array<{ tokenA: string; tokenB: string }> = [];
  private lastOpportunityCheck = 0;
  private readonly minCheckInterval = 5000; // 5 seconds minimum between checks

  constructor(
    private readonly logger: LoggerService,
    private readonly eventEmitter: EventEmitter2,
    private readonly arbitrageConfig: ArbitrageConfigService,
    private readonly priceService: PriceService,
    private readonly dexService: DexService,
    private readonly utils: UtilsService,
  ) {
    this.initializeMonitoredPairs();
  }

  private initializeMonitoredPairs() {
    const tokens = this.arbitrageConfig.monitoredTokens;
    
    // Create all possible pairs
    for (let i = 0; i < tokens.length; i++) {
      for (let j = i + 1; j < tokens.length; j++) {
        this.monitoredPairs.push({
          tokenA: tokens[i],
          tokenB: tokens[j],
        });
      }
    }

    this.logger.info(`Initialized ${this.monitoredPairs.length} monitored pairs`);
  }

  async start() {
    if (this.isRunning) {
      return;
    }

    this.isRunning = true;
    this.logger.info('Opportunity detector started');
    
    // Start price monitoring
    await this.priceService.startPriceMonitoring();
    
    // Initial opportunity scan
    await this.scanForOpportunities();
    
    this.eventEmitter.emit('opportunity_detector.started');
  }

  async stop() {
    if (!this.isRunning) {
      return;
    }

    this.isRunning = false;
    await this.priceService.stopPriceMonitoring();
    
    this.logger.info('Opportunity detector stopped');
    this.eventEmitter.emit('opportunity_detector.stopped');
  }

  @Cron(CronExpression.EVERY_10_SECONDS)
  async scheduledOpportunityScan() {
    if (!this.isRunning) {
      return;
    }

    const now = Date.now();
    if (now - this.lastOpportunityCheck < this.minCheckInterval) {
      return;
    }

    await this.scanForOpportunities();
  }

  private async scanForOpportunities() {
    if (!this.isRunning) {
      return;
    }

    this.lastOpportunityCheck = Date.now();

    try {
      this.logger.debug('Scanning for arbitrage opportunities');

      const opportunities: ArbitrageOpportunity[] = [];

      for (const pair of this.monitoredPairs) {
        const pairOpportunities = await this.scanPairForOpportunities(pair.tokenA, pair.tokenB);
        opportunities.push(...pairOpportunities);
      }

      // Filter and rank opportunities
      const validOpportunities = opportunities
        .filter(opp => this.isOpportunityValid(opp))
        .sort((a, b) => parseFloat(b.profitEstimate) - parseFloat(a.profitEstimate));

      if (validOpportunities.length > 0) {
        this.logger.arbitrage(`Found ${validOpportunities.length} arbitrage opportunities`);
        
        // Emit the best opportunities
        for (const opportunity of validOpportunities.slice(0, 5)) {
          this.eventEmitter.emit('arbitrage.opportunity', opportunity);
        }
      }

    } catch (error) {
      this.logger.error('Error scanning for opportunities', error);
    }
  }

  private async scanPairForOpportunities(
    tokenA: string, 
    tokenB: string
  ): Promise<ArbitrageOpportunity[]> {
    try {
      const opportunities: ArbitrageOpportunity[] = [];
      const supportedDexes = this.arbitrageConfig.supportedDexes;

      // Get prices from all DEXes
      const pricePromises = supportedDexes.map(async (dex) => {
        try {
          const price = await this.priceService.getPrice(tokenA, tokenB, dex);
          return { dex, price };
        } catch (error) {
          return null;
        }
      });

      const priceResults = (await Promise.allSettled(pricePromises))
        .map(result => result.status === 'fulfilled' ? result.value : null)
        .filter(result => result !== null);

      if (priceResults.length < 2) {
        return opportunities; // Need at least 2 DEXes for arbitrage
      }

      // Find arbitrage opportunities
      for (let i = 0; i < priceResults.length; i++) {
        for (let j = i + 1; j < priceResults.length; j++) {
          const buyDex = priceResults[i];
          const sellDex = priceResults[j];

          if (!buyDex || !sellDex) continue;

          // Check both directions
          const opportunity1 = await this.calculateOpportunity(
            tokenA, tokenB, buyDex, sellDex
          );
          
          const opportunity2 = await this.calculateOpportunity(
            tokenA, tokenB, sellDex, buyDex
          );

          if (opportunity1) opportunities.push(opportunity1);
          if (opportunity2) opportunities.push(opportunity2);
        }
      }

      return opportunities;

    } catch (error) {
      this.logger.error(`Error scanning pair ${tokenA}/${tokenB}`, error);
      return [];
    }
  }

  private async calculateOpportunity(
    tokenA: string,
    tokenB: string,
    buyDex: { dex: string; price: PriceData },
    sellDex: { dex: string; price: PriceData }
  ): Promise<ArbitrageOpportunity | null> {
    try {
      const buyPrice = parseFloat(buyDex.price.price);
      const sellPrice = parseFloat(sellDex.price.price);

      // Calculate price difference
      const priceDiff = sellPrice - buyPrice;
      const priceDiffPercent = (priceDiff / buyPrice) * 100;

      // Minimum profit threshold check
      const minProfitThreshold = parseFloat(this.arbitrageConfig.minProfitThreshold);
      if (priceDiffPercent < minProfitThreshold * 100) {
        return null;
      }

      // Estimate gas cost
      const gasEstimate = await this.estimateGasCost(tokenA, tokenB, buyDex.dex, sellDex.dex);
      
      // Calculate profit estimate (simplified)
      const tradeAmount = parseFloat(this.arbitrageConfig.minTradeAmount);
      const grossProfit = tradeAmount * (priceDiff / buyPrice);
      const gasCostEth = parseFloat(this.utils.formatEther(gasEstimate));
      const netProfit = grossProfit - gasCostEth;

      if (netProfit <= 0) {
        return null;
      }

      // Calculate confidence score
      const confidence = this.calculateConfidence(buyDex.price, sellDex.price, priceDiffPercent);

      const opportunity: ArbitrageOpportunity = {
        id: this.utils.generateId(),
        tokenA,
        tokenB,
        buyDex: buyDex.dex,
        sellDex: sellDex.dex,
        buyPrice: buyPrice.toString(),
        sellPrice: sellPrice.toString(),
        profitEstimate: netProfit.toString(),
        gasEstimate,
        timestamp: Date.now(),
        confidence,
      };

      return opportunity;

    } catch (error) {
      this.logger.error('Error calculating opportunity', error);
      return null;
    }
  }

  private async estimateGasCost(
    tokenA: string,
    tokenB: string,
    buyDex: string,
    sellDex: string
  ): Promise<string> {
    try {
      // Simplified gas estimation
      // In a real implementation, this would simulate the actual transaction
      
      const baseGas = 200000; // Base gas for flashloan
      const swapGas = 150000; // Gas per swap
      const totalGas = baseGas + (2 * swapGas); // 2 swaps for arbitrage

      // Get current gas price
      const gasPrice = '20000000000'; // 20 gwei default
      
      return (BigInt(totalGas) * BigInt(gasPrice)).toString();

    } catch (error) {
      this.logger.error('Error estimating gas cost', error);
      return '6000000000000000'; // 0.006 ETH fallback
    }
  }

  private calculateConfidence(
    buyPrice: PriceData,
    sellPrice: PriceData,
    priceDiffPercent: number
  ): number {
    let confidence = 50; // Base confidence

    // Higher confidence for larger price differences
    if (priceDiffPercent > 5) confidence += 30;
    else if (priceDiffPercent > 2) confidence += 20;
    else if (priceDiffPercent > 1) confidence += 10;

    // Higher confidence for recent prices
    const now = Date.now();
    const buyAge = now - buyPrice.timestamp;
    const sellAge = now - sellPrice.timestamp;
    const maxAge = Math.max(buyAge, sellAge);

    if (maxAge < 10000) confidence += 20; // Less than 10 seconds
    else if (maxAge < 30000) confidence += 10; // Less than 30 seconds
    else if (maxAge > 60000) confidence -= 20; // More than 1 minute

    // Clamp confidence between 0 and 100
    return Math.max(0, Math.min(100, confidence));
  }

  private isOpportunityValid(opportunity: ArbitrageOpportunity): boolean {
    // Check minimum profit
    const profit = parseFloat(opportunity.profitEstimate);
    const minProfit = parseFloat(this.arbitrageConfig.minProfitThreshold);
    
    if (profit < minProfit) {
      return false;
    }

    // Check confidence threshold
    if (opportunity.confidence < 60) {
      return false;
    }

    // Check age
    const age = Date.now() - opportunity.timestamp;
    if (age > 30000) { // 30 seconds max age
      return false;
    }

    return true;
  }

  // Manual opportunity detection for testing
  async detectOpportunityForPair(
    tokenA: string, 
    tokenB: string
  ): Promise<ArbitrageOpportunity[]> {
    return await this.scanPairForOpportunities(tokenA, tokenB);
  }

  getMonitoredPairs(): Array<{ tokenA: string; tokenB: string }> {
    return [...this.monitoredPairs];
  }

  addMonitoredPair(tokenA: string, tokenB: string) {
    const exists = this.monitoredPairs.some(
      pair => (pair.tokenA === tokenA && pair.tokenB === tokenB) ||
              (pair.tokenA === tokenB && pair.tokenB === tokenA)
    );

    if (!exists) {
      this.monitoredPairs.push({ tokenA, tokenB });
      this.logger.info(`Added monitored pair: ${tokenA}/${tokenB}`);
    }
  }

  removeMonitoredPair(tokenA: string, tokenB: string) {
    this.monitoredPairs = this.monitoredPairs.filter(
      pair => !((pair.tokenA === tokenA && pair.tokenB === tokenB) ||
                (pair.tokenA === tokenB && pair.tokenB === tokenA))
    );
    this.logger.info(`Removed monitored pair: ${tokenA}/${tokenB}`);
  }

  getStatus() {
    return {
      isRunning: this.isRunning,
      monitoredPairsCount: this.monitoredPairs.length,
      lastCheck: this.lastOpportunityCheck,
      timeSinceLastCheck: Date.now() - this.lastOpportunityCheck,
    };
  }
}
