import { Injectable } from '@nestjs/common';
import { LoggerService } from '@shared/services/logger.service';
import { ArbitrageConfigService } from '@config/arbitrage-config.service';
import { ArbitrageOpportunity } from '@shared/types';

export interface RiskAssessment {
  approved: boolean;
  reason?: string;
  riskScore: number;
}

@Injectable()
export class RiskManagerService {
  private consecutiveFailures = 0;
  private dailyLoss = 0;
  private dailyProfit = 0;
  private lastResetDate = new Date().toDateString();

  constructor(
    private readonly logger: LoggerService,
    private readonly arbitrageConfig: ArbitrageConfigService,
  ) {}

  async assessOpportunity(opportunity: ArbitrageOpportunity): Promise<RiskAssessment> {
    this.resetDailyMetricsIfNeeded();

    let riskScore = 0;
    const reasons: string[] = [];

    // Check consecutive failures
    if (this.consecutiveFailures >= this.arbitrageConfig.maxConsecutiveFailures) {
      return {
        approved: false,
        reason: `Too many consecutive failures: ${this.consecutiveFailures}`,
        riskScore: 100,
      };
    }

    // Check daily loss limit
    const dailyLossLimit = parseFloat(this.arbitrageConfig.dailyLossLimit);
    if (this.dailyLoss >= dailyLossLimit) {
      return {
        approved: false,
        reason: `Daily loss limit reached: ${this.dailyLoss} ETH`,
        riskScore: 100,
      };
    }

    // Assess profit estimate
    const profit = parseFloat(opportunity.profitEstimate);
    const minProfit = parseFloat(this.arbitrageConfig.minProfitThreshold);
    
    if (profit < minProfit) {
      riskScore += 30;
      reasons.push('Low profit estimate');
    }

    // Assess confidence
    if (opportunity.confidence < 70) {
      riskScore += 20;
      reasons.push('Low confidence score');
    }

    // Assess gas cost vs profit ratio
    const gasCostEth = parseFloat(opportunity.gasEstimate) / 1e18;
    const profitToGasRatio = profit / gasCostEth;
    
    if (profitToGasRatio < 2) {
      riskScore += 25;
      reasons.push('Poor profit to gas ratio');
    }

    // Check opportunity age
    const age = Date.now() - opportunity.timestamp;
    if (age > 20000) { // 20 seconds
      riskScore += 15;
      reasons.push('Stale opportunity');
    }

    const approved = riskScore < 50;
    
    if (!approved) {
      this.logger.warn(`Opportunity rejected: ${reasons.join(', ')}`);
    }

    return {
      approved,
      reason: reasons.join(', '),
      riskScore,
    };
  }

  recordFailure(reason?: string) {
    this.consecutiveFailures++;
    this.logger.warn(`Recorded failure (${this.consecutiveFailures} consecutive): ${reason}`);
  }

  recordProfit(profit: number) {
    this.consecutiveFailures = 0;
    this.dailyProfit += profit;
    this.logger.info(`Recorded profit: ${profit} ETH (daily total: ${this.dailyProfit})`);
  }

  recordLoss(loss: number) {
    this.consecutiveFailures++;
    this.dailyLoss += loss;
    this.logger.warn(`Recorded loss: ${loss} ETH (daily total: ${this.dailyLoss})`);
  }

  private resetDailyMetricsIfNeeded() {
    const today = new Date().toDateString();
    if (today !== this.lastResetDate) {
      this.dailyLoss = 0;
      this.dailyProfit = 0;
      this.lastResetDate = today;
      this.logger.info('Daily risk metrics reset');
    }
  }

  getRiskMetrics() {
    return {
      consecutiveFailures: this.consecutiveFailures,
      dailyLoss: this.dailyLoss,
      dailyProfit: this.dailyProfit,
      netDailyPnL: this.dailyProfit - this.dailyLoss,
    };
  }

  reset() {
    this.consecutiveFailures = 0;
    this.dailyLoss = 0;
    this.dailyProfit = 0;
    this.logger.info('Risk metrics reset');
  }
}
