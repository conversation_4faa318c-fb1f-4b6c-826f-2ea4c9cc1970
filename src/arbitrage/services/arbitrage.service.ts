import { Injectable, OnModuleInit } from '@nestjs/common';
import { LoggerService } from '@shared/services/logger.service';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { ArbitrageConfigService } from '@config/arbitrage-config.service';
import { FlashloanContractService } from '@contracts/services/flashloan-contract.service';
import { OpportunityDetectorService } from './opportunity-detector.service';
import { FlashloanStrategyService } from './flashloan-strategy.service';
import { RiskManagerService } from './risk-manager.service';
import { MetricsService } from '@shared/services/metrics.service';
import { 
  ArbitrageOpportunity, 
  ExecutionResult, 
  FlashloanProvider,
  TradeStep 
} from '@shared/types';

@Injectable()
export class ArbitrageService implements OnModuleInit {
  private isRunning = false;
  private executionQueue: ArbitrageOpportunity[] = [];
  private readonly maxConcurrentExecutions = 3;
  private currentExecutions = 0;

  constructor(
    private readonly logger: LoggerService,
    private readonly eventEmitter: EventEmitter2,
    private readonly arbitrageConfig: ArbitrageConfigService,
    private readonly flashloanContract: FlashloanContractService,
    private readonly opportunityDetector: OpportunityDetectorService,
    private readonly flashloanStrategy: FlashloanStrategyService,
    private readonly riskManager: RiskManagerService,
    private readonly metrics: MetricsService,
  ) {}

  async onModuleInit() {
    this.setupEventListeners();
  }

  private setupEventListeners() {
    // Listen for new arbitrage opportunities
    this.eventEmitter.on('arbitrage.opportunity', async (opportunity: ArbitrageOpportunity) => {
      await this.handleOpportunity(opportunity);
    });

    // Listen for flashloan execution results
    this.eventEmitter.on('arbitrage.completed', (result) => {
      this.handleExecutionResult(result);
    });
  }

  async start() {
    if (this.isRunning) {
      return;
    }

    if (!this.arbitrageConfig.enableArbitrage) {
      this.logger.warn('Arbitrage is disabled in configuration');
      return;
    }

    this.isRunning = true;
    this.logger.arbitrage('Arbitrage service started');
    
    // Start opportunity detection
    await this.opportunityDetector.start();
    
    this.eventEmitter.emit('arbitrage.started');
  }

  async stop() {
    if (!this.isRunning) {
      return;
    }

    this.isRunning = false;
    this.executionQueue = [];
    
    await this.opportunityDetector.stop();
    
    this.logger.arbitrage('Arbitrage service stopped');
    this.eventEmitter.emit('arbitrage.stopped');
  }

  private async handleOpportunity(opportunity: ArbitrageOpportunity) {
    if (!this.isRunning) {
      return;
    }

    try {
      this.logger.arbitrage(`New opportunity detected: ${opportunity.tokenA}/${opportunity.tokenB}`);
      this.metrics.recordOpportunity();

      // Risk assessment
      const riskAssessment = await this.riskManager.assessOpportunity(opportunity);
      if (!riskAssessment.approved) {
        this.logger.warn(`Opportunity rejected by risk manager: ${riskAssessment.reason}`);
        return;
      }

      // Check if we can execute immediately or queue
      if (this.currentExecutions < this.maxConcurrentExecutions) {
        await this.executeOpportunity(opportunity);
      } else {
        this.queueOpportunity(opportunity);
      }

    } catch (error) {
      this.logger.error('Error handling arbitrage opportunity', error);
    }
  }

  private queueOpportunity(opportunity: ArbitrageOpportunity) {
    // Add to queue with priority based on profit estimate
    this.executionQueue.push(opportunity);
    this.executionQueue.sort((a, b) => 
      parseFloat(b.profitEstimate) - parseFloat(a.profitEstimate)
    );

    // Limit queue size
    if (this.executionQueue.length > 10) {
      this.executionQueue = this.executionQueue.slice(0, 10);
    }

    this.logger.arbitrage(`Opportunity queued (${this.executionQueue.length} in queue)`);
  }

  private async executeOpportunity(opportunity: ArbitrageOpportunity) {
    this.currentExecutions++;
    const startTime = Date.now();

    try {
      this.logger.arbitrage(`Executing arbitrage: ${opportunity.id}`);

      // Create trade steps for the flashloan contract
      const tradeSteps = await this.flashloanStrategy.createTradeSteps(opportunity);
      
      // Determine optimal flashloan provider
      const provider = await this.flashloanStrategy.selectOptimalProvider(
        opportunity.tokenA,
        opportunity.profitEstimate
      );

      // Calculate trade amount
      const tradeAmount = await this.calculateOptimalTradeAmount(opportunity);

      // Execute flashloan arbitrage
      const result = await this.flashloanContract.executeFlashloan(
        opportunity.tokenA,
        tradeAmount,
        {
          tradeSteps,
          minProfit: this.arbitrageConfig.minProfitThreshold,
          provider,
          maxGasCostWei: opportunity.gasEstimate,
        }
      );

      const executionTime = Date.now() - startTime;

      if (result.success) {
        this.logger.arbitrage(`Arbitrage executed successfully: ${result.transactionHash}`);
        this.metrics.recordSuccessfulTrade(result.profit || '0', executionTime);
      } else {
        this.logger.error(`Arbitrage execution failed: ${result.error}`);
        this.metrics.recordFailedTrade('0', executionTime, result.error);
      }

      this.eventEmitter.emit('arbitrage.execution_completed', {
        opportunity,
        result,
        executionTime,
      });

    } catch (error) {
      const executionTime = Date.now() - startTime;
      this.logger.error(`Arbitrage execution error for ${opportunity.id}`, error);
      this.metrics.recordFailedTrade('0', executionTime, error.message);
    } finally {
      this.currentExecutions--;
      await this.processQueue();
    }
  }

  private async processQueue() {
    if (this.executionQueue.length > 0 && this.currentExecutions < this.maxConcurrentExecutions) {
      const nextOpportunity = this.executionQueue.shift()!;
      
      // Check if opportunity is still valid (not too old)
      const age = Date.now() - nextOpportunity.timestamp;
      if (age < 30000) { // 30 seconds max age
        await this.executeOpportunity(nextOpportunity);
      } else {
        this.logger.warn(`Discarded stale opportunity: ${nextOpportunity.id}`);
        await this.processQueue(); // Try next in queue
      }
    }
  }

  private async calculateOptimalTradeAmount(opportunity: ArbitrageOpportunity): Promise<string> {
    try {
      // Start with a base amount and optimize
      const maxAmount = this.arbitrageConfig.maxTradeAmount;
      const minAmount = this.arbitrageConfig.minTradeAmount;
      
      // For now, use a simple heuristic based on profit estimate
      const profitEth = parseFloat(opportunity.profitEstimate);
      
      if (profitEth > 1.0) {
        return maxAmount;
      } else if (profitEth > 0.1) {
        return (parseFloat(maxAmount) * 0.5).toString();
      } else {
        return minAmount;
      }

    } catch (error) {
      this.logger.error('Error calculating optimal trade amount', error);
      return this.arbitrageConfig.minTradeAmount;
    }
  }

  private handleExecutionResult(result: any) {
    this.logger.arbitrage(`Flashloan execution result received: ${result.transactionHash}`);
    
    // Update metrics and risk management
    if (result.profit) {
      const profitEth = parseFloat(result.profit);
      this.riskManager.recordProfit(profitEth);
    }
  }

  // Manual execution methods for testing/debugging
  async executeManualArbitrage(
    tokenA: string,
    tokenB: string,
    buyDex: string,
    sellDex: string,
    amount: string,
    provider: FlashloanProvider = FlashloanProvider.AAVE
  ): Promise<ExecutionResult> {
    try {
      this.logger.arbitrage(`Manual arbitrage execution: ${tokenA}/${tokenB}`);

      const tradeSteps: TradeStep[] = await this.flashloanStrategy.createManualTradeSteps(
        tokenA,
        tokenB,
        buyDex,
        sellDex
      );

      const result = await this.flashloanContract.executeFlashloan(
        tokenA,
        amount,
        {
          tradeSteps,
          minProfit: '0', // No minimum for manual execution
          provider,
          maxGasCostWei: '50000000000000000', // 0.05 ETH max gas cost
        }
      );

      return {
        success: result.success,
        transactionHash: result.transactionHash,
        blockNumber: result.blockNumber,
        gasUsed: result.gasUsed,
        profit: result.profit,
        error: result.error,
        timestamp: Date.now(),
      };

    } catch (error) {
      this.logger.error('Manual arbitrage execution failed', error);
      return {
        success: false,
        transactionHash: '',
        blockNumber: 0,
        gasUsed: '0',
        error: error.message,
        timestamp: Date.now(),
      };
    }
  }

  getStatus() {
    return {
      isRunning: this.isRunning,
      currentExecutions: this.currentExecutions,
      queueLength: this.executionQueue.length,
      maxConcurrentExecutions: this.maxConcurrentExecutions,
    };
  }

  getQueuedOpportunities(): ArbitrageOpportunity[] {
    return [...this.executionQueue];
  }

  clearQueue() {
    this.executionQueue = [];
    this.logger.arbitrage('Execution queue cleared');
  }
}
