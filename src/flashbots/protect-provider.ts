import { ethers } from 'ethers';
import { config } from '../config';
import { logger } from '../utils/logger';

export interface FlashbotsProtectTransaction {
    to: string;
    data: string;
    value?: string;
    gasLimit?: string;
    maxFeePerGas?: string;
    maxPriorityFeePerGas?: string;
    nonce?: number;
}

export interface FlashbotsProtectResponse {
    success: boolean;
    txHash?: string;
    error?: string;
    fastMode?: boolean;
    maxBlockNumber?: number;
}

export interface FlashbotsProtectOptions {
    fastMode?: boolean;
    maxBlockNumber?: number;
    hints?: string[];
    builders?: string[];
}

/**
 * Flashbots Protect RPC Provider
 * Provides private transaction submission with MEV protection
 * - Transactions are not visible in public mempool
 * - No gas fees for failed transactions
 * - Direct submission to block builders
 */
export class FlashbotsProtectProvider {
    private provider: ethers.JsonRpcProvider;
    private wallet: ethers.Wallet;
    private protectRpcUrl: string;
    private protectProvider: ethers.JsonRpcProvider | null = null;

    constructor(provider: ethers.JsonRpcProvider, wallet: ethers.Wallet) {
        this.provider = provider;
        this.wallet = wallet;
        this.protectRpcUrl = config.flashbotsProtectRpcUrl || 'https://rpc.flashbots.net';
    }

    /**
     * Initialize Flashbots Protect provider
     */
    async initialize(): Promise<void> {
        try {
            // Only initialize on mainnet
            if (config.chainId !== 1) {
                logger.warn('⚠️  Flashbots Protect only available on mainnet');
                return;
            }

            // Create Protect RPC provider
            this.protectProvider = new ethers.JsonRpcProvider(this.protectRpcUrl);

            // Test connection
            await this.protectProvider.getBlockNumber();

            logger.system('🛡️  Flashbots Protect RPC initialized');
            logger.system(`   Protect RPC: ${this.protectRpcUrl}`);
            logger.system('   ✅ Private transaction submission enabled');
            logger.system('   ✅ No gas fees for failed transactions');

        } catch (error) {
            logger.logError(error as Error, 'FlashbotsProtectProvider.initialize');
            logger.error('❌ Failed to initialize Flashbots Protect RPC');
        }
    }

    /**
     * Check if Flashbots Protect is available
     */
    isAvailable(): boolean {
        return this.protectProvider !== null && config.chainId === 1 && config.enableFlashbotsProtect;
    }

    /**
     * Submit transaction via Flashbots Protect RPC
     */
    async submitTransaction(
        transaction: FlashbotsProtectTransaction,
        options?: FlashbotsProtectOptions
    ): Promise<FlashbotsProtectResponse> {
        try {
            if (!this.protectProvider) {
                return { success: false, error: 'Flashbots Protect provider not initialized' };
            }

            logger.system('🛡️  Submitting transaction via Flashbots Protect...');

            // Get current nonce if not provided
            if (transaction.nonce === undefined) {
                transaction.nonce = await this.provider.getTransactionCount(this.wallet.address, 'pending');
            }

            // Prepare transaction with Protect-specific parameters
            const protectTx = {
                ...transaction,
                from: this.wallet.address,
                chainId: config.chainId
            };

            // Add Protect-specific options
            const protectOptions = {
                fastMode: options?.fastMode ?? config.flashbotsProtectFastMode ?? true,
                maxBlockNumber: options?.maxBlockNumber ?? config.flashbotsProtectMaxBlockNumber ?? 25,
                ...options
            };

            logger.system('   Transaction details:', {
                to: protectTx.to,
                value: protectTx.value || '0',
                gasLimit: protectTx.gasLimit,
                nonce: protectTx.nonce,
                fastMode: protectOptions.fastMode,
                maxBlockNumber: protectOptions.maxBlockNumber
            });

            // Sign the transaction
            const signedTx = await this.wallet.signTransaction(protectTx);

            // Submit via Protect RPC with custom method
            const response = await this.protectProvider.send('eth_sendRawTransaction', [signedTx]);

            if (response) {
                logger.system('✅ Transaction submitted via Flashbots Protect', {
                    txHash: response,
                    fastMode: protectOptions.fastMode,
                    maxBlockNumber: protectOptions.maxBlockNumber
                });

                return {
                    success: true,
                    txHash: response,
                    fastMode: protectOptions.fastMode,
                    maxBlockNumber: protectOptions.maxBlockNumber
                };
            } else {
                return { success: false, error: 'No transaction hash returned' };
            }

        } catch (error) {
            const errorMessage = (error as Error).message;
            logger.error('❌ Flashbots Protect submission failed', {
                error: errorMessage,
                to: transaction.to
            });

            return { success: false, error: errorMessage };
        }
    }

    /**
     * Submit MEV-protected transaction with enhanced options
     */
    async submitMEVProtectedTransaction(
        transaction: FlashbotsProtectTransaction,
        options?: {
            fastMode?: boolean;
            maxBlockNumber?: number;
            hints?: string[];
            builders?: string[];
            urgency?: 'low' | 'medium' | 'high' | 'urgent';
        }
    ): Promise<FlashbotsProtectResponse> {
        try {
            // Adjust parameters based on urgency
            const urgency = options?.urgency || 'medium';
            let protectOptions: FlashbotsProtectOptions = {
                fastMode: options?.fastMode ?? true,
                maxBlockNumber: options?.maxBlockNumber ?? 25,
                hints: options?.hints,
                builders: options?.builders
            };

            // Adjust based on urgency
            switch (urgency) {
                case 'urgent':
                    protectOptions.fastMode = true;
                    protectOptions.maxBlockNumber = 50; // More blocks for urgent transactions
                    break;
                case 'high':
                    protectOptions.fastMode = true;
                    protectOptions.maxBlockNumber = 35;
                    break;
                case 'medium':
                    protectOptions.fastMode = true;
                    protectOptions.maxBlockNumber = 25;
                    break;
                case 'low':
                    protectOptions.fastMode = false;
                    protectOptions.maxBlockNumber = 15;
                    break;
            }

            logger.system(`🛡️  Submitting ${urgency} priority MEV-protected transaction...`);

            return await this.submitTransaction(transaction, protectOptions);

        } catch (error) {
            logger.logError(error as Error, 'FlashbotsProtectProvider.submitMEVProtectedTransaction');
            return { success: false, error: (error as Error).message };
        }
    }

    /**
     * Check transaction status via Protect RPC
     */
    async getTransactionStatus(txHash: string): Promise<{
        status: 'pending' | 'included' | 'failed' | 'unknown';
        blockNumber?: number;
        gasUsed?: bigint;
        effectiveGasPrice?: bigint;
    }> {
        try {
            if (!this.protectProvider) {
                return { status: 'unknown' };
            }

            const receipt = await this.protectProvider.getTransactionReceipt(txHash);
            
            if (receipt) {
                return {
                    status: receipt.status === 1 ? 'included' : 'failed',
                    blockNumber: receipt.blockNumber,
                    gasUsed: receipt.gasUsed,
                    effectiveGasPrice: receipt.gasPrice
                };
            }

            // Check if transaction is still pending
            const tx = await this.protectProvider.getTransaction(txHash);
            if (tx) {
                return { status: 'pending' };
            }

            return { status: 'unknown' };

        } catch (error) {
            logger.logError(error as Error, 'FlashbotsProtectProvider.getTransactionStatus');
            return { status: 'unknown' };
        }
    }

    /**
     * Estimate gas for transaction via Protect RPC
     */
    async estimateGas(transaction: FlashbotsProtectTransaction): Promise<bigint | null> {
        try {
            if (!this.protectProvider) {
                return null;
            }

            const gasEstimate = await this.protectProvider.estimateGas({
                to: transaction.to,
                data: transaction.data,
                value: transaction.value || '0',
                from: this.wallet.address
            });

            return gasEstimate;

        } catch (error) {
            logger.logError(error as Error, 'FlashbotsProtectProvider.estimateGas');
            return null;
        }
    }

    /**
     * Get current gas prices optimized for Protect RPC
     */
    async getOptimizedGasPrices(): Promise<{
        maxFeePerGas: bigint;
        maxPriorityFeePerGas: bigint;
    } | null> {
        try {
            if (!this.protectProvider) {
                return null;
            }

            const feeData = await this.protectProvider.getFeeData();
            
            if (feeData.maxFeePerGas && feeData.maxPriorityFeePerGas) {
                // Add 10% buffer for better inclusion chances
                const maxFeePerGas = (feeData.maxFeePerGas * 110n) / 100n;
                const maxPriorityFeePerGas = (feeData.maxPriorityFeePerGas * 110n) / 100n;

                return { maxFeePerGas, maxPriorityFeePerGas };
            }

            return null;

        } catch (error) {
            logger.logError(error as Error, 'FlashbotsProtectProvider.getOptimizedGasPrices');
            return null;
        }
    }

    /**
     * Get Protect RPC status and capabilities
     */
    async getProtectStatus(): Promise<{
        available: boolean;
        fastModeSupported: boolean;
        currentBlockNumber: number;
        networkCongestion: number;
    }> {
        try {
            if (!this.protectProvider) {
                return {
                    available: false,
                    fastModeSupported: false,
                    currentBlockNumber: 0,
                    networkCongestion: 0
                };
            }

            const currentBlock = await this.protectProvider.getBlockNumber();
            const block = await this.protectProvider.getBlock(currentBlock);
            
            // Estimate network congestion based on gas usage
            const gasUsedPercentage = block ? Number(block.gasUsed * 100n / block.gasLimit) : 0;

            return {
                available: true,
                fastModeSupported: true,
                currentBlockNumber: currentBlock,
                networkCongestion: gasUsedPercentage
            };

        } catch (error) {
            logger.logError(error as Error, 'FlashbotsProtectProvider.getProtectStatus');
            return {
                available: false,
                fastModeSupported: false,
                currentBlockNumber: 0,
                networkCongestion: 0
            };
        }
    }
}
