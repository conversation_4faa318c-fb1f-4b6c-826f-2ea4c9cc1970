import { ethers } from 'ethers';
import { FlashbotsBundleProvider, FlashbotsBundleResolution } from '@flashbots/ethers-provider-bundle';
import { logger } from '../utils/logger';
import { bundleAnalyzer } from '../utils/bundle-analyzer';

/**
 * Bundle tracking information
 */
export interface TrackedBundle {
    bundleHash: string;
    targetBlock: number;
    submissionTime: number;
    priorityFee: bigint;
    baseFee: bigint;
    isHighPriority: boolean;
    resolved: boolean;
    resolution?: FlashbotsBundleResolution;
    error?: string;
    txHash?: string;
    profit?: bigint;
    gasUsed?: bigint;
    // Transaction hashes from the bundle for inclusion detection
    bundleTransactionHashes?: string[];
    // Transaction data for pattern matching
    bundleTransactions?: Array<{
        to?: string;
        value?: bigint;
        data?: string;
        gasLimit?: bigint;
        nonce?: number;
    }>;
}

/**
 * Bundle result tracking for Aave no-wait submissions
 */
export class BundleTracker {
    private trackedBundles: Map<string, TrackedBundle> = new Map();
    private provider: ethers.JsonRpcProvider;
    private flashbotsProvider: FlashbotsBundleProvider;
    private trackingInterval?: NodeJS.Timeout;
    private readonly TRACKING_INTERVAL_MS = 5000; // Check every 5 seconds
    private readonly MAX_TRACKING_TIME_MS = 300000; // Track for 5 minutes max

    constructor(provider: ethers.JsonRpcProvider, flashbotsProvider: FlashbotsBundleProvider) {
        this.provider = provider;
        this.flashbotsProvider = flashbotsProvider;

        // Start block monitoring for inclusion detection
        this.startBlockMonitoring();
    }

    /**
     * Start tracking a bundle submission
     */
    trackBundle(
        bundleHash: string,
        targetBlock: number,
        priorityFee: bigint,
        baseFee: bigint,
        isHighPriority: boolean = false,
        bundleTransactionHashes?: string[],
        bundleTransactions?: Array<{
            to?: string;
            value?: bigint;
            data?: string;
            gasLimit?: bigint;
            nonce?: number;
        }>
    ): void {
        const trackedBundle: TrackedBundle = {
            bundleHash,
            targetBlock,
            submissionTime: Date.now(),
            priorityFee,
            baseFee,
            isHighPriority,
            resolved: false,
            bundleTransactionHashes,
            bundleTransactions
        };

        this.trackedBundles.set(bundleHash, trackedBundle);

        logger.debug(`📊 Started tracking bundle: ${bundleHash.substring(0, 10)}...`);
        logger.debug(`   Target Block: ${targetBlock}`);
        logger.debug(`   Priority Fee: ${ethers.formatUnits(priorityFee, 'gwei')} gwei`);

        // Start tracking if not already running
        if (!this.trackingInterval) {
            this.startTracking();
        }
    }

    /**
     * Start the tracking interval
     */
    private startTracking(): void {
        this.trackingInterval = setInterval(async () => {
            await this.checkBundleStatuses();
        }, this.TRACKING_INTERVAL_MS);

        logger.debug('🔄 Bundle tracking started');
    }

    /**
     * Stop tracking (cleanup)
     */
    stopTracking(): void {
        if (this.trackingInterval) {
            clearInterval(this.trackingInterval);
            this.trackingInterval = undefined;
        }
        logger.debug('🛑 Bundle tracking stopped');
    }

    /**
     * Check status of all tracked bundles
     */
    private async checkBundleStatuses(): Promise<void> {
        const currentTime = Date.now();
        const currentBlock = await this.provider.getBlockNumber();
        const bundlesToRemove: string[] = [];

        for (const [bundleHash, bundle] of this.trackedBundles) {
            try {
                // Skip if already resolved
                if (bundle.resolved) {
                    continue;
                }

                // Check if tracking timeout exceeded
                if (currentTime - bundle.submissionTime > this.MAX_TRACKING_TIME_MS) {
                    logger.debug(`⏰ Bundle tracking timeout: ${bundleHash.substring(0, 10)}...`);
                    this.resolveBundleAsTimeout(bundle);
                    bundlesToRemove.push(bundleHash);
                    continue;
                }

                // Check if target block has passed
                if (currentBlock > bundle.targetBlock + 2) {
                    // Block has passed, check if bundle was included
                    await this.checkBundleInclusion(bundle);
                    bundlesToRemove.push(bundleHash);
                    continue;
                }

                // For recent blocks, try to get bundle stats
                if (currentBlock >= bundle.targetBlock) {
                    await this.checkBundleStats(bundle);
                }

            } catch (error) {
                logger.debug(`❌ Error checking bundle ${bundleHash.substring(0, 10)}...: ${(error as Error).message}`);
            }
        }

        // Remove resolved bundles
        bundlesToRemove.forEach(hash => {
            this.trackedBundles.delete(hash);
        });

        // Stop tracking if no bundles left
        if (this.trackedBundles.size === 0 && this.trackingInterval) {
            this.stopTracking();
        }
    }

    /**
     * Check bundle inclusion in blockchain
     */
    private async checkBundleInclusion(bundle: TrackedBundle): Promise<void> {
        try {
            // Get the target block
            const block = await this.provider.getBlock(bundle.targetBlock);
            if (!block) {
                this.resolveBundleAsNotIncluded(bundle, 'Target block not found');
                return;
            }

            // Check if any transactions in the block match our bundle
            // This is a simplified check - in practice, you'd need to match specific transactions
            const bundleIncluded = await this.checkIfBundleInBlock(bundle, block);

            if (bundleIncluded) {
                this.resolveBundleAsIncluded(bundle, block);
            } else {
                this.resolveBundleAsNotIncluded(bundle, 'Block passed without inclusion');
            }

        } catch (error) {
            this.resolveBundleAsNotIncluded(bundle, `Error checking inclusion: ${(error as Error).message}`);
        }
    }

    /**
     * Check bundle stats from Flashbots
     */
    private async checkBundleStats(bundle: TrackedBundle): Promise<void> {
        try {
            const stats = await this.flashbotsProvider.getBundleStats(bundle.bundleHash, bundle.targetBlock);

            if (stats) {
                logger.debug(`📊 Bundle stats received for ${bundle.bundleHash.substring(0, 10)}...`);

                // Check if this is a successful response
                if ('bundleGasPrice' in stats) {
                    logger.debug(`📡 Bundle stats available: ${bundle.bundleHash.substring(0, 10)}...`);

                    // Update bundle with available stats
                    if ('bundleGasPrice' in stats && stats.bundleGasPrice) {
                        // Bundle stats are available, which means it was processed
                        logger.debug(`💰 Bundle gas price: ${stats.bundleGasPrice}`);
                    }
                }
            }

        } catch (error) {
            logger.debug(`❌ Error getting bundle stats: ${(error as Error).message}`);
        }
    }

    /**
     * Check if bundle was included in a specific block
     */
    private async checkIfBundleInBlock(bundle: TrackedBundle, block: ethers.Block): Promise<boolean> {
        try {
            logger.system(`🔍 Checking bundle inclusion in block ${block.number}`);
            logger.system(`   Bundle hash: ${bundle.bundleHash.substring(0, 10)}...`);
            logger.system(`   Block transactions: ${block.transactions.length}`);

            // Method 1: Direct transaction hash matching (most reliable)
            if (bundle.bundleTransactionHashes && bundle.bundleTransactionHashes.length > 0) {
                const blockTxHashes = new Set(block.transactions);
                let matchedTxs = 0;

                for (const bundleTxHash of bundle.bundleTransactionHashes) {
                    if (blockTxHashes.has(bundleTxHash)) {
                        matchedTxs++;
                        logger.info(`✅ Found matching transaction: ${bundleTxHash.substring(0, 10)}...`);
                    }
                }

                // If all bundle transactions are found in the block, bundle was included
                if (matchedTxs === bundle.bundleTransactionHashes.length) {
                    logger.info(`🎉 All ${matchedTxs} bundle transactions found in block!`);
                    return true;
                }

                // If some but not all transactions are found, log partial match
                if (matchedTxs > 0) {
                    logger.info(`⚠️ Partial match: ${matchedTxs}/${bundle.bundleTransactionHashes.length} transactions found`);
                }
            }

            // Method 2: Transaction pattern matching (fallback when hashes not available)
            if (bundle.bundleTransactions && bundle.bundleTransactions.length > 0) {
                logger.system(`🔍 Attempting pattern matching for ${bundle.bundleTransactions.length} transactions`);

                // Get full transaction details for pattern matching
                const blockTxDetails = await Promise.all(
                    block.transactions.slice(0, Math.min(50, block.transactions.length)).map(async (txHash) => {
                        try {
                            return await this.provider.getTransaction(txHash);
                        } catch {
                            return null;
                        }
                    })
                );

                const validBlockTxs = blockTxDetails.filter(tx => tx !== null);
                let patternMatches = 0;

                for (const bundleTx of bundle.bundleTransactions) {
                    const match = validBlockTxs.find(blockTx =>
                        blockTx &&
                        blockTx.to === bundleTx.to &&
                        blockTx.value === bundleTx.value &&
                        blockTx.data === bundleTx.data &&
                        (bundleTx.nonce === undefined || blockTx.nonce === bundleTx.nonce)
                    );

                    if (match) {
                        patternMatches++;
                        logger.info(`✅ Pattern match found: ${match.hash.substring(0, 10)}...`);
                    }
                }

                // If all patterns match, likely our bundle
                if (patternMatches === bundle.bundleTransactions.length) {
                    logger.info(`🎉 All ${patternMatches} transaction patterns matched!`);
                    return true;
                }

                if (patternMatches > 0) {
                    logger.info(`⚠️ Partial pattern match: ${patternMatches}/${bundle.bundleTransactions.length} transactions`);
                }
            }

            // Method 3: Heuristic matching (last resort)
            if (!bundle.bundleTransactionHashes && !bundle.bundleTransactions) {
                logger.system(`🔍 Using heuristic matching (no transaction data available)`);

                // Check if block contains transactions with similar gas patterns to our bundle
                // This is less reliable but better than nothing
                const blockTxDetails = await Promise.all(
                    block.transactions.slice(0, 10).map(async (txHash) => {
                        try {
                            return await this.provider.getTransaction(txHash);
                        } catch {
                            return null;
                        }
                    })
                );

                // Look for transactions with high gas prices (indicating MEV activity)
                const highGasTxs = blockTxDetails.filter(tx =>
                    tx &&
                    tx.maxPriorityFeePerGas &&
                    tx.maxPriorityFeePerGas >= bundle.priorityFee * 80n / 100n // Within 20% of our priority fee
                );

                if (highGasTxs.length > 0) {
                    logger.info(`🤔 Found ${highGasTxs.length} high-gas transactions, possible bundle inclusion`);
                    // This is speculative - only return true if we have strong indicators
                    return false; // Conservative approach for heuristic matching
                }
            }

            logger.system(`❌ No bundle inclusion detected in block ${block.number}`);
            return false;

        } catch (error) {
            logger.error(`❌ Error checking bundle in block: ${(error as Error).message}`);
            return false;
        }
    }

    /**
     * Resolve bundle as included
     */
    private resolveBundleAsIncluded(bundle: TrackedBundle, block: ethers.Block): void {
        bundle.resolved = true;
        bundle.resolution = FlashbotsBundleResolution.BundleIncluded;

        logger.info(`🎉 Bundle included in block ${bundle.targetBlock}!`);
        logger.info(`   Bundle Hash: ${bundle.bundleHash.substring(0, 10)}...`);
        logger.info(`   Block Hash: ${block.hash}`);
        logger.info(`   Gas Used: ${bundle.gasUsed?.toString() || 'Unknown'}`);

        // Record successful inclusion
        bundleAnalyzer.recordBundleSubmission(
            bundle.targetBlock,
            true,
            bundle.priorityFee,
            bundle.baseFee,
            'Bundle included successfully'
        );

        // Emit success event (you could add event emitter here)
        this.emitBundleResult(bundle, true);
    }

    /**
     * Resolve bundle as not included
     */
    private resolveBundleAsNotIncluded(bundle: TrackedBundle, reason: string): void {
        bundle.resolved = true;
        bundle.resolution = FlashbotsBundleResolution.BlockPassedWithoutInclusion;
        bundle.error = reason;

        logger.warn(`⏭️ Bundle not included: ${bundle.bundleHash.substring(0, 10)}...`);
        logger.warn(`   Reason: ${reason}`);
        logger.warn(`   Target Block: ${bundle.targetBlock}`);

        // Record failed inclusion
        bundleAnalyzer.recordBundleSubmission(
            bundle.targetBlock,
            false,
            bundle.priorityFee,
            bundle.baseFee,
            reason
        );

        // Emit failure event
        this.emitBundleResult(bundle, false);
    }

    /**
     * Resolve bundle as timeout
     */
    private resolveBundleAsTimeout(bundle: TrackedBundle): void {
        bundle.resolved = true;
        bundle.error = 'Tracking timeout exceeded';

        logger.warn(`⏰ Bundle tracking timeout: ${bundle.bundleHash.substring(0, 10)}...`);

        // Record as unknown (timeout)
        bundleAnalyzer.recordBundleSubmission(
            bundle.targetBlock,
            false,
            bundle.priorityFee,
            bundle.baseFee,
            'Tracking timeout'
        );

        // Emit timeout event
        this.emitBundleResult(bundle, false);
    }

    /**
     * Emit bundle result (placeholder for event system)
     */
    private emitBundleResult(bundle: TrackedBundle, success: boolean): void {
        // You could implement an event emitter here to notify other parts of the system
        logger.debug(`📡 Bundle result emitted: ${bundle.bundleHash.substring(0, 10)}... - Success: ${success}`);
    }

    /**
     * Get tracking statistics
     */
    getTrackingStats(): {
        activeTracking: number;
        totalTracked: number;
        averageTrackingTime: number;
    } {
        const activeTracking = this.trackedBundles.size;

        // Calculate average tracking time for resolved bundles
        const resolvedBundles = Array.from(this.trackedBundles.values()).filter(b => b.resolved);
        const averageTrackingTime = resolvedBundles.length > 0
            ? resolvedBundles.reduce((sum, b) => sum + (Date.now() - b.submissionTime), 0) / resolvedBundles.length
            : 0;

        return {
            activeTracking,
            totalTracked: resolvedBundles.length + activeTracking,
            averageTrackingTime
        };
    }

    /**
     * Get specific bundle status
     */
    getBundleStatus(bundleHash: string): TrackedBundle | undefined {
        return this.trackedBundles.get(bundleHash);
    }

    /**
     * Get all tracked bundles
     */
    getAllTrackedBundles(): TrackedBundle[] {
        return Array.from(this.trackedBundles.values());
    }

    /**
     * Start monitoring for new blocks to detect bundle inclusions
     */
    private startBlockMonitoring(): void {
        logger.system('🔄 Starting block monitoring for bundle inclusion detection');

        this.provider.on('block', async (blockNumber: number) => {
            try {
                logger.debug(`📦 New block detected: ${blockNumber}, checking ${this.trackedBundles.size} tracked bundles...`);

                if (this.trackedBundles.size === 0) {
                    return; // No bundles to check
                }

                // Check each tracked bundle for inclusion in this block
                for (const [bundleHash, bundle] of this.trackedBundles.entries()) {
                    if (bundle.targetBlock === blockNumber) {
                        logger.debug(`🎯 Checking bundle ${bundleHash.substring(0, 10)}... for inclusion in target block ${blockNumber}`);
                        await this.checkBundleInclusion(bundle);
                    }
                }

            } catch (error) {
                logger.debug(`❌ Error checking bundle inclusions for block ${blockNumber}: ${(error as Error).message}`);
            }
        });

        logger.debug('✅ Block monitoring started successfully');
    }
}

// Export singleton instance
export let bundleTracker: BundleTracker | null = null;

export function initializeBundleTracker(provider: ethers.JsonRpcProvider, flashbotsProvider: FlashbotsBundleProvider): void {
    bundleTracker = new BundleTracker(provider, flashbotsProvider);
}

export function getBundleTracker(): BundleTracker | null {
    return bundleTracker;
}
