import { ethers } from 'ethers';
import { Transaction, MEVOpportunity, DecodedSwap, Pool, Token } from '../types';
import { config, ADDRESSES } from '../config';
import { logger } from '../utils/logger';

import { AdvancedGasEstimator } from '../gas/advanced-estimator';

/**
 * Frontrunning Strategy
 * Detects profitable transactions in mempool and frontruns them
 */
export class FrontrunningStrategy {
  private provider: ethers.Provider;
  private wallet: ethers.Wallet;
  private gasOptimizer: AdvancedGasEstimator;
  private readonly MIN_PROFIT_THRESHOLD = 0.005; // 0.5% minimum profit

  constructor(provider: ethers.Provider, wallet?: ethers.Wallet) {
    this.provider = provider;
    this.wallet = wallet || new ethers.Wallet(config.privateKey, provider);
    this.gasOptimizer = new AdvancedGasEstimator(provider as ethers.JsonRpcProvider);
  }

  /**
   * Analyze a pending transaction for frontrunning opportunities
   */
  async analyzeFrontrunOpportunity(transaction: any): Promise<MEVOpportunity | null> {
    try {
      // Only frontrun large transactions that will cause significant price impact
      const minValueForFrontrun = ethers.parseEther('0.5'); // 0.5 ETH minimum
      if (BigInt(transaction.value.toString()) < minValueForFrontrun) {
        return null;
      }

      // Decode the transaction to understand what it's doing
      const decodedSwap = await this.decodeTransaction(transaction);
      if (!decodedSwap) {
        // For non-DEX transactions, analyze based on value and gas price
        return await this.analyzeValueBasedFrontrun(transaction);
      }

      // Find the pool being used
      const pool = await this.findTargetPool(decodedSwap);
      if (!pool) {
        return null;
      }

      // Calculate potential profit from frontrunning
      const profitAnalysis = await this.calculateFrontrunProfit(decodedSwap, pool, transaction);

      if (!profitAnalysis.isProfitable) {
        logger.warn('Frontrun attack not profitable - skipping');
        return null;
      }

      // Log profitable opportunity found
      logger.profitCalculation(
        ethers.formatEther(profitAnalysis.estimatedProfit),
        profitAnalysis.isProfitable
      );

      // Create frontrun transaction
      const frontRunTx = await this.createFrontrunTransaction(decodedSwap, profitAnalysis.optimalAmount);

      if (!frontRunTx) {
        return null;
      }

      return {
        type: 'frontrun',
        victimTx: transaction,
        decodedSwap,
        pool,
        estimatedProfit: profitAnalysis.estimatedProfit,
        gasEstimate: profitAnalysis.gasEstimate,
        frontRunTx,
        confidence: profitAnalysis.confidence,
        timestamp: Date.now()
      };

    } catch (error) {
      logger.logError(error as Error, 'FrontrunningStrategy.analyzeFrontrunOpportunity');
      return null;
    }
  }

  /**
   * Analyze value-based frontrunning for non-DEX transactions
   */
  private async analyzeValueBasedFrontrun(transaction: any): Promise<MEVOpportunity | null> {
    try {
      const value = BigInt(transaction.value.toString());
      const gasPrice = BigInt(transaction.gasPrice.toString());

      // Only frontrun very large transactions with high gas prices
      const minValue = ethers.parseEther('10'); // 10 ETH minimum
      const minGasPrice = ethers.parseUnits('30', 'gwei'); // 30 gwei minimum

      if (value < minValue || gasPrice < minGasPrice) {
        return null;
      }

      // Estimate potential profit (simplified)
      const estimatedProfit = value / BigInt(1000); // 0.1% of transaction value
      const gasEstimate = ethers.parseEther('0.001'); // Fixed gas estimate

      // Check if profitable
      if (estimatedProfit <= gasEstimate) {
        return null;
      }

      // Create mock frontrun transaction
      const frontRunTx = await this.createValueBasedFrontrunTransaction(transaction);

      if (!frontRunTx) {
        return null;
      }

      return {
        type: 'frontrun',
        victimTx: transaction,
        decodedSwap: null,
        pool: null,
        estimatedProfit,
        gasEstimate,
        frontRunTx,
        confidence: 60, // Lower confidence for value-based frontrunning
        timestamp: Date.now()
      };

    } catch (error) {
      logger.logError(error as Error, 'FrontrunningStrategy.analyzeValueBasedFrontrun');
      return null;
    }
  }

  /**
   * Decode transaction to understand the swap details
   */
  private async decodeTransaction(transaction: any): Promise<DecodedSwap | null> {
    try {
      const data = transaction.data;
      
      // Check if it's a Uniswap V2 swap
      if (transaction.to?.toLowerCase() === ADDRESSES.UNISWAP_V2_ROUTER.toLowerCase()) {
        return this.decodeUniswapV2Swap(data, transaction);
      }
      
      // Check if it's a Uniswap V3 swap
      if (transaction.to?.toLowerCase() === ADDRESSES.UNISWAP_V3_ROUTER.toLowerCase()) {
        return this.decodeUniswapV3Swap(data, transaction);
      }

      return null;
    } catch (error) {
      logger.debug('Failed to decode transaction', { error: (error as Error).message });
      return null;
    }
  }

  private decodeUniswapV2Swap(data: string, transaction: any): DecodedSwap | null {
    try {
      const methodId = data.slice(0, 10);
      
      // swapExactETHForTokens method
      if (methodId === '0x7ff36ab5') {
        return {
          method: 'swapExactETHForTokens',
          protocol: 'uniswap-v2',
          tokenIn: {
            address: ADDRESSES.WETH,
            symbol: 'WETH',
            decimals: 18,
            name: 'Wrapped Ether'
          },
          tokenOut: {
            address: '******************************************',
            symbol: 'UNKNOWN',
            decimals: 18,
            name: 'Unknown Token'
          },
          amountIn: transaction.value,
          amountOutMin: BigInt(0),
          recipient: transaction.from,
          deadline: Math.floor(Date.now() / 1000) + 3600,
          path: []
        };
      }

      return null;
    } catch (error) {
      return null;
    }
  }

  private decodeUniswapV3Swap(data: string, transaction: any): DecodedSwap | null {
    try {
      const methodId = data.slice(0, 10);
      
      // exactInputSingle method
      if (methodId === '0x414bf389') {
        return {
          method: 'exactInputSingle',
          protocol: 'uniswap-v3',
          tokenIn: {
            address: ADDRESSES.WETH,
            symbol: 'WETH',
            decimals: 18,
            name: 'Wrapped Ether'
          },
          tokenOut: {
            address: '******************************************',
            symbol: 'UNKNOWN',
            decimals: 18,
            name: 'Unknown Token'
          },
          amountIn: transaction.value,
          amountOutMin: BigInt(0),
          recipient: transaction.from,
          deadline: Math.floor(Date.now() / 1000) + 3600,
          path: [],
          fee: 3000
        };
      }

      return null;
    } catch (error) {
      return null;
    }
  }

  /**
   * Find the target pool for the swap
   */
  private async findTargetPool(decodedSwap: DecodedSwap): Promise<Pool | null> {
    try {
      // Create a mock pool for testing
      const mockPool: Pool = {
        address: decodedSwap.protocol === 'uniswap-v2'
          ? '******************************************'
          : '******************************************',
        token0: decodedSwap.tokenIn,
        token1: decodedSwap.tokenOut,
        fee: decodedSwap.fee || 3000,
        protocol: decodedSwap.protocol as 'uniswap-v2' | 'uniswap-v3',
        reserves: {
          reserve0: ethers.parseEther('1000'),
          reserve1: ethers.parseUnits('2000000', 6)
        }
      };

      return mockPool;
    } catch (error) {
      logger.logError(error as Error, 'FrontrunningStrategy.findTargetPool');
      return null;
    }
  }

  /**
   * Calculate potential profit from frontrunning
   */
  private async calculateFrontrunProfit(
    decodedSwap: DecodedSwap,
    pool: Pool,
    victimTx: any
  ): Promise<{
    isProfitable: boolean;
    estimatedProfit: bigint;
    gasEstimate: bigint;
    optimalAmount: bigint;
    confidence: number;
  }> {
    try {
      const victimAmount = BigInt(decodedSwap.amountIn.toString());
      const victimPriceImpact = await this.calculatePriceImpact(pool, victimAmount, decodedSwap.tokenIn);

      // Calculate optimal frontrun amount
      const frontrunRatio = this.calculateOptimalFrontrunRatio(victimPriceImpact);
      const optimalAmount = (victimAmount * BigInt(Math.floor(frontrunRatio * 10000))) / BigInt(10000);

      // Estimate gas costs
      const gasStrategy = await this.gasOptimizer.getOptimalGasPrice('fast');
      const frontrunGas = BigInt(200000); // Conservative gas estimate

      const totalGasCost = frontrunGas * gasStrategy.gasPrice;

      // Calculate expected profit
      const expectedPriceIncrease = victimPriceImpact * 0.7;
      const grossProfit = (optimalAmount * BigInt(Math.floor(expectedPriceIncrease * 10000))) / BigInt(10000);
      const netProfit = grossProfit - totalGasCost;

      const minProfit = BigInt(config.minProfitWei);
      const isProfitable = netProfit >= minProfit;

      const confidence = this.calculateConfidence(victimPriceImpact, netProfit, pool);

      return {
        isProfitable,
        estimatedProfit: netProfit,
        gasEstimate: totalGasCost,
        optimalAmount,
        confidence
      };

    } catch (error) {
      logger.logError(error as Error, 'FrontrunningStrategy.calculateFrontrunProfit');
      return {
        isProfitable: false,
        estimatedProfit: BigInt(0),
        gasEstimate: BigInt(0),
        optimalAmount: BigInt(0),
        confidence: 0
      };
    }
  }

  /**
   * Calculate price impact of a trade
   */
  private async calculatePriceImpact(pool: Pool, amount: bigint, tokenIn: Token): Promise<number> {
    try {
      const reserve0 = BigInt(pool.reserves?.reserve0?.toString() || '0');
      const reserve1 = BigInt(pool.reserves?.reserve1?.toString() || '0');

      if (reserve0 === BigInt(0) || reserve1 === BigInt(0)) {
        return 0;
      }

      const k = reserve0 * reserve1;
      const newReserve0 = reserve0 + amount;
      const newReserve1 = k / newReserve0;
      const priceImpact = Number(reserve1 - newReserve1) / Number(reserve1);

      return Math.abs(priceImpact);
    } catch (error) {
      return 0;
    }
  }

  private calculateOptimalFrontrunRatio(priceImpact: number): number {
    if (priceImpact > 0.05) return 0.8;
    if (priceImpact > 0.02) return 0.5;
    if (priceImpact > 0.01) return 0.3;
    return 0.2;
  }

  private calculateConfidence(priceImpact: number, profit: bigint, pool: Pool): number {
    let confidence = 50;
    confidence += Math.min(priceImpact * 1000, 30);
    confidence += Math.min(Number(ethers.formatEther(profit)) * 100, 20);
    confidence += pool.reserves ? 10 : 0;
    return Math.min(confidence, 100);
  }

  /**
   * Create frontrun transaction
   */
  private async createFrontrunTransaction(
    decodedSwap: DecodedSwap,
    amount: bigint
  ): Promise<Transaction | null> {
    try {
      const gasStrategy = await this.gasOptimizer.getOptimalGasPrice('fast');
      const nonce = await this.wallet.getNonce();

      // Simple data for testing - in production you'd encode the actual swap
      const data = "0x7ff36ab5"; // swapExactETHForTokens method signature

      const gasLimit = BigInt(200000); // Conservative gas limit

      return {
        hash: '',
        from: this.wallet.address,
        to: decodedSwap.protocol === 'uniswap-v2' ? ADDRESSES.UNISWAP_V2_ROUTER : ADDRESSES.UNISWAP_V3_ROUTER,
        value: decodedSwap.tokenIn.address === ADDRESSES.WETH ? amount : BigInt(0),
        gasPrice: gasStrategy.gasPrice,
        gasLimit,
        data,
        nonce,
        maxFeePerGas: gasStrategy.maxFeePerGas,
        maxPriorityFeePerGas: gasStrategy.maxPriorityFeePerGas
      };
    } catch (error) {
      logger.logError(error as Error, 'FrontrunningStrategy.createFrontrunTransaction');
      return null;
    }
  }

  /**
   * Create value-based frontrun transaction
   */
  private async createValueBasedFrontrunTransaction(victimTx: any): Promise<Transaction | null> {
    try {
      const gasStrategy = await this.gasOptimizer.getOptimalGasPrice('fast');
      const nonce = await this.wallet.getNonce();

      // Create a transaction with higher gas price to frontrun
      const frontrunGasPrice = BigInt(victimTx.gasPrice.toString()) * BigInt(110) / BigInt(100); // 10% higher

      return {
        hash: '',
        from: this.wallet.address,
        to: victimTx.to,
        value: ethers.parseEther('0.001'), // Small frontrun amount
        gasPrice: frontrunGasPrice,
        gasLimit: BigInt(21000),
        data: '0x',
        nonce,
        maxFeePerGas: frontrunGasPrice,
        maxPriorityFeePerGas: gasStrategy.maxPriorityFeePerGas || gasStrategy.gasPrice / BigInt(10)
      };
    } catch (error) {
      logger.logError(error as Error, 'FrontrunningStrategy.createValueBasedFrontrunTransaction');
      return null;
    }
  }

  /**
   * Execute frontrun attack
   */
  async executeFrontrun(opportunity: MEVOpportunity): Promise<boolean> {
    if (!opportunity.frontRunTx) {
      logger.error('Invalid frontrun opportunity: missing frontrun transaction');
      return false;
    }

    try {
      logger.info('Simulating frontrun attack...');

      // Simplified simulation for testing
      const simulationResult = {
        success: true,
        profit: opportunity.estimatedProfit,
        gasUsed: opportunity.gasEstimate
      };

      logger.bundleSimulation('success', 'Frontrun simulation successful');

      // Check if still profitable
      const netProfit = BigInt(simulationResult.profit.toString()) - BigInt(simulationResult.gasUsed.toString());
      if (netProfit <= 0) {
        logger.warn('Frontrun no longer profitable after simulation');
        return false;
      }

      if (config.dryRun) {
        logger.info('DRY RUN: Would execute frontrun attack');
        logger.profitCalculation(
          ethers.formatEther(simulationResult.profit),
          true
        );
        logger.info(`Gas Used: ${simulationResult.gasUsed.toString()}`);
        return true;
      }

      logger.info('Executing frontrun attack', {
        frontRunHash: opportunity.frontRunTx.hash,
        victimHash: opportunity.victimTx?.hash || 'unknown'
      });

      return true;
    } catch (error) {
      logger.logError(error as Error, 'FrontrunningStrategy.executeFrontrun');
      return false;
    }
  }

  /**
   * Estimate potential profit from frontrunning a transaction
   */
  async estimateFrontrunProfit(transaction: any): Promise<bigint | null> {
    try {
      const opportunity = await this.analyzeFrontrunOpportunity(transaction);
      return opportunity ? BigInt(opportunity.estimatedProfit.toString()) : null;
    } catch (error) {
      logger.logError(error as Error, 'FrontrunningStrategy.estimateFrontrunProfit');
      return null;
    }
  }
}
