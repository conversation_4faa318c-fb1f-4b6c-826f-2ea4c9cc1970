import { ethers } from 'ethers';
import { FlashloanRoute, ArbitrageRoute, Pool, Token } from '../types';
import { PoolManager } from '../dex/pools';
import { GasOptimizer } from '../gas/optimizer';
import { CalldataEncoder } from '../calldata/encoder';
import { BundleSimulator } from '../simulation/simulator';
import { PriceCalculator } from '../execution/price-calculator';
import { config, COMMON_TOKENS, ADDRESSES } from '../config';
import { logger } from '../utils/logger';
import { tokenAmountToWei } from '../utils/denomination';


// Official Balancer V2 Vault interface for flashloans
export const BALANCER_VAULT_ABI = [
  {
    "inputs": [
      {
        "internalType": "contract IFlashLoanRecipient",
        "name": "recipient",
        "type": "address"
      },
      {
        "internalType": "contract IERC20[]",
        "name": "tokens",
        "type": "address[]"
      },
      {
        "internalType": "uint256[]",
        "name": "amounts",
        "type": "uint256[]"
      },
      {
        "internalType": "bytes",
        "name": "userData",
        "type": "bytes"
      }
    ],
    "name": "flashLoan",
    "outputs": [],
    "stateMutability": "nonpayable",
    "type": "function"
  }
];

// Official Balancer V2 flashloan receiver interface
export const BALANCER_FLASHLOAN_RECEIVER_ABI = [
  {
    "inputs": [
      {
        "internalType": "contract IERC20[]",
        "name": "tokens",
        "type": "address[]"
      },
      {
        "internalType": "uint256[]",
        "name": "amounts",
        "type": "uint256[]"
      },
      {
        "internalType": "uint256[]",
        "name": "feeAmounts",
        "type": "uint256[]"
      },
      {
        "internalType": "bytes",
        "name": "userData",
        "type": "bytes"
      }
    ],
    "name": "receiveFlashLoan",
    "outputs": [],
    "stateMutability": "nonpayable",
    "type": "function"
  }
];

export class BalancerFlashloanStrategy {
  protected poolManager: PoolManager;
  protected gasOptimizer: GasOptimizer;
  protected encoder: CalldataEncoder;
  protected simulator: BundleSimulator;
  protected wallet: ethers.Wallet;
  protected priceCalculator: PriceCalculator;
  protected provider: ethers.Provider;
  protected balancerVault: ethers.Contract;
  protected balancerContract: ethers.Contract | null = null;
  protected readonly MIN_PROFIT_THRESHOLD: number;
  protected readonly MAX_PRICE_DIFFERENCE: number;
  protected readonly SAFETY_MARGIN: number;
  protected readonly MAX_FLASHLOAN_AMOUNT: bigint;

  // Cached liquidity data (checked once on startup)
  private cachedLiquidity: Map<string, bigint> = new Map();
  private liquidityLastChecked: number = 0;
  private readonly LIQUIDITY_CACHE_DURATION = 300000; // 5 minutes

  // Confidence thresholds for different trading strategies
  private readonly MAINNET_MIN_CONFIDENCE = 50;      // Balanced approach for mainnet
  private readonly AGGRESSIVE_THRESHOLD = 45;        // High-frequency, smaller profits
  private readonly CONSERVATIVE_THRESHOLD = 60;      // Fewer, higher-confidence trades
  private readonly TESTNET_MIN_CONFIDENCE = 40;      // Lower threshold for testing

  // Logging control for dynamic strategy coordination
  private isCalledFromDynamicStrategy = false;

  // Blacklist for failed token pairs
  private failedPairs: Map<string, { count: number; lastFailed: number }> = new Map();
  private readonly MAX_FAILURES = 5; // Max failures before blacklisting
  private readonly BLACKLIST_DURATION = 300000; // 5 minutes in milliseconds

  constructor(provider: ethers.Provider) {
    this.provider = provider;
    this.poolManager = new PoolManager();
    this.gasOptimizer = new GasOptimizer();
    this.encoder = new CalldataEncoder();
    this.simulator = new BundleSimulator();
    this.wallet = new ethers.Wallet(config.privateKey, provider);
    this.priceCalculator = new PriceCalculator(provider as ethers.JsonRpcProvider);

    // Conservative settings from .env to prevent losses
    this.MIN_PROFIT_THRESHOLD = config.balancerMinPriceDifference; // Use price difference threshold
    this.MAX_PRICE_DIFFERENCE = config.balancerMaxPriceDifference;
    this.SAFETY_MARGIN = config.balancerSafetyMargin;
    this.MAX_FLASHLOAN_AMOUNT = ethers.parseUnits(config.balancerMaxFlashloanAmount.toString(), 6);

    // Initialize Balancer Vault contract (same address on all networks)
    const BALANCER_VAULT_ADDRESS = config.balancerVaultAddress;

    this.balancerVault = new ethers.Contract(
      BALANCER_VAULT_ADDRESS,
      BALANCER_VAULT_ABI,
      provider
    );

    // Initialize Balancer contract if address is provided
    if (config.balancerFlashloanContract) {
      this.balancerContract = new ethers.Contract(
        config.balancerFlashloanContract,
        this.getBalancerContractABI(),
        this.wallet
      );
      logger.system('');
      logger.system(`Balancer Contract: ${config.balancerFlashloanContract}`);
    } else {
      logger.system('⚠️  No Balancer contract address provided - using vault directly');
    }

    logger.system('🔵 Balancer V2 Flashloan Strategy initialized');
    logger.system(`   ZERO FEES - 100% profit retention!`);
    logger.system(`   Vault: ${BALANCER_VAULT_ADDRESS}`);
    logger.system(`   Min Profit: ${this.MIN_PROFIT_THRESHOLD} ETH (configurable)`);
    logger.system(`   Max Amount: ${ethers.formatUnits(this.MAX_FLASHLOAN_AMOUNT, 6)} USDC (configurable)`);
    logger.system('');

    // Initialize liquidity cache on startup
    this.initializeLiquidityCache();
  }

  /**
   * Initialize liquidity cache once on startup
   */
  private async initializeLiquidityCache(): Promise<void> {
    try {
      logger.system('🔍 Checking Balancer liquidity on startup...');

      // Check liquidity for main tokens
      const tokensToCheck = COMMON_TOKENS.filter(t =>
        ['USDC', 'WETH', 'DAI', 'USDT', 'WBTC'].includes(t.symbol)
      );

      for (const token of tokensToCheck) {
        try {
          const liquidity = await this.fetchBalancerLiquidityOnce(token);
          this.cachedLiquidity.set(token.address.toLowerCase(), liquidity);

          logger.system(
            `Liquidity for ${token.symbol}: ${ethers.formatUnits(liquidity, token.decimals)} available`
          );
        } catch (error) {
          logger.debug(`Failed to check ${token.symbol} liquidity:`, error);
          // Set conservative fallback
          this.cachedLiquidity.set(token.address.toLowerCase(), ethers.parseUnits('1000', token.decimals));
        }
      }

      this.liquidityLastChecked = Date.now();
      logger.system('✅ Balancer liquidity cache initialized');

    } catch (error) {
      logger.debug('Error initializing Balancer liquidity cache:', error);
    }
  }

  /**
   * Get confidence threshold based on network and trading mode
   */
  private getConfidenceThreshold(mode: 'aggressive' | 'balanced' | 'conservative' = 'balanced'): number {
    if (config.chainId !== 1) {
      // Testnet - use lower threshold for testing
      return this.TESTNET_MIN_CONFIDENCE;
    }

    // Mainnet thresholds based on trading mode from TRADING_MODE env variable
    switch (mode) {
      case 'aggressive':
        return this.AGGRESSIVE_THRESHOLD;        // 45% - high frequency, smaller profits
      case 'conservative':
        return this.CONSERVATIVE_THRESHOLD;      // 60% - fewer, higher-confidence trades
      case 'balanced':
      default:
        return this.MAINNET_MIN_CONFIDENCE;      // 50% - balanced approach
    }
  }

  /**
   * Get current trading mode from environment or default to balanced
   */
  private getTradingMode(): 'aggressive' | 'balanced' | 'conservative' {
    const mode = process.env.TRADING_MODE?.toLowerCase();

    switch (mode) {
      case 'aggressive':
        return 'aggressive';
      case 'conservative':
        return 'conservative';
      case 'balanced':
      default:
        return 'balanced';
    }
  }

  private getBalancerContractABI(): string[] {
    return [
      // Execute flashloan arbitrage
      'function executeFlashloanArbitrage(address[] memory tokens, uint256[] memory amounts, bytes memory userData) external',
      // Withdraw profits
      'function withdrawProfits(address token) external',
      // Emergency withdraw
      'function emergencyWithdraw(address token, uint256 amount) external',
      // Get vault address
      'function getVault() external pure returns (address)'
    ];
  }

  async scanForBalancerFlashloanOpportunities(txHash: string): Promise<FlashloanRoute[]> {
    const opportunities: FlashloanRoute[] = [];

    try {
      logger.debug('🔵 Scanning for Balancer flashloan opportunities...');

      // Focus on USDC flashloans for arbitrage
      const flashloanToken = COMMON_TOKENS.find(t => t.symbol === 'USDC');
      if (!flashloanToken) {
        logger.warn('USDC token not found in COMMON_TOKENS');
        return opportunities;
      }

      logger.debug('✅ USDC flashloan token found', { address: flashloanToken.address });

      // Check Balancer liquidity for the token
      const availableLiquidity = await this.checkBalancerLiquidity(flashloanToken);
      logger.debug('Balancer liquidity check', {
        available: ethers.formatUnits(availableLiquidity, 6),
        required: ethers.formatUnits(this.MAX_FLASHLOAN_AMOUNT, 6),
        sufficient: availableLiquidity >= this.MAX_FLASHLOAN_AMOUNT
      });

      if (availableLiquidity < this.MAX_FLASHLOAN_AMOUNT) {
        logger.debug('❌ Insufficient Balancer liquidity for flashloan', {
          available: ethers.formatUnits(availableLiquidity, 6),
          required: ethers.formatUnits(this.MAX_FLASHLOAN_AMOUNT, 6)
        });
        return opportunities;
      }

      // Scan for arbitrage opportunities between different DEXs
      logger.debug(`🎯 Scanning ${COMMON_TOKENS.length} tokens for arbitrage opportunities`);

      for (let i = 0; i < COMMON_TOKENS.length; i++) {
        const targetToken = COMMON_TOKENS[i];

        if (targetToken.address === flashloanToken.address) {
          logger.debug(`⏭️  Skipping same token: ${targetToken.symbol}`);
          continue; // Skip same token
        }

        // Check if this pair is blacklisted
        if (this.isPairBlacklisted(flashloanToken.address, targetToken.address)) {
          logger.debug(`🚫 Skipping blacklisted pair: ${flashloanToken.symbol}/${targetToken.symbol}`);
          continue;
        }

        logger.debug(`🔍 Checking arbitrage: ${flashloanToken.symbol} -> ${targetToken.symbol}`);

        // Find arbitrage between Uniswap V2 and V3
        const arbitrageRoute = await this.findArbitrageOpportunity(flashloanToken, targetToken);

        if (arbitrageRoute) {
          logger.info('✅ Arbitrage route found!', {
            profit: ethers.formatEther(arbitrageRoute.expectedProfit),
            confidence: arbitrageRoute.confidence
          });

          // Calculate optimal flashloan amount
          const flashloanAmount = await this.calculateOptimalFlashloanAmount(
            flashloanToken,
            arbitrageRoute
          );

          logger.debug('Flashloan amount calculated', {
            amount: ethers.formatUnits(flashloanAmount, flashloanToken.decimals),
            isPositive: flashloanAmount > BigInt(0)
          });

          if (flashloanAmount > BigInt(0)) {
            const flashloanRoute = await this.buildBalancerFlashloanRoute(
              flashloanToken,
              flashloanAmount,
              arbitrageRoute
            );

            if (flashloanRoute) {
              // Check if we should skip confidence scoring (Pure Profit Mode)
              if (config.pureProfitMode) {
                // Pure Profit Mode: Execute all profitable opportunities
                opportunities.push(flashloanRoute);
                logger.profitCalculation(
                  ethers.formatEther(flashloanRoute.expectedProfit),
                  true
                );
                if (!this.isCalledFromDynamicStrategy) {
                  logger.info('🚀 Opportunity added to list!', {
                    profit: ethers.formatEther(flashloanRoute.expectedProfit),
                    confidence: flashloanRoute.confidence,
                    mode: 'PURE_PROFIT'
                  });
                }
              } else {
                // Traditional Mode: Use confidence threshold
                const tradingMode = this.getTradingMode();
                const requiredConfidence = this.getConfidenceThreshold(tradingMode);

                logger.debug('Flashloan route built', {
                  profit: ethers.formatEther(flashloanRoute.expectedProfit),
                  confidence: flashloanRoute.confidence,
                  requiredConfidence,
                  tradingMode,
                  meetsThreshold: flashloanRoute.confidence >= requiredConfidence
                });

                if (flashloanRoute.confidence >= requiredConfidence) {
                  opportunities.push(flashloanRoute);
                  logger.profitCalculation(
                    ethers.formatEther(flashloanRoute.expectedProfit),
                    true
                  );
                  logger.info('🎉 Opportunity added to list!', {
                    confidence: flashloanRoute.confidence,
                    threshold: requiredConfidence,
                    mode: tradingMode
                  });
                } else {
                  logger.info('❌ Confidence too low', {
                    confidence: flashloanRoute.confidence,
                    required: requiredConfidence,
                    mode: tradingMode
                  });
                }
              }
            } else {
              logger.debug('❌ Failed to build flashloan route');
            }
          } else {
            logger.debug('❌ Flashloan amount is zero');
          }
        } else {
          logger.debug('❌ No arbitrage route found for this pair');
          // Record failure for this pair
          this.recordPairFailure(flashloanToken.address, targetToken.address);
        }
      }

      // Sort by expected profit
      opportunities.sort((a, b) =>
        Number(BigInt(b.expectedProfit.toString()) - BigInt(a.expectedProfit.toString()))
      );

      // logger.systemStatus(`🔵 Found ${opportunities.length} Balancer flashloan opportunities`);
      return opportunities.slice(0, 3); // Return top 3 (Balancer has less liquidity)

    } catch (error) {
      logger.logError(error as Error, 'BalancerFlashloanStrategy.scanForBalancerFlashloanOpportunities');
      return [];
    }
  }

  /**
   * Fetch Balancer liquidity once (used during initialization)
   */
  private async fetchBalancerLiquidityOnce(token: Token): Promise<bigint> {
    try {
      // Use the pool manager to fetch real-time Balancer liquidity
      const liquidityData = await this.poolManager.fetchBalancerLiquidity(token.address);

      if (liquidityData.totalLiquidity > BigInt(0)) {
        // Return 80% of available liquidity for safety
        return (liquidityData.totalLiquidity * BigInt(80)) / BigInt(100);
      }

      // Fallback: return conservative estimate based on typical Balancer liquidity
      return ethers.parseUnits('10000', token.decimals); // 10k tokens
    } catch (error) {
      logger.debug(`Error fetching ${token.symbol} Balancer liquidity:`, error);
      // Fallback to conservative estimate
      return ethers.parseUnits('5000', token.decimals); // 5k tokens as fallback
    }
  }

  /**
   * Check Balancer liquidity using cached data (no repeated API calls)
   */
  private async checkBalancerLiquidity(token: Token): Promise<bigint> {
    const tokenAddress = token.address.toLowerCase();

    // Check if we have cached data and it's still fresh
    const now = Date.now();
    const cacheAge = now - this.liquidityLastChecked;

    if (this.cachedLiquidity.has(tokenAddress) && cacheAge < this.LIQUIDITY_CACHE_DURATION) {
      const cachedAmount = this.cachedLiquidity.get(tokenAddress)!;
      logger.debug(`Using cached Balancer liquidity for ${token.symbol}:`, {
        amount: ethers.formatUnits(cachedAmount, token.decimals),
        cacheAge: Math.round(cacheAge / 1000) + 's'
      });
      return cachedAmount;
    }

    // Cache is stale or missing, refresh it
    logger.debug(`Refreshing Balancer liquidity cache for ${token.symbol}...`);
    const freshLiquidity = await this.fetchBalancerLiquidityOnce(token);
    this.cachedLiquidity.set(tokenAddress, freshLiquidity);
    this.liquidityLastChecked = now;

    return freshLiquidity;
  }

  private async findArbitrageOpportunity(
    tokenA: Token,
    tokenB: Token
  ): Promise<ArbitrageRoute | null> {
    try {
      logger.debug(`🔍 Finding arbitrage: ${tokenA.symbol}/${tokenB.symbol}`);

      // Get pools from both V2 and V3
      const v2Pool = await this.poolManager.getPool(tokenA.address, tokenB.address, 'uniswap-v2');
      const v3Pool = await this.poolManager.getPool(tokenA.address, tokenB.address, 'uniswap-v3', 3000);

      logger.debug('Pool lookup results', {
        v2Found: !!v2Pool,
        v3Found: !!v3Pool,
        v2Address: v2Pool?.address,
        v3Address: v3Pool?.address
      });

      if (!v2Pool || !v3Pool) {
        logger.debug('❌ Missing pools', { v2Pool: !!v2Pool, v3Pool: !!v3Pool });
        return null;
      }

      // Calculate prices on both pools
      const v2Price = this.calculatePoolPrice(v2Pool, tokenA, tokenB);
      const v3Price = this.calculatePoolPrice(v3Pool, tokenA, tokenB);

      if (!v2Price || !v3Price) {
        return null;
      }

      // Check for price difference (lower threshold due to 0% fees)
      const priceDifference = Math.abs(v2Price - v3Price) / Math.min(v2Price, v3Price);

      logger.debug('Price difference analysis', {
        tokenA: tokenA.symbol,
        tokenB: tokenB.symbol,
        v2Price: v2Price.toFixed(6),
        v3Price: v3Price.toFixed(6),
        priceDifference: `${(priceDifference * 100).toFixed(3)}%`,
        minThreshold: `${(this.MIN_PROFIT_THRESHOLD * 100).toFixed(3)}%`,
        profitable: priceDifference >= this.MIN_PROFIT_THRESHOLD
      });

      // Use configurable conservative thresholds
      if (priceDifference < this.MIN_PROFIT_THRESHOLD) {
        logger.debug('Price difference below conservative threshold', {
          priceDiff: `${(priceDifference * 100).toFixed(3)}%`,
          required: `${(this.MIN_PROFIT_THRESHOLD * 100).toFixed(3)}%`,
          reason: 'Conservative filtering to prevent losses'
        });
        return null;
      }

      // Additional validation: check if price difference is realistic (not too high)
      if (priceDifference > this.MAX_PRICE_DIFFERENCE) {
        logger.debug('Price difference too high - likely stale data', {
          priceDiff: `${(priceDifference * 100).toFixed(3)}%`,
          maxAllowed: `${(this.MAX_PRICE_DIFFERENCE * 100).toFixed(1)}%`
        });
        return null;
      }

      // Determine direction (buy low, sell high)
      const buyPool = v2Price < v3Price ? v2Pool : v3Pool;
      const sellPool = v2Price < v3Price ? v3Pool : v2Pool;

      // Calculate optimal amount for arbitrage
      const optimalAmount = await this.calculateOptimalArbitrageAmount(buyPool, sellPool, tokenA, tokenB);

      if (optimalAmount === BigInt(0)) {
        return null;
      }

      // Estimate gas costs
      const gasEstimate = await this.estimateArbitrageGasCost(buyPool, sellPool, optimalAmount);

      // Calculate expected profit (no flashloan fees!)
      const expectedProfit = await this.calculateArbitrageProfit(
        buyPool, sellPool, tokenA, tokenB, optimalAmount, gasEstimate
      );

      if (BigInt(expectedProfit.toString()) <= BigInt(0)) {
        return null;
      }

      return {
        pools: [buyPool, sellPool],
        tokens: [tokenA, tokenB],
        expectedProfit,
        gasEstimate,
        confidence: this.calculateArbitrageConfidence(priceDifference, expectedProfit)
      };

    } catch (error) {
      logger.debug('Error finding arbitrage opportunity', { error: (error as Error).message });
      return null;
    }
  }

  private async buildBalancerFlashloanRoute(
    flashloanToken: Token,
    flashloanAmount: bigint,
    arbitrageRoute: ArbitrageRoute
  ): Promise<FlashloanRoute | null> {
    try {
      logger.debug('🔧 Building Balancer flashloan route', {
        token: flashloanToken.symbol,
        amount: ethers.formatUnits(flashloanAmount, flashloanToken.decimals),
        arbitrageProfit: ethers.formatEther(arbitrageRoute.expectedProfit)
      });

      // Balancer has ZERO flashloan fees!
      const flashloanPremium = BigInt(0);

      // Estimate total gas cost (flashloan + arbitrage)
      const flashloanGas = await this.estimateBalancerFlashloanGasCost();
      const totalGasEstimate = BigInt(arbitrageRoute.gasEstimate.toString()) + flashloanGas;

      logger.debug('Gas cost estimation', {
        arbitrageGas: arbitrageRoute.gasEstimate.toString(),
        flashloanGas: flashloanGas.toString(),
        totalGas: totalGasEstimate.toString(),
        totalGasEth: ethers.formatEther(totalGasEstimate)
      });

      // Calculate expected profit (no premium to subtract!)
      const arbitrageProfitToken = await this.estimateArbitrageProfit(arbitrageRoute, flashloanAmount);
      const arbitrageProfitWei = await tokenAmountToWei(flashloanToken, arbitrageProfitToken);
      const expectedProfit = arbitrageProfitWei - totalGasEstimate; // Only gas costs!

      logger.debug('Profit calculation', {
        arbitrageProfitEth: ethers.formatEther(arbitrageProfitWei),
        totalGasCostEth: ethers.formatEther(totalGasEstimate),
        expectedProfitEth: ethers.formatEther(expectedProfit),
        isProfitable: expectedProfit > 0n
      });

      if (expectedProfit <= 0n) {
        logger.debug('❌ Expected profit is not positive', {
          expectedProfitEth: ethers.formatEther(expectedProfit)
        });
        return null;
      }

      // Calculate confidence (higher due to no fees)
      const profitMargin = Number((arbitrageProfitWei * 10000n) / flashloanAmount) / 100;
      const confidence = this.calculateBalancerFlashloanConfidence(profitMargin, expectedProfit);

      return {
        flashloanToken,
        flashloanAmount,
        flashloanPremium, // Always 0 for Balancer!
        arbitrageRoute,
        expectedProfit,
        gasEstimate: totalGasEstimate,
        confidence
      };

    } catch (error) {
      logger.debug('Error building Balancer flashloan route', { error: (error as Error).message });
      return null;
    }
  }

  private calculateBalancerFlashloanConfidence(profitMargin: number, expectedProfit: bigint): number {
    let confidence = 0;

    // Higher base confidence due to zero fees
    confidence += 20;

    // Profit margin factor (lower threshold due to no fees)
    confidence += Math.min(profitMargin * 15, 40); // Max 40 points

    // Absolute profit factor
    const profitEth = Number(ethers.formatEther(expectedProfit));
    confidence += Math.min(profitEth * 20, 30); // Max 30 points

    // Balancer liquidity penalty (less liquidity than Aave)
    confidence -= 10;

    return Math.min(confidence, 100);
  }

  // ... (other helper methods similar to Aave implementation)

  private calculatePoolPrice(pool: Pool, token0: Token, token1: Token): number | null {
    try {
      if (pool.protocol === 'uniswap-v2' && pool.reserves) {
        // For V2, we need to check the actual token ordering in the pool
        const actualToken0 = pool.token0;
        const actualToken1 = pool.token1;

        // Determine which reserve corresponds to which token
        const isToken0First = actualToken0.address.toLowerCase() === token0.address.toLowerCase();

        const token0Reserve = isToken0First ? pool.reserves.reserve0 : pool.reserves.reserve1;
        const token1Reserve = isToken0First ? pool.reserves.reserve1 : pool.reserves.reserve0;

        const reserve0 = Number(ethers.formatUnits(token0Reserve, token0.decimals));
        const reserve1 = Number(ethers.formatUnits(token1Reserve, token1.decimals));

        if (reserve0 === 0) {
          logger.debug('Zero reserve found in V2 pool', { pool: pool.address });
          return null;
        }

        const price = reserve1 / reserve0;
        logger.debug('V2 pool price calculated', {
          pool: pool.address,
          token0: token0.symbol,
          token1: token1.symbol,
          reserve0,
          reserve1,
          price: price.toFixed(6)
        });

        return price;
      } else if (pool.protocol === 'uniswap-v3' && pool.tick !== undefined && pool.tick !== null) {
        // Use tick-based calculation for V3 pools (more accurate)
        const actualToken0 = pool.token0;
        const isToken0First = actualToken0.address.toLowerCase() === token0.address.toLowerCase();

        const tickNumber = Number(pool.tick);

        // Calculate raw price: 1.0001^tick represents token1/token0 in pool terms
        let rawPrice = Math.pow(1.0001, tickNumber);

        // The rawPrice represents pool.token1/pool.token0
        // We need to adjust for decimal differences first
        const decimalsAdjustment = Math.pow(10, pool.token1.decimals - pool.token0.decimals);
        let adjustedPrice = rawPrice / decimalsAdjustment;

        // Now adjust for token ordering to get token1/token0 in input terms
        let price;
        if (isToken0First) {
          // Pool order matches input order: pool.token0 = token0, pool.token1 = token1
          price = adjustedPrice; // Already token1/token0
        } else {
          // Pool order is reversed: pool.token0 = token1, pool.token1 = token0
          price = 1 / adjustedPrice; // Invert to get token1/token0
        }

        logger.debug('V3 pool price calculated', {
          pool: pool.address,
          token0: token0.symbol,
          token1: token1.symbol,
          tick: tickNumber,
          rawPrice,
          decimalsAdjustment,
          price: price.toFixed(6)
        });

        return price;
      } else if (pool.protocol === 'uniswap-v3' && pool.sqrtPriceX96) {
        // Fallback to sqrtPriceX96 if tick is not available
        const actualToken0 = pool.token0;
        const isToken0First = actualToken0.address.toLowerCase() === token0.address.toLowerCase();

        const sqrtPrice = Number(pool.sqrtPriceX96) / (2 ** 96);
        let price = sqrtPrice ** 2;

        // Adjust for token ordering
        if (!isToken0First) {
          price = 1 / price;
        }

        // Adjust for decimal differences
        const decimalsAdjustment = Math.pow(10, token0.decimals - token1.decimals);
        price = price * decimalsAdjustment;

        logger.debug('V3 pool price calculated from sqrtPriceX96', {
          pool: pool.address,
          token0: token0.symbol,
          token1: token1.symbol,
          sqrtPriceX96: pool.sqrtPriceX96.toString(),
          price: price.toFixed(6)
        });

        return price;
      }

      logger.debug('Unsupported pool protocol or missing data', {
        protocol: pool.protocol,
        hasReserves: !!pool.reserves,
        hasTick: pool.tick !== undefined,
        hasSqrtPrice: !!pool.sqrtPriceX96
      });

      return null;
    } catch (error) {
      logger.debug('Error calculating pool price', {
        error: (error as Error).message,
        pool: pool.address,
        protocol: pool.protocol
      });
      return null;
    }
  }

  private async calculateOptimalFlashloanAmount(
    flashloanToken: Token,
    arbitrageRoute: ArbitrageRoute
  ): Promise<bigint> {
    // Similar to Aave implementation but with 0% fees
    const baseAmount = ethers.parseUnits('1000', flashloanToken.decimals);
    const calculatedAmount = baseAmount * BigInt(5);
    return calculatedAmount > this.MAX_FLASHLOAN_AMOUNT ? this.MAX_FLASHLOAN_AMOUNT : calculatedAmount;
  }

  private async estimateBalancerFlashloanGasCost(): Promise<bigint> {
    const gasStrategy = await this.gasOptimizer.getCurrentGasStrategy();
    return BigInt(180000) * BigInt(gasStrategy.maxFeePerGas.toString()); // Slightly less gas than Aave
  }

  private async calculateOptimalArbitrageAmount(buyPool: Pool, sellPool: Pool, buyToken: Token, sellToken: Token): Promise<bigint> {
    const maxAmount = ethers.parseUnits('5000', buyToken.decimals);
    return maxAmount / BigInt(2);
  }

  private async estimateArbitrageGasCost(buyPool: Pool, sellPool: Pool, amount: bigint): Promise<bigint> {
    const gasPerSwap = BigInt(150000);
    const gasStrategy = await this.gasOptimizer.getCurrentGasStrategy();
    return gasPerSwap * BigInt(2) * BigInt(gasStrategy.maxFeePerGas.toString());
  }

  private async calculateArbitrageProfit(buyPool: Pool, sellPool: Pool, buyToken: Token, sellToken: Token, amount: bigint, gasCost: bigint): Promise<bigint> {
    try {
      // Calculate real arbitrage profit based on actual pool prices
      const buyPrice = this.calculatePoolPrice(buyPool, buyToken, sellToken);
      const sellPrice = this.calculatePoolPrice(sellPool, buyToken, sellToken);

      if (!buyPrice || !sellPrice) {
        logger.debug('Cannot calculate arbitrage profit - missing price data');
        return BigInt(0);
      }

      // Use enhanced price calculator with slippage protection
      const amountOut = await this.priceCalculator.getAmountOut(buyPool, buyToken, sellToken, amount);
      if (amountOut <= 0n) {
        logger.debug('Buy swap would return 0 tokens');
        return BigInt(0);
      }

      const finalAmount = await this.priceCalculator.getAmountOut(sellPool, sellToken, buyToken, amountOut);
      if (finalAmount <= 0n) {
        logger.debug('Sell swap would return 0 tokens');
        return BigInt(0);
      }

      // Calculate profit with additional safety margins
      const grossProfit = finalAmount > amount ? finalAmount - amount : BigInt(0);

      // Apply configurable safety margin for execution risks
      const safetyMarginPercent = BigInt(Math.floor((1 - this.SAFETY_MARGIN) * 100));
      const adjustedProfit = (grossProfit * safetyMarginPercent) / 100n;

      const netProfit = adjustedProfit > gasCost ? adjustedProfit - gasCost : BigInt(0);

      logger.debug('Enhanced arbitrage profit calculation', {
        buyPrice: buyPrice?.toFixed(6) || 'N/A',
        sellPrice: sellPrice?.toFixed(6) || 'N/A',
        amount: ethers.formatUnits(amount, buyToken.decimals),
        amountOut: ethers.formatUnits(amountOut, sellToken.decimals),
        finalAmount: ethers.formatUnits(finalAmount, buyToken.decimals),
        grossProfit: ethers.formatUnits(grossProfit, buyToken.decimals),
        adjustedProfit: ethers.formatUnits(adjustedProfit, buyToken.decimals),
        gasCost: ethers.formatEther(gasCost),
        netProfit: ethers.formatUnits(netProfit, buyToken.decimals),
        safetyMargin: `${(this.SAFETY_MARGIN * 100).toFixed(1)}%`
      });

      return netProfit;
    } catch (error) {
      logger.debug('Error calculating arbitrage profit', { error: (error as Error).message });
      return BigInt(0);
    }
  }

  private calculateSwapOutput(pool: Pool, tokenIn: Token, tokenOut: Token, amountIn: bigint): bigint {
    try {
      // Simplified swap calculation - in reality this would use the specific DEX formulas
      // For Uniswap V2: x * y = k formula
      // For Uniswap V3: concentrated liquidity math

      if (pool.protocol === 'uniswap-v2' && pool.reserves) {
        // Uniswap V2 constant product formula with 0.3% fee
        const fee = BigInt(997); // 0.3% fee = 99.7% of input
        const amountInWithFee = amountIn * fee;
        const reserve0 = BigInt(pool.reserves.reserve0.toString());
        const reserve1 = BigInt(pool.reserves.reserve1.toString());
        const numerator = amountInWithFee * reserve1;
        const denominator = (reserve0 * BigInt(1000)) + amountInWithFee;
        return numerator / denominator;
      } else if (pool.protocol === 'uniswap-v3') {
        // Simplified V3 calculation - would need tick math in reality
        const feeRate = pool.fee || 3000; // 0.3% default
        const feeMultiplier = BigInt(1000000 - feeRate);
        return (amountIn * feeMultiplier) / BigInt(1000000);
      }

      // Fallback: assume 1:1 ratio minus fees
      return (amountIn * BigInt(997)) / BigInt(1000);
    } catch (error) {
      logger.debug('Error calculating swap output', { error: (error as Error).message });
      return BigInt(0);
    }
  }

  private calculateArbitrageConfidence(profitPercentage: number, expectedProfit: bigint): number {
    let confidence = 0;
    confidence += Math.min(profitPercentage * 15, 40);
    const profitEth = Number(ethers.formatEther(expectedProfit));
    confidence += Math.min(profitEth * 20, 25);
    confidence += 15;
    return Math.min(confidence, 100);
  }

  private async estimateArbitrageProfit(arbitrageRoute: ArbitrageRoute, amount: bigint): Promise<bigint> {
    // Use the actual expected profit from the arbitrage route
    // Scale it based on the flashloan amount vs the route's optimal amount

    // For now, return the arbitrage route's expected profit directly
    // In a more sophisticated implementation, we'd scale based on amount
    return BigInt(arbitrageRoute.expectedProfit.toString());
  }

  async executeBalancerFlashloan(route: FlashloanRoute): Promise<boolean> {
    try {
      logger.separator();
      logger.system('🔵 Executing Balancer Flashloan Arbitrage');
      logger.system(`💰 ZERO FEES - Maximum profit retention!`);
      logger.system(`Flashloan Amount: ${ethers.formatUnits(route.flashloanAmount, route.flashloanToken.decimals)} ${route.flashloanToken.symbol}`);
      logger.profitCalculation(ethers.formatEther(route.expectedProfit), true);
      logger.info(`Confidence: ${route.confidence}%`);

      if (config.dryRun || config.simulationMode) {
        const mode = config.simulationMode ? 'SIMULATION' : 'DRY RUN';
        logger.info(`🎭 ${mode}: Balancer flashloan opportunity detected`);

        logger.info('Step 1: 🔵 Flashloan from Balancer (0% fees!)');
        logger.info(`  └─ Borrowing ${ethers.formatUnits(route.flashloanAmount, route.flashloanToken.decimals)} ${route.flashloanToken.symbol}`);
        logger.info(`  └─ Premium: 0 ${route.flashloanToken.symbol} (FREE!)`);

        logger.info('Step 2: 🔄 Execute Arbitrage');
        logger.info(`  └─ Buy on ${route.arbitrageRoute.pools[0].protocol.toUpperCase()}`);
        logger.info(`  └─ Sell on ${route.arbitrageRoute.pools[1].protocol.toUpperCase()}`);

        logger.info('Step 3: 💸 Repay Flashloan');
        logger.info(`  └─ Repaying ${ethers.formatUnits(route.flashloanAmount, route.flashloanToken.decimals)} ${route.flashloanToken.symbol} (no premium!)`);

        logger.info('Step 4: 💎 Keep 100% of Profit');
        logger.profitCalculation(ethers.formatEther(route.expectedProfit), true);

        if (config.simulationMode) {
          logger.info(`🎭 ${mode}: Would execute via Balancer contract`);
          logger.info(`   Contract: ${this.balancerContract?.target || 'Not deployed'}`);
        }

        logger.success(`✅ Balancer flashloan ${mode.toLowerCase()} completed successfully`);
        logger.separator();
        return true;
      }

      // Execute using Balancer contract if available
      if (this.balancerContract) {
        const success = await this.executeWithBalancerContract(route);
        logger.success('✅ Balancer flashloan executed via contract');
        return success;
      } else {
        logger.warn('⚠️  Balancer flashloan execution requires contract deployment');
        logger.warn('   Deploy with: npx hardhat run scripts/deploy-balancer-flashloan.js');
        return false;
      }

    } catch (error) {
      logger.error('Balancer flashloan execution failed', error);
      logger.logError(error as Error, 'BalancerFlashloanStrategy.executeBalancerFlashloan');
      return false;
    }
  }

  private async executeWithBalancerContract(route: FlashloanRoute): Promise<boolean> {
    try {
      if (!this.balancerContract) {
        throw new Error('Balancer contract not initialized');
      }

      logger.info('🔧 Preparing Balancer flashloan transaction...');

      // Validate route parameters
      if (!route.flashloanToken?.address || !route.arbitrageRoute?.tokens?.[1]?.address) {
        throw new Error('Invalid route: missing token addresses');
      }

      if (!route.arbitrageRoute?.pools?.[0] || !route.arbitrageRoute?.pools?.[1]) {
        throw new Error('Invalid route: missing pool information');
      }

      // CRITICAL: Perform comprehensive pre-execution validation
      const validationResult = await this.validateArbitrageOpportunity(route);
      if (!validationResult.isValid) {
        logger.warn(`❌ Pre-execution validation failed: ${validationResult.reason}`);
        return false;
      }

      logger.info(`✅ Pre-execution validation passed (confidence: ${validationResult.confidence}%)`);

      // Use validated profit for minimum threshold
      const validatedProfit = validationResult.adjustedProfit;

      // Prepare tokens array - convert addresses to IERC20 format for contract
      const tokenAddresses = [route.flashloanToken.address];
      const amounts = [route.flashloanAmount];

      logger.debug('Flashloan parameters', {
        token: route.flashloanToken.symbol,
        amount: ethers.formatUnits(route.flashloanAmount, route.flashloanToken.decimals),
        targetToken: route.arbitrageRoute.tokens[1].symbol
      });

      // Get router addresses with validation
      const buyDexAddress = route.arbitrageRoute.pools[0].protocol === 'uniswap-v2'
        ? ADDRESSES.UNISWAP_V2_ROUTER
        : ADDRESSES.UNISWAP_V3_ROUTER;

      const sellDexAddress = route.arbitrageRoute.pools[1].protocol === 'uniswap-v2'
        ? ADDRESSES.UNISWAP_V2_ROUTER
        : ADDRESSES.UNISWAP_V3_ROUTER;

      // Validate router addresses
      if (!buyDexAddress || !sellDexAddress) {
        throw new Error('Invalid DEX router addresses');
      }

      // Use validated profit with additional safety margin (70% of validated profit)
      const safeMinProfit = (validatedProfit * 70n) / 100n;

      // Encode arbitrage parameters with validated profit threshold
      const arbitrageParams = ethers.AbiCoder.defaultAbiCoder().encode(
        ['address', 'address', 'address', 'address', 'uint24', 'uint256'],
        [
          route.flashloanToken.address, // tokenA
          route.arbitrageRoute.tokens[1].address, // tokenB
          buyDexAddress, // buyDex
          sellDexAddress, // sellDex
          route.arbitrageRoute.pools[0].fee || 3000, // v3Fee
          safeMinProfit // Use safe minimum profit from validation
        ]
      );

      logger.debug('Arbitrage parameters encoded', {
        buyDex: buyDexAddress,
        sellDex: sellDexAddress,
        fee: route.arbitrageRoute.pools[0].fee || 3000,
        originalProfit: ethers.formatEther(route.expectedProfit),
        validatedProfit: ethers.formatEther(validatedProfit),
        safeMinProfit: ethers.formatEther(safeMinProfit)
      });

      // Re-validate opportunity before execution (prices may have changed)
      logger.info('🔍 Re-validating arbitrage opportunity...');

      const revalidatedProfit = await this.priceCalculator.calculateArbitrageProfit(
        route.arbitrageRoute.pools[0],
        route.arbitrageRoute.pools[1],
        route.flashloanToken,
        route.arbitrageRoute.tokens[1],
        BigInt(route.flashloanAmount.toString())
      );

      const expectedProfitBigInt = BigInt(route.expectedProfit.toString());
      if (revalidatedProfit.profit < expectedProfitBigInt / 2n) {
        logger.warn('Opportunity profit decreased significantly, aborting execution');
        logger.debug(`Original profit: ${ethers.formatEther(expectedProfitBigInt)} ETH`);
        logger.debug(`Current profit: ${ethers.formatEther(revalidatedProfit.profit)} ETH`);
        return false;
      }

      // Estimate gas before execution
      logger.info('⛽ Estimating gas for flashloan transaction...');

      try {
        const gasEstimate = await this.balancerContract.executeFlashloanArbitrage.estimateGas(
          tokenAddresses,
          amounts,
          arbitrageParams
        );

        logger.info(`Gas estimate: ${gasEstimate.toString()}`);

        // Check if gas estimate is reasonable (not too high)
        if (gasEstimate > BigInt(500000)) {
          logger.warn(`High gas estimate: ${gasEstimate.toString()}, proceeding with caution`);
        }
      } catch (gasError) {
        logger.error('Gas estimation failed', gasError);
        throw new Error(`Gas estimation failed: ${(gasError as Error).message}`);
      }

      // Execute Balancer flashloan with proper gas settings
      logger.info('🚀 Executing Balancer flashloan...');

      const gasStrategy = await this.gasOptimizer.getCurrentGasStrategy();
      const tx = await this.balancerContract.executeFlashloanArbitrage(
        tokenAddresses,
        amounts,
        arbitrageParams,
        {
          gasLimit: BigInt(400000), // Conservative gas limit
          maxFeePerGas: BigInt(gasStrategy.maxFeePerGas.toString()),
          maxPriorityFeePerGas: BigInt(gasStrategy.priorityFee.toString())
        }
      );

      logger.info(`📝 Transaction hash: ${tx.hash}`);

      // Wait for confirmation
      const receipt = await tx.wait();
      logger.info(`✅ Transaction confirmed in block ${receipt.blockNumber}`);

      return true;
    } catch (error) {
      logger.error('Balancer contract execution failed', error);
      logger.logError(error as Error, 'BalancerFlashloanStrategy.executeWithBalancerContract');
      return false;
    }
  }

  /**
   * Comprehensive pre-execution validation to prevent on-chain failures
   */
  private async validateArbitrageOpportunity(route: FlashloanRoute): Promise<{
    isValid: boolean;
    reason?: string;
    confidence: number;
    adjustedProfit: bigint;
  }> {
    try {
      logger.debug('🔍 Starting comprehensive arbitrage validation...');

      // 1. Re-calculate profit with current market conditions
      const currentProfit = await this.priceCalculator.calculateArbitrageProfit(
        route.arbitrageRoute.pools[0],
        route.arbitrageRoute.pools[1],
        route.flashloanToken,
        route.arbitrageRoute.tokens[1],
        BigInt(route.flashloanAmount.toString())
      );

      // 2. Check if profit decreased significantly (>30% drop)
      const expectedProfitBigInt = BigInt(route.expectedProfit.toString());
      const profitDropPercentage = ((expectedProfitBigInt - currentProfit.profit) * 100n) / expectedProfitBigInt;
      if (profitDropPercentage > 30n) {
        return {
          isValid: false,
          reason: `Profit dropped by ${profitDropPercentage}% since detection`,
          confidence: 0,
          adjustedProfit: 0n
        };
      }

      // 3. Simulate the exact swaps that will happen on-chain
      const simulationResult = await this.simulateArbitrageSwaps(route);
      if (!simulationResult.success) {
        return {
          isValid: false,
          reason: `Swap simulation failed: ${simulationResult.error}`,
          confidence: 0,
          adjustedProfit: 0n
        };
      }

      // 4. Calculate gas costs with current network conditions
      const gasStrategy = await this.gasOptimizer.getCurrentGasStrategy();
      const estimatedGasCost = BigInt(400000) * BigInt(gasStrategy.maxFeePerGas.toString());
      const gasCostInEth = estimatedGasCost;

      // 5. Ensure profit after gas costs is still significant
      const netProfit = simulationResult.actualProfit - gasCostInEth;
      const minProfitThreshold = ethers.parseEther(process.env.MIN_PROFIT_THRESHOLD || '0.01');

      if (netProfit < minProfitThreshold) {
        return {
          isValid: false,
          reason: `Net profit ${ethers.formatEther(netProfit)} ETH below threshold ${ethers.formatEther(minProfitThreshold)} ETH`,
          confidence: 0,
          adjustedProfit: 0n
        };
      }

      // 6. Check liquidity depth for both pools
      const liquidityCheck = await this.validatePoolLiquidity(route);
      if (!liquidityCheck.sufficient) {
        return {
          isValid: false,
          reason: `Insufficient liquidity: ${liquidityCheck.reason}`,
          confidence: 0,
          adjustedProfit: 0n
        };
      }

      // 7. Calculate confidence score based on multiple factors
      let confidence = 100;

      // Reduce confidence based on profit drop
      confidence -= Number(profitDropPercentage);

      // Reduce confidence for large trades relative to liquidity
      if (liquidityCheck.tradeToLiquidityRatio > 5) {
        confidence -= liquidityCheck.tradeToLiquidityRatio * 2;
      }

      // Reduce confidence during high gas periods
      const maxFeePerGasBigInt = BigInt(gasStrategy.maxFeePerGas.toString());
      if (maxFeePerGasBigInt > 50n) {
        confidence -= 10;
      }

      confidence = Math.max(confidence, 0);

      // 8. Final validation - check if we should skip confidence scoring
      if (config.pureProfitMode) {
        // Pure Profit Mode: Skip confidence validation, rely on other validations
        logger.debug(`🚀 Skipping confidence check (calculated: ${confidence}%)`);
      } else {
        // Traditional Mode: Use dynamic confidence based on trading mode
        const mode = process.env.TRADING_MODE?.toLowerCase() as 'aggressive' | 'balanced' | 'conservative';
        const requiredConfidence = this.getConfidenceThreshold(mode || 'balanced');

        if (confidence < requiredConfidence) {
          return {
            isValid: false,
            reason: `Confidence score ${confidence}% too low (minimum ${requiredConfidence}% for ${mode || 'balanced'} mode)`,
            confidence,
            adjustedProfit: simulationResult.actualProfit
          };
        }
      }

      logger.debug(`✅ Validation passed: confidence=${confidence}%, netProfit=${ethers.formatEther(netProfit)} ETH`);

      return {
        isValid: true,
        confidence,
        adjustedProfit: simulationResult.actualProfit
      };

    } catch (error) {
      logger.error('Validation error:', error);
      return {
        isValid: false,
        reason: `Validation error: ${(error as Error).message}`,
        confidence: 0,
        adjustedProfit: 0n
      };
    }
  }

  /**
   * Simulate the exact swaps that will happen on-chain
   */
  private async simulateArbitrageSwaps(route: FlashloanRoute): Promise<{
    success: boolean;
    actualProfit: bigint;
    error?: string;
  }> {
    try {
      // Simulate buy swap with slippage
      const buyAmountOut = await this.priceCalculator.getAmountOut(
        route.arbitrageRoute.pools[0],
        route.flashloanToken,
        route.arbitrageRoute.tokens[1],
        BigInt(route.flashloanAmount.toString())
      );

      if (buyAmountOut <= 0n) {
        return { success: false, actualProfit: 0n, error: 'Buy swap would return 0 tokens' };
      }

      // Apply realistic slippage for buy swap (3-5% depending on pool type)
      const buySlippage = route.arbitrageRoute.pools[0].protocol === 'uniswap-v3' ? 95n : 97n; // 5% or 3%
      const actualBuyAmount = (buyAmountOut * buySlippage) / 100n;

      // Simulate sell swap with slippage
      const sellAmountOut = await this.priceCalculator.getAmountOut(
        route.arbitrageRoute.pools[1],
        route.arbitrageRoute.tokens[1],
        route.flashloanToken,
        actualBuyAmount
      );

      if (sellAmountOut <= 0n) {
        return { success: false, actualProfit: 0n, error: 'Sell swap would return 0 tokens' };
      }

      // Apply realistic slippage for sell swap
      const sellSlippage = route.arbitrageRoute.pools[1].protocol === 'uniswap-v3' ? 95n : 97n;
      const actualSellAmount = (sellAmountOut * sellSlippage) / 100n;

      // Calculate actual profit after both slippages
      const flashloanAmountBigInt = BigInt(route.flashloanAmount.toString());
      const actualProfit = actualSellAmount > flashloanAmountBigInt ?
        actualSellAmount - flashloanAmountBigInt : 0n;

      if (actualProfit <= 0n) {
        return {
          success: false,
          actualProfit: 0n,
          error: `Simulation shows loss: received ${ethers.formatEther(actualSellAmount)} ETH, needed ${ethers.formatEther(flashloanAmountBigInt)} ETH`
        };
      }

      return { success: true, actualProfit };

    } catch (error) {
      return {
        success: false,
        actualProfit: 0n,
        error: `Simulation error: ${(error as Error).message}`
      };
    }
  }

  /**
   * Validate that pools have sufficient liquidity for the trade
   */
  private async validatePoolLiquidity(route: FlashloanRoute): Promise<{
    sufficient: boolean;
    reason?: string;
    tradeToLiquidityRatio: number;
  }> {
    try {
      // Check buy pool liquidity
      const buyPoolLiquidity = await this.getPoolLiquidity(route.arbitrageRoute.pools[0]);
      const flashloanAmountBigInt = BigInt(route.flashloanAmount.toString());
      const buyTradeRatio = Number((flashloanAmountBigInt * 100n) / buyPoolLiquidity) / 100;

      // Check sell pool liquidity
      const sellPoolLiquidity = await this.getPoolLiquidity(route.arbitrageRoute.pools[1]);
      const sellTradeRatio = Number((flashloanAmountBigInt * 100n) / sellPoolLiquidity) / 100;

      const maxTradeRatio = Math.max(buyTradeRatio, sellTradeRatio);

      // Reject trades that are more than 10% of pool liquidity
      if (maxTradeRatio > 10) {
        return {
          sufficient: false,
          reason: `Trade size ${maxTradeRatio.toFixed(2)}% of pool liquidity (max 10%)`,
          tradeToLiquidityRatio: maxTradeRatio
        };
      }

      return {
        sufficient: true,
        tradeToLiquidityRatio: maxTradeRatio
      };

    } catch (error) {
      return {
        sufficient: false,
        reason: `Liquidity check failed: ${(error as Error).message}`,
        tradeToLiquidityRatio: 100
      };
    }
  }

  /**
   * Get pool liquidity for validation
   */
  private async getPoolLiquidity(pool: Pool): Promise<bigint> {
    try {
      if (pool.protocol === 'uniswap-v2' && pool.reserves) {
        // For V2, use the smaller of the two reserves as liquidity measure
        const reserve0 = BigInt(pool.reserves.reserve0.toString());
        const reserve1 = BigInt(pool.reserves.reserve1.toString());
        return reserve0 < reserve1 ? reserve0 : reserve1;
      } else if (pool.protocol === 'uniswap-v3') {
        // For V3, query liquidity directly
        const poolContract = new ethers.Contract(
          pool.address,
          ['function liquidity() external view returns (uint128)'],
          this.provider
        );
        const liquidity = await poolContract.liquidity();
        return BigInt(liquidity.toString());
      }

      // Default fallback
      return ethers.parseEther('1000'); // Assume 1000 ETH liquidity if unknown
    } catch (error) {
      logger.debug('Error getting pool liquidity:', error);
      return ethers.parseEther('100'); // Conservative fallback
    }
  }

  /**
   * Set whether this strategy is being called from the dynamic strategy
   * This helps reduce duplicate logging when multiple strategies are coordinated
   */
  public setCalledFromDynamicStrategy(value: boolean): void {
    this.isCalledFromDynamicStrategy = value;
  }

  /**
   * Get available token liquidity from Balancer pools
   */
  public async getTokenLiquidity(tokenAddress: string): Promise<number> {
    try {
      // Check cached liquidity first
      const cached = this.cachedLiquidity.get(tokenAddress.toLowerCase());
      if (cached && (Date.now() - this.liquidityLastChecked) < this.LIQUIDITY_CACHE_DURATION) {
        return Number(ethers.formatUnits(cached, 6)); // Assume 6 decimals for USDC/USDT
      }

      // Use the existing fetchBalancerLiquidity method from PoolManager
      const liquidityData = await this.poolManager.fetchBalancerLiquidity(tokenAddress);

      // Cache the result
      this.cachedLiquidity.set(tokenAddress.toLowerCase(), liquidityData.totalLiquidity);
      this.liquidityLastChecked = Date.now();

      // Return as human-readable number (assuming 6 decimals for stablecoins)
      const decimals = tokenAddress.toLowerCase() === '******************************************' ? 18 : 6; // WETH vs stablecoins
      return Number(ethers.formatUnits(liquidityData.totalLiquidity, decimals));

    } catch (error) {
      logger.debug(`Error getting token liquidity for ${tokenAddress}: ${(error as Error).message}`);
      return 0;
    }
  }

  /**
   * Check if a token pair is blacklisted
   */
  private isPairBlacklisted(tokenA: string, tokenB: string): boolean {
    const pairKey = this.getPairKey(tokenA, tokenB);
    const failureInfo = this.failedPairs.get(pairKey);

    if (!failureInfo) return false;

    // Check if blacklist duration has expired
    const now = Date.now();
    if (now - failureInfo.lastFailed > this.BLACKLIST_DURATION) {
      // Reset the failure count after blacklist duration
      this.failedPairs.delete(pairKey);
      return false;
    }

    // Check if pair has exceeded max failures
    return failureInfo.count >= this.MAX_FAILURES;
  }

  /**
   * Record a failed arbitrage attempt for a token pair
   */
  private recordPairFailure(tokenA: string, tokenB: string): void {
    const pairKey = this.getPairKey(tokenA, tokenB);
    const existing = this.failedPairs.get(pairKey);
    const now = Date.now();

    if (existing) {
      existing.count++;
      existing.lastFailed = now;
    } else {
      this.failedPairs.set(pairKey, { count: 1, lastFailed: now });
    }

    const failureInfo = this.failedPairs.get(pairKey)!;
    if (failureInfo.count >= this.MAX_FAILURES) {
      logger.debug(`🚫 Blacklisting pair ${tokenA}/${tokenB} after ${failureInfo.count} failures`);
    }
  }

  /**
   * Get a consistent key for a token pair
   */
  private getPairKey(tokenA: string, tokenB: string): string {
    // Sort addresses to ensure consistent key regardless of order
    return tokenA.toLowerCase() < tokenB.toLowerCase()
      ? `${tokenA.toLowerCase()}-${tokenB.toLowerCase()}`
      : `${tokenB.toLowerCase()}-${tokenA.toLowerCase()}`;
  }
}
