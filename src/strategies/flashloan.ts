import { ethers } from 'ethers';
import { FlashloanRoute, ArbitrageRoute, Pool, Token, Transaction } from '../types';
import { PoolManager } from '../dex/pools';
import { GasOptimizer } from '../gas/optimizer';
import { CalldataEncoder } from '../calldata/encoder';
import { BundleSimulator } from '../simulation/simulator';
import { FlashloanContractInterface } from '../contracts/flashloan';
import { BalancerFlashloanStrategy } from './balancer-flashloan';
import { config, COMMON_TOKENS, ADDRESSES, getDexConfig, getAvailableDexPairs, DexName, getConfiguredTokens, getPrimaryFlashloanToken, getTargetTokens, validateTokenConfig } from '../config';
import { CurveSwapper } from '../dex/curve';
import { logger } from '../utils/logger';

import { mockOpportunityGenerator } from '../test/mockOpportunities';

// Import ABIs for Uniswap V2 and V3 Routers
import IUniswapV2RouterABI from '../../artifacts/contracts/BalancerFlashloanArbitrage.sol/IUniswapV2Router.json';
import IUniswapV3RouterABI from '../../artifacts/contracts/BalancerFlashloanArbitrage.sol/IUniswapV3Router.json';

const uniswapV2RouterInterface = new ethers.Interface(IUniswapV2RouterABI.abi);
const uniswapV3RouterInterface = new ethers.Interface(IUniswapV3RouterABI.abi);

import { tokenAmountToWei } from '../utils/denomination';

export class FlashloanStrategy {
  private poolManager: PoolManager;
  private gasOptimizer: GasOptimizer;
  private encoder: CalldataEncoder;
  private simulator: BundleSimulator;
  private flashloanInterface: FlashloanContractInterface;
  private balancerStrategy: BalancerFlashloanStrategy;
  private curveSwapper: CurveSwapper;
  private hybridContract: ethers.Contract | null = null;
  private wallet: ethers.Wallet;
  private provider: ethers.Provider;
  private readonly MIN_PROFIT_THRESHOLD: number;
  private readonly FLASHLOAN_PREMIUM_BPS = 9; // 0.09% premium
  private readonly MAX_FLASHLOAN_AMOUNT: bigint;
  private readonly SLIPPAGE_TOLERANCE = 0.005; // 0.5% slippage tolerance

  // Confidence thresholds for different trading strategies
  private readonly MAINNET_MIN_CONFIDENCE = 50;      // Balanced approach for mainnet
  private readonly AGGRESSIVE_THRESHOLD = 45;        // High-frequency, smaller profits
  private readonly CONSERVATIVE_THRESHOLD = 60;      // Fewer, higher-confidence trades
  private readonly TESTNET_MIN_CONFIDENCE = 40;      // Lower threshold for testing

  // Block-based opportunity deduplication
  private processedOpportunitiesThisBlock = new Set<string>();
  private currentBlockNumber = 0;
  private isCalledFromDynamicStrategy = false;

  // Blacklist for failed token pairs
  private failedPairs: Map<string, { count: number; lastFailed: number }> = new Map();
  private readonly MAX_FAILURES = 1; // Max failures before blacklisting
  private readonly BLACKLIST_DURATION = 3000000000000; // 5 minutes in milliseconds

  constructor(provider: ethers.Provider) {
    this.provider = provider;
    this.poolManager = new PoolManager();
    this.gasOptimizer = new GasOptimizer();
    this.encoder = new CalldataEncoder();
    this.simulator = new BundleSimulator();
    this.wallet = new ethers.Wallet(config.privateKey, provider);

    // Network-specific configuration
    const isMainnet = config.chainId === 1;
    this.MIN_PROFIT_THRESHOLD = isMainnet ? 0.005 : 0.002; // 0.5% mainnet, 0.2% testnet (much lower)

    // Set max flashloan amount based on primary token
    const primaryToken = getPrimaryFlashloanToken();
    if (primaryToken?.symbol === 'WETH') {
      this.MAX_FLASHLOAN_AMOUNT = isMainnet
        ? ethers.parseUnits('100', 18)  // 100 ETH on mainnet
        : ethers.parseUnits('10', 18);  // 10 ETH on testnet
    } else {
      // For USDC/USDT and other tokens - use realistic amounts based on available liquidity
      this.MAX_FLASHLOAN_AMOUNT = isMainnet
        ? ethers.parseUnits('25000', 6) // 25k USDC on mainnet (realistic for Balancer liquidity)
        : ethers.parseUnits('10000', 6); // 10k USDC on testnet
    }

    this.flashloanInterface = new FlashloanContractInterface(
      provider,
      ADDRESSES.AAVE_POOL,
      ADDRESSES.AAVE_POOL_ADDRESSES_PROVIDER
    );

    // Initialize Balancer strategy for 0% fee flashloans
    this.balancerStrategy = new BalancerFlashloanStrategy(provider);

    // Initialize Curve swapper for low-slippage stablecoin swaps
    this.curveSwapper = new CurveSwapper(provider as ethers.JsonRpcProvider, this.wallet);

    // Initialize hybrid contract if address is provided
    if (config.hybridFlashloanContract) {
      this.hybridContract = new ethers.Contract(
        config.hybridFlashloanContract,
        this.getHybridContractABI(),
        this.wallet
      );
      logger.system(`🔄 Hybrid Contract: ${config.hybridFlashloanContract}`);
    } else {
      logger.warn('⚠️  No hybrid contract address provided - using individual strategies');
    }

    if (isMainnet) {
      logger.system('🚨 MAINNET MODE: Using conservative flashloan parameters');
      logger.system('🔄 HYBRID STRATEGY: Aave + Balancer flashloans');
      logger.system(`   Min Profit: ${this.MIN_PROFIT_THRESHOLD * 100}%`);
      logger.system(`   Max Amount: ${ethers.formatUnits(this.MAX_FLASHLOAN_AMOUNT, 6)} USDC`);
      logger.system('   💰 Balancer: 0% fees | Aave: 0.09% fees');
      logger.system('')
    }
  }

  /**
   * Handle new block - reset processed opportunities for the new block
   */
  handleNewBlock(blockNumber: number): void {
    if (blockNumber > this.currentBlockNumber) {
      const previousBlock = this.currentBlockNumber;
      this.currentBlockNumber = blockNumber;
      const clearedCount = this.processedOpportunitiesThisBlock.size;
      this.processedOpportunitiesThisBlock.clear();

      if (clearedCount > 0) {
        logger.debug(`🔄 FlashloanStrategy: New block ${blockNumber} (prev: ${previousBlock}) - cleared ${clearedCount} processed opportunities`);
      }
    }
  }

  /**
   * Get confidence threshold based on network and trading mode
   */
  private getConfidenceThreshold(mode: 'aggressive' | 'balanced' | 'conservative' = 'balanced'): number {
    // Use different thresholds based on network
    if (config.chainId !== 1) {
      // Testnet - use lower threshold for testing
      return this.TESTNET_MIN_CONFIDENCE;
    }

    // Mainnet thresholds based on trading mode
    switch (mode) {
      case 'aggressive':
        return this.AGGRESSIVE_THRESHOLD;
      case 'conservative':
        return this.CONSERVATIVE_THRESHOLD;
      case 'balanced':
      default:
        return this.MAINNET_MIN_CONFIDENCE;
    }
  }

  /**
   * Get current trading mode from environment or default to balanced
   */
  private getTradingMode(): 'aggressive' | 'balanced' | 'conservative' {
    const mode = process.env.TRADING_MODE?.toLowerCase();

    switch (mode) {
      case 'aggressive':
        return 'aggressive';
      case 'conservative':
        return 'conservative';
      case 'balanced':
      default:
        return 'balanced';
    }
  }

  private getHybridContractABI(): string[] {
    return [
      // Execute optimal flashloan
      'function executeOptimalFlashloan(address asset, uint256 amount, bytes calldata params) external',
      // Get optimal provider
      'function getOptimalProvider(address asset, uint256 amount) external view returns (uint8)',
      // Withdraw profits
      'function withdrawProfits(address token) external',
      // Emergency withdraw
      'function emergencyWithdraw(address token, uint256 amount) external'
    ];
  }

  async scanForFlashloanOpportunities(txHash: string): Promise<FlashloanRoute[]> {
    const opportunities: FlashloanRoute[] = [];

    try {
      // Silent scanning - only log results, not process
      logger.debug('Scanning for flashloan opportunities', {
        dexPairs: config.flashloanDexPairs.join(', '),
        crossDex: config.enableCrossDexArbitrage
      });

      // Get available DEXs for current network
      const availableDexs = getAvailableDexPairs();
      const dexConfig = getDexConfig();

      // Filter configured DEX pairs to only include available ones
      const configuredDexs = config.flashloanDexPairs.filter(dex =>
        availableDexs.includes(dex as DexName)
      );

      if (configuredDexs.length < 2) {
        logger.debug('Insufficient DEXs for arbitrage', {
          configured: config.flashloanDexPairs,
          available: availableDexs,
          filtered: configuredDexs,
          required: 2
        });
        return opportunities;
      }

      // Validate token configuration
      const tokenValidation = validateTokenConfig();
      if (!tokenValidation.valid) {
        logger.error('❌ Token configuration errors:');
        tokenValidation.errors.forEach(error => logger.error(`   • ${error}`));
        return opportunities;
      }

      // Get primary flashloan token
      const flashloanToken = getPrimaryFlashloanToken();
      if (!flashloanToken) {
        logger.warn('Primary flashloan token not found');
        return opportunities;
      }

      // Get target tokens for arbitrage
      const targetTokens = config.enableAllTokenPairs ? getConfiguredTokens() : getTargetTokens();

      logger.debug('Starting flashloan opportunity scan', {
        configuredDexs,
        availableDexs,
        flashloanToken: flashloanToken.symbol,
        targetTokens: targetTokens.map(t => t.symbol)
      });

      logger.debug('Flashloan scan configuration', {
        primaryToken: `${flashloanToken.symbol} (${flashloanToken.name})`,
        targetTokens: targetTokens.map((t: any) => t.symbol).join(', ')
      });

      if (targetTokens.length === 0) {
        logger.error('No target tokens configured');
        return opportunities;
      }

      // Scan for arbitrage opportunities between configured DEXs and tokens
      for (let i = 0; i < targetTokens.length; i++) {
        const targetToken = targetTokens[i];

        if (targetToken.address === flashloanToken.address) {
          continue; // Skip same token
        }

        if (config.enableCrossDexArbitrage) {
          // Find best arbitrage across all configured DEX combinations
          const arbitrageRoutes = await this.findCrossDexArbitrage(
            flashloanToken,
            targetToken,
            configuredDexs as DexName[]
          );

          for (const arbitrageRoute of arbitrageRoutes) {
            const flashloanRoute = await this.buildEnhancedFlashloanRoute(
              flashloanToken,
              arbitrageRoute
            );

            // Check if we should skip confidence scoring (Pure Profit Mode)
            if (config.pureProfitMode) {
              // Pure Profit Mode: Execute all profitable opportunities
              if (flashloanRoute) {
                // Create unique identifier for opportunity deduplication (include block number)
                const opportunityId = `flashloan-block${this.currentBlockNumber}hash${txHash}-${flashloanRoute.flashloanToken.symbol}-${ethers.formatEther(flashloanRoute.expectedProfit)}-${flashloanRoute.confidence.toFixed(2)}`;

                // Check if we've already processed this opportunity in the current block
                if (this.processedOpportunitiesThisBlock.has(opportunityId)) {
                  logger.debug('🚫 Skipping already processed flashloan opportunity in this block', {
                    id: opportunityId,
                    block: this.currentBlockNumber
                  });
                  continue;
                }

                // Mark as processed for this block
                this.processedOpportunitiesThisBlock.add(opportunityId);

                opportunities.push(flashloanRoute);
                if (!this.isCalledFromDynamicStrategy) {
                  logger.debug('🚀 Flashloan opportunity found', {
                    profit: ethers.formatEther(flashloanRoute.expectedProfit),
                    confidence: flashloanRoute.confidence,
                    mode: 'PURE_PROFIT',
                    token: flashloanRoute.flashloanToken.symbol,
                    block: this.currentBlockNumber
                  });
                }
              }
            } else {
              // Traditional Mode: Use confidence threshold
              const tradingMode = this.getTradingMode();
              const minConfidence = this.getConfidenceThreshold(tradingMode);
              if (flashloanRoute && flashloanRoute.confidence >= minConfidence) {
                // Create unique identifier for opportunity deduplication (include block number)
                const opportunityId = `flashloan-block${this.currentBlockNumber}hash${txHash}-${flashloanRoute.flashloanToken.symbol}-${ethers.formatEther(flashloanRoute.expectedProfit)}-${flashloanRoute.confidence.toFixed(2)}`;

                // Check if we've already processed this opportunity in the current block
                if (this.processedOpportunitiesThisBlock.has(opportunityId)) {
                  logger.debug('🚫 Skipping already processed flashloan opportunity in this block', {
                    id: opportunityId,
                    block: this.currentBlockNumber
                  });
                  continue;
                }

                // Mark as processed for this block
                this.processedOpportunitiesThisBlock.add(opportunityId);

                opportunities.push(flashloanRoute);
                if (!this.isCalledFromDynamicStrategy) {
                  logger.system('🚀 Flashloan opportunity found', {
                    profit: ethers.formatEther(flashloanRoute.expectedProfit),
                    confidence: flashloanRoute.confidence,
                    threshold: minConfidence,
                    mode: tradingMode,
                    block: this.currentBlockNumber
                  });
                }
              } else if (flashloanRoute) {
                logger.debug('Flashloan opportunity filtered out', {
                  profit: ethers.formatEther(flashloanRoute.expectedProfit),
                  confidence: flashloanRoute.confidence,
                  minRequired: minConfidence,
                  reason: 'Low confidence'
                });
              }
            }
          }
        } else {
          // Use specific buy/sell DEX configuration
          const arbitrageRoute = await this.findSpecificDexArbitrage(
            flashloanToken,
            targetToken,
            config.flashloanBuyDex as DexName,
            config.flashloanSellDex as DexName
          );

          if (arbitrageRoute) {
            const flashloanRoute = await this.buildEnhancedFlashloanRoute(
              flashloanToken,
              arbitrageRoute
            );

            // Check if we should skip confidence scoring (Pure Profit Mode)
            if (config.pureProfitMode) {
              // Pure Profit Mode: Execute all profitable opportunities
              if (flashloanRoute) {
                opportunities.push(flashloanRoute);
                if (!this.isCalledFromDynamicStrategy) {
                  logger.system('🚀 Flashloan opportunity found', {
                    profit: ethers.formatEther(flashloanRoute.expectedProfit),
                    confidence: flashloanRoute.confidence,
                    mode: 'PURE_PROFIT',
                    token: flashloanRoute.flashloanToken.symbol
                  });
                }
              }
            } else {
              // Traditional Mode: Use confidence threshold
              const tradingMode = this.getTradingMode();
              const minConfidence = this.getConfidenceThreshold(tradingMode);
              if (flashloanRoute && flashloanRoute.confidence >= minConfidence) {
                opportunities.push(flashloanRoute);
                if (!this.isCalledFromDynamicStrategy) {
                  logger.system('🚀 Flashloan opportunity found', {
                    profit: ethers.formatEther(flashloanRoute.expectedProfit),
                    confidence: flashloanRoute.confidence,
                    threshold: minConfidence,
                    mode: tradingMode
                  });
                }
              }
            }
          }
        }
      }

      // Also scan Balancer opportunities (0% fees!)
      const balancerOpportunities = await this.balancerStrategy.scanForBalancerFlashloanOpportunities(txHash);
      opportunities.push(...balancerOpportunities);

      // Add mock opportunities if enabled (for testing on Sepolia)
      logger.debug('Mock opportunity check', {
        mockEnabled: mockOpportunityGenerator.isMockEnabled(),
        realOpportunities: opportunities.length,
        chainId: config.chainId,
        testMode: process.env.ENABLE_TEST_MODE,
        mockOpportunities: process.env.MOCK_OPPORTUNITIES
      });

      if (mockOpportunityGenerator.isMockEnabled() && opportunities.length === 0) {
        logger.info('🧪 No real opportunities found, generating mock opportunities for testing...');
        const mockOpportunities = mockOpportunityGenerator.generateMockFlashloanOpportunities();
        opportunities.push(...mockOpportunities);

        if (mockOpportunities.length > 0) {
          logger.info(`🧪 Generated ${mockOpportunities.length} mock opportunities for testing`);
        } else {
          logger.info('⚠️  Mock opportunity generator returned no opportunities');
        }
      } else if (mockOpportunityGenerator.isMockEnabled() && opportunities.length > 0) {
        logger.info(`✅ Found ${opportunities.length} real opportunities, skipping mock generation`);
      }

      // Sort by expected profit
      opportunities.sort((a, b) =>
        Number(BigInt(b.expectedProfit.toString()) - BigInt(a.expectedProfit.toString()))
      );

      const aaveCount = opportunities.length - balancerOpportunities.length;
      const balancerCount = balancerOpportunities.length;

      // Only log if opportunities found and not called from dynamic strategy
      if (opportunities.length > 0 && !this.isCalledFromDynamicStrategy) {
        logger.debug(`Found ${opportunities.length} flashloan opportunities`, {
          aave: aaveCount,
          balancer: balancerCount,
          mock: mockOpportunityGenerator.isMockEnabled() ? 'enabled' : 'disabled'
        });
      }

      return opportunities.slice(0, 80); // Return top 8 (mix of both)

    } catch (error) {
      logger.logError(error as Error, 'FlashloanStrategy.scanForFlashloanOpportunities');
      return [];
    }
  }

  private async findCrossDexArbitrage(
    flashloanToken: Token,
    targetToken: Token,
    availableDexs: DexName[]
  ): Promise<ArbitrageRoute[]> {
    const routes: ArbitrageRoute[] = [];
    const dexConfig = getDexConfig();

    try {
      // Try all combinations of available DEXs
      for (let i = 0; i < availableDexs.length; i++) {
        for (let j = 0; j < availableDexs.length; j++) {
          if (i === j) continue; // Skip same DEX

          const buyDex = availableDexs[i];
          const sellDex = availableDexs[j];

          const route = await this.findSpecificDexArbitrage(
            flashloanToken,
            targetToken,
            buyDex,
            sellDex
          );

          if (route) {
            routes.push(route);
          }
        }
      }

      return routes;
    } catch (error) {
      logger.debug('Error finding cross-DEX arbitrage', { error: (error as Error).message });
      return routes;
    }
  }

  private async findSpecificDexArbitrage(
    tokenA: Token,
    tokenB: Token,
    buyDex: DexName,
    sellDex: DexName
  ): Promise<ArbitrageRoute | null> {
    try {
      const dexConfig = getDexConfig();

      if (!dexConfig[buyDex].available || !dexConfig[sellDex].available) {
        return null;
      }

      // Get pools for both DEXs with fallback to alternative fee tiers
      const buyPool = await this.findBestPool(tokenA, tokenB, buyDex);
      const sellPool = await this.findBestPool(tokenA, tokenB, sellDex);

      logger.debug('Pool retrieval results', {
        buyDex,
        sellDex,
        buyPoolExists: !!buyPool,
        sellPoolExists: !!sellPool,
        buyPoolAddress: buyPool?.address,
        sellPoolAddress: sellPool?.address
      });

      if (!buyPool || !sellPool) {
        logger.debug('Missing pools for arbitrage', {
          buyDex,
          sellDex,
          buyPool: !!buyPool,
          sellPool: !!sellPool
        });
        return null;
      }

      // Calculate prices
      const buyPrice = this.calculatePoolPrice(buyPool, tokenA, tokenB);
      const sellPrice = this.calculatePoolPrice(sellPool, tokenA, tokenB);

      logger.debug('Price calculation results', {
        buyDex,
        sellDex,
        buyPrice,
        sellPrice,
        tokenA: tokenA.symbol,
        tokenB: tokenB.symbol
      });

      if (!buyPrice || !sellPrice) {
        logger.debug('Price calculation failed', {
          buyDex,
          sellDex,
          buyPrice,
          sellPrice,
          buyPoolProtocol: buyPool.protocol,
          sellPoolProtocol: sellPool.protocol
        });
        return null;
      }

      // Check if arbitrage is profitable
      const priceDifference = Math.abs(sellPrice - buyPrice) / Math.min(buyPrice, sellPrice);
      const minSpreadRequired = config.minArbitrageSpread / 100;

      logger.debug('Price analysis', {
        buyDex,
        sellDex,
        buyPrice: buyPrice.toFixed(6),
        sellPrice: sellPrice.toFixed(6),
        priceDifference: `${(priceDifference * 100).toFixed(3)}%`,
        minRequired: `${(minSpreadRequired * 100).toFixed(3)}%`,
        profitable: priceDifference >= minSpreadRequired
      });

      if (priceDifference < minSpreadRequired) {
        return null; // Not enough spread
      }

      // Enhanced price sanity check to filter out unrealistic arbitrage opportunities
      const maxReasonableSpread = 0.1; // 10% maximum reasonable spread
      if (priceDifference > maxReasonableSpread) {
        logger.warn('Price difference too large - likely pool state issue', {
          buyDex,
          sellDex,
          buyPrice: buyPrice.toFixed(8),
          sellPrice: sellPrice.toFixed(8),
          priceDifference: `${(priceDifference * 100).toFixed(3)}%`,
          maxAllowed: `${(maxReasonableSpread * 100).toFixed(1)}%`,
          reason: 'Unrealistic arbitrage opportunity filtered out',
          buyPool: {
            protocol: buyPool.protocol,
            token0: buyPool.token0.symbol,
            token1: buyPool.token1.symbol,
            tick: buyPool.tick?.toString(),
            hasReserves: !!buyPool.reserves
          },
          sellPool: {
            protocol: sellPool.protocol,
            token0: sellPool.token0.symbol,
            token1: sellPool.token1.symbol,
            tick: sellPool.tick?.toString(),
            hasReserves: !!sellPool.reserves
          },
          suggestion: 'Check V3 tick interpretation or pool data freshness'
        });
        return null; // Price difference too large to be realistic
      }

      // Calculate optimal amount and profit
      const optimalAmount = await this.calculateOptimalArbitrageAmount(buyPool, sellPool, tokenA, tokenB);
      if (optimalAmount === BigInt(0)) {
        return null;
      }

      const gasEstimate = await this.estimateArbitrageGasCost(buyPool, sellPool, optimalAmount);
      const expectedProfit = await this.calculateArbitrageProfit(
        buyPool, sellPool, tokenA, tokenB, optimalAmount, gasEstimate
      );

      if (BigInt(expectedProfit.toString()) <= BigInt(0)) {
        return null;
      }

      logger.debug(`${buyDex} → ${sellDex} arbitrage found`, {
        spread: `${priceDifference.toFixed(2)}%`,
        profit: ethers.formatEther(expectedProfit)
      });

      return {
        pools: [buyPool, sellPool],
        tokens: [tokenA, tokenB],
        expectedProfit,
        gasEstimate,
        confidence: this.calculateArbitrageConfidence(priceDifference, expectedProfit)
      };

    } catch (error) {
      logger.debug('Error finding specific DEX arbitrage', { error: (error as Error).message });
      return null;
    }
  }

  private async buildEnhancedFlashloanRoute(
    flashloanToken: Token,
    arbitrageRoute: ArbitrageRoute
  ): Promise<FlashloanRoute | null> {
    try {
      // Calculate optimal flashloan amount
      const flashloanAmount = await this.calculateOptimalFlashloanAmount(
        flashloanToken,
        arbitrageRoute
      );

      if (flashloanAmount <= BigInt(0)) {
        return null;
      }

      // Calculate flashloan premium
      const flashloanPremium = await this.calculateFlashloanPremium(flashloanAmount);

      // Estimate total gas cost (flashloan + arbitrage)
      const flashloanGas = await this.estimateFlashloanGasCost();
      const totalGasEstimate = BigInt(arbitrageRoute.gasEstimate.toString()) + flashloanGas;

      // Calculate expected profit (token units) then normalize to wei for comparisons/logs upstream
      const arbitrageProfit = await this.estimateArbitrageProfit(arbitrageRoute, flashloanAmount);
      const expectedProfitToken = arbitrageProfit - flashloanPremium;
      const expectedProfitWei = await tokenAmountToWei(flashloanToken, expectedProfitToken);
      const expectedProfit = expectedProfitWei - totalGasEstimate;

      if (expectedProfit <= 0n) {
        return null;
      }

      // Calculate confidence based on profit margin using wei-based expectedProfit
      const profitMargin = Number((expectedProfitWei * 10000n) / flashloanAmount) / 100;
      const confidence = this.calculateFlashloanConfidence(profitMargin, expectedProfit);

      return {
        flashloanToken,
        flashloanAmount,
        flashloanPremium,
        arbitrageRoute,
        expectedProfit,
        gasEstimate: totalGasEstimate,
        confidence
      };

    } catch (error) {
      logger.debug('Error building enhanced flashloan route', { error: (error as Error).message });
      return null;
    }
  }

  private async findArbitrageOpportunity(
    tokenA: Token,
    tokenB: Token
  ): Promise<ArbitrageRoute | null> {
    try {
      // Get pools from both V2 and V3
      const v2Pool = await this.poolManager.getPool(tokenA.address, tokenB.address, 'uniswap-v2');
      const v3Pool = await this.poolManager.getPool(tokenA.address, tokenB.address, 'uniswap-v3', 3000);

      if (!v2Pool || !v3Pool) {
        return null;
      }

      // Calculate prices on both pools
      const v2Price = this.calculatePoolPrice(v2Pool, tokenA, tokenB);
      const v3Price = this.calculatePoolPrice(v3Pool, tokenA, tokenB);

      if (!v2Price || !v3Price) {
        return null;
      }

      // Check for price difference
      const priceDifference = Math.abs(v2Price - v3Price) / Math.min(v2Price, v3Price);

      if (priceDifference < this.MIN_PROFIT_THRESHOLD) {
        return null;
      }

      // Determine direction (buy low, sell high)
      const buyPool = v2Price < v3Price ? v2Pool : v3Pool;
      const sellPool = v2Price < v3Price ? v3Pool : v2Pool;

      // Calculate optimal amount for arbitrage
      const optimalAmount = await this.calculateOptimalArbitrageAmount(buyPool, sellPool, tokenA, tokenB);

      if (optimalAmount === BigInt(0)) {
        return null;
      }

      // Estimate gas costs
      const gasEstimate = await this.estimateArbitrageGasCost(buyPool, sellPool, optimalAmount);

      // Calculate expected profit
      const expectedProfit = await this.calculateArbitrageProfit(
        buyPool, sellPool, tokenA, tokenB, optimalAmount, gasEstimate
      );

      if (BigInt(expectedProfit.toString()) <= BigInt(0)) {
        return null;
      }

      return {
        pools: [buyPool, sellPool],
        tokens: [tokenA, tokenB],
        expectedProfit,
        gasEstimate,
        confidence: this.calculateArbitrageConfidence(priceDifference, expectedProfit)
      };

    } catch (error) {
      logger.debug('Error finding arbitrage opportunity', { error: (error as Error).message });
      return null;
    }
  }

  private async calculateOptimalFlashloanAmount(
    flashloanToken: Token,
    arbitrageRoute: ArbitrageRoute
  ): Promise<bigint> {
    try {
      // Determine base amount based on token and configuration
      let baseAmount: bigint;
      const isMainnet = config.chainId === 1;

      if (flashloanToken.symbol === 'WETH') {
        // Use smaller, more conservative WETH amounts to reduce price impact
        const configAmount = config.flashloanBaseAmountWeth || 5;
        const conservativeAmount = isMainnet ? Math.min(configAmount, 2) : configAmount; // Cap at 2 ETH for mainnet
        baseAmount = ethers.parseUnits(conservativeAmount.toString(), flashloanToken.decimals);
      } else if (flashloanToken.symbol === 'USDC' || flashloanToken.symbol === 'USDT') {
        // Dynamic amount based on available Balancer liquidity
        const availableLiquidity = await this.getBalancerLiquidity(flashloanToken.address);
        if (availableLiquidity > 0) {
          // Use 60% of available liquidity, capped at configured max
          const maxConfigAmount = config.flashloanBaseAmountUsdc || 20000;
          const dynamicAmount = Math.min(availableLiquidity * 0.6, maxConfigAmount);
          const finalAmount = Math.max(dynamicAmount, 1000); // Minimum 1k USDC
          baseAmount = ethers.parseUnits(finalAmount.toString(), flashloanToken.decimals);
          logger.debug(`💰 Dynamic flashloan amount for ${flashloanToken.symbol}`, {
            availableLiquidity: availableLiquidity.toFixed(2),
            dynamicAmount: dynamicAmount.toFixed(2),
            finalAmount: finalAmount.toFixed(2)
          });
        } else {
          // Fallback to small amount if liquidity check fails
          baseAmount = ethers.parseUnits('1000', flashloanToken.decimals);
        }
      } else {
        // For other tokens: use a reasonable default
        baseAmount = ethers.parseUnits('1000', flashloanToken.decimals);
      }

      let optimalAmount = BigInt(0);
      let maxNetProfit = BigInt(0);

      // Test different flashloan amounts with configurable range
      const maxMultiplier = config.flashloanMaxMultiplier || (isMainnet ? 20 : 10);

      logger.debug('Testing flashloan amounts', {
        token: flashloanToken.symbol,
        baseAmount: ethers.formatUnits(baseAmount, flashloanToken.decimals),
        maxMultiplier,
        network: isMainnet ? 'mainnet' : 'testnet'
      });

      for (let multiplier = 1; multiplier <= maxMultiplier; multiplier++) {
        const testAmount = baseAmount * BigInt(multiplier);

        // Check against max flashloan limit
        if (testAmount > this.MAX_FLASHLOAN_AMOUNT) {
          logger.debug('Test amount exceeds max flashloan limit', {
            testAmount: ethers.formatUnits(testAmount, flashloanToken.decimals),
            maxLimit: ethers.formatUnits(this.MAX_FLASHLOAN_AMOUNT, flashloanToken.decimals)
          });
          break;
        }

        // Calculate flashloan premium
        const premium = await this.calculateFlashloanPremium(testAmount);

        // Estimate arbitrage profit with this amount
        const arbitrageProfit = await this.estimateArbitrageProfit(arbitrageRoute, testAmount);

        // Calculate net profit (arbitrage profit - flashloan premium - gas)
        const netProfit = arbitrageProfit - premium - BigInt(arbitrageRoute.gasEstimate.toString());

        logger.debug('Flashloan amount test', {
          multiplier,
          amount: ethers.formatUnits(testAmount, flashloanToken.decimals),
          premium: ethers.formatUnits(premium, flashloanToken.decimals),
          arbitrageProfit: ethers.formatEther(arbitrageProfit),
          netProfit: ethers.formatEther(netProfit),
          profitable: netProfit > BigInt(0)
        });

        if (netProfit > maxNetProfit) {
          maxNetProfit = netProfit;
          optimalAmount = testAmount;
        }
      }

      logger.debug('Optimal flashloan amount calculated', {
        token: flashloanToken.symbol,
        optimalAmount: ethers.formatUnits(optimalAmount, flashloanToken.decimals),
        expectedProfit: ethers.formatEther(maxNetProfit)
      });

      return optimalAmount;
    } catch (error) {
      logger.debug('Error calculating optimal flashloan amount', { error: (error as Error).message });
      return BigInt(0);
    }
  }

  private async buildFlashloanRoute(
    flashloanToken: Token,
    flashloanAmount: bigint,
    arbitrageRoute: ArbitrageRoute
  ): Promise<FlashloanRoute | null> {
    try {
      // Calculate flashloan premium
      const flashloanPremium = await this.calculateFlashloanPremium(flashloanAmount);

      // Estimate total gas cost (flashloan + arbitrage)
      const flashloanGas = await this.estimateFlashloanGasCost();
      const totalGasEstimate = BigInt(arbitrageRoute.gasEstimate.toString()) + flashloanGas;

      // Calculate expected profit (token units) then normalize to wei for comparisons/logs upstream
      const arbitrageProfit = await this.estimateArbitrageProfit(arbitrageRoute, flashloanAmount);
      const expectedProfitToken = arbitrageProfit - flashloanPremium;
      const expectedProfitWei = await tokenAmountToWei(flashloanToken, expectedProfitToken);
      const expectedProfit = expectedProfitWei - totalGasEstimate;

      if (expectedProfit <= 0n) {
        return null;
      }

      // Calculate confidence based on profit margin using wei-based expectedProfit
      const profitMargin = Number((expectedProfitWei * 10000n) / flashloanAmount) / 100;
      const confidence = this.calculateFlashloanConfidence(profitMargin, expectedProfit);

      return {
        flashloanToken,
        flashloanAmount,
        flashloanPremium,
        arbitrageRoute,
        expectedProfit,
        gasEstimate: totalGasEstimate,
        confidence
      };

    } catch (error) {
      logger.debug('Error building flashloan route', { error: (error as Error).message });
      return null;
    }
  }

  private async calculateFlashloanPremium(amount: bigint): Promise<bigint> {
    try {
      const premiumBps = await this.flashloanInterface.getFlashloanPremium();
      return (amount * premiumBps) / BigInt(10000);
    } catch (error) {
      // Fallback to default premium
      return (amount * BigInt(this.FLASHLOAN_PREMIUM_BPS)) / BigInt(10000);
    }
  }

  private calculatePoolPrice(pool: Pool, token0: Token, token1: Token): number | null {
    logger.debug('Calculating pool price', {
      protocol: pool.protocol,
      hasReserves: !!pool.reserves,
      hasTick: pool.tick !== undefined && pool.tick !== null,
      tick: pool.tick?.toString(), // Convert BigInt to string for logging
      poolToken0: pool.token0.symbol,
      poolToken1: pool.token1.symbol,
      inputToken0: token0.symbol,
      inputToken1: token1.symbol
    });

    // Calculate the raw price from the pool
    let rawPrice: number | null = null;

    if (pool.protocol === 'uniswap-v2' && pool.reserves) {
      rawPrice = this.calculateV2Price(pool);
    } else if (pool.protocol === 'uniswap-v3' && pool.tick !== undefined && pool.tick !== null) {
      rawPrice = this.calculateV3Price(pool);
    } else if (pool.protocol === 'curve' && pool.reserves && pool.curveTokenIndices) {
      rawPrice = this.calculateCurvePrice(pool);
    }

    if (rawPrice === null) {
      return null;
    }

    // Normalize the price to always represent the same token ratio
    // We want to return the price in terms of the input tokens (token0/token1)
    return this.normalizePrice(rawPrice, pool, token0, token1);
  }



  private async calculateOptimalArbitrageAmount(
    buyPool: Pool,
    sellPool: Pool,
    buyToken: Token,
    sellToken: Token
  ): Promise<bigint> {
    // Simplified calculation - in practice would use more sophisticated optimization
    const maxAmount = ethers.parseUnits('5000', buyToken.decimals); // Max 5000 tokens
    return maxAmount / BigInt(2); // Use half as starting point
  }

  private async estimateArbitrageGasCost(
    buyPool: Pool,
    sellPool: Pool,
    amount: bigint
  ): Promise<bigint> {
    // Estimate gas for two swaps within flashloan
    const gasPerSwap = BigInt(150000); // Approximate gas per swap
    const gasStrategy = await this.gasOptimizer.getCurrentGasStrategy();
    return gasPerSwap * BigInt(2) * BigInt(gasStrategy.maxFeePerGas.toString());
  }

  private async calculateArbitrageProfit(
    buyPool: Pool,
    sellPool: Pool,
    buyToken: Token,
    sellToken: Token,
    amount: bigint,
    gasCost: bigint
  ): Promise<bigint> {
    // Simplified profit calculation
    // In practice, would simulate actual swaps
    const estimatedReturn = (amount * BigInt(102)) / BigInt(100); // 2% profit estimate
    return estimatedReturn - amount - gasCost;
  }

  private calculateArbitrageConfidence(profitPercentage: number, expectedProfit: bigint): number {
    let confidence = 0;

    // Profit percentage factor
    confidence += Math.min(profitPercentage * 15, 40); // Max 40 points

    // Absolute profit factor
    const profitEth = Number(ethers.formatEther(expectedProfit));
    confidence += Math.min(profitEth * 20, 25); // Max 25 points

    // Arbitrage base confidence
    confidence += 15;

    return Math.min(confidence, 100);
  }

  private async estimateArbitrageProfit(arbitrageRoute: ArbitrageRoute, amount: bigint): Promise<bigint> {
    try {
      const [buyPool, sellPool] = arbitrageRoute.pools;
      const [tokenA, tokenB] = arbitrageRoute.tokens;

      // Calculate prices on both pools
      const buyPrice = this.calculatePoolPrice(buyPool, tokenA, tokenB);
      const sellPrice = this.calculatePoolPrice(sellPool, tokenA, tokenB);

      if (!buyPrice || !sellPrice) {
        // Fallback to conservative estimate
        return (amount * BigInt(101)) / BigInt(100) - amount; // 1% profit estimate
      }

      // Calculate price difference
      const priceDifference = Math.abs(sellPrice - buyPrice) / Math.min(buyPrice, sellPrice);

      // Estimate profit based on actual price difference
      // Account for slippage and fees (reduce profit by ~30%)
      const effectiveProfitRate = Math.max(priceDifference * 0.7, 0.001); // Min 0.1% profit
      const maxProfitRate = 0.05; // Cap at 5% to be realistic

      const finalProfitRate = Math.min(effectiveProfitRate, maxProfitRate);
      const estimatedProfit = (amount * BigInt(Math.floor(finalProfitRate * 10000))) / BigInt(10000);

      logger.debug('Arbitrage profit estimation', {
        amount: ethers.formatUnits(amount, tokenA.decimals),
        buyPrice: buyPrice.toFixed(6),
        sellPrice: sellPrice.toFixed(6),
        priceDifference: `${(priceDifference * 100).toFixed(3)}%`,
        effectiveProfitRate: `${(finalProfitRate * 100).toFixed(3)}%`,
        estimatedProfit: ethers.formatEther(estimatedProfit),
        buyPool: buyPool.protocol,
        sellPool: sellPool.protocol
      });

      return estimatedProfit;
    } catch (error) {
      logger.debug('Error estimating arbitrage profit', { error: (error as Error).message });
      // Fallback to conservative estimate
      return (amount * BigInt(101)) / BigInt(100) - amount; // 1% profit estimate
    }
  }

  private async estimateFlashloanGasCost(): Promise<bigint> {
    // Estimate gas for flashloan execution
    const gasStrategy = await this.gasOptimizer.getCurrentGasStrategy();
    return BigInt(200000) * BigInt(gasStrategy.maxFeePerGas.toString()); // ~200k gas for flashloan
  }

  private calculateFlashloanConfidence(profitMargin: number, expectedProfit: bigint): number {
    let confidence = 0;

    // Profit margin factor (higher threshold for flashloans)
    confidence += Math.min(profitMargin * 10, 35); // Max 35 points

    // Absolute profit factor
    const profitEth = Number(ethers.formatEther(expectedProfit));
    confidence += Math.min(profitEth * 15, 25); // Max 25 points

    // Flashloan complexity penalty
    confidence += 10; // Base confidence for flashloan

    return Math.min(confidence, 100);
  }

  /**
   * Try to get a pool with sufficient liquidity by testing multiple fee tiers
   */
  private async getPoolWithFallback(
    tokenA: Token,
    tokenB: Token,
    protocol: 'uniswap-v2' | 'uniswap-v3' | 'sushiswap' | 'balancer' | 'curve',
    feeTiers: number[]
  ): Promise<Pool | null> {
    // For non-V3 protocols, just use the first fee tier
    if (protocol !== 'uniswap-v3') {
      const pool = await this.poolManager.getPool(tokenA.address, tokenB.address, protocol, feeTiers[0]);
      if (pool) {
        const liquidityInfo = this.calculatePoolLiquidity(pool);
        logger.debug('V2 pool liquidity check', {
          protocol,
          tokenA: tokenA.symbol,
          tokenB: tokenB.symbol,
          liquidityUsd: liquidityInfo.liquidityUsd,
          poolAddress: pool.address
        });
      }
      return pool;
    }

    // For Uniswap V3, try multiple fee tiers in order of preference
    const preferredFeeTiers = [3000, 500, 10000, 100]; // 0.3%, 0.05%, 1%, 0.01%

    // Combine configured fee tiers with preferred ones, removing duplicates
    const allFeeTiers = [...new Set([...feeTiers, ...preferredFeeTiers])];

    let bestPool: Pool | null = null;
    let bestLiquidity = 0;

    for (const fee of allFeeTiers) {
      try {
        const pool = await this.poolManager.getPool(tokenA.address, tokenB.address, protocol, fee);

        if (pool) {
          const liquidityInfo = this.calculatePoolLiquidity(pool);

          logger.debug('V3 pool liquidity check', {
            protocol,
            fee,
            tokenA: tokenA.symbol,
            tokenB: tokenB.symbol,
            liquidityUsd: liquidityInfo.liquidityUsd,
            poolAddress: pool.address
          });

          // Check if this pool has sufficient liquidity
          const price = this.calculatePoolPrice(pool, tokenA, tokenB);

          if (price !== null && liquidityInfo.liquidityUsd > bestLiquidity) {
            bestPool = pool;
            bestLiquidity = liquidityInfo.liquidityUsd;

            logger.debug('Found better pool with fee tier', {
              protocol,
              fee,
              tokenA: tokenA.symbol,
              tokenB: tokenB.symbol,
              price,
              liquidityUsd: liquidityInfo.liquidityUsd,
              poolAddress: pool.address
            });
          }
        }
      } catch (error) {
        logger.debug('Error getting pool for fee tier', {
          protocol,
          fee,
          error: (error as Error).message
        });
      }
    }

    if (bestPool) {
      logger.debug('Selected best pool', {
        protocol,
        tokenA: tokenA.symbol,
        tokenB: tokenB.symbol,
        bestLiquidityUsd: bestLiquidity,
        poolAddress: bestPool.address
      });
    } else {
      logger.debug('No suitable pool found for any fee tier', {
        protocol,
        tokenA: tokenA.symbol,
        tokenB: tokenB.symbol,
        triedFeeTiers: allFeeTiers
      });
    }

    return bestPool;
  }

  /**
   * Calculate pool liquidity in USD (helper method)
   */
  private calculatePoolLiquidity(pool: Pool): { liquidityUsd: number; reserve0: number; reserve1: number } {
    if (pool.protocol === 'uniswap-v2' && pool.reserves) {
      const reserve0 = Number(ethers.formatUnits(pool.reserves.reserve0, pool.token0.decimals));
      const reserve1 = Number(ethers.formatUnits(pool.reserves.reserve1, pool.token1.decimals));

      let liquidityUsd = 0;

      // For WETH/USDC pairs, estimate USD liquidity
      if ((pool.token0.symbol === 'WETH' && pool.token1.symbol === 'USDC') ||
          (pool.token0.symbol === 'USDC' && pool.token1.symbol === 'WETH')) {
        // Assume ETH price ~$2000 for rough estimation
        const ethAmount = pool.token0.symbol === 'WETH' ? reserve0 : reserve1;
        const usdcAmount = pool.token0.symbol === 'USDC' ? reserve0 : reserve1;
        liquidityUsd = (ethAmount * 2000) + usdcAmount;
      } else {
        // For other pairs, estimate based on reserves
        liquidityUsd = Math.max(reserve0, reserve1) * 2; // Rough estimate
      }

      return { liquidityUsd, reserve0, reserve1 };
    }

    // For V3 pools, we'd need to calculate based on liquidity and current price
    // For now, return a default that passes the check
    return { liquidityUsd: 1000, reserve0: 0, reserve1: 0 };
  }

  /**
   * Find the best pool for a token pair on a specific DEX
   */
  private async findBestPool(tokenA: Token, tokenB: Token, dexName: DexName): Promise<Pool | null> {
    const dexConfig = getDexConfig();
    const dex = dexConfig[dexName];

    if (!dex.available) {
      return null;
    }

    // Try to get pool with fallback
    const pool = await this.getPoolWithFallback(
      tokenA,
      tokenB,
      dex.protocol,
      dex.fees || [3000]
    );

    if (pool) {
      const liquidityInfo = this.calculatePoolLiquidity(pool);

      logger.debug('Pool found for DEX', {
        dex: dexName,
        protocol: dex.protocol,
        tokenA: tokenA.symbol,
        tokenB: tokenB.symbol,
        liquidityUsd: liquidityInfo.liquidityUsd,
        poolAddress: pool.address
      });

      // If liquidity is very low, log a warning but still return the pool
      if (liquidityInfo.liquidityUsd < 50) {
        logger.warn('Pool has very low liquidity', {
          dex: dexName,
          liquidityUsd: liquidityInfo.liquidityUsd,
          tokenA: tokenA.symbol,
          tokenB: tokenB.symbol,
          suggestion: 'Consider using different token pairs or adding liquidity to testnet'
        });
      }
    }

    return pool;
  }

  /**
   * Calculate V2 pool price (token1/token0 ratio)
   */
  private calculateV2Price(pool: Pool): number | null {
    if (!pool.reserves?.reserve0 || !pool.reserves?.reserve1) {
      logger.debug('V2 pool has null reserves');
      return null;
    }

    // Format reserves using the correct decimals for each pool token
    const reserve0 = Number(ethers.formatUnits(pool.reserves.reserve0, pool.token0.decimals));
    const reserve1 = Number(ethers.formatUnits(pool.reserves.reserve1, pool.token1.decimals));

    // Calculate standardized price: token1/token0 ratio
    const price = reserve1 / reserve0;

    logger.debug('V2 price calculated', {
      price,
      reserve0,
      reserve1,
      poolToken0: pool.token0.symbol,
      poolToken1: pool.token1.symbol,
      calculation: `${pool.token1.symbol}/${pool.token0.symbol} = ${reserve1}/${reserve0} = ${price}`
    });

    // Sanity check for unrealistic prices
    if (!isFinite(price) || price <= 0 || price > 1e12) {
      logger.warn('V2 price calculation resulted in unrealistic value', {
        price,
        reserve0,
        reserve1,
        poolToken0: pool.token0.symbol,
        poolToken1: pool.token1.symbol
      });
      return null;
    }

    return price;
  }

  /**
   * Calculate V3 pool price (token1/token0 ratio)
   */
  private calculateV3Price(pool: Pool): number | null {
    const tickNumber = Number(pool.tick);

    logger.debug('Calculating V3 price from tick', {
      tick: tickNumber,
      poolToken0: pool.token0.symbol,
      poolToken1: pool.token1.symbol,
      token0Decimals: pool.token0.decimals,
      token1Decimals: pool.token1.decimals
    });

    // In Uniswap V3, price = 1.0001^tick represents token1/token0 in their smallest units
    // This is the core formula from Uniswap V3 documentation

    // Calculate raw price from tick: price = 1.0001^tick
    const rawPrice = Math.pow(1.0001, tickNumber);

    // The rawPrice represents token1/token0 in their smallest units
    // To get the human-readable price, we need to adjust for decimal differences
    // Formula: adjustedPrice = rawPrice / (10^(token1.decimals - token0.decimals))
    const decimalsAdjustment = Math.pow(10, pool.token1.decimals - pool.token0.decimals);
    let adjustedPrice = rawPrice / decimalsAdjustment;

    // Check for extreme values that might indicate calculation errors
    if (!isFinite(rawPrice) || rawPrice <= 0) {
      logger.warn('Invalid raw price from tick calculation', {
        tick: tickNumber,
        rawPrice,
        poolToken0: pool.token0.symbol,
        poolToken1: pool.token1.symbol
      });
      return null;
    }

    // For WETH/USDC pairs, validate the price makes sense
    if ((pool.token0.symbol === 'USDC' && pool.token1.symbol === 'WETH') ||
        (pool.token0.symbol === 'WETH' && pool.token1.symbol === 'USDC')) {

      // Expected ranges based on current market conditions:
      // - If token0=USDC, token1=WETH: price should be ~0.0004 (1 USDC ≈ 0.0004 WETH when ETH ≈ $2500)
      // - If token0=WETH, token1=USDC: price should be ~2500 (1 WETH ≈ 2500 USDC)

      if (pool.token0.symbol === 'USDC' && pool.token1.symbol === 'WETH') {
        // This is USDC/WETH pool, price = WETH/USDC (amount of WETH per USDC)
        // Should be around 0.0003 to 0.001 (ETH price between $1000-$3333)
        if (adjustedPrice < 0.0001 || adjustedPrice > 0.01) {
          logger.debug('V3 USDC/WETH price outside expected range, but keeping calculated value', {
            price: adjustedPrice,
            tick: tickNumber,
            poolToken0: pool.token0.symbol,
            poolToken1: pool.token1.symbol,
            expectedRange: '0.0001 to 0.01'
          });
        }
      } else if (pool.token0.symbol === 'WETH' && pool.token1.symbol === 'USDC') {
        // This is WETH/USDC pool, price = USDC/WETH (amount of USDC per WETH)
        // Should be around 1000 to 10000 (ETH price between $1000-$10000)
        if (adjustedPrice < 100 || adjustedPrice > 20000) {
          logger.debug('V3 WETH/USDC price outside expected range, but keeping calculated value', {
            price: adjustedPrice,
            tick: tickNumber,
            poolToken0: pool.token0.symbol,
            poolToken1: pool.token1.symbol,
            expectedRange: '100 to 20000'
          });
        }
      }
    }

    logger.debug('V3 price calculated', {
      price: adjustedPrice,
      tick: pool.tick?.toString(),
      rawPrice,
      tickNumber,
      decimalsAdjustment,
      poolToken0: pool.token0.symbol,
      poolToken1: pool.token1.symbol,
      calculation: `1.0001^${tickNumber} / 10^(${pool.token1.decimals} - ${pool.token0.decimals}) = ${rawPrice} / ${decimalsAdjustment} = ${adjustedPrice}`
    });

    // Final sanity check for unrealistic prices
    if (!isFinite(adjustedPrice) || adjustedPrice <= 0 || adjustedPrice > 1e12) {
      logger.warn('V3 price calculation resulted in unrealistic value', {
        price: adjustedPrice,
        tick: pool.tick?.toString(),
        rawPrice,
        poolToken0: pool.token0.symbol,
        poolToken1: pool.token1.symbol
      });
      return null;
    }

    return adjustedPrice;
  }

  /**
   * Calculate Curve pool price (token1/token0 ratio)
   */
  private calculateCurvePrice(pool: Pool): number | null {
    if (!pool.reserves?.reserve0 || !pool.reserves?.reserve1) {
      return null;
    }

    const reserve0 = Number(ethers.formatUnits(pool.reserves.reserve0, pool.token0.decimals));
    const reserve1 = Number(ethers.formatUnits(pool.reserves.reserve1, pool.token1.decimals));

    if (reserve0 === 0 || reserve1 === 0) {
      return null;
    }

    // Calculate standardized price: token1/token0 ratio
    const price = reserve1 / reserve0;

    logger.debug('Curve price calculated', {
      price,
      reserve0,
      reserve1,
      poolToken0: pool.token0.symbol,
      poolToken1: pool.token1.symbol,
      calculation: `${pool.token1.symbol}/${pool.token0.symbol} = ${reserve1}/${reserve0} = ${price}`
    });

    return price;
  }

  /**
   * Normalize price to ensure consistent comparison between pools
   */
  private normalizePrice(rawPrice: number, pool: Pool, token0: Token, token1: Token): number {
    // The rawPrice represents pool.token1/pool.token0
    // We need to convert it to represent token1/token0 (input tokens)

    logger.debug('Normalizing price', {
      rawPrice,
      poolToken0: pool.token0.symbol,
      poolToken1: pool.token1.symbol,
      inputToken0: token0.symbol,
      inputToken1: token1.symbol
    });

    // If the pool tokens match the input tokens in the same order
    if (pool.token0.address.toLowerCase() === token0.address.toLowerCase() &&
        pool.token1.address.toLowerCase() === token1.address.toLowerCase()) {
      logger.debug('Pool tokens match input tokens in same order', { normalizedPrice: rawPrice });
      return rawPrice; // token1/token0
    }

    // If the pool tokens are in reverse order
    if (pool.token0.address.toLowerCase() === token1.address.toLowerCase() &&
        pool.token1.address.toLowerCase() === token0.address.toLowerCase()) {
      const normalizedPrice = 1 / rawPrice;
      logger.debug('Pool tokens in reverse order, inverting price', {
        originalPrice: rawPrice,
        normalizedPrice
      });
      return normalizedPrice; // Invert to get token1/token0
    }

    // This shouldn't happen if we're comparing the right pools
    logger.warn('Token mismatch in price normalization', {
      poolToken0: pool.token0.symbol,
      poolToken1: pool.token1.symbol,
      inputToken0: token0.symbol,
      inputToken1: token1.symbol,
      rawPrice
    });

    return rawPrice;
  }

  async executeFlashloan(route: FlashloanRoute): Promise<boolean> {
    try {
      logger.separator();
      logger.info('🚀 Executing Flashloan Arbitrage Attack');
      logger.info(`Flashloan Amount: ${ethers.formatUnits(route.flashloanAmount, route.flashloanToken.decimals)} ${route.flashloanToken.symbol}`);
      logger.profitCalculation(ethers.formatEther(route.expectedProfit), true);
      logger.info(`Confidence: ${route.confidence}%`);

      if (config.dryRun || config.simulationMode) {
        const mode = config.simulationMode ? 'SIMULATION' : 'DRY RUN';
        logger.info(`🎭 ${mode}: Flashloan arbitrage opportunity detected`);

        // Simulate the flashloan execution steps
        logger.info('Step 1: 💰 Flashloan from Aave');
        logger.info(`  └─ Borrowing ${ethers.formatUnits(route.flashloanAmount, route.flashloanToken.decimals)} ${route.flashloanToken.symbol}`);
        logger.info(`  └─ Premium: ${ethers.formatUnits(route.flashloanPremium, route.flashloanToken.decimals)} ${route.flashloanToken.symbol}`);

        logger.info('Step 2: 🔄 Execute Arbitrage');
        logger.info(`  └─ Buy on ${route.arbitrageRoute.pools[0].protocol.toUpperCase()}`);
        logger.info(`  └─ Sell on ${route.arbitrageRoute.pools[1].protocol.toUpperCase()}`);

        logger.info('Step 3: 💸 Repay Flashloan');
        const totalRepayment = BigInt(route.flashloanAmount.toString()) + BigInt(route.flashloanPremium.toString());
        logger.info(`  └─ Repaying ${ethers.formatUnits(totalRepayment, route.flashloanToken.decimals)} ${route.flashloanToken.symbol}`);

        logger.info('Step 4: 💎 Keep Profit');
        logger.profitCalculation(ethers.formatEther(route.expectedProfit), true);

        if (config.simulationMode) {
          logger.info(`🎭 ${mode}: Would execute via ${this.hybridContract ? 'hybrid contract' : 'fallback method'}`);
          if (this.hybridContract) {
            logger.info(`   Contract: ${this.hybridContract.target}`);
          }
        }

        logger.success(`✅ Flashloan arbitrage ${mode.toLowerCase()} completed successfully`);
        logger.separator();
        return true;
      }

      // Execute using hybrid contract if available
      if (this.hybridContract) {
        const success = await this.executeWithHybridContract(route);
        logger.success('✅ Flashloan arbitrage executed via hybrid contract');
        logger.separator();
        return success;
      } else {
        // Fallback to individual strategy execution
        logger.warn('⚠️  Using fallback execution (no hybrid contract)');

        // Create flashloan transaction
        const flashloanTx = await this.createFlashloanTransaction(route);

        if (!flashloanTx) {
          logger.error('Failed to create flashloan transaction');
          return false;
        }

        // Simulate the transaction bundle
        const simulationResult = await this.simulator.simulateBundle({
          transactions: [flashloanTx],
          blockNumber: await this.provider.getBlockNumber() + 1
        });

        if (!simulationResult.success) {
          logger.error('Flashloan simulation failed', simulationResult.error);
          return false;
        }

        logger.success('✅ Flashloan arbitrage executed successfully');
        logger.separator();
        return true;
      }

    } catch (error) {
      logger.error('Flashloan execution failed', error);
      logger.logError(error as Error, 'FlashloanStrategy.executeFlashloan');
      return false;
    }
  }

  private async executeWithHybridContract(route: FlashloanRoute): Promise<boolean> {
    try {
      if (!this.hybridContract) {
        throw new Error('Hybrid contract not initialized');
      }

      // Get optimal provider from contract
      const optimalProvider = await this.hybridContract.getOptimalProvider(
        route.flashloanToken.address,
        route.flashloanAmount
      );

      logger.info(`🔄 Optimal provider: ${optimalProvider === 0 ? 'Aave' : 'Balancer'}`);

      // Calculate amountOutMin for slippage control
      const slippageFactor = BigInt(Math.floor((1 - this.SLIPPAGE_TOLERANCE) * 10000));
      const amountOutMin = (ethers.toBigInt(route.flashloanAmount) * slippageFactor) / BigInt(10000);

      // Determine buy and sell routers based on protocol
      const buyRouterAddress = route.arbitrageRoute.pools[0].protocol === 'uniswap-v2' ? ADDRESSES.UNISWAP_V2_ROUTER : ADDRESSES.UNISWAP_V3_ROUTER;
      const sellRouterAddress = route.arbitrageRoute.pools[1].protocol === 'uniswap-v2' ? ADDRESSES.UNISWAP_V2_ROUTER : ADDRESSES.UNISWAP_V3_ROUTER;

      // Encode buy swap data
      let buySwapData: string;
      if (route.arbitrageRoute.pools[0].protocol === 'uniswap-v2') {
        buySwapData = uniswapV2RouterInterface.encodeFunctionData('swapExactTokensForTokens', [
          route.flashloanAmount,
          amountOutMin, // Use calculated amountOutMin
          [route.flashloanToken.address, route.arbitrageRoute.tokens[1].address], // path
          this.wallet.address, // to
          Math.floor(Date.now() / 1000) + 300 // deadline
        ]);
      } else { // Uniswap V3
        buySwapData = uniswapV3RouterInterface.encodeFunctionData('exactInputSingle', [{
          tokenIn: route.flashloanToken.address,
          tokenOut: route.arbitrageRoute.tokens[1].address,
          fee: route.arbitrageRoute.pools[0].fee || 3000,
          recipient: this.wallet.address,
          deadline: Math.floor(Date.now() / 1000) + 300,
          amountIn: route.flashloanAmount,
          amountOutMinimum: amountOutMin, // Use calculated amountOutMin
          sqrtPriceLimitX96: 0
        }]);
      }

      // Encode sell swap data
      // For the sell swap, the amountIn will be the balance of tokenB after the first swap.
      // This is a simplification; a more robust solution would simulate the first swap
      // to get the exact amount of tokenB. For now, we'll use the flashloanAmount as a proxy
      // for the expected amount of tokenB, and apply slippage.
      const expectedTokenBAmount = route.flashloanAmount; // This is a rough estimate
      const amountOutMinSell = (ethers.toBigInt(expectedTokenBAmount) * slippageFactor) / BigInt(10000);

      let sellSwapData: string;
      if (route.arbitrageRoute.pools[1].protocol === 'uniswap-v2') {
        sellSwapData = uniswapV2RouterInterface.encodeFunctionData('swapExactTokensForTokens', [
          expectedTokenBAmount, // amountIn (rough estimate)
          amountOutMinSell, // Use calculated amountOutMin
          [route.arbitrageRoute.tokens[1].address, route.flashloanToken.address], // path
          this.wallet.address, // to
          Math.floor(Date.now() / 1000) + 300 // deadline
        ]);
      } else { // Uniswap V3
        sellSwapData = uniswapV3RouterInterface.encodeFunctionData('exactInputSingle', [{
          tokenIn: route.arbitrageRoute.tokens[1].address,
          tokenOut: route.flashloanToken.address,
          fee: route.arbitrageRoute.pools[1].fee || 3000,
          recipient: this.wallet.address,
          deadline: Math.floor(Date.now() / 1000) + 300,
          amountIn: expectedTokenBAmount, // amountIn (rough estimate)
          amountOutMinimum: amountOutMinSell, // Use calculated amountOutMin
          sqrtPriceLimitX96: 0
        }]);
      }

      // Encode arbitrage parameters for the FlashloanArbitrage.sol contract
      // The struct ArbitrageParams in Solidity has 6 fields:
      // address tokenA;
      // address tokenB;
      // address buyRouter;
      // address sellRouter;
      // bytes buySwapData;
      // bytes sellSwapData;
      const arbitrageParams = ethers.AbiCoder.defaultAbiCoder().encode(
        ['address', 'address', 'address', 'address', 'bytes', 'bytes'],
        [
          route.flashloanToken.address, // tokenA
          route.arbitrageRoute.tokens[1].address, // tokenB
          buyRouterAddress,
          sellRouterAddress,
          buySwapData,
          sellSwapData
        ]
      );

      // Execute optimal flashloan
      const tx = await this.hybridContract.executeOptimalFlashloan(
        route.flashloanToken.address,
        route.flashloanAmount,
        arbitrageParams
      );

      logger.info(`📝 Transaction hash: ${tx.hash}`);

      // Wait for confirmation
      const receipt = await tx.wait();
      logger.info(`✅ Transaction confirmed in block ${receipt.blockNumber}`);

      return true;
    } catch (error) {
      logger.error('Hybrid contract execution failed', error);
      logger.logError(error as Error, 'FlashloanStrategy.executeWithHybridContract');
      return false;
    }
  }

  private async createFlashloanTransaction(route: FlashloanRoute): Promise<Transaction | null> {
    try {
      // Encode the arbitrage parameters for the flashloan callback
      const arbitrageData = {
        routers: [
          route.arbitrageRoute.pools[0].protocol === 'uniswap-v2' ? ADDRESSES.UNISWAP_V2_ROUTER : ADDRESSES.UNISWAP_V3_ROUTER,
          route.arbitrageRoute.pools[1].protocol === 'uniswap-v2' ? ADDRESSES.UNISWAP_V2_ROUTER : ADDRESSES.UNISWAP_V3_ROUTER
        ],
        amounts: [route.flashloanAmount, route.flashloanAmount],
        swapData: [
          this.encoder.encodeArbitrageSwap(
            route.arbitrageRoute.tokens[0],
            route.arbitrageRoute.tokens[1],
            route.flashloanAmount,
            route.arbitrageRoute.pools[0].protocol as 'uniswap-v2' | 'uniswap-v3',
            this.wallet.address,
            route.arbitrageRoute.pools[0].fee
          ),
          this.encoder.encodeArbitrageSwap(
            route.arbitrageRoute.tokens[1],
            route.arbitrageRoute.tokens[0],
            route.flashloanAmount,
            route.arbitrageRoute.pools[1].protocol as 'uniswap-v2' | 'uniswap-v3',
            this.wallet.address,
            route.arbitrageRoute.pools[1].fee
          )
        ]
      };

      const params = this.flashloanInterface.encodeFlashloanParams(arbitrageData);

      // Create flashloan call data
      const flashloanCalldata = new ethers.Interface([
        'function flashLoan(address[] assets, uint256[] amounts, uint256[] interestRateModes, address onBehalfOf, bytes params, uint16 referralCode)'
      ]).encodeFunctionData('flashLoan', [
        [route.flashloanToken.address],
        [route.flashloanAmount],
        [0], // Variable interest rate mode
        this.wallet.address,
        params,
        0 // No referral
      ]);

      const gasStrategy = await this.gasOptimizer.getCurrentGasStrategy();
      const nonce = await this.wallet.getNonce();

      return {
        hash: '',
        from: this.wallet.address,
        to: ADDRESSES.AAVE_POOL,
        value: BigInt(0),
        gasPrice: gasStrategy.maxFeePerGas,
        gasLimit: route.gasEstimate,
        data: flashloanCalldata,
        nonce,
        maxFeePerGas: gasStrategy.maxFeePerGas,
        maxPriorityFeePerGas: gasStrategy.priorityFee
      };

    } catch (error) {
      logger.logError(error as Error, 'FlashloanStrategy.createFlashloanTransaction');
      return null;
    }
  }

  /**
   * Set whether this strategy is being called from the dynamic strategy
   * This helps reduce duplicate logging when multiple strategies are coordinated
   */
  public setCalledFromDynamicStrategy(value: boolean): void {
    this.isCalledFromDynamicStrategy = value;
  }

  /**
   * Get available Balancer liquidity for a token
   */
  private async getBalancerLiquidity(tokenAddress: string): Promise<number> {
    try {
      // Check if we have a Balancer strategy instance
      if (this.balancerStrategy) {
        return await this.balancerStrategy.getTokenLiquidity(tokenAddress);
      }

      // Fallback: return 0 if no Balancer strategy available
      return 0;
    } catch (error) {
      logger.debug(`Error getting Balancer liquidity for ${tokenAddress}: ${(error as Error).message}`);
      return 0;
    }
  }

  /**
   * Check if a token pair is blacklisted
   */
  private isPairBlacklisted(tokenA: string, tokenB: string): boolean {
    const pairKey = this.getPairKey(tokenA, tokenB);
    const failureInfo = this.failedPairs.get(pairKey);

    if (!failureInfo) return false;

    // Check if blacklist duration has expired
    const now = Date.now();
    if (now - failureInfo.lastFailed > this.BLACKLIST_DURATION) {
      // Reset the failure count after blacklist duration
      this.failedPairs.delete(pairKey);
      return false;
    }

    // Check if pair has exceeded max failures
    return failureInfo.count >= this.MAX_FAILURES;
  }

  /**
   * Record a failed arbitrage attempt for a token pair
   */
  private recordPairFailure(tokenA: string, tokenB: string): void {
    const pairKey = this.getPairKey(tokenA, tokenB);
    const existing = this.failedPairs.get(pairKey);
    const now = Date.now();

    if (existing) {
      existing.count++;
      existing.lastFailed = now;
    } else {
      this.failedPairs.set(pairKey, { count: 1, lastFailed: now });
    }

    const failureInfo = this.failedPairs.get(pairKey)!;
    if (failureInfo.count >= this.MAX_FAILURES) {
      logger.debug(`🚫 Blacklisting pair ${tokenA}/${tokenB} after ${failureInfo.count} failures`);
    }
  }

  /**
   * Get a consistent key for a token pair
   */
  private getPairKey(tokenA: string, tokenB: string): string {
    // Sort addresses to ensure consistent key regardless of order
    return tokenA.toLowerCase() < tokenB.toLowerCase()
      ? `${tokenA.toLowerCase()}-${tokenB.toLowerCase()}`
      : `${tokenB.toLowerCase()}-${tokenA.toLowerCase()}`;
  }
}
