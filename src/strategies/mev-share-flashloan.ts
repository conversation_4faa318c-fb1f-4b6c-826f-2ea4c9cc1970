import { ethers } from 'ethers';
import { FlashloanRoute, ArbitrageRoute } from '../types';
import { MEVShareEventMonitor, BackrunOpportunity } from '../mev-share/event-monitor';
import { BalancerFlashloanStrategy } from './balancer-flashloan';
import { FlashbotsBundleManager } from '../flashbots/bundle-provider';
import { FlashbotsBundleTransaction } from '@flashbots/ethers-provider-bundle';
import { config } from '../config';
import { logger } from '../utils/logger';

import { statusDashboard } from '../utils/statusDashboard';

export interface MEVShareFlashloanRoute extends FlashloanRoute {
  userTxHash: string;
  backrunOpportunity: BackrunOpportunity;
  bundleTransactions: FlashbotsBundleTransaction[];
}

/**
 * MEV-Share Enhanced Flashloan Strategy
 * Combines Balancer flashloans with MEV-Share backrun opportunities
 */
export class MEVShareFlashloanStrategy extends BalancerFlashloanStrategy {
  private mevShareMonitor: MEVShareEventMonitor;
  private flashbotsManager: FlashbotsBundleManager;
  private readonly MIN_GAS_PROTECTION: bigint;
  private readonly MAX_GAS_COST_ETH: number;

  constructor(
    provider: ethers.Provider,
    mevShareMonitor: MEVShareEventMonitor,
    flashbotsManager: FlashbotsBundleManager
  ) {
    super(provider);
    this.mevShareMonitor = mevShareMonitor;
    this.flashbotsManager = flashbotsManager;

    // Gas protection settings
    const isMainnet = config.chainId === 1;
    this.MIN_GAS_PROTECTION = isMainnet
      ? ethers.parseEther('0.005')  // 0.005 ETH minimum profit after gas
      : ethers.parseEther('0.001'); // 0.001 ETH on testnet

    this.MAX_GAS_COST_ETH = isMainnet ? 0.02 : 0.005; // Maximum gas cost allowed

    this.setupEventListeners();
    logger.system('🔄 MEV-Share Flashloan Strategy initialized');
    logger.system(`   Gas Protection: ${ethers.formatEther(this.MIN_GAS_PROTECTION)} ETH`);
    logger.system(`   Max Gas Cost: ${this.MAX_GAS_COST_ETH} ETH`);
  }

  /**
   * Setup MEV-Share event listeners
   */
  private setupEventListeners(): void {
    this.mevShareMonitor.on('backrunOpportunity', async (opportunity: BackrunOpportunity) => {
      await this.handleBackrunOpportunity(opportunity);
    });
  }

  /**
   * Handle detected backrun opportunity
   */
  private async handleBackrunOpportunity(opportunity: BackrunOpportunity): Promise<void> {
    try {
      logger.separator();
      logger.info('🎯 MEV-Share Backrun Opportunity Detected');
      logger.transactionHash(opportunity.userTxHash, 'User transaction');
      logger.profitCalculation(ethers.formatEther(opportunity.estimatedProfit), true);
      logger.info(`Confidence: ${opportunity.confidence}%`);

      // Check gas protection
      if (!this.passesGasProtection(opportunity)) {
        logger.warn('❌ Opportunity rejected: Gas protection threshold not met');
        return;
      }

      // Create MEV-Share flashloan route
      const mevShareRoute = await this.createMEVShareFlashloanRoute(opportunity);
      if (!mevShareRoute) {
        logger.error('❌ Failed to create MEV-Share flashloan route');
        return;
      }

      // Execute via Flashbots bundle
      const success = await this.executeMEVShareFlashloan(mevShareRoute);
      if (success) {
        logger.success('✅ MEV-Share flashloan executed successfully');
      } else {
        logger.error('❌ MEV-Share flashloan execution failed');
      }

    } catch (error) {
      logger.logError(error as Error, 'MEVShareFlashloanStrategy.handleBackrunOpportunity');
      logger.error('MEV-Share opportunity handling failed', error);
    }
  }

  /**
   * Check if opportunity passes gas protection threshold
   */
  private passesGasProtection(opportunity: BackrunOpportunity): boolean {
    const gasEstimateEth = Number(ethers.formatEther(opportunity.gasEstimate));
    const profitAfterGas = opportunity.estimatedProfit - opportunity.gasEstimate;

    // Check maximum gas cost
    if (gasEstimateEth > this.MAX_GAS_COST_ETH) {
      logger.info(`   Gas cost too high: ${gasEstimateEth.toFixed(4)} ETH > ${this.MAX_GAS_COST_ETH} ETH`);
      return false;
    }

    // Check minimum profit after gas
    if (profitAfterGas < this.MIN_GAS_PROTECTION) {
      logger.info(`   Profit after gas too low: ${ethers.formatEther(profitAfterGas)} ETH`);
      return false;
    }

    // Check confidence threshold
    if (opportunity.confidence < 70) {
      logger.info(`   Confidence too low: ${opportunity.confidence}%`);
      return false;
    }

    logger.info('✅ Gas protection checks passed');
    logger.info(`   Estimated Gas: ${gasEstimateEth.toFixed(4)} ETH`);
    logger.info(`   Profit After Gas: ${ethers.formatEther(profitAfterGas)} ETH`);

    return true;
  }

  /**
   * Create MEV-Share flashloan route
   */
  private async createMEVShareFlashloanRoute(
    opportunity: BackrunOpportunity
  ): Promise<MEVShareFlashloanRoute | null> {
    try {
      // First, create a regular flashloan route based on the opportunity
      const flashloanRoutes = await this.scanForBalancerFlashloanOpportunities("");

      if (flashloanRoutes.length === 0) {
        logger.warn('❌ No flashloan opportunities found');
        return null;
      }

      // Select the best route
      const bestRoute = flashloanRoutes[0];

      // Create bundle transactions
      const bundleTransactions = await this.createBundleTransactions(bestRoute, opportunity);

      const mevShareRoute: MEVShareFlashloanRoute = {
        ...bestRoute,
        userTxHash: opportunity.userTxHash,
        backrunOpportunity: opportunity,
        bundleTransactions
      };

      logger.info('✅ MEV-Share flashloan route created');
      logger.info(`   Bundle transactions: ${bundleTransactions.length}`);

      return mevShareRoute;

    } catch (error) {
      logger.logError(error as Error, 'MEVShareFlashloanStrategy.createMEVShareFlashloanRoute');
      return null;
    }
  }

  /**
   * Create bundle transactions for MEV-Share execution
   */
  private async createBundleTransactions(
    route: FlashloanRoute,
    opportunity: BackrunOpportunity
  ): Promise<FlashbotsBundleTransaction[]> {
    const transactions: FlashbotsBundleTransaction[] = [];

    try {
      // Create flashloan transaction
      const flashloanTx = await this.createFlashloanTransaction(route);
      if (flashloanTx) {
        transactions.push(flashloanTx);
      }

      logger.info(`✅ Created ${transactions.length} bundle transactions`);
      return transactions;

    } catch (error) {
      logger.logError(error as Error, 'MEVShareFlashloanStrategy.createBundleTransactions');
      return [];
    }
  }

  /**
   * Create flashloan transaction for bundle
   */
  private async createFlashloanTransaction(route: FlashloanRoute): Promise<FlashbotsBundleTransaction | null> {
    try {
      if (!this.balancerContract) {
        logger.error('❌ Balancer contract not available');
        return null;
      }

      // Prepare transaction data
      const tokens = [route.flashloanToken.address];
      const amounts = [route.flashloanAmount];

      // Encode arbitrage parameters
      const arbitrageParams = ethers.AbiCoder.defaultAbiCoder().encode(
        ['address', 'address', 'uint256'],
        [
          route.flashloanToken.address,
          route.arbitrageRoute.tokens[1].address,
          route.expectedProfit
        ]
      );

      // Create transaction request
      const txRequest = await this.balancerContract.executeFlashloanArbitrage.populateTransaction(
        tokens,
        amounts,
        arbitrageParams
      );

      // Add gas settings
      const gasStrategy = await this.gasOptimizer.getCurrentGasStrategy();

      // Get current nonce for the wallet
      const nonce = await this.provider.getTransactionCount(this.wallet.address, 'pending');

      txRequest.gasLimit = BigInt(400000); // Conservative gas limit
      txRequest.maxFeePerGas = BigInt(gasStrategy.maxFeePerGas.toString());
      txRequest.maxPriorityFeePerGas = BigInt(gasStrategy.priorityFee.toString());
      txRequest.nonce = nonce;
      txRequest.type = 2; // EIP-1559 transaction type
      txRequest.chainId = BigInt(config.chainId); // Required for EIP-1559 transactions

      return this.flashbotsManager.createBundleTransaction(txRequest);

    } catch (error) {
      logger.logError(error as Error, 'MEVShareFlashloanStrategy.createFlashloanTransaction');
      return null;
    }
  }
  /**
   * Derive a min/max timestamp window for the backrun based on opportunity timing.
   * Currently uses a small window around the next block estimate.
   */
  private deriveTimingWindowFromOpportunity(opportunity: BackrunOpportunity): { minTimestamp?: number; maxTimestamp?: number } {
    try {
      const nowSec = Math.floor(Date.now() / 1000);
      const windowSec = (config.estimatedBlockTimeSec || 12);
      return {
        minTimestamp: nowSec,
        maxTimestamp: nowSec + windowSec * 2 // two blocks ahead allowance
      };
    } catch {
      return {};
    }
  }


  /**
   * Public method to execute a backrun opportunity by building a MEV-Share route and submitting the bundle.
   */
  public async executeBackrunOpportunity(opportunity: BackrunOpportunity): Promise<boolean> {
    const route = await this.createMEVShareFlashloanRoute(opportunity);
    if (!route) return false;
    return this.executeMEVShareFlashloan(route);
  }

  /** Execute MEV-Share flashloan via Flashbots bundle */
  private async executeMEVShareFlashloan(route: MEVShareFlashloanRoute): Promise<boolean> {
    try {
      logger.separator();
      logger.info('📦 Executing MEV-Share Flashloan Bundle');
      logger.info(`User Tx: ${route.userTxHash}`);
      logger.info(`Bundle Size: ${route.bundleTransactions.length} transactions`);
      logger.profitCalculation(ethers.formatEther(route.expectedProfit), true);

      // Check wallet balance before execution
      const hasBalance = await this.checkWalletBalanceForMEVShare(route);
      if (!hasBalance) {
        logger.warn('⚠️  Insufficient wallet balance for gas costs, skipping MEV-Share execution');
        return false;
      }

      if (config.dryRun) {
        logger.info('DRY RUN: Simulating MEV-Share bundle execution...');

        logger.info('Step 1: 📦 Create Bundle');
        logger.info(`  └─ User Transaction: ${route.userTxHash}`);
        logger.info(`  └─ Backrun Transaction: Balancer Flashloan`);

        logger.info('Step 2: 🔍 Simulate Bundle');
        await logger.expectedProfit(BigInt(route.expectedProfit.toString()), 'MEV-Share Bundle');
        await logger.gasCost(BigInt(route.gasEstimate.toString()), 'MEV-Share Bundle');

        logger.info('Step 3: 📡 Submit to Flashbots');
        logger.system(`  └─ Bundle Hash: 0x${Math.random().toString(16).slice(2, 18)}...`);

        logger.success('✅ MEV-Share bundle simulation completed');
        logger.separator();
        return true;
      }

      // Determine target block(s)
      const currentBlock = await this.flashbotsManager.getCurrentBlock();
      const startBlock = currentBlock + 1; // Backrun the next block by default

      // Compose bundle: user tx hash first, then our backrun tx(s)
      const bundleTransactions: any[] = [
        { hash: route.userTxHash },
        ...route.bundleTransactions
      ];

      // Simulate bundle against the first target block
      const sim = await this.flashbotsManager.simulateBundle(
        bundleTransactions as any,
        startBlock
      );
      if (!sim.success) {
        logger.error('❌ Bundle simulation failed');
        logger.error(`   Error: ${sim.error}`);
        return false;
      }
      logger.system('✅ Bundle simulation successful');

      // Submit with high priority and multi-block targeting to improve inclusion
      const isHighPriority = true;
      const blockCount = config.enableMultiBlockAttacks ? Math.max(1, config.maxBlocksAhead) : 1;

      if (blockCount > 1) {
        const timingWindow = this.deriveTimingWindowFromOpportunity(route.backrunOpportunity);
        const result = await this.flashbotsManager.submitBundleMultiBlock(
          bundleTransactions as any,
          startBlock,
          blockCount,
          { isHighPriority, ...timingWindow }
        );

        if (result.success) {
          statusDashboard.recordSuccessfulTransaction({
            timestamp: Date.now(),
            type: 'mev-share',
            profit: BigInt(route.expectedProfit.toString()),
            gasUsed: BigInt(route.gasEstimate.toString()),
            bundleHash: result.bundleHash,
            confidence: route.backrunOpportunity.confidence,
            details: `Backrun: ${route.userTxHash.slice(0, 10)}...`
          });
          logger.system('🎉 MEV-Share bundle included', {
            block: result.includedInBlock,
            bundleHash: result.bundleHash
          });
          return true;
        }

        logger.warn('⏭️  Multi-block submission did not include bundle');
        logger.warn(`   Error: ${result.error}`);
        return false;
      } else {
        // Single-block submission with retry escalation
        let success = false;
        let lastError: string | undefined;
        for (let attempt = 0; attempt < config.bundleRetryCount; attempt++) {
          const submission = await this.flashbotsManager.submitBundle(
            bundleTransactions as any,
            startBlock + attempt,
            { isHighPriority, retryCount: attempt }
          );

          if (submission.success) {
            statusDashboard.recordSuccessfulTransaction({
              timestamp: Date.now(),
              type: 'mev-share',
              profit: BigInt(route.expectedProfit.toString()),
              gasUsed: BigInt(route.gasEstimate.toString()),
              bundleHash: submission.bundleHash,
              confidence: route.backrunOpportunity.confidence,
              details: `Backrun: ${route.userTxHash.slice(0, 10)}...`
            });
            logger.system('🎉 MEV-Share bundle submitted successfully', {
              bundleHash: submission.bundleHash,
              targetBlock: startBlock + attempt
            });
            success = true;
            break;
          } else {
            lastError = submission.error;
            logger.warn('⏭️  Block passed without inclusion or submission failed', {
              attempt: attempt + 1,
              targetBlock: startBlock + attempt,
              error: submission.error
            });
          }
        }
        return success;
      }

    } catch (error) {
      logger.logError(error as Error, 'MEVShareFlashloanStrategy.executeMEVShareFlashloan');
      logger.error('MEV-Share flashloan execution failed', error);
      return false;
    }
  }

  /**
   * Start MEV-Share monitoring
   */
  async startMonitoring(): Promise<void> {
    try {
      await this.mevShareMonitor.initialize();
      await this.mevShareMonitor.start();
      logger.system('🔄 MEV-Share flashloan monitoring started');
    } catch (error) {
      logger.logError(error as Error, 'MEVShareFlashloanStrategy.startMonitoring');
      logger.error('❌ Failed to start MEV-Share monitoring');
    }
  }

  /**
   * Stop MEV-Share monitoring
   */
  async stopMonitoring(): Promise<void> {
    try {
      await this.mevShareMonitor.stop();
      logger.system('🛑 MEV-Share flashloan monitoring stopped');
    } catch (error) {
      logger.logError(error as Error, 'MEVShareFlashloanStrategy.stopMonitoring');
    }
  }

  /**
   * Check if wallet has sufficient balance for MEV-Share execution
   */
  private async checkWalletBalanceForMEVShare(route: MEVShareFlashloanRoute): Promise<boolean> {
    try {
      // Get current wallet balance using the inherited wallet property
      const balance = await this.provider.getBalance(this.wallet.address);

      // Estimate gas cost for the bundle transactions
      const gasLimit = BigInt(route.gasEstimate?.toString() || '400000'); // Default gas limit for flashloan
      const gasPrice = ethers.parseUnits('20', 'gwei'); // Conservative gas price estimate
      const estimatedGasCost = gasLimit * gasPrice;

      // Add 15% buffer for gas price fluctuations and bundle complexity
      const gasCostWithBuffer = estimatedGasCost + (estimatedGasCost * BigInt(15) / BigInt(100));

      logger.info(`💰 MEV-Share Balance Check:`);
      logger.info(`   Current Balance: ${ethers.formatEther(balance)} ETH`);
      logger.info(`   Estimated Gas Cost: ${ethers.formatEther(estimatedGasCost)} ETH`);
      logger.info(`   Gas Cost + Buffer: ${ethers.formatEther(gasCostWithBuffer)} ETH`);

      const hasBalance = balance >= gasCostWithBuffer;

      if (!hasBalance) {
        logger.warn(`⚠️  Insufficient balance for MEV-Share gas costs!`);
        logger.warn(`   Need: ${ethers.formatEther(gasCostWithBuffer)} ETH`);
        logger.warn(`   Have: ${ethers.formatEther(balance)} ETH`);
        logger.warn(`   Deficit: ${ethers.formatEther(gasCostWithBuffer - balance)} ETH`);
      }

      return hasBalance;
    } catch (error) {
      logger.logError(error as Error, 'MEVShareFlashloanStrategy.checkWalletBalanceForMEVShare');
      logger.warn('⚠️  Could not verify wallet balance for MEV-Share, proceeding with caution');
      return true; // Default to true if balance check fails to avoid blocking execution
    }
  }

  /**
   * Get strategy status
   */
  getStatus(): {
    mevShareAvailable: boolean;
    monitoring: boolean;
    gasProtection: string;
    maxGasCost: string;
  } {
    const mevShareStatus = this.mevShareMonitor.getStatus();

    return {
      mevShareAvailable: mevShareStatus.isAvailable,
      monitoring: mevShareStatus.isRunning,
      gasProtection: ethers.formatEther(this.MIN_GAS_PROTECTION) + ' ETH',
      maxGasCost: this.MAX_GAS_COST_ETH + ' ETH'
    };
  }
}
