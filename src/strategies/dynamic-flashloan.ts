import {ethers} from 'ethers';
import {FlashloanRoute} from '../types';
import {FlashloanStrategy} from './flashloan';
import {BalancerFlashloanStrategy} from './balancer-flashloan';
import {UniswapV3FlashSwapStrategy} from './uniswap-v3-flash';
import {MEVShareFlashloanStrategy} from './mev-share-flashloan';
import {FlashbotsBundleManager} from '../flashbots/bundle-provider';
import {FlashbotsExecutor} from '../execution/flashbots-executor';
import {GasOptimizer} from '../gas/optimizer';
import {config} from '../config';
import {logger} from '../utils/logger';
import { TokenRegistry } from '../tokens/registry';
import { LightArbitrageService, TradeStep, DEXType, FlashloanProvider } from '../services/LightArbitrageService';
import { FlashloanAttackOrchestrator, OrchestratorConfig } from '../attacks/flashloan/FlashloanAttackOrchestrator';

/**
 * Strategy performance statistics
 */
interface StrategyStats {
    totalOpportunities: number;
    successfulExecutions: number;
    totalProfit: bigint;
    averageGasCost: bigint;
    averageExecutionTime: number;
    successRate: number;
    profitability: number;
}

/**
 * Enhanced flashloan route with strategy information
 */
interface EnhancedFlashloanRoute extends FlashloanRoute {
    strategy: 'aave' | 'balancer' | 'uniswap-v3' | 'mev-share';
    estimatedGasCost: bigint;
    netProfit: bigint;
    riskScore: number;
    executionComplexity: number;
}

/**
 * Dynamic Flashloan Strategy Selector
 * Combines all flashloan strategies and selects the most profitable one
 */
export class DynamicFlashloanStrategy {
    private flashbotsManager: FlashbotsBundleManager;
    private flashbotsExecutor: FlashbotsExecutor;
    private gasOptimizer: GasOptimizer;
    private tokenRegistry: TokenRegistry;


    // Individual strategies
    private aaveFlashloanStrategy: FlashloanStrategy;
    private balancerFlashloanStrategy: BalancerFlashloanStrategy;
    private uniswapV3FlashSwapStrategy: UniswapV3FlashSwapStrategy;
    private mevShareFlashloanStrategy: MEVShareFlashloanStrategy | null = null;
    private lightArbitrageService: LightArbitrageService | null = null;
    private flashloanAttackOrchestrator: FlashloanAttackOrchestrator | null = null;

    // Strategy performance tracking
    private strategyStats: Map<string, StrategyStats> = new Map();

    constructor(
        provider: ethers.Provider,
        wallet: ethers.Wallet,
        flashbotsManager: FlashbotsBundleManager,
        flashbotsExecutor: FlashbotsExecutor,
        gasOptimizer: GasOptimizer
    ) {
        this.flashbotsManager = flashbotsManager;
        this.flashbotsExecutor = flashbotsExecutor;
        this.gasOptimizer = gasOptimizer;

        // Initialize individual strategies
        this.aaveFlashloanStrategy = new FlashloanStrategy(provider);
        this.balancerFlashloanStrategy = new BalancerFlashloanStrategy(provider);
        this.uniswapV3FlashSwapStrategy = new UniswapV3FlashSwapStrategy(provider as ethers.JsonRpcProvider, wallet);

        // Initialize Light contract service if contract address is configured
        if (config.LIGHT_CONTRACT_ADDRESS) {
            try {
                const lightContractABI = require('../../artifacts/contracts/HybridFlashloanArbitrageLight.sol/HybridFlashloanArbitrageLite.json').abi;
                this.lightArbitrageService = new LightArbitrageService(
                    config.LIGHT_CONTRACT_ADDRESS,
                    provider,
                    wallet,
                    lightContractABI
                );

                // Initialize Flashloan Attack Orchestrator
                const orchestratorConfig: OrchestratorConfig = {
                    enabledVectors: ['weth-usdc-v3-sushi', 'weth-usdc-sushi-v3', 'usdc-dai-balancer-sushi'],
                    minProfitETH: '0.01',
                    maxGasCostGwei: '50',
                    defaultSlippage: 100,
                    scanInterval: 5000,
                    dryRun: config.dryRun,
                    useFlashbots: config.useFlashbots,
                    maxConcurrentAttacks: 3
                };

                this.flashloanAttackOrchestrator = new FlashloanAttackOrchestrator(
                    this.lightArbitrageService,
                    provider,
                    wallet,
                    orchestratorConfig
                );

                logger.system('✅ Light Arbitrage Service initialized (Off-chain architecture)');
                logger.system('✅ Flashloan Attack Orchestrator initialized');
            } catch (error) {
                logger.error('❌ Failed to initialize Light Arbitrage Service:', error);
            }
        }

        // Configure strategies to suppress duplicate logging when called from dynamic strategy
        this.aaveFlashloanStrategy.setCalledFromDynamicStrategy(true);
        this.balancerFlashloanStrategy.setCalledFromDynamicStrategy(true);
        this.tokenRegistry = new TokenRegistry(provider as ethers.JsonRpcProvider);


        // Initialize strategy statistics
        this.initializeStrategyStats();

        logger.system('🚀 Dynamic Flashloan Strategy initialized');
        const strategies = ['Aave', 'Balancer', 'Uniswap V3', 'MEV-Share'];
        if (this.lightArbitrageService) {
            strategies.push('Light Contract (Off-chain)');
        }
        logger.system(`   Strategies: ${strategies.join(', ')}`);
    }

    /**
     * Initialize strategy performance tracking
     */
    private initializeStrategyStats(): void {
        const strategies = ['aave', 'balancer', 'uniswap-v3', 'mev-share'];
        if (this.lightArbitrageService) {
            strategies.push('light-contract');
        }

        for (const strategy of strategies) {
            this.strategyStats.set(strategy, {
                totalOpportunities: 0,
                successfulExecutions: 0,
                totalProfit: BigInt(0),
                averageGasCost: BigInt(0),
                averageExecutionTime: 0,
                successRate: 0,
                profitability: 0
            });
        }
    }

    /**
     * Set MEV-Share strategy (called by bot after initialization)
     */
    setMEVShareStrategy(strategy: MEVShareFlashloanStrategy): void {
        this.mevShareFlashloanStrategy = strategy;
        logger.info('✅ MEV-Share strategy integrated');
    }

    private async toWei(amount: bigint, token: any): Promise<bigint> {
        const { tokenAmountToWei } = await import('../utils/denomination');
        return tokenAmountToWei(token, amount);
    }

    /**
     * Scan for all flashloan opportunities across all strategies
     */
    async scanForOpportunities(txHash: string): Promise<EnhancedFlashloanRoute[]> {
        const allOpportunities: EnhancedFlashloanRoute[] = [];

        try {
            // Validate configuration before scanning
            if (!this.validateConfiguration()) {
                logger.warn('Dynamic flashloan strategy configuration invalid, skipping opportunity search');
                return [];
            }

            logger.debug('🔍 Scanning for dynamic flashloan opportunities...');

            // Scan all strategies in parallel for maximum efficiency
            const [
                aaveOpportunities,
                balancerOpportunities,
                uniswapV3Opportunities
            ] = await Promise.all([
                this.scanAaveOpportunities(txHash),
                this.scanBalancerOpportunities(txHash),
                this.scanUniswapV3Opportunities(txHash)
            ]);

            // Combine all opportunities
            allOpportunities.push(...aaveOpportunities);
            allOpportunities.push(...balancerOpportunities);
            allOpportunities.push(...uniswapV3Opportunities);

            // Rank opportunities by profitability and confidence
            const rankedOpportunities = this.rankOpportunities(allOpportunities);

            if (rankedOpportunities.length > 0) {
                const best = rankedOpportunities[0];
                logger.debug(`   Best: ${best.strategy} - ${ethers.formatEther(best.expectedProfit)} ETH (${best.confidence}%)`);
            }

            return rankedOpportunities;

        } catch (error) {
            logger.logError(error as Error, 'DynamicFlashloanStrategy.scanForOpportunities');
            logger.error('Dynamic flashloan scan failed', error);
            return [];
        }
    }

    /**
     * Validate configuration before executing strategies
     */
    private validateConfiguration(): boolean {
        // Loosen validation: allow scanning as long as core providers are available
        // Contract addresses are optional accelerators
        const hasAnyProvider = true; // Assuming base providers (Aave Pool, Balancer Vault) are configured via ADDRESSES
        if (!hasAnyProvider) {
            logger.error('No base providers available for flashloans');
            return false;
        }
        return true;
    }

    /**
     * Scan Aave flashloan opportunities
     */
    private async scanAaveOpportunities(txHash: string): Promise<EnhancedFlashloanRoute[]> {
        try {
            // Validate Aave contract before scanning
            if (!config.aaveFlashloanContract || !ethers.isAddress(config.aaveFlashloanContract)) {
                logger.warn('Invalid Aave flashloan contract, skipping Aave opportunities');
                return [];
            }

            const opportunities = await this.aaveFlashloanStrategy.scanForFlashloanOpportunities(txHash);
            const enhanced: EnhancedFlashloanRoute[] = [];
            for (const r of opportunities) {
                enhanced.push(await this.enhanceRoute(r, 'aave'));
            }
            return enhanced;
        } catch (error) {
            logger.error('Error scanning Aave opportunities:', error);
            return [];
        }
    }

    /**
     * Scan Balancer flashloan opportunities
     */
    private async scanBalancerOpportunities(txHash: string): Promise<EnhancedFlashloanRoute[]> {
        try {
            // Validate Balancer contract before scanning
            if (!config.balancerFlashloanContract || !ethers.isAddress(config.balancerFlashloanContract)) {
                logger.warn('Invalid Balancer flashloan contract, skipping Balancer opportunities');
                return [];
            }

            const opportunities = await this.balancerFlashloanStrategy.scanForBalancerFlashloanOpportunities(txHash);
            const enhanced: EnhancedFlashloanRoute[] = [];
            for (const r of opportunities) {
                enhanced.push(await this.enhanceRoute(r, 'balancer'));
            }
            return enhanced;
        } catch (error) {
            logger.error('Error scanning Balancer opportunities:', error);
            return [];
        }
    }

    /**
     * Scan Uniswap V3 flash swap opportunities
     */
    private async scanUniswapV3Opportunities(txHash: string): Promise<EnhancedFlashloanRoute[]> {
        try {
            const opportunities = await this.uniswapV3FlashSwapStrategy.scanForOpportunities();
            const enhanced: EnhancedFlashloanRoute[] = [];
            for (const r of opportunities) {
                enhanced.push(await this.enhanceV3Route(r));
            }
            return enhanced;
        } catch (error) {
            logger.error('Error scanning Uniswap V3 opportunities:', error);
            return [];
        }
    }

    /**
     * Enhance V3 route with strategy-specific information
     */
    private async enhanceV3Route(route: any): Promise<EnhancedFlashloanRoute> {
        const tokenA = await this.tokenRegistry.getToken(route.tokenA) || {address: route.tokenA, symbol: 'TOKEN_A', name: 'Token A', decimals: 18};
        const tokenB = await this.tokenRegistry.getToken(route.tokenB) || {address: route.tokenB, symbol: 'TOKEN_B', name: 'Token B', decimals: 18};

        const estimatedGasCost = await this.estimateGasCostWei('uniswap-v3');
        const expectedProfitWei = await this.toWei(route.expectedProfit || 0n, tokenA);
        const netProfit = expectedProfitWei - estimatedGasCost;

        return {
            flashloanToken: tokenA,
            flashloanAmount: route.amount,
            flashloanPremium: BigInt(0),
            arbitrageRoute: {
                tokens: [tokenA, tokenB],
                pools: [],
                expectedProfit: expectedProfitWei,
                gasEstimate: BigInt(300000),
                confidence: route.confidence || 50
            },
            expectedProfit: expectedProfitWei,
            confidence: route.confidence || 50,
            gasEstimate: BigInt(300000),
            strategy: 'uniswap-v3',
            estimatedGasCost,
            netProfit,
            riskScore: this.calculateRiskScore({ ...route, expectedProfit: expectedProfitWei } as any, 'uniswap-v3'),
            executionComplexity: this.calculateExecutionComplexity('uniswap-v3')
        };
    }

    /**
     * Enhance route with strategy-specific information
     */
    private async enhanceRoute(route: FlashloanRoute, strategy: 'aave' | 'balancer' | 'uniswap-v3' | 'mev-share'): Promise<EnhancedFlashloanRoute> {
        // Convert expectedProfit to wei for consistent comparison
        const expectedProfitWei = await this.toWei(BigInt(route.expectedProfit.toString()), route.flashloanToken);
        const estimatedGasCost = await this.estimateGasCostWei(strategy);
        const netProfit = expectedProfitWei - estimatedGasCost;

        return {
            ...route,
            expectedProfit: expectedProfitWei,
            strategy,
            estimatedGasCost,
            netProfit,
            riskScore: this.calculateRiskScore({ ...route, expectedProfit: expectedProfitWei } as any, strategy),
            executionComplexity: this.calculateExecutionComplexity(strategy)
        };
    }

    /**
     * Estimate gas cost for different strategies
     */
    private async estimateGasCostWei(strategy: string): Promise<bigint> {
        const gas = await this.gasOptimizer.getCurrentGasStrategy();
        const gasPrice = BigInt(gas.maxFeePerGas.toString());
        const limits: Record<string, bigint> = {
            'aave': 400000n,
            'balancer': 350000n,
            'uniswap-v3': 300000n,
            'mev-share': 450000n,
            'default': 400000n
        };
        const gasLimit = limits[strategy] ?? limits['default'];
        return gasLimit * gasPrice;
    }

    /**
     * Calculate risk score for a route and strategy
     */
    private calculateRiskScore(route: FlashloanRoute, strategy: string): number {
        let riskScore = 0;

        // Base risk from confidence (lower confidence = higher risk)
        riskScore += (100 - route.confidence) * 0.5;

        // Strategy-specific risks
        switch (strategy) {
            case 'aave':
                riskScore += 10; // Aave fees add risk
                break;
            case 'balancer':
                riskScore += 5; // Balancer is generally safer
                break;
            case 'uniswap-v3':
                riskScore += 15; // V3 flash swaps have more complexity
                break;
            case 'mev-share':
                riskScore += 20; // MEV-Share has timing risks
                break;
        }

        // Profit size risk (very large profits might be suspicious)
        const profitEth = Number(ethers.formatEther(route.expectedProfit));
        if (profitEth > 10) riskScore += 25;
        else if (profitEth > 5) riskScore += 15;
        else if (profitEth > 1) riskScore += 5;

        return Math.min(riskScore, 100); // Cap at 100
    }

    /**
     * Calculate execution complexity
     */
    private calculateExecutionComplexity(strategy: string): number {
        switch (strategy) {
            case 'balancer':
                return 1; // Simplest - direct flashloan
            case 'aave':
                return 2; // Moderate - flashloan with fees
            case 'uniswap-v3':
                return 3; // Complex - flash swaps
            case 'mev-share':
                return 4; // Most complex - bundle coordination
            default:
                return 2;
        }
    }

    /**
     * Rank opportunities by profitability, confidence, and risk
     */
    private rankOpportunities(opportunities: EnhancedFlashloanRoute[]): EnhancedFlashloanRoute[] {
        return opportunities
            .filter(route => route.netProfit > 0n)
            .sort((a, b) => {
                if (a.netProfit !== b.netProfit) return b.netProfit > a.netProfit ? 1 : -1;
                if (a.confidence !== b.confidence) return b.confidence - a.confidence;
                if (a.riskScore !== b.riskScore) return a.riskScore - b.riskScore;
                return a.executionComplexity - b.executionComplexity;
            });
    }

    /**
     * Execute the best flashloan opportunity with MEV protection
     * @param opportunities Optional pre-scanned opportunities to avoid redundant scanning
     * @param txHash
     */
    async executeBestOpportunity(opportunities: EnhancedFlashloanRoute[], txHash: string): Promise<boolean> {
        try {
            // Use provided opportunities or scan if none provided
            const availableOpportunities = opportunities || await this.scanForOpportunities(txHash);

            if (availableOpportunities.length === 0) {
                logger.info('❌ No profitable flashloan opportunities found');
                return false;
            }

            const bestOpportunity = availableOpportunities[0];
            logger.system(`🎯 Executing best opportunity: ${bestOpportunity.strategy}`);
            logger.system(`   Expected profit: ${ethers.formatEther(bestOpportunity.netProfit)} ETH`);
            logger.system(`   Confidence: ${bestOpportunity.confidence}%`);
            logger.system(`   Risk score: ${bestOpportunity.riskScore}/100`);

            // Log whether we used pre-scanned opportunities or scanned fresh
            if (opportunities) {
                logger.debug('📋 Using pre-scanned opportunities (avoiding redundant scan)');
            } else {
                logger.debug('🔍 Scanned fresh opportunities');
            }

            // Execute with MEV protection via Flashbots
            const success = await this.executeWithMEVProtection(bestOpportunity);

            // Update strategy statistics
            this.updateStrategyStats(bestOpportunity.strategy, success, bestOpportunity.netProfit);

            return success;

        } catch (error) {
            logger.logError(error as Error, 'DynamicFlashloanStrategy.executeBestOpportunity');
            logger.error('Failed to execute best opportunity', error);
            return false;
        }
    }

    /**
     * Execute flashloan with MEV protection via Flashbots
     */
    private async executeWithMEVProtection(route: EnhancedFlashloanRoute): Promise<boolean> {
        try {
            logger.system('🛡️  Executing with MEV protection via Flashbots...');

            // Check if Flashbots is available
            if (!this.flashbotsManager.isAvailable()) {
                logger.info('⚠️  Flashbots not available, using regular execution');
                return await this.executeRegular(route);
            }

            // Execute based on strategy
            switch (route.strategy) {
                case 'aave':
                    return await this.executeAaveWithFlashbots(route);
                case 'balancer':
                    return await this.executeBalancerWithFlashbots(route);
                case 'uniswap-v3':
                    return await this.executeUniswapV3WithFlashbots(route);
                case 'mev-share':
                    return await this.executeMEVShareWithFlashbots(route);
                case 'light-contract':
                    return await this.executeLightContractWithFlashbots(route);
                default:
                    logger.info(`❌ Unknown strategy: ${route.strategy}`);
                    return false;
            }

        } catch (error) {
            logger.logError(error as Error, 'DynamicFlashloanStrategy.executeWithMEVProtection');
            logger.error('MEV-protected execution failed', error);
            return false;
        }
    }

    /**
     * Execute Aave flashloan with Flashbots
     */
    private async executeAaveWithFlashbots(route: EnhancedFlashloanRoute): Promise<boolean> {
        try {
            logger.system('🏦 Executing Aave flashloan via Flashbots...');

            // Pre-execution profitability check with current gas prices
            const gasStrategy = await this.gasOptimizer.getCurrentGasStrategy();
            const estimatedGasLimit = 300000n; // Conservative estimate for Aave flashloan
            const actualGasCost = estimatedGasLimit * BigInt(gasStrategy.maxFeePerGas.toString());
            const expectedProfitWei = await this.toWei(BigInt(route.expectedProfit.toString()), route.flashloanToken);
            const netProfit = expectedProfitWei - actualGasCost;

            await logger.profitabilityAnalysis(
                expectedProfitWei,
                actualGasCost,
                netProfit,
                'Pre-execution Check'
            );

            if (netProfit <= BigInt(config.minProfitWei)) {
                logger.system('Aave flashloan no longer profitable with current gas prices');
                logger.system(`Required minimum: ${ethers.formatEther(config.minProfitWei)} ETH`);
                return false;
            }

            // Apply profit optimization if enabled
            const optimizedRoute = config.enableProfitMaximization ?
                await this.optimizeRouteForProfit(route) : route;

            const result = await this.flashbotsExecutor.executeAaveFlashloan(optimizedRoute, {
                useFlashbots: config.enableFlashbots,
                urgency: this.getConfiguredUrgency(),
                maxGasCostEth: config.maxGasCostEth,
                slippageTolerance: config.slippageTolerance
            });

            if (result.success) {
                logger.success('✅ Aave flashloan executed successfully');
                logger.system(`   Transaction: ${result.txHash}`);
                return true;
            } else {
                logger.system(`❌ Aave flashloan failed: ${result.error}`);
                return false;
            }

        } catch (error) {
            logger.logError(error as Error, 'DynamicFlashloanStrategy.executeAaveWithFlashbots');
            return false;
        }
    }

    /**
     * Execute Balancer flashloan with Flashbots
     */
    private async executeBalancerWithFlashbots(route: EnhancedFlashloanRoute): Promise<boolean> {
        try {
            logger.info('⚖️  Executing Balancer flashloan via Flashbots...');

            const success = await this.balancerFlashloanStrategy.executeBalancerFlashloan(route);

            if (success) {
                logger.info('✅ Balancer flashloan executed successfully');
                return true;
            } else {
                logger.info('❌ Balancer flashloan execution failed');
                return false;
            }

        } catch (error) {
            logger.logError(error as Error, 'DynamicFlashloanStrategy.executeBalancerWithFlashbots');
            return false;
        }
    }

    /**
     * Execute Uniswap V3 flash swap with Flashbots
     */
    private async executeUniswapV3WithFlashbots(route: EnhancedFlashloanRoute): Promise<boolean> {
        try {
            logger.info('🦄 Executing Uniswap V3 flash swap via Flashbots...');

            // Convert route to V3 flash swap format
            const v3Route = this.convertToV3Route(route);
            const result = await this.uniswapV3FlashSwapStrategy.executeFlashSwap(v3Route);

            if (result.success) {
                logger.info('✅ Uniswap V3 flash swap executed successfully');
                return true;
            } else {
                logger.info('❌ Uniswap V3 flash swap execution failed');
                return false;
            }

        } catch (error) {
            logger.logError(error as Error, 'DynamicFlashloanStrategy.executeUniswapV3WithFlashbots');
            return false;
        }
    }

    /**
     * Execute MEV-Share flashloan with Flashbots
     */
    private async executeMEVShareWithFlashbots(route: EnhancedFlashloanRoute): Promise<boolean> {
        // Phase 2: MEV-Share is handled by the MEVShareFlashloanStrategy via event listeners
        logger.info('MEV-Share execution delegated to event-driven handler');
        return false;
    }

    /**
     * Execute Light contract arbitrage with Flashbots (Off-chain architecture)
     */
    private async executeLightContractWithFlashbots(route: EnhancedFlashloanRoute): Promise<boolean> {
        try {
            if (!this.lightArbitrageService) {
                logger.error('❌ Light Arbitrage Service not initialized');
                return false;
            }

            logger.system('🚀 Executing Light contract arbitrage via Flashbots (Off-chain architecture)...');

            // Convert route to Light contract trade steps
            const tradeSteps = this.convertToLightContractSteps(route);

            if (tradeSteps.length === 0) {
                logger.error('❌ Failed to convert route to Light contract steps');
                return false;
            }

            // Execute arbitrage with Light contract
            const tx = await this.lightArbitrageService.executeArbitrage(
                route.tokenIn,
                route.amount.toString(),
                tradeSteps,
                route.netProfit.toString(),
                route.provider === 'aave' ? FlashloanProvider.AAVE : FlashloanProvider.BALANCER,
                ethers.parseUnits('50', 'gwei').toString() // Max gas cost
            );

            logger.system(`✅ Light contract transaction submitted: ${tx.hash}`);

            // Wait for confirmation
            const receipt = await tx.wait();

            if (receipt?.status === 1) {
                logger.system(`🎉 Light contract arbitrage successful! Block: ${receipt.blockNumber}`);
                logger.system(`⛽ Gas used: ${receipt.gasUsed.toString()}`);
                return true;
            } else {
                logger.error('❌ Light contract transaction failed');
                return false;
            }

        } catch (error) {
            logger.logError(error as Error, 'DynamicFlashloanStrategy.executeLightContractWithFlashbots');
            logger.error('Light contract execution failed:', error);
            return false;
        }
    }

    /**
     * Execute without MEV protection (fallback)
     */
    private async executeRegular(route: EnhancedFlashloanRoute): Promise<boolean> {
        try {
            switch (route.strategy) {
                case 'aave':
                    return await this.aaveFlashloanStrategy.executeFlashloan(route);
                case 'balancer':
                    return await this.balancerFlashloanStrategy.executeBalancerFlashloan(route);
                case 'uniswap-v3':
                    const v3Route = this.convertToV3Route(route);
                    const result = await this.uniswapV3FlashSwapStrategy.executeFlashSwap(v3Route);
                    return result.success;
                case 'light-contract':
                    return await this.executeLightContractRegular(route);
                default:
                    return false;
            }
        } catch (error) {
            logger.logError(error as Error, 'DynamicFlashloanStrategy.executeRegular');
            return false;
        }
    }

    /**
     * Execute Light contract arbitrage without MEV protection
     */
    private async executeLightContractRegular(route: EnhancedFlashloanRoute): Promise<boolean> {
        try {
            if (!this.lightArbitrageService) {
                logger.error('❌ Light Arbitrage Service not initialized');
                return false;
            }

            logger.system('🚀 Executing Light contract arbitrage (regular execution)...');

            // Convert route to Light contract trade steps
            const tradeSteps = this.convertToLightContractSteps(route);

            if (tradeSteps.length === 0) {
                logger.error('❌ Failed to convert route to Light contract steps');
                return false;
            }

            // Execute arbitrage with Light contract
            const tx = await this.lightArbitrageService.executeArbitrage(
                route.tokenIn,
                route.amount.toString(),
                tradeSteps,
                route.netProfit.toString(),
                route.provider === 'aave' ? FlashloanProvider.AAVE : FlashloanProvider.BALANCER,
                ethers.parseUnits('50', 'gwei').toString()
            );

            logger.system(`✅ Light contract transaction submitted: ${tx.hash}`);

            // Wait for confirmation
            const receipt = await tx.wait();

            if (receipt?.status === 1) {
                logger.system(`🎉 Light contract arbitrage successful! Block: ${receipt.blockNumber}`);
                return true;
            } else {
                logger.error('❌ Light contract transaction failed');
                return false;
            }

        } catch (error) {
            logger.logError(error as Error, 'DynamicFlashloanStrategy.executeLightContractRegular');
            return false;
        }
    }

    /**
     * Convert flashloan route to Light contract trade steps (Off-chain architecture)
     */
    private convertToLightContractSteps(route: EnhancedFlashloanRoute): TradeStep[] {
        try {
            const tradeSteps: TradeStep[] = [];

            // Get DEX addresses from Light service
            const dexAddresses = this.lightArbitrageService?.getDEXAddresses();
            const tokens = this.lightArbitrageService?.getTokenAddresses();

            if (!dexAddresses || !tokens) {
                logger.error('❌ DEX addresses or tokens not available from Light service');
                return [];
            }

            // Convert buy path to trade steps
            if (route.buyPath && route.buyPath.length >= 2) {
                for (let i = 0; i < route.buyPath.length - 1; i++) {
                    const tokenIn = route.buyPath[i];
                    const tokenOut = route.buyPath[i + 1];

                    // Determine DEX type and address based on route
                    const { dexAddress, dexType } = this.getDEXInfoForRoute(route, 'buy', dexAddresses);

                    tradeSteps.push({
                        dex: dexAddress,
                        dexType,
                        tokenIn,
                        tokenOut,
                        slippageToleranceBps: 100, // 1% default
                        v3Fee: dexType === DEXType.V3 ? this.getV3Fee(tokenIn, tokenOut) : 0,
                        balancerPoolId: dexType === DEXType.BALANCER_V2 ? this.getBalancerPoolId(tokenIn, tokenOut) : '0x0000000000000000000000000000000000000000000000000000000000000000'
                    });
                }
            }

            // Convert sell path to trade steps
            if (route.sellPath && route.sellPath.length >= 2) {
                for (let i = 0; i < route.sellPath.length - 1; i++) {
                    const tokenIn = route.sellPath[i];
                    const tokenOut = route.sellPath[i + 1];

                    // Determine DEX type and address based on route
                    const { dexAddress, dexType } = this.getDEXInfoForRoute(route, 'sell', dexAddresses);

                    tradeSteps.push({
                        dex: dexAddress,
                        dexType,
                        tokenIn,
                        tokenOut,
                        slippageToleranceBps: 150, // 1.5% for sell side
                        v3Fee: dexType === DEXType.V3 ? this.getV3Fee(tokenIn, tokenOut) : 0,
                        balancerPoolId: dexType === DEXType.BALANCER_V2 ? this.getBalancerPoolId(tokenIn, tokenOut) : '0x0000000000000000000000000000000000000000000000000000000000000000'
                    });
                }
            }

            logger.debug(`✅ Converted route to ${tradeSteps.length} Light contract trade steps`);
            return tradeSteps;

        } catch (error) {
            logger.error('❌ Failed to convert route to Light contract steps:', error);
            return [];
        }
    }

    /**
     * Get DEX info for route conversion
     */
    private getDEXInfoForRoute(route: EnhancedFlashloanRoute, side: 'buy' | 'sell', dexAddresses: any): { dexAddress: string, dexType: DEXType } {
        const dexKey = side === 'buy' ? route.buyDex : route.sellDex;

        // Map DEX names to addresses and types
        switch (dexKey?.toLowerCase()) {
            case 'uniswap-v2':
                return { dexAddress: dexAddresses.UNISWAP_V2, dexType: DEXType.V2 };
            case 'uniswap-v3':
                return { dexAddress: dexAddresses.UNISWAP_V3, dexType: DEXType.V3 };
            case 'sushiswap':
                return { dexAddress: dexAddresses.SUSHISWAP, dexType: DEXType.V2 };
            case 'balancer-v2':
                return { dexAddress: dexAddresses.BALANCER_V2, dexType: DEXType.BALANCER_V2 };
            case 'curve':
                return { dexAddress: dexAddresses.CURVE_3POOL, dexType: DEXType.CURVE };
            default:
                // Default to Uniswap V3
                return { dexAddress: dexAddresses.UNISWAP_V3, dexType: DEXType.V3 };
        }
    }

    /**
     * Get V3 fee for token pair
     */
    private getV3Fee(tokenIn: string, tokenOut: string): number {
        const tokens = this.lightArbitrageService?.getTokenAddresses();
        if (!tokens) return 3000; // Default 0.3%

        // Common V3 fees based on token pairs
        if ((tokenIn === tokens.WETH && tokenOut === tokens.USDC) ||
            (tokenIn === tokens.USDC && tokenOut === tokens.WETH)) {
            return 3000; // 0.3%
        }
        if ((tokenIn === tokens.USDC && tokenOut === tokens.DAI) ||
            (tokenIn === tokens.DAI && tokenOut === tokens.USDC)) {
            return 100; // 0.01%
        }

        return 3000; // Default 0.3%
    }

    /**
     * Get Balancer pool ID for token pair
     */
    private getBalancerPoolId(tokenIn: string, tokenOut: string): string {
        const pools = this.lightArbitrageService?.getBalancerPools();
        const tokens = this.lightArbitrageService?.getTokenAddresses();

        if (!pools || !tokens) {
            return '0x0000000000000000000000000000000000000000000000000000000000000000';
        }

        // Map token addresses to symbols and find pool
        const getSymbol = (address: string) => {
            if (address === tokens.WETH) return 'WETH';
            if (address === tokens.USDC) return 'USDC';
            if (address === tokens.DAI) return 'DAI';
            return 'UNKNOWN';
        };

        const symbolIn = getSymbol(tokenIn);
        const symbolOut = getSymbol(tokenOut);

        const poolKey1 = `${symbolIn}-${symbolOut}`;
        const poolKey2 = `${symbolOut}-${symbolIn}`;

        return pools[poolKey1] || pools[poolKey2] || '0x0000000000000000000000000000000000000000000000000000000000000000';
    }

    /**
     * Convert flashloan route to Uniswap V3 format
     */
    private convertToV3Route(route: FlashloanRoute): any {
        // Convert the generic flashloan route to V3-specific format
        return {
            tokenA: route.flashloanToken.address,
            tokenB: route.arbitrageRoute.tokens[1].address,
            borrowFee: 3000, // Default to 0.3% fee tier
            sellFee: 500,    // Default to 0.05% fee tier
            amount: route.flashloanAmount,
            expectedProfit: route.expectedProfit,
            confidence: route.confidence
        };
    }

    /**
     * Update strategy performance statistics
     */
    private updateStrategyStats(strategy: string, success: boolean, profit: bigint): void {
        const stats = this.strategyStats.get(strategy);
        if (!stats) return;

        stats.totalOpportunities++;
        if (success) {
            stats.successfulExecutions++;
            stats.totalProfit += profit;
        }

        stats.successRate = (stats.successfulExecutions / stats.totalOpportunities) * 100;
        stats.profitability = Number(ethers.formatEther(stats.totalProfit));

        this.strategyStats.set(strategy, stats);

        logger.system(`📊 ${strategy} stats: ${stats.successRate.toFixed(1)}% success, ${stats.profitability.toFixed(4)} ETH total`);
    }

    /**
     * Get strategy performance report
     */
    getStrategyReport(): Record<string, StrategyStats> {
        const report: Record<string, StrategyStats> = {};

        for (const [strategy, stats] of this.strategyStats.entries()) {
            report[strategy] = {...stats};
        }

        return report;
    }

    /**
     * Get the best performing strategy
     */
    getBestStrategy(): string {
        let bestStrategy = 'balancer'; // Default to Balancer (0% fees)
        let bestScore = 0;

        for (const [strategy, stats] of this.strategyStats.entries()) {
            // Calculate composite score: success rate * profitability
            const score = stats.successRate * stats.profitability;

            if (score > bestScore) {
                bestScore = score;
                bestStrategy = strategy;
            }
        }

        return bestStrategy;
    }

    /**
     * Check if any strategy is currently profitable
     */
    async isAnyStrategyProfitable(txHash: string): Promise<boolean> {
        const opportunities = await this.scanForOpportunities(txHash);
        return opportunities.length > 0 && opportunities[0].netProfit > BigInt(0);
    }

    /**
     * Get current market conditions summary
     */
    async getMarketConditions(txHash: string): Promise<{
        totalOpportunities: number;
        bestProfit: string;
        bestStrategy: string;
        averageConfidence: number;
        flashbotsAvailable: boolean;
    }> {
        const opportunities = await this.scanForOpportunities(txHash);

        const totalOpportunities = opportunities.length;
        const bestProfit = opportunities.length > 0 ? ethers.formatEther(opportunities[0].netProfit) : '0';
        const bestStrategy = opportunities.length > 0 ? opportunities[0].strategy : 'none';
        const averageConfidence = opportunities.length > 0
            ? opportunities.reduce((sum, op) => sum + op.confidence, 0) / opportunities.length
            : 0;
        const flashbotsAvailable = this.flashbotsManager.isAvailable();

        return {
            totalOpportunities,
            bestProfit,
            bestStrategy,
            averageConfidence,
            flashbotsAvailable
        };
    }

    /**
     * Get configured urgency level for gas pricing
     * Uses configuration parameters to determine optimal urgency
     */
    private getConfiguredUrgency(): 'slow' | 'standard' | 'fast' | 'instant' {
        // Use configured urgency from environment
        if (config.gasUrgency) {
            return config.gasUrgency;
        }

        // Fallback: Determine urgency based on other configuration parameters
        if (config.pureProfitMode) {
            return 'instant'; // Maximum speed for pure profit mode
        }

        if (config.maxGasPriceGwei >= 100) {
            return 'fast'; // High gas budget allows fast execution
        }

        if (config.maxGasPriceGwei >= 50) {
            return 'standard'; // Medium gas budget
        }

        return 'slow'; // Conservative gas budget
    }

    /**
     * Optimize route for maximum profit using advanced MEV parameters
     */
    private async optimizeRouteForProfit(route: FlashloanRoute): Promise<FlashloanRoute> {
        try {
            // Apply profit margin multiplier
            const requiredProfitMargin = config.profitMarginMultiplier || 1.2;
            const minProfitWithMargin = BigInt(Math.floor(Number(route.expectedProfit) * requiredProfitMargin));

            // Check if route meets minimum profit margin
            const profitMarginPercent = (Number(route.expectedProfit) / Number(route.flashloanAmount)) * 100;
            if (profitMarginPercent < config.minProfitMarginPercent) {
                logger.debug(`⚠️  Route profit margin ${profitMarginPercent.toFixed(2)}% below minimum ${config.minProfitMarginPercent}%`);
                // Could potentially skip this route or adjust parameters
            }

            const optimizedRoute: FlashloanRoute = {
                ...route,
                expectedProfit: minProfitWithMargin,
                confidence: Math.min(route.confidence * 1.1, 100), // Slight confidence boost for optimized routes
            };

            logger.debug(`💰 Profit optimization applied:`);
            logger.debug(`   Original profit: ${ethers.formatEther(route.expectedProfit)} ETH`);
            logger.debug(`   Required margin: ${requiredProfitMargin}x`);
            logger.debug(`   New minimum profit: ${ethers.formatEther(minProfitWithMargin)} ETH`);

            return optimizedRoute;
        } catch (error) {
            logger.debug('Error optimizing route for profit', { error: (error as Error).message });
            return route;
        }
    }

    /**
     * Market conditions summary from a pre-scanned list of opportunities
     */
    async getMarketConditionsFrom(opportunities: EnhancedFlashloanRoute[]): Promise<{
        totalOpportunities: number;
        bestProfit: string;
        bestStrategy: string;
        averageConfidence: number;
        flashbotsAvailable: boolean;
    }> {
        const totalOpportunities = opportunities.length;
        const bestProfit = opportunities.length > 0 ? ethers.formatEther(opportunities[0].netProfit) : '0';
        const bestStrategy = opportunities.length > 0 ? opportunities[0].strategy : 'none';
        const averageConfidence = opportunities.length > 0
            ? opportunities.reduce((sum, op) => sum + op.confidence, 0) / opportunities.length
            : 0;
        const flashbotsAvailable = this.flashbotsManager.isAvailable();

        return { totalOpportunities, bestProfit, bestStrategy, averageConfidence, flashbotsAvailable };
    }


    /**
     * Validate route meets advanced MEV profit requirements
     */
    private validateAdvancedProfitRequirements(route: FlashloanRoute): boolean {
        try {
            // Check minimum profit threshold
            const expectedProfitBigInt = typeof route.expectedProfit === 'bigint' ? route.expectedProfit : BigInt(route.expectedProfit);
            if (expectedProfitBigInt < BigInt(config.minProfitWei)) {
                return false;
            }

            // Check profit margin percentage
            const profitMarginPercent = (Number(expectedProfitBigInt) / Number(route.flashloanAmount)) * 100;
            if (profitMarginPercent < config.minProfitMarginPercent) {
                return false;
            }

            // Check profit vs gas cost ratio
            const estimatedGasCost = ethers.parseEther(config.maxGasCostEth.toString());
            const profitToGasRatio = Number(expectedProfitBigInt) / Number(estimatedGasCost);
            if (profitToGasRatio < 1.5) { // Require at least 1.5x profit vs gas cost
                return false;
            }

            return true;

        } catch (error) {
            logger.warn(`Profit validation failed: ${(error as Error).message}`);
            return false;
        }
    }
}
