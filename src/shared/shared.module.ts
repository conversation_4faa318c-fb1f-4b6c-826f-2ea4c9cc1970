import { Module, Global } from '@nestjs/common';
import { LoggerService } from './services/logger.service';
import { EventBusService } from './services/event-bus.service';
import { MetricsService } from './services/metrics.service';
import { UtilsService } from './services/utils.service';

@Global()
@Module({
  providers: [
    LoggerService,
    EventBusService,
    MetricsService,
    UtilsService,
  ],
  exports: [
    LoggerService,
    EventBusService,
    MetricsService,
    UtilsService,
  ],
})
export class SharedModule {}
