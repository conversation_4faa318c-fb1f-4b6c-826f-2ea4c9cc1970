export enum FlashloanProvider {
  AAVE = 'AAVE',
  BALANCER = 'BALANCER',
}

export enum DEXType {
  V2 = 'V2',
  V3 = 'V3',
  CURVE = 'CURVE',
  BALANCER_V2 = 'BALANCER_V2',
}

export interface TradeStep {
  dex: string;
  dexType: DEXType;
  tokenIn: string;
  tokenOut: string;
  slippageToleranceBps: number;
  v3Fee?: number;
  balancerPoolId?: string;
}

export interface ArbitrageParams {
  tradeSteps: TradeStep[];
  minProfit: string;
  provider: FlashloanProvider;
  maxGasCostWei: string;
}

export interface ArbitrageOpportunity {
  id: string;
  tokenA: string;
  tokenB: string;
  buyDex: string;
  sellDex: string;
  buyPrice: string;
  sellPrice: string;
  profitEstimate: string;
  gasEstimate: string;
  timestamp: number;
  confidence: number;
}

export interface BundleTransaction {
  to: string;
  data: string;
  value?: string;
  gasLimit?: string;
  gasPrice?: string;
  maxFeePerGas?: string;
  maxPriorityFeePerGas?: string;
}

export interface MEVBundle {
  transactions: BundleTransaction[];
  blockNumber: number;
  minTimestamp?: number;
  maxTimestamp?: number;
  revertingTxHashes?: string[];
}

export interface TokenInfo {
  address: string;
  symbol: string;
  name: string;
  decimals: number;
  chainId: number;
}

export interface PoolInfo {
  address: string;
  token0: string;
  token1: string;
  fee?: number;
  dexType: DEXType;
  reserve0?: string;
  reserve1?: string;
  liquidity?: string;
}

export interface PriceData {
  token: string;
  price: string;
  dex: string;
  timestamp: number;
  blockNumber: number;
}

export interface GasEstimate {
  gasLimit: string;
  gasPrice: string;
  maxFeePerGas: string;
  maxPriorityFeePerGas: string;
  totalCost: string;
}

export interface ExecutionResult {
  success: boolean;
  transactionHash?: string;
  blockNumber?: number;
  gasUsed?: string;
  profit?: string;
  error?: string;
  timestamp: number;
}

export interface BotStatus {
  isRunning: boolean;
  uptime: number;
  lastActivity: number;
  currentBlock: number;
  networkName: string;
  walletAddress: string;
  walletBalance: string;
}

export interface PerformanceMetrics {
  totalOpportunities: number;
  successfulTrades: number;
  failedTrades: number;
  totalProfit: string;
  totalLoss: string;
  averageProfit: string;
  successRate: number;
  averageExecutionTime: number;
}

export interface RiskMetrics {
  consecutiveFailures: number;
  dailyLoss: string;
  maxDrawdown: string;
  isExecutionPaused: boolean;
  lastFailureReason?: string;
}
