import { Injectable } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { LoggerService } from './logger.service';

export interface EventData {
  [key: string]: any;
}

@Injectable()
export class EventBusService {
  constructor(
    private readonly eventEmitter: EventEmitter2,
    private readonly logger: LoggerService,
  ) {}

  emit(event: string, data?: EventData): boolean {
    try {
      this.logger.debug(`Emitting event: ${event}`, 'EventBus', data);
      return this.eventEmitter.emit(event, data);
    } catch (error) {
      this.logger.error(`Failed to emit event: ${event}`, error, 'EventBus');
      return false;
    }
  }

  on(event: string, listener: (...args: any[]) => void): void {
    this.eventEmitter.on(event, listener);
    this.logger.debug(`Registered listener for event: ${event}`, 'EventBus');
  }

  once(event: string, listener: (...args: any[]) => void): void {
    this.eventEmitter.once(event, listener);
    this.logger.debug(`Registered one-time listener for event: ${event}`, 'EventBus');
  }

  off(event: string, listener: (...args: any[]) => void): void {
    this.eventEmitter.off(event, listener);
    this.logger.debug(`Removed listener for event: ${event}`, 'EventBus');
  }

  removeAllListeners(event?: string): void {
    this.eventEmitter.removeAllListeners(event);
    this.logger.debug(`Removed all listeners${event ? ` for event: ${event}` : ''}`, 'EventBus');
  }

  listenerCount(event: string): number {
    return this.eventEmitter.listenerCount(event);
  }

  // Convenience methods for common events
  emitArbitrageOpportunity(data: EventData): boolean {
    return this.emit('arbitrage.opportunity', data);
  }

  emitFlashloanExecuted(data: EventData): boolean {
    return this.emit('flashloan.executed', data);
  }

  emitBundleSubmitted(data: EventData): boolean {
    return this.emit('bundle.submitted', data);
  }

  emitBlockUpdate(data: EventData): boolean {
    return this.emit('block.update', data);
  }

  emitPriceUpdate(data: EventData): boolean {
    return this.emit('price.update', data);
  }

  emitError(data: EventData): boolean {
    return this.emit('error', data);
  }

  emitMetricsUpdate(data: EventData): boolean {
    return this.emit('metrics.update', data);
  }
}
