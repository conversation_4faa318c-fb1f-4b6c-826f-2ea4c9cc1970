import { Injectable } from '@nestjs/common';
import { ethers } from 'ethers';

@Injectable()
export class UtilsService {
  
  /**
   * Format ETH amount with proper decimals
   */
  formatEther(amount: string | ethers.BigNumberish): string {
    try {
      return ethers.formatEther(amount);
    } catch (error) {
      return '0';
    }
  }

  /**
   * Parse ETH amount to wei
   */
  parseEther(amount: string): string {
    try {
      return ethers.parseEther(amount).toString();
    } catch (error) {
      return '0';
    }
  }

  /**
   * Format token amount with custom decimals
   */
  formatUnits(amount: string | ethers.BigNumberish, decimals: number): string {
    try {
      return ethers.formatUnits(amount, decimals);
    } catch (error) {
      return '0';
    }
  }

  /**
   * Parse token amount with custom decimals
   */
  parseUnits(amount: string, decimals: number): string {
    try {
      return ethers.parseUnits(amount, decimals).toString();
    } catch (error) {
      return '0';
    }
  }

  /**
   * Check if address is valid
   */
  isValidAddress(address: string): boolean {
    try {
      return ethers.isAddress(address);
    } catch (error) {
      return false;
    }
  }

  /**
   * Get checksum address
   */
  getChecksumAddress(address: string): string {
    try {
      return ethers.getAddress(address);
    } catch (error) {
      return address;
    }
  }

  /**
   * Calculate percentage change
   */
  calculatePercentageChange(oldValue: number, newValue: number): number {
    if (oldValue === 0) return 0;
    return ((newValue - oldValue) / oldValue) * 100;
  }

  /**
   * Calculate profit percentage
   */
  calculateProfitPercentage(cost: string, revenue: string): number {
    const costNum = parseFloat(cost);
    const revenueNum = parseFloat(revenue);
    
    if (costNum === 0) return 0;
    return ((revenueNum - costNum) / costNum) * 100;
  }

  /**
   * Format uptime in human readable format
   */
  formatUptime(milliseconds: number): string {
    const seconds = Math.floor(milliseconds / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);

    if (days > 0) {
      return `${days}d ${hours % 24}h ${minutes % 60}m`;
    } else if (hours > 0) {
      return `${hours}h ${minutes % 60}m`;
    } else if (minutes > 0) {
      return `${minutes}m ${seconds % 60}s`;
    } else {
      return `${seconds}s`;
    }
  }

  /**
   * Format large numbers with K, M, B suffixes
   */
  formatLargeNumber(num: number): string {
    if (num >= 1e9) {
      return (num / 1e9).toFixed(2) + 'B';
    } else if (num >= 1e6) {
      return (num / 1e6).toFixed(2) + 'M';
    } else if (num >= 1e3) {
      return (num / 1e3).toFixed(2) + 'K';
    } else {
      return num.toFixed(2);
    }
  }

  /**
   * Sleep for specified milliseconds
   */
  sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Retry function with exponential backoff
   */
  async retry<T>(
    fn: () => Promise<T>,
    maxRetries: number = 3,
    baseDelay: number = 1000,
  ): Promise<T> {
    let lastError: Error;

    for (let i = 0; i <= maxRetries; i++) {
      try {
        return await fn();
      } catch (error) {
        lastError = error as Error;
        
        if (i === maxRetries) {
          throw lastError;
        }

        const delay = baseDelay * Math.pow(2, i);
        await this.sleep(delay);
      }
    }

    throw lastError!;
  }

  /**
   * Generate unique ID
   */
  generateId(): string {
    return Math.random().toString(36).substring(2) + Date.now().toString(36);
  }

  /**
   * Deep clone object
   */
  deepClone<T>(obj: T): T {
    return JSON.parse(JSON.stringify(obj));
  }

  /**
   * Check if object is empty
   */
  isEmpty(obj: any): boolean {
    if (obj == null) return true;
    if (Array.isArray(obj) || typeof obj === 'string') return obj.length === 0;
    if (typeof obj === 'object') return Object.keys(obj).length === 0;
    return false;
  }

  /**
   * Clamp number between min and max
   */
  clamp(num: number, min: number, max: number): number {
    return Math.min(Math.max(num, min), max);
  }

  /**
   * Convert basis points to percentage
   */
  bpsToPercentage(bps: number): number {
    return bps / 100;
  }

  /**
   * Convert percentage to basis points
   */
  percentageToBps(percentage: number): number {
    return percentage * 100;
  }

  /**
   * Calculate gas cost in ETH
   */
  calculateGasCostEth(gasUsed: string, gasPrice: string): string {
    try {
      const gasUsedBN = ethers.getBigInt(gasUsed);
      const gasPriceBN = ethers.getBigInt(gasPrice);
      const totalCost = gasUsedBN * gasPriceBN;
      return ethers.formatEther(totalCost);
    } catch (error) {
      return '0';
    }
  }

  /**
   * Get current timestamp in seconds
   */
  getCurrentTimestamp(): number {
    return Math.floor(Date.now() / 1000);
  }

  /**
   * Get current timestamp in milliseconds
   */
  getCurrentTimestampMs(): number {
    return Date.now();
  }
}
