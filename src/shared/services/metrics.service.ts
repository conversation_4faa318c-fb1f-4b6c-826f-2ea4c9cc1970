import { Injectable } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { PerformanceMetrics, RiskMetrics } from '@shared/types';

@Injectable()
export class MetricsService {
  private performanceMetrics: PerformanceMetrics = {
    totalOpportunities: 0,
    successfulTrades: 0,
    failedTrades: 0,
    totalProfit: '0',
    totalLoss: '0',
    averageProfit: '0',
    successRate: 0,
    averageExecutionTime: 0,
  };

  private riskMetrics: RiskMetrics = {
    consecutiveFailures: 0,
    dailyLoss: '0',
    maxDrawdown: '0',
    isExecutionPaused: false,
  };

  private executionTimes: number[] = [];
  private dailyProfits: { date: string; profit: number }[] = [];
  private readonly maxExecutionTimeHistory = 100;
  private readonly maxDailyProfitHistory = 30;

  constructor(private readonly eventEmitter: EventEmitter2) {}

  recordOpportunity(): void {
    this.performanceMetrics.totalOpportunities++;
    this.emitMetricsUpdate();
  }

  recordSuccessfulTrade(profit: string, executionTimeMs: number): void {
    this.performanceMetrics.successfulTrades++;
    this.riskMetrics.consecutiveFailures = 0;
    
    const profitNum = parseFloat(profit);
    const currentTotalProfit = parseFloat(this.performanceMetrics.totalProfit);
    this.performanceMetrics.totalProfit = (currentTotalProfit + profitNum).toString();
    
    this.recordExecutionTime(executionTimeMs);
    this.recordDailyProfit(profitNum);
    this.updateAverageProfit();
    this.updateSuccessRate();
    
    this.emitMetricsUpdate();
  }

  recordFailedTrade(loss: string, executionTimeMs: number, reason?: string): void {
    this.performanceMetrics.failedTrades++;
    this.riskMetrics.consecutiveFailures++;
    this.riskMetrics.lastFailureReason = reason;
    
    const lossNum = parseFloat(loss);
    const currentTotalLoss = parseFloat(this.performanceMetrics.totalLoss);
    this.performanceMetrics.totalLoss = (currentTotalLoss + lossNum).toString();
    
    const currentDailyLoss = parseFloat(this.riskMetrics.dailyLoss);
    this.riskMetrics.dailyLoss = (currentDailyLoss + lossNum).toString();
    
    this.recordExecutionTime(executionTimeMs);
    this.recordDailyProfit(-lossNum);
    this.updateAverageProfit();
    this.updateSuccessRate();
    this.updateMaxDrawdown();
    
    this.emitMetricsUpdate();
  }

  private recordExecutionTime(timeMs: number): void {
    this.executionTimes.push(timeMs);
    if (this.executionTimes.length > this.maxExecutionTimeHistory) {
      this.executionTimes.shift();
    }
    
    const sum = this.executionTimes.reduce((a, b) => a + b, 0);
    this.performanceMetrics.averageExecutionTime = sum / this.executionTimes.length;
  }

  private recordDailyProfit(profit: number): void {
    const today = new Date().toISOString().split('T')[0];
    const existingEntry = this.dailyProfits.find(entry => entry.date === today);
    
    if (existingEntry) {
      existingEntry.profit += profit;
    } else {
      this.dailyProfits.push({ date: today, profit });
      if (this.dailyProfits.length > this.maxDailyProfitHistory) {
        this.dailyProfits.shift();
      }
    }
  }

  private updateAverageProfit(): void {
    const totalTrades = this.performanceMetrics.successfulTrades + this.performanceMetrics.failedTrades;
    if (totalTrades > 0) {
      const totalProfit = parseFloat(this.performanceMetrics.totalProfit);
      const totalLoss = parseFloat(this.performanceMetrics.totalLoss);
      const netProfit = totalProfit - totalLoss;
      this.performanceMetrics.averageProfit = (netProfit / totalTrades).toString();
    }
  }

  private updateSuccessRate(): void {
    const totalTrades = this.performanceMetrics.successfulTrades + this.performanceMetrics.failedTrades;
    if (totalTrades > 0) {
      this.performanceMetrics.successRate = 
        (this.performanceMetrics.successfulTrades / totalTrades) * 100;
    }
  }

  private updateMaxDrawdown(): void {
    if (this.dailyProfits.length === 0) return;
    
    let maxDrawdown = 0;
    let peak = 0;
    let runningTotal = 0;
    
    for (const entry of this.dailyProfits) {
      runningTotal += entry.profit;
      if (runningTotal > peak) {
        peak = runningTotal;
      }
      const drawdown = peak - runningTotal;
      if (drawdown > maxDrawdown) {
        maxDrawdown = drawdown;
      }
    }
    
    this.riskMetrics.maxDrawdown = maxDrawdown.toString();
  }

  pauseExecution(reason?: string): void {
    this.riskMetrics.isExecutionPaused = true;
    this.riskMetrics.lastFailureReason = reason;
    this.emitMetricsUpdate();
  }

  resumeExecution(): void {
    this.riskMetrics.isExecutionPaused = false;
    this.riskMetrics.consecutiveFailures = 0;
    this.riskMetrics.lastFailureReason = undefined;
    this.emitMetricsUpdate();
  }

  resetDailyMetrics(): void {
    this.riskMetrics.dailyLoss = '0';
    const today = new Date().toISOString().split('T')[0];
    this.dailyProfits = this.dailyProfits.filter(entry => entry.date === today);
    this.emitMetricsUpdate();
  }

  getPerformanceMetrics(): PerformanceMetrics {
    return { ...this.performanceMetrics };
  }

  getRiskMetrics(): RiskMetrics {
    return { ...this.riskMetrics };
  }

  getDailyProfits(): { date: string; profit: number }[] {
    return [...this.dailyProfits];
  }

  getExecutionTimes(): number[] {
    return [...this.executionTimes];
  }

  private emitMetricsUpdate(): void {
    this.eventEmitter.emit('metrics.updated', {
      performance: this.getPerformanceMetrics(),
      risk: this.getRiskMetrics(),
    });
  }

  // Reset all metrics (useful for testing or fresh starts)
  reset(): void {
    this.performanceMetrics = {
      totalOpportunities: 0,
      successfulTrades: 0,
      failedTrades: 0,
      totalProfit: '0',
      totalLoss: '0',
      averageProfit: '0',
      successRate: 0,
      averageExecutionTime: 0,
    };

    this.riskMetrics = {
      consecutiveFailures: 0,
      dailyLoss: '0',
      maxDrawdown: '0',
      isExecutionPaused: false,
    };

    this.executionTimes = [];
    this.dailyProfits = [];
    this.emitMetricsUpdate();
  }
}
