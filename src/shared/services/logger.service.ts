import { Injectable, Logger } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';

export enum LogLevel {
  ERROR = 'error',
  WARN = 'warn',
  INFO = 'info',
  DEBUG = 'debug',
  VERBOSE = 'verbose',
}

export interface LogEntry {
  level: LogLevel;
  message: string;
  context?: string;
  timestamp: number;
  data?: any;
}

@Injectable()
export class LoggerService {
  private readonly logger = new Logger(LoggerService.name);
  private readonly logHistory: LogEntry[] = [];
  private readonly maxHistorySize = 1000;

  constructor(private readonly eventEmitter: EventEmitter2) {}

  error(message: string, error?: any, context?: string): void {
    this.log(LogLevel.ERROR, message, context, error);
    this.logger.error(message, error?.stack || error, context);
  }

  warn(message: string, context?: string, data?: any): void {
    this.log(LogLevel.WARN, message, context, data);
    this.logger.warn(message, context);
  }

  info(message: string, context?: string, data?: any): void {
    this.log(LogLevel.INFO, message, context, data);
    this.logger.log(message, context);
  }

  debug(message: string, context?: string, data?: any): void {
    this.log(LogLevel.DEBUG, message, context, data);
    this.logger.debug(message, context);
  }

  verbose(message: string, context?: string, data?: any): void {
    this.log(LogLevel.VERBOSE, message, context, data);
    this.logger.verbose(message, context);
  }

  system(message: string, data?: any): void {
    this.info(`🤖 ${message}`, 'SYSTEM', data);
  }

  arbitrage(message: string, data?: any): void {
    this.info(`💰 ${message}`, 'ARBITRAGE', data);
  }

  flashloan(message: string, data?: any): void {
    this.info(`⚡ ${message}`, 'FLASHLOAN', data);
  }

  bundle(message: string, data?: any): void {
    this.info(`📦 ${message}`, 'BUNDLE', data);
  }

  monitoring(message: string, data?: any): void {
    this.info(`👁️ ${message}`, 'MONITORING', data);
  }

  private log(level: LogLevel, message: string, context?: string, data?: any): void {
    const entry: LogEntry = {
      level,
      message,
      context,
      timestamp: Date.now(),
      data,
    };

    this.addToHistory(entry);
    this.eventEmitter.emit('log.entry', entry);
  }

  private addToHistory(entry: LogEntry): void {
    this.logHistory.push(entry);
    if (this.logHistory.length > this.maxHistorySize) {
      this.logHistory.shift();
    }
  }

  getRecentLogs(count: number = 50): LogEntry[] {
    return this.logHistory.slice(-count);
  }

  getLogsByLevel(level: LogLevel, count: number = 50): LogEntry[] {
    return this.logHistory
      .filter(entry => entry.level === level)
      .slice(-count);
  }

  clear(): void {
    this.logHistory.length = 0;
    this.eventEmitter.emit('log.cleared');
  }
}
