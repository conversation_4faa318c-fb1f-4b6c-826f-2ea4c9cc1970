import { Injectable, OnModuleInit, OnModuleDestroy } from '@nestjs/common';
import { ethers } from 'ethers';
import { LoggerService } from '@shared/services/logger.service';
import { BlockchainConfigService } from '@config/blockchain-config.service';
import { EventEmitter2 } from '@nestjs/event-emitter';

export interface ProviderHealth {
  url: string;
  isHealthy: boolean;
  latency: number;
  blockNumber: number;
  lastChecked: number;
  errorCount: number;
}

@Injectable()
export class ProviderService implements OnModuleInit, OnModuleDestroy {
  private providers: Map<string, ethers.JsonRpcProvider> = new Map();
  private providerHealth: Map<string, ProviderHealth> = new Map();
  private currentProvider: ethers.JsonRpcProvider | null = null;
  private healthCheckInterval: NodeJS.Timeout | null = null;
  private readonly healthCheckIntervalMs = 30000; // 30 seconds
  private readonly maxErrorCount = 5;

  constructor(
    private readonly logger: LoggerService,
    private readonly blockchainConfig: BlockchainConfigService,
    private readonly eventEmitter: EventEmitter2,
  ) {}

  async onModuleInit() {
    await this.initializeProviders();
    this.startHealthChecks();
  }

  onModuleDestroy() {
    this.stopHealthChecks();
    this.disconnectAll();
  }

  private async initializeProviders() {
    const rpcUrls = this.blockchainConfig.rpcUrls;
    
    if (rpcUrls.length === 0) {
      throw new Error('No RPC URLs configured');
    }

    this.logger.info(`Initializing ${rpcUrls.length} RPC providers`);

    for (const url of rpcUrls) {
      try {
        const provider = new ethers.JsonRpcProvider(url);
        this.providers.set(url, provider);
        
        // Initialize health status
        this.providerHealth.set(url, {
          url,
          isHealthy: false,
          latency: 0,
          blockNumber: 0,
          lastChecked: 0,
          errorCount: 0,
        });

        this.logger.debug(`Provider initialized: ${url}`);
      } catch (error) {
        this.logger.error(`Failed to initialize provider: ${url}`, error);
      }
    }

    // Set initial current provider
    await this.selectBestProvider();
  }

  private startHealthChecks() {
    this.healthCheckInterval = setInterval(async () => {
      await this.checkAllProvidersHealth();
      await this.selectBestProvider();
    }, this.healthCheckIntervalMs);

    this.logger.info('Provider health checks started');
  }

  private stopHealthChecks() {
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
      this.healthCheckInterval = null;
    }
  }

  private async checkAllProvidersHealth() {
    const healthChecks = Array.from(this.providers.entries()).map(
      ([url, provider]) => this.checkProviderHealth(url, provider)
    );

    await Promise.allSettled(healthChecks);
  }

  private async checkProviderHealth(url: string, provider: ethers.JsonRpcProvider) {
    const health = this.providerHealth.get(url)!;
    const startTime = Date.now();

    try {
      const blockNumber = await provider.getBlockNumber();
      const latency = Date.now() - startTime;

      health.isHealthy = true;
      health.latency = latency;
      health.blockNumber = blockNumber;
      health.lastChecked = Date.now();
      health.errorCount = 0;

      this.logger.debug(`Provider health check passed: ${url}`, 'Provider', {
        latency,
        blockNumber,
      });

    } catch (error) {
      health.isHealthy = false;
      health.latency = Date.now() - startTime;
      health.lastChecked = Date.now();
      health.errorCount++;

      this.logger.warn(`Provider health check failed: ${url}`, 'Provider', {
        error: error.message,
        errorCount: health.errorCount,
      });

      // If provider has too many errors, consider it unhealthy
      if (health.errorCount >= this.maxErrorCount) {
        this.logger.error(`Provider marked as unhealthy: ${url}`);
        this.eventEmitter.emit('provider.unhealthy', { url, health });
      }
    }
  }

  private async selectBestProvider() {
    const healthyProviders = Array.from(this.providerHealth.values())
      .filter(health => health.isHealthy && health.errorCount < this.maxErrorCount)
      .sort((a, b) => {
        // Sort by block number (descending) then by latency (ascending)
        if (a.blockNumber !== b.blockNumber) {
          return b.blockNumber - a.blockNumber;
        }
        return a.latency - b.latency;
      });

    if (healthyProviders.length === 0) {
      this.logger.error('No healthy providers available');
      this.currentProvider = null;
      this.eventEmitter.emit('provider.all_unhealthy');
      return;
    }

    const bestProvider = healthyProviders[0];
    const provider = this.providers.get(bestProvider.url)!;

    if (this.currentProvider !== provider) {
      this.currentProvider = provider;
      this.logger.info(`Switched to provider: ${bestProvider.url}`, 'Provider', {
        latency: bestProvider.latency,
        blockNumber: bestProvider.blockNumber,
      });
      this.eventEmitter.emit('provider.switched', { url: bestProvider.url, health: bestProvider });
    }
  }

  getCurrentProvider(): ethers.JsonRpcProvider | null {
    return this.currentProvider;
  }

  getProvider(url?: string): ethers.JsonRpcProvider | null {
    if (url) {
      return this.providers.get(url) || null;
    }
    return this.getCurrentProvider();
  }

  getAllProviders(): ethers.JsonRpcProvider[] {
    return Array.from(this.providers.values());
  }

  getProviderHealth(): ProviderHealth[] {
    return Array.from(this.providerHealth.values());
  }

  getHealthyProviders(): ProviderHealth[] {
    return Array.from(this.providerHealth.values())
      .filter(health => health.isHealthy && health.errorCount < this.maxErrorCount);
  }

  async getCurrentBlockNumber(): Promise<number> {
    if (!this.currentProvider) {
      throw new Error('No provider available');
    }

    try {
      return await this.currentProvider.getBlockNumber();
    } catch (error) {
      this.logger.error('Failed to get current block number', error);
      throw error;
    }
  }

  async getGasPrice(): Promise<ethers.FeeData> {
    if (!this.currentProvider) {
      throw new Error('No provider available');
    }

    try {
      return await this.currentProvider.getFeeData();
    } catch (error) {
      this.logger.error('Failed to get gas price', error);
      throw error;
    }
  }

  async getBalance(address: string): Promise<string> {
    if (!this.currentProvider) {
      throw new Error('No provider available');
    }

    try {
      const balance = await this.currentProvider.getBalance(address);
      return balance.toString();
    } catch (error) {
      this.logger.error(`Failed to get balance for ${address}`, error);
      throw error;
    }
  }

  async getTransactionCount(address: string): Promise<number> {
    if (!this.currentProvider) {
      throw new Error('No provider available');
    }

    try {
      return await this.currentProvider.getTransactionCount(address);
    } catch (error) {
      this.logger.error(`Failed to get transaction count for ${address}`, error);
      throw error;
    }
  }

  private disconnectAll() {
    for (const provider of this.providers.values()) {
      try {
        provider.destroy();
      } catch (error) {
        this.logger.warn('Error disconnecting provider', error);
      }
    }
    this.providers.clear();
    this.providerHealth.clear();
    this.currentProvider = null;
  }

  isHealthy(): boolean {
    return this.currentProvider !== null && this.getHealthyProviders().length > 0;
  }

  getNetworkInfo() {
    return {
      chainId: this.blockchainConfig.chainId,
      providersCount: this.providers.size,
      healthyProvidersCount: this.getHealthyProviders().length,
      currentProvider: this.currentProvider ? 
        Array.from(this.providers.entries()).find(([, provider]) => provider === this.currentProvider)?.[0] : 
        null,
    };
  }
}
