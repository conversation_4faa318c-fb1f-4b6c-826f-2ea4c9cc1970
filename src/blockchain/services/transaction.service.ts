import { Injectable } from '@nestjs/common';
import { ethers } from 'ethers';
import { LoggerService } from '@shared/services/logger.service';
import { WalletService } from './wallet.service';
import { GasService } from './gas.service';
import { BlockchainMonitorService } from './blockchain-monitor.service';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { ExecutionResult } from '@shared/types';

export interface TransactionOptions {
  gasLimit?: string;
  gasPrice?: string;
  maxFeePerGas?: string;
  maxPriorityFeePerGas?: string;
  value?: string;
  nonce?: number;
  priority?: 'slow' | 'standard' | 'fast' | 'urgent';
  waitForConfirmation?: boolean;
  confirmations?: number;
  timeout?: number;
}

@Injectable()
export class TransactionService {
  private pendingTransactions = new Map<string, {
    hash: string;
    timestamp: number;
    description: string;
  }>();

  constructor(
    private readonly logger: LoggerService,
    private readonly walletService: WalletService,
    private readonly gasService: GasService,
    private readonly blockchainMonitor: BlockchainMonitorService,
    private readonly eventEmitter: EventEmitter2,
  ) {}

  async sendTransaction(
    to: string,
    data: string,
    options: TransactionOptions = {},
    description: string = 'Transaction'
  ): Promise<ExecutionResult> {
    const startTime = Date.now();

    try {
      this.logger.info(`Preparing ${description}`, 'Transaction', { to });

      const wallet = this.walletService.getWallet();
      
      // Build transaction request
      const transaction: ethers.TransactionRequest = {
        to,
        data,
        value: options.value || '0',
      };

      // Set nonce if not provided
      if (options.nonce !== undefined) {
        transaction.nonce = options.nonce;
      } else {
        transaction.nonce = await wallet.getNonce();
      }

      // Handle gas settings
      if (options.gasLimit) {
        transaction.gasLimit = options.gasLimit;
      } else {
        transaction.gasLimit = await this.gasService.estimateGas(transaction);
      }

      // Handle gas pricing
      if (options.gasPrice) {
        // Legacy transaction
        transaction.gasPrice = options.gasPrice;
      } else if (options.maxFeePerGas && options.maxPriorityFeePerGas) {
        // EIP-1559 transaction
        transaction.maxFeePerGas = options.maxFeePerGas;
        transaction.maxPriorityFeePerGas = options.maxPriorityFeePerGas;
      } else {
        // Use optimal gas strategy
        const priority = options.priority || 'standard';
        const gasStrategy = await this.gasService.getOptimalGasStrategy(priority);
        
        if (gasStrategy.gasPrice) {
          transaction.gasPrice = gasStrategy.gasPrice;
        } else {
          transaction.maxFeePerGas = gasStrategy.maxFeePerGas;
          transaction.maxPriorityFeePerGas = gasStrategy.maxPriorityFeePerGas;
        }
      }

      // Send transaction
      const tx = await wallet.sendTransaction(transaction);
      
      this.logger.info(`${description} sent: ${tx.hash}`);
      
      // Track pending transaction
      this.pendingTransactions.set(tx.hash, {
        hash: tx.hash,
        timestamp: Date.now(),
        description,
      });

      this.eventEmitter.emit('transaction.sent', {
        hash: tx.hash,
        to,
        description,
        gasLimit: transaction.gasLimit?.toString(),
        gasPrice: transaction.gasPrice?.toString(),
        maxFeePerGas: transaction.maxFeePerGas?.toString(),
      });

      // Wait for confirmation if requested
      let receipt: ethers.TransactionReceipt | null = null;
      if (options.waitForConfirmation !== false) {
        const confirmations = options.confirmations || 1;
        const timeout = options.timeout || 60000;
        
        receipt = await this.blockchainMonitor.waitForTransaction(
          tx.hash, 
          confirmations, 
          timeout
        );
      }

      const executionTime = Date.now() - startTime;
      
      if (receipt) {
        this.pendingTransactions.delete(tx.hash);
        
        const success = receipt.status === 1;
        const gasUsed = receipt.gasUsed.toString();
        
        if (success) {
          this.logger.info(`${description} confirmed: ${tx.hash}`, 'Transaction', {
            gasUsed,
            blockNumber: receipt.blockNumber,
            executionTime,
          });
        } else {
          this.logger.error(`${description} failed: ${tx.hash}`, undefined, 'Transaction');
        }

        this.eventEmitter.emit('transaction.confirmed', {
          hash: tx.hash,
          success,
          gasUsed,
          blockNumber: receipt.blockNumber,
          description,
          executionTime,
        });

        return {
          success,
          transactionHash: tx.hash,
          blockNumber: receipt.blockNumber,
          gasUsed,
          timestamp: Date.now(),
        };
      } else {
        // Transaction sent but not confirmed
        return {
          success: true,
          transactionHash: tx.hash,
          blockNumber: 0,
          gasUsed: '0',
          timestamp: Date.now(),
        };
      }

    } catch (error) {
      const executionTime = Date.now() - startTime;
      
      this.logger.error(`${description} failed`, error, 'Transaction');
      
      this.eventEmitter.emit('transaction.failed', {
        description,
        error: error.message,
        executionTime,
      });

      return {
        success: false,
        transactionHash: '',
        blockNumber: 0,
        gasUsed: '0',
        error: error.message,
        timestamp: Date.now(),
      };
    }
  }

  async sendContractTransaction(
    contract: ethers.Contract,
    methodName: string,
    args: any[],
    options: TransactionOptions = {},
    description?: string
  ): Promise<ExecutionResult> {
    try {
      const contractDescription = description || `${contract.target}.${methodName}()`;
      
      // Encode the function call
      const data = contract.interface.encodeFunctionData(methodName, args);
      
      return await this.sendTransaction(
        contract.target as string,
        data,
        options,
        contractDescription
      );

    } catch (error) {
      this.logger.error(`Failed to send contract transaction`, error);
      return {
        success: false,
        transactionHash: '',
        blockNumber: 0,
        gasUsed: '0',
        error: error.message,
        timestamp: Date.now(),
      };
    }
  }

  async estimateTransactionCost(
    to: string,
    data: string,
    value: string = '0',
    priority: 'slow' | 'standard' | 'fast' | 'urgent' = 'standard'
  ): Promise<{
    gasLimit: string;
    gasPrice?: string;
    maxFeePerGas?: string;
    maxPriorityFeePerGas?: string;
    totalCostEth: string;
  }> {
    try {
      const transaction: ethers.TransactionRequest = { to, data, value };
      const gasEstimate = await this.gasService.getGasEstimateForTransaction(transaction, priority);
      
      const totalCostEth = ethers.formatEther(gasEstimate.totalCost);
      
      return {
        gasLimit: gasEstimate.gasLimit,
        gasPrice: gasEstimate.gasPrice !== '0' ? gasEstimate.gasPrice : undefined,
        maxFeePerGas: gasEstimate.maxFeePerGas !== '0' ? gasEstimate.maxFeePerGas : undefined,
        maxPriorityFeePerGas: gasEstimate.maxPriorityFeePerGas !== '0' ? gasEstimate.maxPriorityFeePerGas : undefined,
        totalCostEth,
      };

    } catch (error) {
      this.logger.error('Failed to estimate transaction cost', error);
      throw error;
    }
  }

  getPendingTransactions(): Array<{
    hash: string;
    timestamp: number;
    description: string;
    age: number;
  }> {
    const now = Date.now();
    return Array.from(this.pendingTransactions.values()).map(tx => ({
      ...tx,
      age: now - tx.timestamp,
    }));
  }

  async checkPendingTransactions(): Promise<void> {
    const pending = Array.from(this.pendingTransactions.keys());
    
    for (const hash of pending) {
      try {
        const receipt = await this.blockchainMonitor.getTransactionReceipt(hash);
        if (receipt) {
          const tx = this.pendingTransactions.get(hash)!;
          this.pendingTransactions.delete(hash);
          
          const success = receipt.status === 1;
          this.logger.info(`Pending transaction resolved: ${hash} (${success ? 'success' : 'failed'})`);
          
          this.eventEmitter.emit('transaction.resolved', {
            hash,
            success,
            gasUsed: receipt.gasUsed.toString(),
            blockNumber: receipt.blockNumber,
            description: tx.description,
          });
        }
      } catch (error) {
        this.logger.warn(`Error checking pending transaction ${hash}`, error);
      }
    }
  }

  clearOldPendingTransactions(maxAgeMs: number = 300000): void { // 5 minutes default
    const cutoff = Date.now() - maxAgeMs;
    
    for (const [hash, tx] of this.pendingTransactions.entries()) {
      if (tx.timestamp < cutoff) {
        this.pendingTransactions.delete(hash);
        this.logger.warn(`Removed old pending transaction: ${hash}`);
      }
    }
  }

  getTransactionStatus() {
    return {
      pendingCount: this.pendingTransactions.size,
      oldestPending: this.pendingTransactions.size > 0 ? 
        Math.min(...Array.from(this.pendingTransactions.values()).map(tx => tx.timestamp)) : 
        null,
    };
  }
}
