import { Injectable } from '@nestjs/common';
import { ethers } from 'ethers';
import { LoggerService } from '@shared/services/logger.service';
import { BlockchainConfigService } from '@config/blockchain-config.service';
import { ProviderService } from './provider.service';
import { GasEstimate } from '@shared/types';

export interface GasStrategy {
  name: string;
  gasPrice?: string;
  maxFeePerGas?: string;
  maxPriorityFeePerGas?: string;
  gasLimit?: string;
}

@Injectable()
export class GasService {
  private gasHistory: { timestamp: number; gasPrice: string; baseFee?: string }[] = [];
  private readonly maxHistorySize = 100;

  constructor(
    private readonly logger: LoggerService,
    private readonly blockchainConfig: BlockchainConfigService,
    private readonly providerService: ProviderService,
  ) {}

  async getCurrentGasPrice(): Promise<ethers.FeeData> {
    const provider = this.providerService.getCurrentProvider();
    if (!provider) {
      throw new Error('No provider available');
    }

    try {
      const feeData = await provider.getFeeData();
      
      // Store in history
      this.addToHistory({
        timestamp: Date.now(),
        gasPrice: feeData.gasPrice?.toString() || '0',
        baseFee: feeData.maxFeePerGas?.toString(),
      });

      return feeData;
    } catch (error) {
      this.logger.error('Failed to get current gas price', error);
      throw error;
    }
  }

  async estimateGas(transaction: ethers.TransactionRequest): Promise<string> {
    const provider = this.providerService.getCurrentProvider();
    if (!provider) {
      throw new Error('No provider available');
    }

    try {
      const gasEstimate = await provider.estimateGas(transaction);
      const multiplier = this.blockchainConfig.gasLimitMultiplier;
      const adjustedGas = Math.floor(Number(gasEstimate) * multiplier);
      
      this.logger.debug(`Gas estimate: ${gasEstimate} -> ${adjustedGas} (${multiplier}x multiplier)`);
      
      return adjustedGas.toString();
    } catch (error) {
      this.logger.error('Failed to estimate gas', error);
      throw error;
    }
  }

  async getOptimalGasStrategy(priority: 'slow' | 'standard' | 'fast' | 'urgent' = 'standard'): Promise<GasStrategy> {
    try {
      const feeData = await this.getCurrentGasPrice();
      const maxGasPrice = ethers.parseUnits(this.blockchainConfig.maxGasPrice, 'gwei');
      const maxPriorityFee = ethers.parseUnits(this.blockchainConfig.maxPriorityFeePerGas, 'gwei');

      // EIP-1559 transaction (Type 2)
      if (feeData.maxFeePerGas && feeData.maxPriorityFeePerGas) {
        const baseFee = feeData.maxFeePerGas - feeData.maxPriorityFeePerGas;
        
        let priorityFeeMultiplier: number;
        let maxFeeMultiplier: number;

        switch (priority) {
          case 'slow':
            priorityFeeMultiplier = 0.8;
            maxFeeMultiplier = 1.1;
            break;
          case 'standard':
            priorityFeeMultiplier = 1.0;
            maxFeeMultiplier = 1.25;
            break;
          case 'fast':
            priorityFeeMultiplier = 1.5;
            maxFeeMultiplier = 1.5;
            break;
          case 'urgent':
            priorityFeeMultiplier = 2.0;
            maxFeeMultiplier = 2.0;
            break;
        }

        const priorityFee = feeData.maxPriorityFeePerGas * BigInt(Math.floor(priorityFeeMultiplier * 100)) / 100n;
        const maxFee = baseFee + priorityFee * BigInt(Math.floor(maxFeeMultiplier * 100)) / 100n;

        // Apply limits
        const finalPriorityFee = priorityFee > maxPriorityFee ? maxPriorityFee : priorityFee;
        const finalMaxFee = maxFee > maxGasPrice ? maxGasPrice : maxFee;

        return {
          name: `EIP-1559 ${priority}`,
          maxFeePerGas: finalMaxFee.toString(),
          maxPriorityFeePerGas: finalPriorityFee.toString(),
        };
      }

      // Legacy transaction (Type 0)
      if (feeData.gasPrice) {
        let multiplier: number;
        
        switch (priority) {
          case 'slow':
            multiplier = 0.9;
            break;
          case 'standard':
            multiplier = 1.1;
            break;
          case 'fast':
            multiplier = 1.3;
            break;
          case 'urgent':
            multiplier = 1.5;
            break;
        }

        const gasPrice = feeData.gasPrice * BigInt(Math.floor(multiplier * 100)) / 100n;
        const finalGasPrice = gasPrice > maxGasPrice ? maxGasPrice : gasPrice;

        return {
          name: `Legacy ${priority}`,
          gasPrice: finalGasPrice.toString(),
        };
      }

      throw new Error('Unable to determine gas strategy');

    } catch (error) {
      this.logger.error('Failed to get optimal gas strategy', error);
      throw error;
    }
  }

  async calculateGasCost(gasLimit: string, strategy: GasStrategy): Promise<GasEstimate> {
    try {
      const gasLimitBN = ethers.getBigInt(gasLimit);
      let totalCost: bigint;
      let gasPrice: string;
      let maxFeePerGas: string;
      let maxPriorityFeePerGas: string;

      if (strategy.maxFeePerGas && strategy.maxPriorityFeePerGas) {
        // EIP-1559
        const maxFee = ethers.getBigInt(strategy.maxFeePerGas);
        const priorityFee = ethers.getBigInt(strategy.maxPriorityFeePerGas);
        
        totalCost = gasLimitBN * maxFee;
        gasPrice = '0';
        maxFeePerGas = strategy.maxFeePerGas;
        maxPriorityFeePerGas = strategy.maxPriorityFeePerGas;
      } else if (strategy.gasPrice) {
        // Legacy
        const gasPriceBN = ethers.getBigInt(strategy.gasPrice);
        totalCost = gasLimitBN * gasPriceBN;
        gasPrice = strategy.gasPrice;
        maxFeePerGas = '0';
        maxPriorityFeePerGas = '0';
      } else {
        throw new Error('Invalid gas strategy');
      }

      return {
        gasLimit,
        gasPrice,
        maxFeePerGas,
        maxPriorityFeePerGas,
        totalCost: totalCost.toString(),
      };

    } catch (error) {
      this.logger.error('Failed to calculate gas cost', error);
      throw error;
    }
  }

  async getGasEstimateForTransaction(
    transaction: ethers.TransactionRequest,
    priority: 'slow' | 'standard' | 'fast' | 'urgent' = 'standard'
  ): Promise<GasEstimate> {
    try {
      const gasLimit = await this.estimateGas(transaction);
      const strategy = await this.getOptimalGasStrategy(priority);
      
      return await this.calculateGasCost(gasLimit, strategy);
    } catch (error) {
      this.logger.error('Failed to get gas estimate for transaction', error);
      throw error;
    }
  }

  async isGasPriceAcceptable(gasPrice: string): Promise<boolean> {
    try {
      const maxGasPrice = ethers.parseUnits(this.blockchainConfig.maxGasPrice, 'gwei');
      const currentGasPrice = ethers.getBigInt(gasPrice);
      
      return currentGasPrice <= maxGasPrice;
    } catch (error) {
      this.logger.error('Failed to check if gas price is acceptable', error);
      return false;
    }
  }

  getGasHistory(): { timestamp: number; gasPrice: string; baseFee?: string }[] {
    return [...this.gasHistory];
  }

  getAverageGasPrice(periodMs: number = 300000): string { // 5 minutes default
    const cutoff = Date.now() - periodMs;
    const recentHistory = this.gasHistory.filter(entry => entry.timestamp > cutoff);
    
    if (recentHistory.length === 0) {
      return '0';
    }

    const sum = recentHistory.reduce((acc, entry) => {
      return acc + BigInt(entry.gasPrice);
    }, 0n);

    const average = sum / BigInt(recentHistory.length);
    return average.toString();
  }

  private addToHistory(entry: { timestamp: number; gasPrice: string; baseFee?: string }) {
    this.gasHistory.push(entry);
    if (this.gasHistory.length > this.maxHistorySize) {
      this.gasHistory.shift();
    }
  }

  async waitForLowerGasPrice(targetGasPrice: string, timeoutMs: number = 300000): Promise<boolean> {
    const startTime = Date.now();
    const targetPrice = ethers.getBigInt(targetGasPrice);

    while (Date.now() - startTime < timeoutMs) {
      try {
        const feeData = await this.getCurrentGasPrice();
        const currentPrice = feeData.gasPrice || feeData.maxFeePerGas;
        
        if (currentPrice && currentPrice <= targetPrice) {
          this.logger.info(`Gas price reached target: ${ethers.formatUnits(currentPrice, 'gwei')} gwei`);
          return true;
        }

        // Wait 30 seconds before checking again
        await new Promise(resolve => setTimeout(resolve, 30000));
      } catch (error) {
        this.logger.warn('Error checking gas price while waiting', error);
      }
    }

    this.logger.warn(`Timeout waiting for gas price to reach ${ethers.formatUnits(targetPrice, 'gwei')} gwei`);
    return false;
  }
}
