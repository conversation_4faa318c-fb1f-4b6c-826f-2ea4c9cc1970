import { Injectable, OnModuleInit, OnModuleDestroy } from '@nestjs/common';
import { ethers } from 'ethers';
import { LoggerService } from '@shared/services/logger.service';
import { ProviderService } from './provider.service';
import { EventEmitter2 } from '@nestjs/event-emitter';

export interface BlockInfo {
  number: number;
  hash: string;
  timestamp: number;
  gasUsed: string;
  gasLimit: string;
  baseFeePerGas?: string;
  transactionCount: number;
}

@Injectable()
export class BlockchainMonitorService implements OnModuleInit, OnModuleDestroy {
  private isMonitoring = false;
  private currentBlock = 0;
  private blockSubscription: any = null;

  constructor(
    private readonly logger: LoggerService,
    private readonly providerService: ProviderService,
    private readonly eventEmitter: EventEmitter2,
  ) {}

  async onModuleInit() {
    // Wait a bit for provider to be ready
    setTimeout(() => {
      this.startMonitoring();
    }, 2000);
  }

  onModuleDestroy() {
    this.stopMonitoring();
  }

  async startMonitoring() {
    if (this.isMonitoring) {
      return;
    }

    const provider = this.providerService.getCurrentProvider();
    if (!provider) {
      this.logger.warn('No provider available for blockchain monitoring');
      return;
    }

    try {
      this.isMonitoring = true;
      
      // Get current block
      this.currentBlock = await provider.getBlockNumber();
      this.logger.info(`Starting blockchain monitoring from block ${this.currentBlock}`);

      // Subscribe to new blocks
      this.blockSubscription = provider.on('block', async (blockNumber: number) => {
        await this.handleNewBlock(blockNumber);
      });

      this.eventEmitter.emit('blockchain.monitoring_started', { 
        currentBlock: this.currentBlock 
      });

    } catch (error) {
      this.logger.error('Failed to start blockchain monitoring', error);
      this.isMonitoring = false;
    }
  }

  stopMonitoring() {
    if (!this.isMonitoring) {
      return;
    }

    this.isMonitoring = false;

    if (this.blockSubscription) {
      const provider = this.providerService.getCurrentProvider();
      if (provider) {
        provider.off('block', this.blockSubscription);
      }
      this.blockSubscription = null;
    }

    this.logger.info('Blockchain monitoring stopped');
    this.eventEmitter.emit('blockchain.monitoring_stopped');
  }

  private async handleNewBlock(blockNumber: number) {
    try {
      if (blockNumber <= this.currentBlock) {
        return; // Skip if we've already processed this block
      }

      this.currentBlock = blockNumber;
      
      const provider = this.providerService.getCurrentProvider();
      if (!provider) {
        return;
      }

      // Get block details
      const block = await provider.getBlock(blockNumber, false);
      if (!block) {
        this.logger.warn(`Failed to get block ${blockNumber}`);
        return;
      }

      const blockInfo: BlockInfo = {
        number: block.number,
        hash: block.hash,
        timestamp: block.timestamp,
        gasUsed: block.gasUsed.toString(),
        gasLimit: block.gasLimit.toString(),
        baseFeePerGas: block.baseFeePerGas?.toString(),
        transactionCount: block.transactions.length,
      };

      this.logger.monitoring(`New block: ${blockNumber} (${block.transactions.length} txs)`);

      // Emit events
      this.eventEmitter.emit('blockchain.new_block', blockInfo);
      this.eventEmitter.emit('block.update', blockInfo);

      // Check for significant gas price changes
      if (block.baseFeePerGas) {
        this.eventEmitter.emit('blockchain.gas_update', {
          blockNumber,
          baseFeePerGas: block.baseFeePerGas.toString(),
        });
      }

    } catch (error) {
      this.logger.error(`Error handling new block ${blockNumber}`, error);
    }
  }

  async getCurrentBlock(): Promise<number> {
    if (this.currentBlock > 0) {
      return this.currentBlock;
    }

    const provider = this.providerService.getCurrentProvider();
    if (!provider) {
      throw new Error('No provider available');
    }

    try {
      this.currentBlock = await provider.getBlockNumber();
      return this.currentBlock;
    } catch (error) {
      this.logger.error('Failed to get current block', error);
      throw error;
    }
  }

  async getBlock(blockNumber: number): Promise<ethers.Block | null> {
    const provider = this.providerService.getCurrentProvider();
    if (!provider) {
      throw new Error('No provider available');
    }

    try {
      return await provider.getBlock(blockNumber, true);
    } catch (error) {
      this.logger.error(`Failed to get block ${blockNumber}`, error);
      return null;
    }
  }

  async getTransaction(txHash: string): Promise<ethers.TransactionResponse | null> {
    const provider = this.providerService.getCurrentProvider();
    if (!provider) {
      throw new Error('No provider available');
    }

    try {
      return await provider.getTransaction(txHash);
    } catch (error) {
      this.logger.error(`Failed to get transaction ${txHash}`, error);
      return null;
    }
  }

  async getTransactionReceipt(txHash: string): Promise<ethers.TransactionReceipt | null> {
    const provider = this.providerService.getCurrentProvider();
    if (!provider) {
      throw new Error('No provider available');
    }

    try {
      return await provider.getTransactionReceipt(txHash);
    } catch (error) {
      this.logger.error(`Failed to get transaction receipt ${txHash}`, error);
      return null;
    }
  }

  async waitForTransaction(
    txHash: string, 
    confirmations: number = 1, 
    timeout: number = 60000
  ): Promise<ethers.TransactionReceipt | null> {
    const provider = this.providerService.getCurrentProvider();
    if (!provider) {
      throw new Error('No provider available');
    }

    try {
      this.logger.info(`Waiting for transaction ${txHash} (${confirmations} confirmations)`);
      return await provider.waitForTransaction(txHash, confirmations, timeout);
    } catch (error) {
      this.logger.error(`Failed to wait for transaction ${txHash}`, error);
      return null;
    }
  }

  isMonitoringActive(): boolean {
    return this.isMonitoring;
  }

  getMonitoringStatus() {
    return {
      isMonitoring: this.isMonitoring,
      currentBlock: this.currentBlock,
      hasProvider: this.providerService.getCurrentProvider() !== null,
    };
  }

  // Listen for provider switches and restart monitoring
  async handleProviderSwitch() {
    if (this.isMonitoring) {
      this.stopMonitoring();
      // Wait a bit for the new provider to be ready
      setTimeout(() => {
        this.startMonitoring();
      }, 1000);
    }
  }
}
