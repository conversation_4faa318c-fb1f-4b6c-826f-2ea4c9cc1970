import { Injectable, OnModuleInit } from '@nestjs/common';
import { ethers } from 'ethers';
import { LoggerService } from '@shared/services/logger.service';
import { BlockchainConfigService } from '@config/blockchain-config.service';
import { ProviderService } from './provider.service';
import { EventEmitter2 } from '@nestjs/event-emitter';

@Injectable()
export class WalletService implements OnModuleInit {
  private wallet: ethers.Wallet | null = null;
  private address: string = '';

  constructor(
    private readonly logger: LoggerService,
    private readonly blockchainConfig: BlockchainConfigService,
    private readonly providerService: ProviderService,
    private readonly eventEmitter: EventEmitter2,
  ) {}

  async onModuleInit() {
    await this.initializeWallet();
    this.setupProviderSwitchListener();
  }

  private async initializeWallet() {
    try {
      const privateKey = this.blockchainConfig.privateKey;
      const provider = this.providerService.getCurrentProvider();

      if (!provider) {
        throw new Error('No provider available for wallet initialization');
      }

      this.wallet = new ethers.Wallet(privateKey, provider);
      this.address = this.wallet.address;

      this.logger.info(`Wallet initialized: ${this.address}`);
      
      // Check initial balance
      await this.checkBalance();

    } catch (error) {
      this.logger.error('Failed to initialize wallet', error);
      throw error;
    }
  }

  private setupProviderSwitchListener() {
    this.eventEmitter.on('provider.switched', async (data) => {
      if (this.wallet) {
        const newProvider = this.providerService.getCurrentProvider();
        if (newProvider) {
          this.wallet = this.wallet.connect(newProvider);
          this.logger.info(`Wallet reconnected to new provider: ${data.url}`);
        }
      }
    });
  }

  getWallet(): ethers.Wallet {
    if (!this.wallet) {
      throw new Error('Wallet not initialized');
    }
    return this.wallet;
  }

  getAddress(): string {
    return this.address;
  }

  async getBalance(): Promise<string> {
    if (!this.wallet) {
      throw new Error('Wallet not initialized');
    }

    try {
      const balance = await this.wallet.provider!.getBalance(this.address);
      return balance.toString();
    } catch (error) {
      this.logger.error('Failed to get wallet balance', error);
      throw error;
    }
  }

  async getBalanceEth(): Promise<string> {
    const balance = await this.getBalance();
    return ethers.formatEther(balance);
  }

  async checkBalance(): Promise<void> {
    try {
      const balanceEth = await this.getBalanceEth();
      this.logger.info(`Wallet balance: ${balanceEth} ETH`);
      
      const balanceNum = parseFloat(balanceEth);
      if (balanceNum < 0.01) {
        this.logger.warn('Low wallet balance detected');
        this.eventEmitter.emit('wallet.low_balance', { 
          address: this.address, 
          balance: balanceEth 
        });
      }

      this.eventEmitter.emit('wallet.balance_updated', { 
        address: this.address, 
        balance: balanceEth 
      });

    } catch (error) {
      this.logger.error('Failed to check wallet balance', error);
    }
  }

  async getNonce(): Promise<number> {
    if (!this.wallet) {
      throw new Error('Wallet not initialized');
    }

    try {
      return await this.wallet.getNonce();
    } catch (error) {
      this.logger.error('Failed to get wallet nonce', error);
      throw error;
    }
  }

  async signTransaction(transaction: ethers.TransactionRequest): Promise<string> {
    if (!this.wallet) {
      throw new Error('Wallet not initialized');
    }

    try {
      return await this.wallet.signTransaction(transaction);
    } catch (error) {
      this.logger.error('Failed to sign transaction', error);
      throw error;
    }
  }

  async sendTransaction(transaction: ethers.TransactionRequest): Promise<ethers.TransactionResponse> {
    if (!this.wallet) {
      throw new Error('Wallet not initialized');
    }

    try {
      this.logger.info('Sending transaction', 'Wallet', {
        to: transaction.to,
        value: transaction.value ? ethers.formatEther(transaction.value) : '0',
        gasLimit: transaction.gasLimit?.toString(),
        gasPrice: transaction.gasPrice?.toString(),
      });

      const tx = await this.wallet.sendTransaction(transaction);
      
      this.logger.info(`Transaction sent: ${tx.hash}`);
      this.eventEmitter.emit('wallet.transaction_sent', {
        hash: tx.hash,
        to: transaction.to,
        value: transaction.value?.toString() || '0',
      });

      return tx;

    } catch (error) {
      this.logger.error('Failed to send transaction', error);
      this.eventEmitter.emit('wallet.transaction_failed', {
        error: error.message,
        transaction,
      });
      throw error;
    }
  }

  async estimateGas(transaction: ethers.TransactionRequest): Promise<string> {
    if (!this.wallet) {
      throw new Error('Wallet not initialized');
    }

    try {
      const gasEstimate = await this.wallet.estimateGas(transaction);
      return gasEstimate.toString();
    } catch (error) {
      this.logger.error('Failed to estimate gas', error);
      throw error;
    }
  }

  async getTokenBalance(tokenAddress: string): Promise<string> {
    if (!this.wallet) {
      throw new Error('Wallet not initialized');
    }

    try {
      const tokenContract = new ethers.Contract(
        tokenAddress,
        [
          'function balanceOf(address owner) view returns (uint256)',
          'function decimals() view returns (uint8)',
        ],
        this.wallet.provider
      );

      const balance = await tokenContract.balanceOf(this.address);
      return balance.toString();

    } catch (error) {
      this.logger.error(`Failed to get token balance for ${tokenAddress}`, error);
      throw error;
    }
  }

  async getTokenBalanceFormatted(tokenAddress: string): Promise<string> {
    if (!this.wallet) {
      throw new Error('Wallet not initialized');
    }

    try {
      const tokenContract = new ethers.Contract(
        tokenAddress,
        [
          'function balanceOf(address owner) view returns (uint256)',
          'function decimals() view returns (uint8)',
        ],
        this.wallet.provider
      );

      const [balance, decimals] = await Promise.all([
        tokenContract.balanceOf(this.address),
        tokenContract.decimals(),
      ]);

      return ethers.formatUnits(balance, decimals);

    } catch (error) {
      this.logger.error(`Failed to get formatted token balance for ${tokenAddress}`, error);
      throw error;
    }
  }

  async approveToken(
    tokenAddress: string, 
    spenderAddress: string, 
    amount: string
  ): Promise<ethers.TransactionResponse> {
    if (!this.wallet) {
      throw new Error('Wallet not initialized');
    }

    try {
      const tokenContract = new ethers.Contract(
        tokenAddress,
        ['function approve(address spender, uint256 amount) returns (bool)'],
        this.wallet
      );

      this.logger.info(`Approving token: ${tokenAddress} for ${spenderAddress}`);
      const tx = await tokenContract.approve(spenderAddress, amount);
      
      this.logger.info(`Token approval transaction sent: ${tx.hash}`);
      return tx;

    } catch (error) {
      this.logger.error('Failed to approve token', error);
      throw error;
    }
  }

  async getTokenAllowance(
    tokenAddress: string, 
    spenderAddress: string
  ): Promise<string> {
    if (!this.wallet) {
      throw new Error('Wallet not initialized');
    }

    try {
      const tokenContract = new ethers.Contract(
        tokenAddress,
        ['function allowance(address owner, address spender) view returns (uint256)'],
        this.wallet.provider
      );

      const allowance = await tokenContract.allowance(this.address, spenderAddress);
      return allowance.toString();

    } catch (error) {
      this.logger.error('Failed to get token allowance', error);
      throw error;
    }
  }

  isInitialized(): boolean {
    return this.wallet !== null;
  }

  getWalletInfo() {
    return {
      address: this.address,
      isInitialized: this.isInitialized(),
      hasProvider: this.wallet?.provider !== null,
    };
  }
}
