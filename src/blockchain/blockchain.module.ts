import { Module } from '@nestjs/common';
import { ConfigurationModule } from '@config/configuration.module';
import { ProviderService } from './services/provider.service';
import { WalletService } from './services/wallet.service';
import { GasService } from './services/gas.service';
import { BlockchainMonitorService } from './services/blockchain-monitor.service';
import { TransactionService } from './services/transaction.service';

@Module({
  imports: [ConfigurationModule],
  providers: [
    ProviderService,
    WalletService,
    GasService,
    BlockchainMonitorService,
    TransactionService,
  ],
  exports: [
    ProviderService,
    WalletService,
    GasService,
    BlockchainMonitorService,
    TransactionService,
  ],
})
export class BlockchainModule {}
