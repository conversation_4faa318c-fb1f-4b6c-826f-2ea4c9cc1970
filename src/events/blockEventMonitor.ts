import { ethers, WebSocketProvider } from 'ethers';
import { EventEmitter } from 'events';
import { config } from '../config';
import { logger } from '../utils/logger';

import { statusDashboard } from '../utils/statusDashboard';

export interface BlockData {
  number: number;
  hash: string;
  timestamp: number;
  gasUsed: bigint;
  gasLimit: bigint;
  baseFeePerGas?: bigint;
  transactions: string[];
  miner: string;
}

export interface ReorgData {
  oldBlock: BlockData;
  newBlock: BlockData;
  depth: number;
}

/**
 * Real-time block event monitor using websockets
 * Replaces interval-based block tracking with event-driven approach
 */
export class BlockEventMonitor extends EventEmitter {
  private wsProvider: WebSocketProvider | null = null;
  private provider: ethers.JsonRpcProvider;
  private isRunning: boolean = false;
  private lastBlockNumber: number = 0;
  private blockHistory: Map<number, BlockData> = new Map();
  private readonly MAX_BLOCK_HISTORY = 100;

  constructor() {
    super();
    this.provider = new ethers.JsonRpcProvider(config.rpcUrl);
  }

  async initialize(): Promise<void> {
    try {
      // Setup WebSocket provider for real-time block monitoring
      if (config.mempoolWebsocketUrl) {
        this.wsProvider = new WebSocketProvider(config.mempoolWebsocketUrl);
        logger.system('Block event monitor WebSocket provider initialized');
      } else {
        logger.warn('No WebSocket URL configured for block monitoring');
      }

      // Get current block number
      this.lastBlockNumber = await this.provider.getBlockNumber();
      logger.system(`Block monitor initialized at block ${this.lastBlockNumber}`);

    } catch (error) {
      logger.logError(error as Error, 'BlockEventMonitor.initialize');
      throw error;
    }
  }

  async start(): Promise<void> {
    if (this.isRunning) {
      logger.warn('Block event monitor is already running');
      return;
    }

    this.isRunning = true;
    logger.system('Starting real-time block monitoring...');

    try {
      if (this.wsProvider) {
        // Listen for new blocks via websocket
        this.wsProvider.on('block', async (blockNumber: number) => {
          await this.handleNewBlockNumber(blockNumber);
        });

        logger.system('WebSocket block monitoring started');
      } else {
        // Fallback to polling if no websocket available
        logger.warn('No WebSocket available, using polling fallback');
        this.startPollingFallback();
      }

      this.emit('started');
      logger.system('Block event monitoring started');

    } catch (error) {
      this.isRunning = false;
      logger.logError(error as Error, 'BlockEventMonitor.start');
      throw error;
    }
  }

  async stop(): Promise<void> {
    if (!this.isRunning) {
      logger.warn('Block event monitor is not running');
      return;
    }

    this.isRunning = false;
    logger.system('Stopping block event monitoring...');

    try {
      if (this.wsProvider) {
        this.wsProvider.removeAllListeners('block');
        // Don't destroy the provider as it might be shared
      }

      this.blockHistory.clear();
      this.emit('stopped');
      logger.system('Block event monitoring stopped');

    } catch (error) {
      logger.logError(error as Error, 'BlockEventMonitor.stop');
    }
  }

  private async handleNewBlockNumber(blockNumber: number): Promise<void> {
    try {
      // Check for reorg
      if (blockNumber <= this.lastBlockNumber) {
        await this.handlePotentialReorg(blockNumber);
        return;
      }

      // Get full block data
      const block = await this.provider.getBlock(blockNumber, false);
      if (!block) {
        logger.debug('Block not found', { blockNumber });
        return;
      }

      const blockData: BlockData = {
        number: block.number,
        hash: block.hash || `0x${'0'.repeat(64)}`, // Handle null hash
        timestamp: block.timestamp,
        gasUsed: block.gasUsed,
        gasLimit: block.gasLimit,
        baseFeePerGas: block.baseFeePerGas || undefined,
        transactions: block.transactions as string[],
        miner: block.miner
      };

      // Store in history
      this.addToHistory(blockData);

      // Update status dashboard
      statusDashboard.updateBlockInfo(blockData.number, blockData.gasUsed, blockData.baseFeePerGas);

      // Emit new block event
      this.emit('newBlock', blockData);

      this.lastBlockNumber = blockNumber;

      logger.debug('New block processed', {
        number: blockNumber,
        hash: (block.hash || '0x0000000000000000').slice(0, 10),
        txCount: block.transactions.length,
        gasUsed: ethers.formatUnits(block.gasUsed, 'gwei')
      });

    } catch (error) {
      logger.debug('Error processing new block', {
        blockNumber,
        error: (error as Error).message
      });
    }
  }

  private async handlePotentialReorg(blockNumber: number): Promise<void> {
    try {
      const currentBlock = await this.provider.getBlock(blockNumber, false);
      const storedBlock = this.blockHistory.get(blockNumber);

      if (currentBlock && storedBlock && currentBlock.hash !== storedBlock.hash) {
        // Reorg detected!
        const reorgData: ReorgData = {
          oldBlock: storedBlock,
          newBlock: {
            number: currentBlock.number,
            hash: currentBlock.hash || `0x${'0'.repeat(64)}`, // Handle null hash
            timestamp: currentBlock.timestamp,
            gasUsed: currentBlock.gasUsed,
            gasLimit: currentBlock.gasLimit,
            baseFeePerGas: currentBlock.baseFeePerGas || undefined,
            transactions: currentBlock.transactions as string[],
            miner: currentBlock.miner
          },
          depth: this.lastBlockNumber - blockNumber
        };

        logger.warn(`Block reorg detected at block ${blockNumber}, depth: ${reorgData.depth}`);
        this.emit('blockReorg', reorgData);

        // Update history with new block
        this.addToHistory(reorgData.newBlock);
      }

    } catch (error) {
      logger.debug('Error handling potential reorg', {
        blockNumber,
        error: (error as Error).message
      });
    }
  }

  private addToHistory(blockData: BlockData): void {
    this.blockHistory.set(blockData.number, blockData);

    // Cleanup old blocks
    if (this.blockHistory.size > this.MAX_BLOCK_HISTORY) {
      const oldestBlock = Math.min(...this.blockHistory.keys());
      this.blockHistory.delete(oldestBlock);
    }
  }

  private startPollingFallback(): void {
    const pollInterval = 1000; // 1 second polling

    const poll = async () => {
      if (!this.isRunning) return;

      try {
        const currentBlock = await this.provider.getBlockNumber();
        if (currentBlock > this.lastBlockNumber) {
          await this.handleNewBlockNumber(currentBlock);
        }
      } catch (error) {
        logger.debug('Polling error', { error: (error as Error).message });
      }

      setTimeout(poll, pollInterval);
    };

    poll();
    logger.info('Block polling fallback started');
  }

  getStatus(): { isRunning: boolean; lastBlock: number; historySize: number } {
    return {
      isRunning: this.isRunning,
      lastBlock: this.lastBlockNumber,
      historySize: this.blockHistory.size
    };
  }

  getBlockHistory(): BlockData[] {
    return Array.from(this.blockHistory.values()).sort((a, b) => b.number - a.number);
  }
}
