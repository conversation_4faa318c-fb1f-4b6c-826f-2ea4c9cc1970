import {MEVBot} from './core/bot';
import {logger} from './utils/logger';

async function main() {
  // Initialize dashboard early if enabled
  if (process.env.WEB_DASHBOARD === 'true') {
    const { webDashboard } = await import('./server/webDashboard');

    // Start web dashboard immediately to capture all logs
    webDashboard.start();

    // Give it a moment to initialize
    await new Promise(resolve => setTimeout(resolve, 100));
  }

  logger.system('🤖 Initializing Advanced MEV Bot...');

  try {
    // Use the full MEV bot implementation
    const bot = new MEVBot();

    // Handle graceful shutdown with improved signal handling
    let isShuttingDown = false;

    const gracefulShutdown = async (signal: string) => {
      if (isShuttingDown) {
        logger.warn(`Already shutting down, ignoring ${signal}`);
        return;
      }

      isShuttingDown = true;
      logger.info(`Received ${signal}, shutting down gracefully...`);

      try {
        await Promise.race([
          bot.stop(),
          new Promise((_, reject) =>
            setTimeout(() => reject(new Error('Shutdown timeout')), 10000)
          )
        ]);
        logger.info('✅ Graceful shutdown completed');
        process.exit(0);
      } catch (error) {
        logger.error('❌ Shutdown error, forcing exit:', error);
        process.exit(1);
      }
    };

    process.on('SIGINT', () => gracefulShutdown('SIGINT'));
    process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));

    // Handle uncaught exceptions
    process.on('uncaughtException', (error) => {
      logger.logError(error, 'UncaughtException');
      if (!isShuttingDown) {
        bot.emergencyStop();
        process.exit(1);
      }
    });

    process.on('unhandledRejection', (reason, promise) => {
      logger.error('Unhandled Rejection at:', { promise, reason });
      if (!isShuttingDown) {
        bot.emergencyStop();
        process.exit(1);
      }
    });

    // Start the bot
    await bot.start();

    // Status is now handled by the live dashboard - no need for periodic logs

    logger.system('🚀 Advanced MEV Bot is now running');

  } catch (error) {
    logger.logError(error as Error, 'main');

    // Fallback to simple bot if advanced bot fails
    logger.warn('⚠️  Advanced MEV Bot failed');
  }
}

// Start the application
main().catch((error) => {
  logger.logError(error, 'main');
  process.exit(1);
});
