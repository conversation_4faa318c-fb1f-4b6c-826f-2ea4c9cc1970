import { ethers, WebSocketProvider } from 'ethers';
import { FlashbotsBundleProvider } from '@flashbots/ethers-provider-bundle';
import { config } from '../config';
import { RPCManager } from '../providers/rpcManager';
import { logger } from '../utils/logger';

import { statusDashboard } from '../utils/statusDashboard';
import { Transaction, MempoolFilter } from '../types';
import { EventEmitter } from 'events';

export class MempoolMonitor extends EventEmitter {
  private provider: ethers.JsonRpcProvider;
  private wsProvider: WebSocketProvider | null = null;
  private flashbotsProvider: FlashbotsBundleProvider | null = null;
  private isRunning: boolean = false;
  private filters: MempoolFilter[] = [];

  constructor() {
    super();
    this.provider = new ethers.JsonRpcProvider(config.rpcUrl);
    this.setupProviders();
  }

  private async setupProviders(): Promise<void> {
    try {
      // Setup WebSocket provider for real-time mempool monitoring
      if (config.enableEthersMempool && config.mempoolWebsocketUrl) {
        this.wsProvider = new WebSocketProvider(config.mempoolWebsocketUrl);
        logger.system('WebSocket provider initialized');
      }

      // Setup Flashbots provider
      if (config.enableFlashbotsMempool) {
        const authSigner = new ethers.Wallet(config.flashbotsSignerKey);
        this.flashbotsProvider = await FlashbotsBundleProvider.create(
          this.provider,
          authSigner,
          config.flashbotsRpcUrl
        );
        logger.info('Flashbots provider initialized');
      }
    } catch (error) {
      logger.logError(error as Error, 'MempoolMonitor.setupProviders');
    }
  }

  addFilter(filter: MempoolFilter): void {
    this.filters.push(filter);
    logger.system('Mempool filter added');
  }

  removeFilter(index: number): void {
    if (index >= 0 && index < this.filters.length) {
      const removed = this.filters.splice(index, 1);
      logger.system('Mempool filter removed');
    }
  }

  private shouldProcessTransaction(tx: Transaction): boolean {
    if (this.filters.length === 0) return true;

    return this.filters.some(filter => {
      // Check excluded addresses first (hard exclusion)
      if (filter.excludeAddresses.length > 0) {
        const isExcluded = filter.excludeAddresses.some(addr =>
          tx.from.toLowerCase() === addr.toLowerCase() ||
          tx.to?.toLowerCase() === addr.toLowerCase()
        );
        if (isExcluded) return false;
      }

      // Check maximum gas price (hard limit)
      if (tx.gasPrice && BigInt(tx.gasPrice.toString()) > BigInt(filter.maxGasPrice.toString())) return false;

      // More permissive logic: transaction passes if it meets ANY of these criteria:
      // 1. Meets minimum value requirement
      const meetsValueRequirement = BigInt(tx.value.toString()) >= BigInt(filter.minValue.toString());

      // 2. Involves target tokens
      const hasTargetToken = filter.targetTokens.length === 0 || filter.targetTokens.some(token =>
        tx.data.toLowerCase().includes(token.toLowerCase().slice(2))
      );

      // 3. Involves target pools
      const hasTargetPool = filter.targetPools.length === 0 || filter.targetPools.some(pool =>
        tx.to?.toLowerCase() === pool.toLowerCase()
      );

      // Transaction passes if it meets value requirement OR involves target tokens OR target pools
      return meetsValueRequirement || hasTargetToken || hasTargetPool;
    });
  }

  private isRelevantTransaction(tx: Transaction): boolean {
    // Check if transaction is relevant for MEV opportunities
    if (!tx.to || !tx.data || tx.data === '0x') {
      return false;
    }

    // Check for DEX function selectors
    const dexSelectors = [
      '0x7ff36ab5', // swapExactETHForTokens
      '0x18cbafe5', // swapExactTokensForETH
      '0x38ed1739', // swapExactTokensForTokens
      '0x8803dbee', // swapTokensForExactTokens
      '0x414bf389', // swapExactETHForTokensSupportingFeeOnTransferTokens
      '0xb6f9de95', // swapExactTokensForETHSupportingFeeOnTransferTokens
      '0x128acb08', // swapTokensForExactETH
      '0xfb3bdb41', // swapETHForExactTokens
      '0xc04b8d59', // exactInputSingle (Uniswap V3)
      '0x414bf389', // exactInput (Uniswap V3)
      '0xdb3e2198', // exactOutputSingle (Uniswap V3)
      '0x09b81346'  // exactOutput (Uniswap V3)
    ];

    const functionSelector = tx.data.slice(0, 10);
    const isRelevant = dexSelectors.includes(functionSelector);

    // Also check for high-value transactions
    const highValue = BigInt(tx.value.toString()) > ethers.parseEther('1'); // > 1 ETH

    return isRelevant || highValue;
  }

  private async handlePendingTransaction(txHash: string): Promise<void> {
    try {
      const tx = await this.provider.getTransaction(txHash);
      if (!tx) return;

      const transaction: Transaction = {
        hash: tx.hash,
        from: tx.from,
        to: tx.to || '',
        value: tx.value,
        gasPrice: tx.gasPrice || ethers.parseUnits('0', 'gwei'),
        gasLimit: tx.gasLimit,
        data: tx.data,
        nonce: tx.nonce,
        maxFeePerGas: tx.maxFeePerGas || undefined,
        maxPriorityFeePerGas: tx.maxPriorityFeePerGas || undefined
      };

      if (this.shouldProcessTransaction(transaction)) {
        // Update dashboard instead of spammy logs
        const isRelevant = this.isRelevantTransaction(transaction);
        statusDashboard.recordTransaction(isRelevant, BigInt(transaction.gasPrice.toString()));

        this.emit('pendingTransaction', transaction);

        // Only log relevant transactions at debug level
        if (isRelevant) {
          logger.debug('Relevant transaction detected', {
            hash: txHash,
            from: tx.from,
            to: tx.to,
            value: ethers.formatEther(tx.value)
          });
        }
      } else {
        // Still count non-relevant transactions for stats
        statusDashboard.recordTransaction(false, BigInt(transaction.gasPrice.toString()));
      }
    } catch (error) {
      logger.debug('Error processing pending transaction', { txHash, error: (error as Error).message });
    }
  }

  async start(): Promise<void> {
    if (this.isRunning) {
      logger.warn('Mempool monitor is already running');
      return;
    }

    this.isRunning = true;
    logger.system('Starting mempool monitor...');

    try {
      // Start WebSocket monitoring
      if (this.wsProvider && config.enableEthersMempool) {
        this.wsProvider.on('pending', (txHash: string) => {
          this.handlePendingTransaction(txHash);
        });
        logger.system('WebSocket mempool monitoring started');
      }

      // Start Flashbots mempool monitoring (if available)
      if (this.flashbotsProvider && config.enableFlashbotsMempool) {
        // Note: Flashbots doesn't provide direct mempool access
        // This would need to be implemented based on their specific API
        logger.system('Flashbots mempool monitoring started');
      }

      this.emit('started');
      logger.system('Mempool monitor started successfully');
    } catch (error) {
      this.isRunning = false;
      logger.logError(error as Error, 'MempoolMonitor.start');
      throw error;
    }
  }

  async stop(): Promise<void> {
    if (!this.isRunning) {
      logger.warn('Mempool monitor is not running');
      return;
    }

    this.isRunning = false;
    logger.info('Stopping mempool monitor...');

    try {
      if (this.wsProvider) {
        this.wsProvider.removeAllListeners('pending');
        await this.wsProvider.destroy();
      }

      this.emit('stopped');
      logger.info('Mempool monitor stopped successfully');
    } catch (error) {
      logger.logError(error as Error, 'MempoolMonitor.stop');
    }
  }

  getStatus(): { isRunning: boolean; filtersCount: number } {
    return {
      isRunning: this.isRunning,
      filtersCount: this.filters.length
    };
  }
}
