#!/usr/bin/env node

import { ethers } from 'ethers';
import { Logger } from './utils/logger';
import { config } from './config';
import { FlashloanAttackLauncher } from './attacks/flashloan/FlashloanAttackLauncher';

const logger = new Logger();

/**
 * Main Flashloan Attack Application
 */
class FlashloanAttackApp {
  private provider: ethers.Provider;
  private wallet: ethers.Wallet;
  private attackLauncher: FlashloanAttackLauncher;

  constructor() {
    // Initialize provider
    this.provider = new ethers.JsonRpcProvider(config.rpcUrl);

    // Initialize wallet
    if (!config.privateKey) {
      throw new Error('PRIVATE_KEY not configured in .env file');
    }
    this.wallet = new ethers.Wallet(config.privateKey, this.provider);
    
    // Initialize attack launcher
    this.attackLauncher = new FlashloanAttackLauncher(this.provider, this.wallet);
  }

  /**
   * Start the application
   */
  async start(): Promise<void> {
    try {
      logger.info('🚀 FLASHLOAN ATTACK APPLICATION STARTING');
      logger.info('═'.repeat(80));
      logger.info('🎯 PRODUCTION-READY FLASHLOAN ATTACK SYSTEM');
      logger.info('   ✅ Light Contract (Off-chain architecture)');
      logger.info('   ✅ Multi-DEX arbitrage support');
      logger.info('   ✅ Comprehensive validation & monitoring');
      logger.info('   ✅ Flashbots MEV protection');
      logger.info('   ✅ Real-time opportunity scanning');
      logger.info('═'.repeat(80));

      // Display environment info
      await this.displayEnvironmentInfo();

      // Initialize the attack system
      await this.attackLauncher.initialize();

      // Display available vectors
      this.attackLauncher.displayAvailableVectors();

      // Handle command line arguments
      await this.handleCommandLineArgs();

    } catch (error) {
      logger.error('❌ Application startup failed:', error);
      process.exit(1);
    }
  }

  /**
   * Display environment information
   */
  private async displayEnvironmentInfo(): Promise<void> {
    try {
      logger.info('🌐 ENVIRONMENT INFORMATION:');
      
      // Network info
      const network = await this.provider.getNetwork();
      logger.info(`   Network: ${network.name} (Chain ID: ${network.chainId})`);
      logger.info(`   RPC URL: ${config.rpcUrl}`);
      
      // Wallet info
      const balance = await this.provider.getBalance(this.wallet.address);
      logger.info(`   Wallet: ${this.wallet.address}`);
      logger.info(`   Balance: ${ethers.formatEther(balance)} ETH`);
      
      // Contract info
      if (config.LIGHT_CONTRACT_ADDRESS) {
        logger.info(`   Light Contract: ${config.LIGHT_CONTRACT_ADDRESS}`);

        // Check if contract exists
        const code = await this.provider.getCode(config.LIGHT_CONTRACT_ADDRESS);
        if (code === '0x') {
          logger.warn('⚠️  Light contract not deployed at specified address');
        } else {
          logger.info(`   Contract Size: ${Math.round(code.length / 2)} bytes`);
        }
      }
      
      // Configuration
      logger.info(`   Dry Run: ${config.dryRun ? 'YES' : 'NO'}`);
      logger.info(`   Flashbots: ${config.useFlashbots ? 'YES' : 'NO'}`);
      logger.info(`   Min Profit: ${config.minProfitThreshold || '0.01'} ETH`);
      logger.info('');

    } catch (error) {
      logger.error('Failed to get environment info:', error);
    }
  }

  /**
   * Handle command line arguments
   */
  private async handleCommandLineArgs(): Promise<void> {
    const args = process.argv.slice(2);
    
    if (args.length === 0) {
      // No arguments - start automated attacks
      await this.startAutomatedMode();
      return;
    }

    const command = args[0].toLowerCase();

    switch (command) {
      case 'auto':
      case 'automated':
        await this.startAutomatedMode();
        break;
        
      case 'attack':
        if (args.length < 2) {
          logger.error('Usage: npm run flashloan-attack attack <vector-id> [amount]');
          process.exit(1);
        }
        await this.executeSpecificAttack(args[1], args[2]);
        break;
        
      case 'status':
        this.displayStatus();
        break;
        
      case 'vectors':
        this.attackLauncher.displayAvailableVectors();
        break;
        
      case 'report':
        this.attackLauncher.generateReport();
        break;
        
      case 'help':
        this.displayHelp();
        break;
        
      default:
        logger.error(`Unknown command: ${command}`);
        this.displayHelp();
        process.exit(1);
    }
  }

  /**
   * Start automated attack mode
   */
  private async startAutomatedMode(): Promise<void> {
    logger.info('🤖 Starting Automated Attack Mode');
    logger.info('   Press Ctrl+C to stop');
    logger.info('');

    // Handle graceful shutdown
    process.on('SIGINT', async () => {
      logger.info('\n🛑 Received shutdown signal');
      await this.attackLauncher.stopAutomatedAttacks();
      process.exit(0);
    });

    process.on('SIGTERM', async () => {
      logger.info('\n🛑 Received termination signal');
      await this.attackLauncher.stopAutomatedAttacks();
      process.exit(0);
    });

    // Start automated attacks
    await this.attackLauncher.startAutomatedAttacks();
  }

  /**
   * Execute specific attack
   */
  private async executeSpecificAttack(vectorId: string, amount?: string): Promise<void> {
    logger.info(`🎯 Executing specific attack: ${vectorId}`);
    
    try {
      await this.attackLauncher.executeSpecificAttack(vectorId, amount);
      logger.info('✅ Attack execution completed');
    } catch (error) {
      logger.error('❌ Attack execution failed:', error);
    }
    
    process.exit(0);
  }

  /**
   * Display current status
   */
  private displayStatus(): void {
    const status = this.attackLauncher.getStatus();
    
    logger.info('📊 CURRENT STATUS:');
    logger.info('═'.repeat(40));
    
    if (!status.initialized) {
      logger.info('❌ System not initialized');
      return;
    }
    
    logger.info(`Running: ${status.isRunning ? 'YES' : 'NO'}`);
    logger.info(`Active Attacks: ${status.activeAttacks}`);
    
    if (status.currentSession) {
      logger.info(`Session: ${status.currentSession.id}`);
      logger.info(`Total Attacks: ${status.currentSession.totalAttacks}`);
      logger.info(`Successful: ${status.currentSession.successfulAttacks}`);
      logger.info(`Total Profit: ${ethers.formatEther(status.currentSession.totalProfit)} ETH`);
    }
    
    const stats = status.statistics;
    logger.info(`Overall Success Rate: ${stats.successRate.toFixed(1)}%`);
    logger.info(`Net Profit: ${ethers.formatEther(stats.netProfit)} ETH`);
  }

  /**
   * Display help information
   */
  private displayHelp(): void {
    logger.info('🆘 FLASHLOAN ATTACK APPLICATION HELP');
    logger.info('═'.repeat(50));
    logger.info('');
    logger.info('USAGE:');
    logger.info('  npm run flashloan-attack [command] [options]');
    logger.info('');
    logger.info('COMMANDS:');
    logger.info('  auto, automated     Start automated attack scanning');
    logger.info('  attack <vector> [amount]  Execute specific attack vector');
    logger.info('  status              Show current system status');
    logger.info('  vectors             List available attack vectors');
    logger.info('  report              Generate performance report');
    logger.info('  help                Show this help message');
    logger.info('');
    logger.info('EXAMPLES:');
    logger.info('  npm run flashloan-attack auto');
    logger.info('  npm run flashloan-attack attack weth-usdc-v3-sushi');
    logger.info('  npm run flashloan-attack attack weth-usdc-v3-sushi 1.0');
    logger.info('  npm run flashloan-attack status');
    logger.info('');
    logger.info('AVAILABLE ATTACK VECTORS:');
    logger.info('  weth-usdc-v3-sushi     WETH/USDC: Uniswap V3 → SushiSwap');
    logger.info('  weth-usdc-sushi-v3     WETH/USDC: SushiSwap → Uniswap V3');
    logger.info('  usdc-dai-balancer-sushi USDC/DAI: Balancer V2 → SushiSwap');
    logger.info('  weth-dai-complex       Multi-DEX complex arbitrage');
    logger.info('');
    logger.info('CONFIGURATION:');
    logger.info('  Set LIGHT_CONTRACT_ADDRESS in .env file');
    logger.info('  Set ENABLE_LIGHT_CONTRACT=true in .env file');
    logger.info('  Set DRY_RUN=false for live trading');
    logger.info('  Set USE_FLASHBOTS=true for MEV protection');
  }
}

/**
 * Application entry point
 */
async function main() {
  try {
    const app = new FlashloanAttackApp();
    await app.start();
  } catch (error) {
    logger.error('❌ Application failed to start:', error);
    process.exit(1);
  }
}

// Start the application
if (require.main === module) {
  main().catch(error => {
    logger.error('❌ Unhandled error:', error);
    process.exit(1);
  });
}

export { FlashloanAttackApp };
