import { ethers } from 'ethers';
import { Logger } from '../../utils/logger';
import { config } from '../../config';
import { LightArbitrageService } from '../../services/LightArbitrageService';
import { FlashloanAttackOrchestrator, OrchestratorConfig } from './FlashloanAttackOrchestrator';

export class FlashloanAttackLauncher {
  private logger = new Logger();
  private provider: ethers.Provider;
  private wallet: ethers.Wallet;
  private lightArbitrageService: LightArbitrageService | null = null;
  private attackOrchestrator: FlashloanAttackOrchestrator | null = null;

  constructor(provider: ethers.Provider, wallet: ethers.Wallet) {
    this.provider = provider;
    this.wallet = wallet;
  }

  /**
   * Initialize the flashloan attack system
   */
  async initialize(): Promise<void> {
    try {
      this.logger.info('🚀 Initializing Flashloan Attack System');
      this.logger.info('═'.repeat(60));

      // Check if Light contract is configured
      if (!config.LIGHT_CONTRACT_ADDRESS) {
        throw new Error('Light contract address not configured. Set LIGHT_CONTRACT_ADDRESS in .env');
      }

      if (!config.enableLightContract) {
        throw new Error('Light contract not enabled. Set ENABLE_LIGHT_CONTRACT=true in .env');
      }

      this.logger.info(`📋 Light Contract: ${config.LIGHT_CONTRACT_ADDRESS}`);
      this.logger.info(`💰 Wallet: ${this.wallet.address}`);

      // Check wallet balance
      const balance = await this.provider.getBalance(this.wallet.address);
      this.logger.info(`💰 Balance: ${ethers.formatEther(balance)} ETH`);

      if (balance < ethers.parseEther('0.1')) {
        this.logger.warn('⚠️  Low ETH balance. Consider funding the wallet for gas costs.');
      }

      // Load Light contract ABI
      const lightContractABI = require('../../../artifacts/contracts/HybridFlashloanArbitrageLight.sol/HybridFlashloanArbitrageLite.json').abi;

      // Initialize Light Arbitrage Service
      this.lightArbitrageService = new LightArbitrageService(
        config.LIGHT_CONTRACT_ADDRESS,
        this.provider,
        this.wallet,
        lightContractABI
      );

      this.logger.info('✅ Light Arbitrage Service initialized');

      // Create orchestrator configuration
      const orchestratorConfig: OrchestratorConfig = {
        enabledVectors: this.getEnabledVectors(),
        minProfitETH: config.minProfitThreshold || '0.01',
        maxGasCostGwei: config.maxGasCostEth ? (parseFloat(config.maxGasCostEth) * 1e9).toString() : '50',
        defaultSlippage: config.slippageTolerance || 100,
        scanInterval: 5000, // 5 seconds
        dryRun: config.dryRun,
        useFlashbots: config.useFlashbots,
        maxConcurrentAttacks: 3
      };

      // Initialize Attack Orchestrator
      this.attackOrchestrator = new FlashloanAttackOrchestrator(
        this.lightArbitrageService,
        this.provider,
        this.wallet,
        orchestratorConfig
      );

      this.logger.info('✅ Flashloan Attack Orchestrator initialized');
      this.logger.info('═'.repeat(60));

      // Display configuration
      this.displayConfiguration(orchestratorConfig);

    } catch (error) {
      this.logger.error('❌ Failed to initialize Flashloan Attack System:', error);
      throw error;
    }
  }

  /**
   * Start automated flashloan attacks
   */
  async startAutomatedAttacks(): Promise<void> {
    if (!this.attackOrchestrator) {
      throw new Error('Attack orchestrator not initialized. Call initialize() first.');
    }

    this.logger.info('🎯 Starting Automated Flashloan Attacks');
    this.logger.info('═'.repeat(60));

    if (config.dryRun) {
      this.logger.info('🧪 DRY RUN MODE: No real transactions will be executed');
    } else {
      this.logger.info('⚡ LIVE MODE: Real transactions will be executed');
      this.logger.info('💰 Ensure sufficient ETH balance for gas costs');
    }

    await this.attackOrchestrator.start();
  }

  /**
   * Stop automated attacks
   */
  async stopAutomatedAttacks(): Promise<void> {
    if (!this.attackOrchestrator) {
      this.logger.warn('Attack orchestrator not initialized');
      return;
    }

    this.logger.info('🛑 Stopping Automated Flashloan Attacks');
    await this.attackOrchestrator.stop();
  }

  /**
   * Execute specific attack
   */
  async executeSpecificAttack(
    vectorId: string,
    amount?: string
  ): Promise<void> {
    if (!this.attackOrchestrator) {
      throw new Error('Attack orchestrator not initialized. Call initialize() first.');
    }

    this.logger.info(`🎯 Executing Specific Attack: ${vectorId}`);
    
    const attackAmount = amount ? ethers.parseEther(amount) : undefined;
    const result = await this.attackOrchestrator.executeAttack(vectorId, attackAmount);

    if (result.success) {
      this.logger.info('✅ Attack executed successfully');
      if (result.actualProfit) {
        this.logger.info(`💰 Profit: ${ethers.formatEther(result.actualProfit)} ETH`);
      }
    } else {
      this.logger.error(`❌ Attack failed: ${result.error}`);
    }
  }

  /**
   * Get system status
   */
  getStatus() {
    if (!this.attackOrchestrator) {
      return { initialized: false };
    }

    return {
      initialized: true,
      ...this.attackOrchestrator.getStatus()
    };
  }

  /**
   * Get enabled attack vectors based on configuration
   */
  private getEnabledVectors(): string[] {
    const vectors = [];

    // Always enable the most profitable and tested vectors
    vectors.push('weth-usdc-v3-sushi');
    vectors.push('weth-usdc-sushi-v3');

    // Add stablecoin arbitrage if configured
    if (config.enableStablecoinArbitrage !== false) {
      vectors.push('usdc-dai-balancer-sushi');
    }

    // Add complex multi-DEX attacks if configured
    if (config.enableComplexArbitrage === true) {
      vectors.push('weth-dai-complex');
    }

    return vectors;
  }

  /**
   * Display system configuration
   */
  private displayConfiguration(orchestratorConfig: OrchestratorConfig): void {
    this.logger.info('⚙️  ATTACK CONFIGURATION:');
    this.logger.info(`   Enabled Vectors: ${orchestratorConfig.enabledVectors.join(', ')}`);
    this.logger.info(`   Min Profit: ${orchestratorConfig.minProfitETH} ETH`);
    this.logger.info(`   Max Gas Cost: ${orchestratorConfig.maxGasCostGwei} gwei`);
    this.logger.info(`   Default Slippage: ${orchestratorConfig.defaultSlippage / 100}%`);
    this.logger.info(`   Scan Interval: ${orchestratorConfig.scanInterval}ms`);
    this.logger.info(`   Dry Run: ${orchestratorConfig.dryRun ? 'YES' : 'NO'}`);
    this.logger.info(`   Flashbots: ${orchestratorConfig.useFlashbots ? 'YES' : 'NO'}`);
    this.logger.info(`   Max Concurrent: ${orchestratorConfig.maxConcurrentAttacks}`);
    this.logger.info('');

    if (this.lightArbitrageService) {
      const dexes = this.lightArbitrageService.getDEXAddresses();
      const tokens = this.lightArbitrageService.getTokenAddresses();
      const pools = this.lightArbitrageService.getBalancerPools();

      this.logger.info('🏪 SUPPORTED DEXs:');
      this.logger.info(`   Uniswap V2: ${dexes.UNISWAP_V2}`);
      this.logger.info(`   Uniswap V3: ${dexes.UNISWAP_V3}`);
      this.logger.info(`   SushiSwap: ${dexes.SUSHISWAP}`);
      this.logger.info(`   Balancer V2: ${dexes.BALANCER_V2}`);
      this.logger.info('');

      this.logger.info('🪙 SUPPORTED TOKENS:');
      this.logger.info(`   WETH: ${tokens.WETH}`);
      this.logger.info(`   USDC: ${tokens.USDC}`);
      this.logger.info(`   DAI: ${tokens.DAI}`);
      this.logger.info('');

      this.logger.info('🏊 BALANCER POOLS:');
      Object.entries(pools).forEach(([pair, poolId]) => {
        this.logger.info(`   ${pair}: ${poolId}`);
      });
    }
  }

  /**
   * Display available attack vectors
   */
  displayAvailableVectors(): void {
    if (!this.attackOrchestrator) {
      this.logger.error('Attack orchestrator not initialized');
      return;
    }

    const status = this.attackOrchestrator.getStatus();
    const vectorStats = status.availableVectors;

    this.logger.info('🎯 AVAILABLE ATTACK VECTORS:');
    this.logger.info('═'.repeat(60));
    
    vectorStats.vectors.forEach(vector => {
      this.logger.info(`📋 ${vector.id}:`);
      this.logger.info(`   Name: ${vector.name || 'N/A'}`);
      this.logger.info(`   Profitability: ${vector.profitability}/10`);
      this.logger.info(`   Risk: ${vector.risk}/10`);
      this.logger.info('');
    });

    this.logger.info(`📊 STATISTICS:`);
    this.logger.info(`   Total Vectors: ${vectorStats.totalVectors}`);
    this.logger.info(`   Avg Profitability: ${vectorStats.avgProfitability}/10`);
    this.logger.info(`   Avg Risk: ${vectorStats.avgRisk}/10`);
    this.logger.info(`   Low Risk Count: ${vectorStats.lowRiskCount}`);
    this.logger.info(`   High Profit Count: ${vectorStats.highProfitCount}`);
  }

  /**
   * Generate performance report
   */
  generateReport(): void {
    if (!this.attackOrchestrator) {
      this.logger.error('Attack orchestrator not initialized');
      return;
    }

    const status = this.attackOrchestrator.getStatus();
    const stats = status.statistics;

    this.logger.info('📊 FLASHLOAN ATTACK PERFORMANCE REPORT');
    this.logger.info('═'.repeat(60));
    this.logger.info(`Total Attacks: ${stats.totalAttacks}`);
    this.logger.info(`Successful: ${stats.successfulAttacks} (${stats.successRate.toFixed(1)}%)`);
    this.logger.info(`Failed: ${stats.failedAttacks}`);
    this.logger.info(`Total Profit: ${ethers.formatEther(stats.totalProfit)} ETH`);
    this.logger.info(`Net Profit: ${ethers.formatEther(stats.netProfit)} ETH`);
    
    if (stats.bestAttack) {
      this.logger.info(`Best Attack: ${ethers.formatEther(stats.bestAttack.actualProfit)} ETH`);
    }
  }
}
