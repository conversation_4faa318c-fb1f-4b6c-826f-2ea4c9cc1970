import { ethers } from 'ethers';
import { Logger } from '../../utils/logger';
import { LightArbitrageService, FlashloanProvider } from '../../services/LightArbitrageService';
import { FlashloanAttackAnalyzer, AttackVector } from './FlashloanAttackAnalyzer';
import { FlashloanAttackExecutor, AttackExecutionConfig, AttackExecutionResult } from './FlashloanAttackExecutor';
import { FlashloanAttackValidator } from './FlashloanAttackValidator';
import { FlashloanAttackMonitor } from './FlashloanAttackMonitor';

export interface OrchestratorConfig {
  enabledVectors: string[];
  minProfitETH: string;
  maxGasCostGwei: string;
  defaultSlippage: number;
  scanInterval: number;
  dryRun: boolean;
  useFlashbots: boolean;
  maxConcurrentAttacks: number;
}

export interface AttackSession {
  id: string;
  startTime: number;
  endTime?: number;
  totalAttacks: number;
  successfulAttacks: number;
  totalProfit: bigint;
  isActive: boolean;
}

export class FlashloanAttackOrchestrator {
  private logger = new Logger();
  private lightArbitrageService: LightArbitrageService;
  private attackAnalyzer: FlashloanAttackAnalyzer;
  private attackExecutor: FlashloanAttackExecutor;
  private attackValidator: FlashloanAttackValidator;
  private attackMonitor: FlashloanAttackMonitor;
  private provider: ethers.Provider;
  private wallet: ethers.Wallet;
  
  private config: OrchestratorConfig;
  private isRunning = false;
  private currentSession: AttackSession | null = null;
  private activeAttacks = new Set<string>();

  constructor(
    lightArbitrageService: LightArbitrageService,
    provider: ethers.Provider,
    wallet: ethers.Wallet,
    config: OrchestratorConfig
  ) {
    this.lightArbitrageService = lightArbitrageService;
    this.provider = provider;
    this.wallet = wallet;
    this.config = config;

    // Initialize components
    this.attackAnalyzer = new FlashloanAttackAnalyzer(provider);
    this.attackExecutor = new FlashloanAttackExecutor(lightArbitrageService, provider, wallet);
    this.attackValidator = new FlashloanAttackValidator(provider);
    this.attackMonitor = new FlashloanAttackMonitor(provider);
  }

  /**
   * Start the flashloan attack orchestrator
   */
  async start(): Promise<void> {
    if (this.isRunning) {
      this.logger.warn('Orchestrator already running');
      return;
    }

    this.logger.info('🚀 Starting Flashloan Attack Orchestrator');
    this.logger.info('═'.repeat(60));
    this.logger.info('🎯 ATTACK CONFIGURATION:');
    this.logger.info(`   Enabled Vectors: ${this.config.enabledVectors.join(', ')}`);
    this.logger.info(`   Min Profit: ${this.config.minProfitETH} ETH`);
    this.logger.info(`   Max Gas: ${this.config.maxGasCostGwei} gwei`);
    this.logger.info(`   Scan Interval: ${this.config.scanInterval}ms`);
    this.logger.info(`   Dry Run: ${this.config.dryRun ? 'YES' : 'NO'}`);
    this.logger.info(`   Flashbots: ${this.config.useFlashbots ? 'YES' : 'NO'}`);
    this.logger.info('═'.repeat(60));

    this.isRunning = true;
    
    // Start new session
    this.currentSession = {
      id: `session_${Date.now()}`,
      startTime: Date.now(),
      totalAttacks: 0,
      successfulAttacks: 0,
      totalProfit: BigInt(0),
      isActive: true
    };

    // Start monitoring
    this.attackMonitor.startMonitoring();

    // Start attack loop
    this.startAttackLoop();
  }

  /**
   * Stop the orchestrator
   */
  async stop(): Promise<void> {
    this.logger.info('🛑 Stopping Flashloan Attack Orchestrator');
    
    this.isRunning = false;
    
    // End current session
    if (this.currentSession) {
      this.currentSession.endTime = Date.now();
      this.currentSession.isActive = false;
    }

    // Stop monitoring
    this.attackMonitor.stopMonitoring();

    // Wait for active attacks to complete
    if (this.activeAttacks.size > 0) {
      this.logger.info(`⏳ Waiting for ${this.activeAttacks.size} active attacks to complete...`);
      
      let timeout = 30000; // 30 seconds timeout
      const startWait = Date.now();
      
      while (this.activeAttacks.size > 0 && (Date.now() - startWait) < timeout) {
        await this.sleep(1000);
      }
    }

    this.logger.info('✅ Orchestrator stopped');
    
    // Generate final report
    this.generateSessionReport();
  }

  /**
   * Execute specific attack vector
   */
  async executeAttack(vectorId: string, amount?: bigint): Promise<AttackExecutionResult> {
    try {
      this.logger.info(`🎯 Executing specific attack: ${vectorId}`);

      // Get attack vector
      const vector = this.attackAnalyzer.getAttackVector(vectorId);
      if (!vector) {
        throw new Error(`Attack vector not found: ${vectorId}`);
      }

      // Calculate optimal amount if not provided
      const attackAmount = amount || await this.attackAnalyzer.calculateOptimalAmount(vector);

      // Create execution config
      const config: AttackExecutionConfig = {
        vectorId,
        amount: attackAmount,
        minProfit: ethers.parseEther(this.config.minProfitETH),
        maxGasCost: ethers.parseUnits(this.config.maxGasCostGwei, 'gwei'),
        slippageTolerance: this.config.defaultSlippage,
        provider: FlashloanProvider.AAVE,
        dryRun: this.config.dryRun,
        useFlashbots: this.config.useFlashbots
      };

      // Execute attack
      const result = await this.attackExecutor.executeAttack(config);

      // Update session stats
      if (this.currentSession) {
        this.currentSession.totalAttacks++;
        if (result.success) {
          this.currentSession.successfulAttacks++;
          if (result.actualProfit) {
            this.currentSession.totalProfit += result.actualProfit;
          }
        }
      }

      return result;

    } catch (error) {
      this.logger.error(`Failed to execute attack ${vectorId}: ${error}`);
      throw error;
    }
  }

  /**
   * Get best attack opportunities
   */
  async getBestOpportunities(): Promise<AttackVector[]> {
    const enabledVectors = this.attackAnalyzer.getAttackVectors()
      .filter(vector => this.config.enabledVectors.includes(vector.id));

    // Sort by profitability and risk
    return enabledVectors.sort((a, b) => {
      const scoreA = a.expectedProfitability - a.riskLevel;
      const scoreB = b.expectedProfitability - b.riskLevel;
      return scoreB - scoreA;
    });
  }

  /**
   * Main attack loop
   */
  private async startAttackLoop(): Promise<void> {
    this.logger.info('🔄 Starting attack scanning loop');

    while (this.isRunning) {
      try {
        await this.scanAndExecuteAttacks();
        await this.sleep(this.config.scanInterval);
      } catch (error) {
        this.logger.error(`Attack loop error: ${error}`);
        await this.sleep(this.config.scanInterval * 2); // Longer delay on error
      }
    }
  }

  /**
   * Scan for opportunities and execute attacks
   */
  private async scanAndExecuteAttacks(): Promise<void> {
    // Check if we're at max concurrent attacks
    if (this.activeAttacks.size >= this.config.maxConcurrentAttacks) {
      this.logger.debug('Max concurrent attacks reached, skipping scan');
      return;
    }

    // Get best opportunities
    const opportunities = await this.getBestOpportunities();
    
    for (const vector of opportunities) {
      if (!this.isRunning) break;
      if (this.activeAttacks.size >= this.config.maxConcurrentAttacks) break;

      try {
        // Analyze opportunity
        const opportunity = await this.attackAnalyzer.analyzeAttackOpportunity(vector.id);
        if (!opportunity) continue;

        // Check if opportunity is profitable enough
        if (opportunity.confidence < 6) continue;
        if (opportunity.priceSpread < 0.15) continue; // Minimum 0.15% spread

        // Calculate optimal amount
        const amount = await this.attackAnalyzer.calculateOptimalAmount(vector);

        // Execute attack asynchronously
        this.executeAttackAsync(vector.id, amount);

      } catch (error) {
        this.logger.debug(`Failed to analyze opportunity ${vector.id}: ${error}`);
      }
    }
  }

  /**
   * Execute attack asynchronously
   */
  private async executeAttackAsync(vectorId: string, amount: bigint): Promise<void> {
    const attackId = `${vectorId}_${Date.now()}`;
    this.activeAttacks.add(attackId);

    try {
      const result = await this.executeAttack(vectorId, amount);
      
      if (result.success) {
        this.logger.info(`✅ Async attack ${vectorId} completed successfully`);
      } else {
        this.logger.warn(`❌ Async attack ${vectorId} failed: ${result.error}`);
      }

    } catch (error) {
      this.logger.error(`Async attack ${vectorId} error: ${error}`);
    } finally {
      this.activeAttacks.delete(attackId);
    }
  }

  /**
   * Generate session report
   */
  private generateSessionReport(): void {
    if (!this.currentSession) return;

    const duration = (this.currentSession.endTime || Date.now()) - this.currentSession.startTime;
    const successRate = this.currentSession.totalAttacks > 0 ? 
      (this.currentSession.successfulAttacks / this.currentSession.totalAttacks) * 100 : 0;

    const report = `
🎯 FLASHLOAN ATTACK SESSION REPORT
═══════════════════════════════════

📊 SESSION STATISTICS:
   Session ID: ${this.currentSession.id}
   Duration: ${Math.round(duration / 1000)}s
   Total Attacks: ${this.currentSession.totalAttacks}
   Successful: ${this.currentSession.successfulAttacks}
   Success Rate: ${successRate.toFixed(1)}%
   Total Profit: ${ethers.formatEther(this.currentSession.totalProfit)} ETH

📈 PERFORMANCE METRICS:
${this.attackMonitor.generatePerformanceReport()}
`;

    this.logger.info(report);
  }

  /**
   * Get current status
   */
  getStatus() {
    return {
      isRunning: this.isRunning,
      currentSession: this.currentSession,
      activeAttacks: this.activeAttacks.size,
      config: this.config,
      statistics: this.attackMonitor.getAttackStatistics(),
      availableVectors: this.attackAnalyzer.getAttackVectorStats()
    };
  }

  /**
   * Update configuration
   */
  updateConfig(newConfig: Partial<OrchestratorConfig>): void {
    this.config = { ...this.config, ...newConfig };
    this.logger.info('⚙️ Configuration updated:', newConfig);
  }

  /**
   * Sleep utility
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}
