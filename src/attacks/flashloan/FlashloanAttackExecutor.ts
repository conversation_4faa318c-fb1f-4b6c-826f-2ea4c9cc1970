import { ethers } from 'ethers';
import { Logger } from '../../utils/logger';
import { LightArbitrageService, TradeStep, DEXType, FlashloanProvider } from '../../services/LightArbitrageService';
import { FlashloanAttackAnalyzer, AttackVector, AttackOpportunity } from './FlashloanAttackAnalyzer';
import { FlashloanAttackValidator } from './FlashloanAttackValidator';
import { FlashloanAttackMonitor } from './FlashloanAttackMonitor';

export interface AttackExecutionResult {
  success: boolean;
  transactionHash?: string;
  blockNumber?: number;
  gasUsed?: bigint;
  actualProfit?: bigint;
  executionTime: number;
  error?: string;
}

export interface AttackExecutionConfig {
  vectorId: string;
  amount: bigint;
  minProfit: bigint;
  maxGasCost: bigint;
  slippageTolerance: number;
  provider: FlashloanProvider;
  dryRun: boolean;
  useFlashbots: boolean;
}

export class FlashloanAttackExecutor {
  private logger = new Logger();
  private lightArbitrageService: LightArbitrageService;
  private attackAnalyzer: FlashloanAttackAnalyzer;
  private attackValidator: FlashloanAttackValidator;
  private attackMonitor: FlashloanAttackMonitor;
  private provider: ethers.Provider;
  private wallet: ethers.Wallet;

  constructor(
    lightArbitrageService: LightArbitrageService,
    provider: ethers.Provider,
    wallet: ethers.Wallet
  ) {
    this.lightArbitrageService = lightArbitrageService;
    this.provider = provider;
    this.wallet = wallet;
    this.attackAnalyzer = new FlashloanAttackAnalyzer(provider);
    this.attackValidator = new FlashloanAttackValidator(provider);
    this.attackMonitor = new FlashloanAttackMonitor(provider);
  }

  /**
   * Execute flashloan attack with comprehensive validation and monitoring
   */
  async executeAttack(config: AttackExecutionConfig): Promise<AttackExecutionResult> {
    const startTime = Date.now();
    
    try {
      this.logger.info(`🚀 Executing flashloan attack: ${config.vectorId}`);
      this.logger.info(`💰 Amount: ${ethers.formatEther(config.amount)} ETH`);
      this.logger.info(`🎯 Min Profit: ${ethers.formatEther(config.minProfit)} ETH`);
      this.logger.info(`⛽ Max Gas: ${ethers.formatUnits(config.maxGasCost, 'gwei')} gwei`);
      this.logger.info(`🔄 Provider: ${config.provider === FlashloanProvider.AAVE ? 'AAVE' : 'BALANCER'}`);
      this.logger.info(`🧪 Dry Run: ${config.dryRun ? 'YES' : 'NO'}`);

      // Step 1: Get and validate attack vector
      const vector = this.attackAnalyzer.getAttackVector(config.vectorId);
      if (!vector) {
        throw new Error(`Attack vector not found: ${config.vectorId}`);
      }

      // Step 2: Validate attack parameters
      const validation = this.attackAnalyzer.validateAttackParameters(
        vector,
        config.amount,
        config.maxGasCost
      );

      if (!validation.valid) {
        throw new Error(`Attack validation failed: ${validation.errors.join(', ')}`);
      }

      // Step 3: Analyze current market opportunity
      const opportunity = await this.attackAnalyzer.analyzeAttackOpportunity(config.vectorId);
      if (!opportunity) {
        throw new Error('No attack opportunity found');
      }

      if (opportunity.confidence < 5) {
        throw new Error(`Low confidence opportunity: ${opportunity.confidence}/10`);
      }

      // Step 4: Pre-execution validation
      const preValidation = await this.attackValidator.validatePreExecution(
        vector,
        config.amount,
        opportunity
      );

      if (!preValidation.valid) {
        throw new Error(`Pre-execution validation failed: ${preValidation.errors.join(', ')}`);
      }

      // Step 5: Convert attack vector to trade steps
      const tradeSteps = this.convertVectorToTradeSteps(vector, config);
      
      if (tradeSteps.length === 0) {
        throw new Error('Failed to convert attack vector to trade steps');
      }

      this.logger.info(`📋 Generated ${tradeSteps.length} trade steps`);
      tradeSteps.forEach((step, index) => {
        this.logger.debug(`   Step ${index + 1}: ${this.getTokenSymbol(step.tokenIn)} → ${this.getTokenSymbol(step.tokenOut)} via ${this.getDEXName(step.dexType)}`);
      });

      // Step 6: Execute attack (dry run or real)
      let result: AttackExecutionResult;

      if (config.dryRun) {
        result = await this.executeDryRun(vector, tradeSteps, config);
      } else {
        result = await this.executeRealAttack(vector, tradeSteps, config);
      }

      // Step 7: Post-execution monitoring
      if (result.success && result.transactionHash) {
        await this.attackMonitor.monitorExecution(result.transactionHash, vector);
      }

      result.executionTime = Date.now() - startTime;
      
      this.logger.info(`✅ Attack execution completed in ${result.executionTime}ms`);
      return result;

    } catch (error) {
      const executionTime = Date.now() - startTime;
      this.logger.error(`❌ Attack execution failed: ${error}`);
      
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
        executionTime
      };
    }
  }

  /**
   * Execute dry run simulation
   */
  private async executeDryRun(
    vector: AttackVector,
    tradeSteps: TradeStep[],
    config: AttackExecutionConfig
  ): Promise<AttackExecutionResult> {
    this.logger.info('🧪 Executing dry run simulation...');

    try {
      // Simulate the attack without actual execution
      const simulationResult = await this.simulateAttack(vector, tradeSteps, config);
      
      this.logger.info(`📊 Simulation Results:`);
      this.logger.info(`   Estimated Profit: ${ethers.formatEther(simulationResult.estimatedProfit)} ETH`);
      this.logger.info(`   Gas Estimate: ${simulationResult.gasEstimate.toString()}`);
      this.logger.info(`   Success Probability: ${simulationResult.successProbability}%`);

      return {
        success: true,
        actualProfit: simulationResult.estimatedProfit,
        gasUsed: simulationResult.gasEstimate,
        executionTime: 0
      };

    } catch (error) {
      throw new Error(`Dry run simulation failed: ${error}`);
    }
  }

  /**
   * Execute real attack
   */
  private async executeRealAttack(
    vector: AttackVector,
    tradeSteps: TradeStep[],
    config: AttackExecutionConfig
  ): Promise<AttackExecutionResult> {
    this.logger.info('⚡ Executing REAL flashloan attack...');

    try {
      // Execute the attack using Light contract
      const tx = await this.lightArbitrageService.executeArbitrage(
        vector.tokenA,
        config.amount.toString(),
        tradeSteps,
        config.minProfit.toString(),
        config.provider,
        config.maxGasCost.toString()
      );

      this.logger.info(`📤 Transaction submitted: ${tx.hash}`);

      // Wait for confirmation
      const receipt = await tx.wait();
      
      if (receipt?.status === 1) {
        this.logger.info(`🎉 Attack successful! Block: ${receipt.blockNumber}`);
        
        // Calculate actual profit (simplified - in production, parse logs)
        const actualProfit = await this.calculateActualProfit(receipt, vector);
        
        return {
          success: true,
          transactionHash: tx.hash,
          blockNumber: receipt.blockNumber,
          gasUsed: receipt.gasUsed,
          actualProfit,
          executionTime: 0
        };
      } else {
        throw new Error('Transaction failed');
      }

    } catch (error) {
      throw new Error(`Real attack execution failed: ${error}`);
    }
  }

  /**
   * Convert attack vector to Light contract trade steps
   */
  private convertVectorToTradeSteps(
    vector: AttackVector,
    config: AttackExecutionConfig
  ): TradeStep[] {
    const tradeSteps: TradeStep[] = [];

    // Simple 2-step arbitrage
    if (vector.id.includes('weth-usdc') && !vector.id.includes('complex')) {
      // Step 1: TokenA → TokenB
      tradeSteps.push({
        dex: vector.dexBuy,
        dexType: this.getDEXType(vector.dexBuy),
        tokenIn: vector.tokenA,
        tokenOut: vector.tokenB,
        slippageToleranceBps: config.slippageTolerance,
        v3Fee: this.getV3Fee(vector.tokenA, vector.tokenB),
        balancerPoolId: this.getBalancerPoolId(vector.tokenA, vector.tokenB)
      });

      // Step 2: TokenB → TokenA
      tradeSteps.push({
        dex: vector.dexSell,
        dexType: this.getDEXType(vector.dexSell),
        tokenIn: vector.tokenB,
        tokenOut: vector.tokenA,
        slippageToleranceBps: config.slippageTolerance,
        v3Fee: this.getV3Fee(vector.tokenB, vector.tokenA),
        balancerPoolId: this.getBalancerPoolId(vector.tokenB, vector.tokenA)
      });
    }
    // Complex 3-step arbitrage: WETH → USDC → DAI → WETH
    else if (vector.id.includes('complex')) {
      const tokens = this.lightArbitrageService.getTokenAddresses();
      const dexes = this.lightArbitrageService.getDEXAddresses();

      // Step 1: WETH → USDC (Uniswap V3)
      tradeSteps.push({
        dex: dexes.UNISWAP_V3,
        dexType: DEXType.V3,
        tokenIn: tokens.WETH,
        tokenOut: tokens.USDC,
        slippageToleranceBps: 100,
        v3Fee: 3000,
        balancerPoolId: '0x0000000000000000000000000000000000000000000000000000000000000000'
      });

      // Step 2: USDC → DAI (Balancer V2)
      tradeSteps.push({
        dex: dexes.BALANCER_V2,
        dexType: DEXType.BALANCER_V2,
        tokenIn: tokens.USDC,
        tokenOut: tokens.DAI,
        slippageToleranceBps: 50,
        v3Fee: 0,
        balancerPoolId: '0x06df3b2bbb68adc8b0e302443692037ed9f91b42000000000000000000000063'
      });

      // Step 3: DAI → WETH (SushiSwap)
      tradeSteps.push({
        dex: dexes.SUSHISWAP,
        dexType: DEXType.V2,
        tokenIn: tokens.DAI,
        tokenOut: tokens.WETH,
        slippageToleranceBps: 150,
        v3Fee: 0,
        balancerPoolId: '0x0000000000000000000000000000000000000000000000000000000000000000'
      });
    }

    return tradeSteps;
  }

  /**
   * Helper methods
   */
  private getDEXType(dexAddress: string): DEXType {
    const dexes = this.lightArbitrageService.getDEXAddresses();
    
    if (dexAddress === dexes.UNISWAP_V3) return DEXType.V3;
    if (dexAddress === dexes.BALANCER_V2) return DEXType.BALANCER_V2;
    if (dexAddress === dexes.CURVE_3POOL) return DEXType.CURVE;
    return DEXType.V2; // Default to V2 for SushiSwap, etc.
  }

  private getV3Fee(tokenIn: string, tokenOut: string): number {
    // Standard V3 fees based on token pairs
    return 3000; // 0.3% default
  }

  private getBalancerPoolId(tokenIn: string, tokenOut: string): string {
    const pools = this.lightArbitrageService.getBalancerPools();
    const tokens = this.lightArbitrageService.getTokenAddresses();
    
    // Map addresses to symbols and find pool
    const getSymbol = (address: string) => {
      if (address === tokens.WETH) return 'WETH';
      if (address === tokens.USDC) return 'USDC';
      if (address === tokens.DAI) return 'DAI';
      return 'UNKNOWN';
    };

    const symbolIn = getSymbol(tokenIn);
    const symbolOut = getSymbol(tokenOut);
    
    return pools[`${symbolIn}-${symbolOut}`] || pools[`${symbolOut}-${symbolIn}`] || '0x0000000000000000000000000000000000000000000000000000000000000000';
  }

  private getTokenSymbol(address: string): string {
    const tokens = this.lightArbitrageService.getTokenAddresses();
    if (address === tokens.WETH) return 'WETH';
    if (address === tokens.USDC) return 'USDC';
    if (address === tokens.DAI) return 'DAI';
    return 'UNKNOWN';
  }

  private getDEXName(dexType: DEXType): string {
    switch (dexType) {
      case DEXType.V2: return 'V2';
      case DEXType.V3: return 'V3';
      case DEXType.CURVE: return 'Curve';
      case DEXType.BALANCER_V2: return 'Balancer V2';
      default: return 'Unknown';
    }
  }

  private async simulateAttack(
    vector: AttackVector,
    tradeSteps: TradeStep[],
    config: AttackExecutionConfig
  ): Promise<{ estimatedProfit: bigint; gasEstimate: bigint; successProbability: number }> {
    // Mock simulation results (in production, this would use actual price feeds)
    return {
      estimatedProfit: ethers.parseEther('0.05'),
      gasEstimate: vector.gasEstimate,
      successProbability: 85
    };
  }

  private async calculateActualProfit(
    receipt: ethers.TransactionReceipt,
    vector: AttackVector
  ): Promise<bigint> {
    // Mock profit calculation (in production, parse transaction logs)
    return ethers.parseEther('0.045');
  }
}
