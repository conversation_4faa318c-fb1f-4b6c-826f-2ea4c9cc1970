import { ethers } from 'ethers';
import { Logger } from '../../utils/logger';
import { AttackVector, AttackOpportunity } from './FlashloanAttackAnalyzer';

export interface ValidationResult {
  valid: boolean;
  errors: string[];
  warnings: string[];
  riskScore: number; // 1-10 scale
}

export interface LiquidityCheck {
  tokenA: {
    address: string;
    symbol: string;
    liquidity: bigint;
    sufficient: boolean;
  };
  tokenB: {
    address: string;
    symbol: string;
    liquidity: bigint;
    sufficient: boolean;
  };
}

export interface GasAnalysis {
  currentGasPrice: bigint;
  estimatedGasCost: bigint;
  maxAcceptableGas: bigint;
  profitable: boolean;
  marginOfSafety: number; // percentage
}

export class FlashloanAttackValidator {
  private logger = new Logger();
  private provider: ethers.Provider;

  // Risk thresholds
  private readonly RISK_THRESHOLDS = {
    MAX_SLIPPAGE: 500, // 5%
    MIN_LIQUIDITY_ETH: ethers.parseEther('100'), // 100 ETH minimum liquidity
    MAX_GAS_PRICE_GWEI: 100, // 100 gwei max
    MIN_PROFIT_MARGIN: 0.1, // 10% minimum profit margin
    MAX_RISK_SCORE: 7 // Maximum acceptable risk score
  };

  constructor(provider: ethers.Provider) {
    this.provider = provider;
  }

  /**
   * Comprehensive pre-execution validation
   */
  async validatePreExecution(
    vector: AttackVector,
    amount: bigint,
    opportunity: AttackOpportunity
  ): Promise<ValidationResult> {
    const errors: string[] = [];
    const warnings: string[] = [];
    let riskScore = 0;

    try {
      this.logger.debug(`Validating pre-execution for ${vector.name}`);

      // 1. Validate network conditions
      const networkValidation = await this.validateNetworkConditions();
      if (!networkValidation.valid) {
        errors.push(...networkValidation.errors);
        riskScore += 2;
      }
      warnings.push(...networkValidation.warnings);

      // 2. Validate gas conditions
      const gasValidation = await this.validateGasConditions(vector, amount);
      if (!gasValidation.valid) {
        errors.push(...gasValidation.errors);
        riskScore += 3;
      }
      warnings.push(...gasValidation.warnings);

      // 3. Validate liquidity
      const liquidityValidation = await this.validateLiquidity(vector, amount);
      if (!liquidityValidation.valid) {
        errors.push(...liquidityValidation.errors);
        riskScore += 4;
      }
      warnings.push(...liquidityValidation.warnings);

      // 4. Validate opportunity
      const opportunityValidation = this.validateOpportunity(opportunity, amount);
      if (!opportunityValidation.valid) {
        errors.push(...opportunityValidation.errors);
        riskScore += 2;
      }
      warnings.push(...opportunityValidation.warnings);

      // 5. Validate slippage tolerance
      const slippageValidation = this.validateSlippageTolerance(vector);
      if (!slippageValidation.valid) {
        errors.push(...slippageValidation.errors);
        riskScore += 1;
      }
      warnings.push(...slippageValidation.warnings);

      // 6. Calculate final risk score
      riskScore += vector.riskLevel;
      
      if (riskScore > this.RISK_THRESHOLDS.MAX_RISK_SCORE) {
        errors.push(`Risk score too high: ${riskScore}/${this.RISK_THRESHOLDS.MAX_RISK_SCORE}`);
      }

      const valid = errors.length === 0;
      
      this.logger.debug(`Validation result: ${valid ? 'VALID' : 'INVALID'}, Risk: ${riskScore}/10`);
      
      return {
        valid,
        errors,
        warnings,
        riskScore
      };

    } catch (error) {
      this.logger.error(`Validation failed: ${error}`);
      return {
        valid: false,
        errors: [`Validation error: ${error}`],
        warnings,
        riskScore: 10
      };
    }
  }

  /**
   * Validate network conditions
   */
  private async validateNetworkConditions(): Promise<ValidationResult> {
    const errors: string[] = [];
    const warnings: string[] = [];

    try {
      // Check if network is responsive
      const blockNumber = await this.provider.getBlockNumber();
      if (!blockNumber || blockNumber === 0) {
        errors.push('Network not responsive');
      }

      // Check block time (should be recent)
      const block = await this.provider.getBlock(blockNumber);
      if (block) {
        const blockAge = Date.now() / 1000 - block.timestamp;
        if (blockAge > 60) { // More than 1 minute old
          warnings.push(`Block is ${Math.round(blockAge)}s old`);
        }
      }

      return {
        valid: errors.length === 0,
        errors,
        warnings,
        riskScore: 0
      };

    } catch (error) {
      return {
        valid: false,
        errors: [`Network validation failed: ${error}`],
        warnings,
        riskScore: 5
      };
    }
  }

  /**
   * Validate gas conditions
   */
  private async validateGasConditions(
    vector: AttackVector,
    amount: bigint
  ): Promise<ValidationResult> {
    const errors: string[] = [];
    const warnings: string[] = [];

    try {
      const feeData = await this.provider.getFeeData();
      const currentGasPrice = feeData.gasPrice || BigInt(0);
      
      // Check if gas price is reasonable
      const maxGasPriceWei = ethers.parseUnits(this.RISK_THRESHOLDS.MAX_GAS_PRICE_GWEI.toString(), 'gwei');
      
      if (currentGasPrice > maxGasPriceWei) {
        errors.push(`Gas price too high: ${ethers.formatUnits(currentGasPrice, 'gwei')} gwei`);
      }

      // Estimate total gas cost
      const estimatedGasCost = vector.gasEstimate * currentGasPrice;
      const profitThreshold = amount * BigInt(Math.floor(this.RISK_THRESHOLDS.MIN_PROFIT_MARGIN * 100)) / BigInt(100);
      
      if (estimatedGasCost > profitThreshold) {
        errors.push(`Gas cost ${ethers.formatEther(estimatedGasCost)} ETH exceeds profit threshold`);
      }

      if (currentGasPrice > maxGasPriceWei / BigInt(2)) {
        warnings.push(`High gas price: ${ethers.formatUnits(currentGasPrice, 'gwei')} gwei`);
      }

      return {
        valid: errors.length === 0,
        errors,
        warnings,
        riskScore: 0
      };

    } catch (error) {
      return {
        valid: false,
        errors: [`Gas validation failed: ${error}`],
        warnings,
        riskScore: 3
      };
    }
  }

  /**
   * Validate liquidity conditions
   */
  private async validateLiquidity(
    vector: AttackVector,
    amount: bigint
  ): Promise<ValidationResult> {
    const errors: string[] = [];
    const warnings: string[] = [];

    try {
      // For now, use simplified liquidity validation
      // In production, this would check actual DEX liquidity
      
      // Check if amount is within reasonable bounds
      if (amount > vector.maxAmount) {
        errors.push(`Amount ${ethers.formatEther(amount)} ETH exceeds maximum ${ethers.formatEther(vector.maxAmount)} ETH`);
      }

      if (amount < vector.minAmount) {
        errors.push(`Amount ${ethers.formatEther(amount)} ETH below minimum ${ethers.formatEther(vector.minAmount)} ETH`);
      }

      // Warning for large amounts
      if (amount > vector.maxAmount / BigInt(2)) {
        warnings.push(`Large amount may cause high slippage`);
      }

      return {
        valid: errors.length === 0,
        errors,
        warnings,
        riskScore: 0
      };

    } catch (error) {
      return {
        valid: false,
        errors: [`Liquidity validation failed: ${error}`],
        warnings,
        riskScore: 4
      };
    }
  }

  /**
   * Validate opportunity parameters
   */
  private validateOpportunity(
    opportunity: AttackOpportunity,
    amount: bigint
  ): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Check confidence level
    if (opportunity.confidence < 5) {
      errors.push(`Low confidence: ${opportunity.confidence}/10`);
    } else if (opportunity.confidence < 7) {
      warnings.push(`Medium confidence: ${opportunity.confidence}/10`);
    }

    // Check price spread
    if (opportunity.priceSpread < 0.1) {
      errors.push(`Price spread too low: ${opportunity.priceSpread}%`);
    } else if (opportunity.priceSpread < 0.2) {
      warnings.push(`Low price spread: ${opportunity.priceSpread}%`);
    }

    // Check opportunity age
    const opportunityAge = Date.now() / 1000 - opportunity.timestamp;
    if (opportunityAge > 30) { // More than 30 seconds old
      warnings.push(`Opportunity is ${Math.round(opportunityAge)}s old`);
    }

    return {
      valid: errors.length === 0,
      errors,
      warnings,
      riskScore: 0
    };
  }

  /**
   * Validate slippage tolerance
   */
  private validateSlippageTolerance(vector: AttackVector): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    if (vector.slippageTolerance > this.RISK_THRESHOLDS.MAX_SLIPPAGE) {
      errors.push(`Slippage tolerance too high: ${vector.slippageTolerance / 100}%`);
    } else if (vector.slippageTolerance > this.RISK_THRESHOLDS.MAX_SLIPPAGE / 2) {
      warnings.push(`High slippage tolerance: ${vector.slippageTolerance / 100}%`);
    }

    return {
      valid: errors.length === 0,
      errors,
      warnings,
      riskScore: 0
    };
  }

  /**
   * Validate post-execution results
   */
  async validatePostExecution(
    transactionHash: string,
    expectedProfit: bigint
  ): Promise<ValidationResult> {
    const errors: string[] = [];
    const warnings: string[] = [];

    try {
      const receipt = await this.provider.getTransactionReceipt(transactionHash);
      
      if (!receipt) {
        errors.push('Transaction receipt not found');
        return { valid: false, errors, warnings, riskScore: 10 };
      }

      if (receipt.status !== 1) {
        errors.push('Transaction failed');
        return { valid: false, errors, warnings, riskScore: 10 };
      }

      // Check gas usage
      const gasUsed = receipt.gasUsed;
      if (gasUsed > ethers.parseUnits('500000', 'wei')) {
        warnings.push(`High gas usage: ${gasUsed.toString()}`);
      }

      // In production, parse logs to verify actual profit
      // For now, assume success if transaction succeeded
      
      return {
        valid: errors.length === 0,
        errors,
        warnings,
        riskScore: 0
      };

    } catch (error) {
      return {
        valid: false,
        errors: [`Post-execution validation failed: ${error}`],
        warnings,
        riskScore: 5
      };
    }
  }

  /**
   * Get validation statistics
   */
  getValidationStats() {
    return {
      riskThresholds: this.RISK_THRESHOLDS,
      validationChecks: [
        'Network conditions',
        'Gas conditions',
        'Liquidity validation',
        'Opportunity validation',
        'Slippage tolerance',
        'Risk score calculation'
      ]
    };
  }
}
