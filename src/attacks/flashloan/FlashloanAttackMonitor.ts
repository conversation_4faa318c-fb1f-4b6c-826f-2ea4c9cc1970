import { ethers } from 'ethers';
import { Logger } from '../../utils/logger';
import { AttackVector } from './FlashloanAttackAnalyzer';

export interface AttackMetrics {
  transactionHash: string;
  blockNumber: number;
  gasUsed: bigint;
  gasPrice: bigint;
  totalGasCost: bigint;
  executionTime: number;
  actualProfit: bigint;
  profitMargin: number;
  success: boolean;
  timestamp: number;
}

export interface AttackStatistics {
  totalAttacks: number;
  successfulAttacks: number;
  failedAttacks: number;
  successRate: number;
  totalProfit: bigint;
  totalGasCost: bigint;
  netProfit: bigint;
  averageProfit: bigint;
  averageGasCost: bigint;
  bestAttack: AttackMetrics | null;
  worstAttack: AttackMetrics | null;
}

export class FlashloanAttackMonitor {
  private logger = new Logger();
  private provider: ethers.Provider;
  private attackHistory: AttackMetrics[] = [];
  private isMonitoring = false;

  constructor(provider: ethers.Provider) {
    this.provider = provider;
  }

  /**
   * Monitor attack execution in real-time
   */
  async monitorExecution(
    transactionHash: string,
    vector: AttackVector
  ): Promise<AttackMetrics> {
    try {
      this.logger.info(`📊 Monitoring attack execution: ${transactionHash}`);
      
      const startTime = Date.now();
      
      // Wait for transaction confirmation
      const receipt = await this.provider.waitForTransaction(transactionHash, 1, 30000); // 30s timeout
      
      if (!receipt) {
        throw new Error('Transaction confirmation timeout');
      }

      const executionTime = Date.now() - startTime;
      
      // Get transaction details
      const transaction = await this.provider.getTransaction(transactionHash);
      if (!transaction) {
        throw new Error('Transaction not found');
      }

      // Calculate metrics
      const gasUsed = receipt.gasUsed;
      const gasPrice = transaction.gasPrice || BigInt(0);
      const totalGasCost = gasUsed * gasPrice;
      const success = receipt.status === 1;
      
      // Parse profit from logs (simplified - in production, parse actual logs)
      const actualProfit = success ? await this.parseActualProfit(receipt) : BigInt(0);
      const profitMargin = success && actualProfit > 0 ? 
        Number(((actualProfit - totalGasCost) * BigInt(100)) / actualProfit) : 0;

      const metrics: AttackMetrics = {
        transactionHash,
        blockNumber: receipt.blockNumber,
        gasUsed,
        gasPrice,
        totalGasCost,
        executionTime,
        actualProfit,
        profitMargin,
        success,
        timestamp: Math.floor(Date.now() / 1000)
      };

      // Store metrics
      this.attackHistory.push(metrics);
      
      // Log results
      this.logAttackResults(metrics, vector);
      
      return metrics;

    } catch (error) {
      this.logger.error(`Failed to monitor attack execution: ${error}`);
      throw error;
    }
  }

  /**
   * Start continuous monitoring
   */
  startMonitoring(): void {
    if (this.isMonitoring) {
      this.logger.warn('Monitoring already started');
      return;
    }

    this.isMonitoring = true;
    this.logger.info('📊 Started continuous attack monitoring');

    // Monitor every 30 seconds
    setInterval(() => {
      this.generatePeriodicReport();
    }, 30000);
  }

  /**
   * Stop monitoring
   */
  stopMonitoring(): void {
    this.isMonitoring = false;
    this.logger.info('📊 Stopped attack monitoring');
  }

  /**
   * Get attack statistics
   */
  getAttackStatistics(): AttackStatistics {
    const totalAttacks = this.attackHistory.length;
    const successfulAttacks = this.attackHistory.filter(a => a.success).length;
    const failedAttacks = totalAttacks - successfulAttacks;
    const successRate = totalAttacks > 0 ? (successfulAttacks / totalAttacks) * 100 : 0;

    const successfulMetrics = this.attackHistory.filter(a => a.success);
    
    const totalProfit = successfulMetrics.reduce((sum, a) => sum + a.actualProfit, BigInt(0));
    const totalGasCost = this.attackHistory.reduce((sum, a) => sum + a.totalGasCost, BigInt(0));
    const netProfit = totalProfit - totalGasCost;
    
    const averageProfit = successfulAttacks > 0 ? totalProfit / BigInt(successfulAttacks) : BigInt(0);
    const averageGasCost = totalAttacks > 0 ? totalGasCost / BigInt(totalAttacks) : BigInt(0);

    // Find best and worst attacks
    const bestAttack = successfulMetrics.length > 0 ? 
      successfulMetrics.reduce((best, current) => 
        current.actualProfit > best.actualProfit ? current : best
      ) : null;

    const worstAttack = this.attackHistory.length > 0 ? 
      this.attackHistory.reduce((worst, current) => 
        current.success && current.actualProfit < worst.actualProfit ? current : worst
      ) : null;

    return {
      totalAttacks,
      successfulAttacks,
      failedAttacks,
      successRate,
      totalProfit,
      totalGasCost,
      netProfit,
      averageProfit,
      averageGasCost,
      bestAttack,
      worstAttack
    };
  }

  /**
   * Get recent attack history
   */
  getRecentAttacks(limit: number = 10): AttackMetrics[] {
    return this.attackHistory
      .sort((a, b) => b.timestamp - a.timestamp)
      .slice(0, limit);
  }

  /**
   * Generate performance report
   */
  generatePerformanceReport(): string {
    const stats = this.getAttackStatistics();
    
    const report = `
📊 FLASHLOAN ATTACK PERFORMANCE REPORT
═══════════════════════════════════════

📈 EXECUTION STATISTICS:
   Total Attacks: ${stats.totalAttacks}
   Successful: ${stats.successfulAttacks} (${stats.successRate.toFixed(1)}%)
   Failed: ${stats.failedAttacks}

💰 PROFIT ANALYSIS:
   Total Profit: ${ethers.formatEther(stats.totalProfit)} ETH
   Total Gas Cost: ${ethers.formatEther(stats.totalGasCost)} ETH
   Net Profit: ${ethers.formatEther(stats.netProfit)} ETH
   Average Profit: ${ethers.formatEther(stats.averageProfit)} ETH
   Average Gas Cost: ${ethers.formatEther(stats.averageGasCost)} ETH

🏆 BEST PERFORMANCE:
   ${stats.bestAttack ? `
   Transaction: ${stats.bestAttack.transactionHash}
   Profit: ${ethers.formatEther(stats.bestAttack.actualProfit)} ETH
   Margin: ${stats.bestAttack.profitMargin.toFixed(2)}%
   ` : 'No successful attacks yet'}

📉 WORST PERFORMANCE:
   ${stats.worstAttack ? `
   Transaction: ${stats.worstAttack.transactionHash}
   Profit: ${ethers.formatEther(stats.worstAttack.actualProfit)} ETH
   Margin: ${stats.worstAttack.profitMargin.toFixed(2)}%
   ` : 'No attacks recorded yet'}

🕒 RECENT ATTACKS:
${this.getRecentAttacks(5).map(attack => 
  `   ${attack.success ? '✅' : '❌'} ${attack.transactionHash.slice(0, 10)}... | ${ethers.formatEther(attack.actualProfit)} ETH`
).join('\n')}
`;

    return report;
  }

  /**
   * Parse actual profit from transaction logs
   */
  private async parseActualProfit(receipt: ethers.TransactionReceipt): Promise<bigint> {
    try {
      // In production, parse the actual profit from contract logs
      // For now, return a mock value based on gas usage
      const gasUsed = receipt.gasUsed;
      
      // Estimate profit based on gas usage (very simplified)
      if (gasUsed < ethers.parseUnits('200000', 'wei')) {
        return ethers.parseEther('0.02'); // Small profit
      } else if (gasUsed < ethers.parseUnits('400000', 'wei')) {
        return ethers.parseEther('0.05'); // Medium profit
      } else {
        return ethers.parseEther('0.08'); // Large profit
      }

    } catch (error) {
      this.logger.error(`Failed to parse actual profit: ${error}`);
      return BigInt(0);
    }
  }

  /**
   * Log attack results
   */
  private logAttackResults(metrics: AttackMetrics, vector: AttackVector): void {
    if (metrics.success) {
      this.logger.info(`🎉 Attack SUCCESS: ${vector.name}`);
      this.logger.info(`   💰 Profit: ${ethers.formatEther(metrics.actualProfit)} ETH`);
      this.logger.info(`   ⛽ Gas Cost: ${ethers.formatEther(metrics.totalGasCost)} ETH`);
      this.logger.info(`   📊 Margin: ${metrics.profitMargin.toFixed(2)}%`);
      this.logger.info(`   ⏱️  Execution: ${metrics.executionTime}ms`);
    } else {
      this.logger.error(`❌ Attack FAILED: ${vector.name}`);
      this.logger.error(`   ⛽ Gas Cost: ${ethers.formatEther(metrics.totalGasCost)} ETH`);
      this.logger.error(`   ⏱️  Execution: ${metrics.executionTime}ms`);
    }
  }

  /**
   * Generate periodic report
   */
  private generatePeriodicReport(): void {
    if (this.attackHistory.length === 0) return;

    const recentAttacks = this.getRecentAttacks(5);
    const recentSuccessRate = recentAttacks.filter(a => a.success).length / recentAttacks.length * 100;
    
    this.logger.info(`📊 Recent Performance: ${recentSuccessRate.toFixed(1)}% success rate (last 5 attacks)`);
  }

  /**
   * Clear attack history
   */
  clearHistory(): void {
    this.attackHistory = [];
    this.logger.info('📊 Attack history cleared');
  }

  /**
   * Export attack data
   */
  exportAttackData(): AttackMetrics[] {
    return [...this.attackHistory];
  }
}
