import { ethers } from 'ethers';
import { FlashloanProvider, DEXType, TradeStep } from '@shared/types';

export interface IFlashloanContract {
  executeOptimalFlashloan(
    asset: string,
    amount: string,
    params: string
  ): Promise<ethers.ContractTransaction>;

  pause(): Promise<ethers.ContractTransaction>;
  unpause(): Promise<ethers.ContractTransaction>;
  
  owner(): Promise<string>;
  paused(): Promise<boolean>;
  
  // Events
  on(event: 'FlashloanExecuted', listener: FlashloanExecutedListener): void;
  on(event: 'ArbitrageCompleted', listener: ArbitrageCompletedListener): void;
  on(event: 'EmergencyWithdraw', listener: EmergencyWithdrawListener): void;
}

export type FlashloanExecutedListener = (
  provider: FlashloanProvider,
  asset: string,
  amount: bigint,
  premium: bigint,
  event: ethers.EventLog
) => void;

export type ArbitrageCompletedListener = (
  asset: string,
  initialAmount: bigint,
  finalAmount: bigint,
  profit: bigint,
  gasUsed: bigint,
  event: ethers.EventLog
) => void;

export type EmergencyWithdrawListener = (
  token: string,
  amount: bigint,
  recipient: string,
  event: ethers.EventLog
) => void;

export interface FlashloanContractParams {
  tradeSteps: TradeStep[];
  minProfit: string;
  provider: FlashloanProvider;
  maxGasCostWei: string;
}

export interface ContractAddresses {
  aavePool: string;
  balancerVault: string;
  uniswapV3Quoter: string;
}

export interface FlashloanExecutionResult {
  success: boolean;
  transactionHash: string;
  blockNumber: number;
  gasUsed: string;
  profit?: string;
  error?: string;
}

export interface ContractCallOptions {
  gasLimit?: string;
  gasPrice?: string;
  maxFeePerGas?: string;
  maxPriorityFeePerGas?: string;
  value?: string;
  nonce?: number;
}
