export const FLASHLOAN_ARBITRAGE_LIGHT_ABI = [
  // Constructor
  {
    "inputs": [
      {"internalType": "address", "name": "_aavePool", "type": "address"},
      {"internalType": "address", "name": "_balancerVault", "type": "address"},
      {"internalType": "address", "name": "_uniswapV3Quoter", "type": "address"}
    ],
    "stateMutability": "nonpayable",
    "type": "constructor"
  },
  
  // Main execution function
  {
    "inputs": [
      {"internalType": "address", "name": "asset", "type": "address"},
      {"internalType": "uint256", "name": "amount", "type": "uint256"},
      {"internalType": "bytes", "name": "params", "type": "bytes"}
    ],
    "name": "executeOptimalFlashloan",
    "outputs": [],
    "stateMutability": "nonpayable",
    "type": "function"
  },
  
  // Aave flashloan callback
  {
    "inputs": [
      {"internalType": "address", "name": "asset", "type": "address"},
      {"internalType": "uint256", "name": "amount", "type": "uint256"},
      {"internalType": "uint256", "name": "premium", "type": "uint256"},
      {"internalType": "address", "name": "initiator", "type": "address"},
      {"internalType": "bytes", "name": "params", "type": "bytes"}
    ],
    "name": "executeOperation",
    "outputs": [{"internalType": "bool", "name": "", "type": "bool"}],
    "stateMutability": "nonpayable",
    "type": "function"
  },
  
  // Balancer flashloan callback
  {
    "inputs": [
      {"internalType": "contract IERC20[]", "name": "tokens", "type": "address[]"},
      {"internalType": "uint256[]", "name": "amounts", "type": "uint256[]"},
      {"internalType": "uint256[]", "name": "feeAmounts", "type": "uint256[]"},
      {"internalType": "bytes", "name": "userData", "type": "bytes"}
    ],
    "name": "receiveFlashLoan",
    "outputs": [],
    "stateMutability": "nonpayable",
    "type": "function"
  },
  
  // Owner functions
  {
    "inputs": [],
    "name": "owner",
    "outputs": [{"internalType": "address", "name": "", "type": "address"}],
    "stateMutability": "view",
    "type": "function"
  },
  
  {
    "inputs": [{"internalType": "address", "name": "newOwner", "type": "address"}],
    "name": "transferOwnership",
    "outputs": [],
    "stateMutability": "nonpayable",
    "type": "function"
  },
  
  // Pause functions
  {
    "inputs": [],
    "name": "pause",
    "outputs": [],
    "stateMutability": "nonpayable",
    "type": "function"
  },
  
  {
    "inputs": [],
    "name": "unpause",
    "outputs": [],
    "stateMutability": "nonpayable",
    "type": "function"
  },
  
  {
    "inputs": [],
    "name": "paused",
    "outputs": [{"internalType": "bool", "name": "", "type": "bool"}],
    "stateMutability": "view",
    "type": "function"
  },
  
  // Emergency functions
  {
    "inputs": [
      {"internalType": "address", "name": "token", "type": "address"},
      {"internalType": "uint256", "name": "amount", "type": "uint256"}
    ],
    "name": "emergencyWithdraw",
    "outputs": [],
    "stateMutability": "nonpayable",
    "type": "function"
  },
  
  // View functions
  {
    "inputs": [],
    "name": "AAVE_POOL",
    "outputs": [{"internalType": "contract IAavePool", "name": "", "type": "address"}],
    "stateMutability": "view",
    "type": "function"
  },
  
  {
    "inputs": [],
    "name": "BALANCER_VAULT",
    "outputs": [{"internalType": "contract IBalancerVault", "name": "", "type": "address"}],
    "stateMutability": "view",
    "type": "function"
  },
  
  {
    "inputs": [],
    "name": "UNISWAP_V3_QUOTER",
    "outputs": [{"internalType": "contract IUniswapV3Quoter", "name": "", "type": "address"}],
    "stateMutability": "view",
    "type": "function"
  },
  
  // Events
  {
    "anonymous": false,
    "inputs": [
      {"indexed": true, "internalType": "enum FlashloanProvider", "name": "provider", "type": "uint8"},
      {"indexed": true, "internalType": "address", "name": "asset", "type": "address"},
      {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"},
      {"indexed": false, "internalType": "uint256", "name": "premium", "type": "uint256"}
    ],
    "name": "FlashloanExecuted",
    "type": "event"
  },
  
  {
    "anonymous": false,
    "inputs": [
      {"indexed": true, "internalType": "address", "name": "asset", "type": "address"},
      {"indexed": false, "internalType": "uint256", "name": "initialAmount", "type": "uint256"},
      {"indexed": false, "internalType": "uint256", "name": "finalAmount", "type": "uint256"},
      {"indexed": false, "internalType": "uint256", "name": "profit", "type": "uint256"},
      {"indexed": false, "internalType": "uint256", "name": "gasUsed", "type": "uint256"}
    ],
    "name": "ArbitrageCompleted",
    "type": "event"
  },
  
  {
    "anonymous": false,
    "inputs": [
      {"indexed": true, "internalType": "address", "name": "token", "type": "address"},
      {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"},
      {"indexed": true, "internalType": "address", "name": "recipient", "type": "address"}
    ],
    "name": "EmergencyWithdraw",
    "type": "event"
  },
  
  {
    "anonymous": false,
    "inputs": [{"indexed": false, "internalType": "address", "name": "account", "type": "address"}],
    "name": "Paused",
    "type": "event"
  },
  
  {
    "anonymous": false,
    "inputs": [{"indexed": false, "internalType": "address", "name": "account", "type": "address"}],
    "name": "Unpaused",
    "type": "event"
  }
] as const;
