import { Injectable, OnModuleInit } from '@nestjs/common';
import { ethers } from 'ethers';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { ContractFactoryService } from './contract-factory.service';
import { LoggerService } from '@shared/services/logger.service';
import { ArbitrageConfigService } from '@config/arbitrage-config.service';
import { 
  FlashloanContractParams, 
  FlashloanExecutionResult,
  ContractCallOptions,
  IFlashloanContract 
} from '../interfaces/flashloan-contract.interface';
import { FlashloanProvider, DEXType, TradeStep } from '@shared/types';

@Injectable()
export class FlashloanContractService implements OnModuleInit {
  private contract: ethers.Contract | null = null;
  private contractAddress: string;

  constructor(
    private readonly contractFactory: ContractFactoryService,
    private readonly logger: LoggerService,
    private readonly arbitrageConfig: ArbitrageConfigService,
    private readonly eventEmitter: EventEmitter2,
  ) {
    this.contractAddress = this.arbitrageConfig.flashloanContractAddress;
  }

  async onModuleInit() {
    await this.initializeContract();
  }

  private async initializeContract() {
    try {
      this.logger.info(`Initializing flashloan contract at ${this.contractAddress}`);
      
      // Contract will be initialized when provider is set
      this.logger.info(`Flashloan contract service initialized`);
    } catch (error) {
      this.logger.error(`Failed to initialize flashloan contract`, error);
      throw error;
    }
  }

  setProvider(signerOrProvider: ethers.Signer | ethers.Provider) {
    try {
      this.contract = this.contractFactory.createFlashloanContract(
        this.contractAddress,
        signerOrProvider
      );
      
      this.setupEventListeners();
      this.logger.info(`Flashloan contract provider set successfully`);
    } catch (error) {
      this.logger.error(`Failed to set contract provider`, error);
      throw error;
    }
  }

  private setupEventListeners() {
    if (!this.contract) return;

    // Listen to FlashloanExecuted events
    this.contract.on('FlashloanExecuted', (provider, asset, amount, premium, event) => {
      this.logger.flashloan(`Flashloan executed: ${ethers.formatEther(amount)} ${asset}`);
      this.eventEmitter.emit('flashloan.executed', {
        provider,
        asset,
        amount: amount.toString(),
        premium: premium.toString(),
        transactionHash: event.transactionHash,
        blockNumber: event.blockNumber,
      });
    });

    // Listen to ArbitrageCompleted events
    this.contract.on('ArbitrageCompleted', (asset, initialAmount, finalAmount, profit, gasUsed, event) => {
      const profitEth = ethers.formatEther(profit);
      this.logger.arbitrage(`Arbitrage completed: ${profitEth} ETH profit`);
      this.eventEmitter.emit('arbitrage.completed', {
        asset,
        initialAmount: initialAmount.toString(),
        finalAmount: finalAmount.toString(),
        profit: profit.toString(),
        gasUsed: gasUsed.toString(),
        transactionHash: event.transactionHash,
        blockNumber: event.blockNumber,
      });
    });

    // Listen to EmergencyWithdraw events
    this.contract.on('EmergencyWithdraw', (token, amount, recipient, event) => {
      this.logger.warn(`Emergency withdraw: ${ethers.formatEther(amount)} ${token} to ${recipient}`);
      this.eventEmitter.emit('contract.emergency_withdraw', {
        token,
        amount: amount.toString(),
        recipient,
        transactionHash: event.transactionHash,
        blockNumber: event.blockNumber,
      });
    });
  }

  async executeFlashloan(
    asset: string,
    amount: string,
    params: FlashloanContractParams,
    options?: ContractCallOptions,
  ): Promise<FlashloanExecutionResult> {
    if (!this.contract) {
      throw new Error('Contract not initialized. Call setProvider() first.');
    }

    try {
      this.logger.flashloan(`Executing flashloan: ${ethers.formatEther(amount)} ${asset}`);
      
      // Encode parameters
      const encodedParams = this.encodeArbitrageParams(params);
      
      // Prepare transaction options
      const txOptions: any = {};
      if (options?.gasLimit) txOptions.gasLimit = options.gasLimit;
      if (options?.gasPrice) txOptions.gasPrice = options.gasPrice;
      if (options?.maxFeePerGas) txOptions.maxFeePerGas = options.maxFeePerGas;
      if (options?.maxPriorityFeePerGas) txOptions.maxPriorityFeePerGas = options.maxPriorityFeePerGas;
      if (options?.nonce !== undefined) txOptions.nonce = options.nonce;

      // Execute transaction
      const tx = await this.contract.executeOptimalFlashloan(
        asset,
        amount,
        encodedParams,
        txOptions
      );

      this.logger.flashloan(`Transaction submitted: ${tx.hash}`);

      // Wait for confirmation
      const receipt = await tx.wait();

      const result: FlashloanExecutionResult = {
        success: receipt.status === 1,
        transactionHash: receipt.transactionHash,
        blockNumber: receipt.blockNumber,
        gasUsed: receipt.gasUsed.toString(),
      };

      if (result.success) {
        this.logger.flashloan(`Flashloan executed successfully`);
      } else {
        this.logger.error(`Flashloan execution failed`);
      }

      return result;

    } catch (error) {
      this.logger.error(`Flashloan execution failed`, error);
      return {
        success: false,
        transactionHash: '',
        blockNumber: 0,
        gasUsed: '0',
        error: error.message,
      };
    }
  }

  private encodeArbitrageParams(params: FlashloanContractParams): string {
    try {
      // Create the struct that matches the Solidity ArbitrageParams
      const encodedParams = ethers.AbiCoder.defaultAbiCoder().encode(
        [
          'tuple(tuple(address dex, uint8 dexType, address tokenIn, address tokenOut, uint256 slippageToleranceBps, uint24 v3Fee, bytes32 balancerPoolId)[] tradeSteps, uint256 minProfit, uint8 provider, uint256 maxGasCostWei)'
        ],
        [
          {
            tradeSteps: params.tradeSteps.map(step => ({
              dex: step.dex,
              dexType: this.getDexTypeEnum(step.dexType),
              tokenIn: step.tokenIn,
              tokenOut: step.tokenOut,
              slippageToleranceBps: step.slippageToleranceBps,
              v3Fee: step.v3Fee || 0,
              balancerPoolId: step.balancerPoolId || '0x0000000000000000000000000000000000000000000000000000000000000000'
            })),
            minProfit: params.minProfit,
            provider: params.provider === FlashloanProvider.AAVE ? 0 : 1,
            maxGasCostWei: params.maxGasCostWei
          }
        ]
      );

      return encodedParams;
    } catch (error) {
      this.logger.error(`Failed to encode arbitrage parameters`, error);
      throw error;
    }
  }

  private getDexTypeEnum(dexType: DEXType): number {
    switch (dexType) {
      case DEXType.V2: return 0;
      case DEXType.V3: return 1;
      case DEXType.CURVE: return 2;
      case DEXType.BALANCER_V2: return 3;
      default: return 0;
    }
  }

  async pauseContract(options?: ContractCallOptions): Promise<ethers.ContractTransaction> {
    if (!this.contract) {
      throw new Error('Contract not initialized');
    }

    const txOptions: any = {};
    if (options?.gasLimit) txOptions.gasLimit = options.gasLimit;
    if (options?.gasPrice) txOptions.gasPrice = options.gasPrice;

    return await this.contract.pause(txOptions);
  }

  async unpauseContract(options?: ContractCallOptions): Promise<ethers.ContractTransaction> {
    if (!this.contract) {
      throw new Error('Contract not initialized');
    }

    const txOptions: any = {};
    if (options?.gasLimit) txOptions.gasLimit = options.gasLimit;
    if (options?.gasPrice) txOptions.gasPrice = options.gasPrice;

    return await this.contract.unpause(txOptions);
  }

  async isContractPaused(): Promise<boolean> {
    if (!this.contract) {
      throw new Error('Contract not initialized');
    }

    return await this.contract.paused();
  }

  async getContractOwner(): Promise<string> {
    if (!this.contract) {
      throw new Error('Contract not initialized');
    }

    return await this.contract.owner();
  }

  getContractAddress(): string {
    return this.contractAddress;
  }

  isInitialized(): boolean {
    return this.contract !== null;
  }
}
