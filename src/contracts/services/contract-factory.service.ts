import { Injectable } from '@nestjs/common';
import { ethers } from 'ethers';
import { AbiService } from './abi.service';
import { LoggerService } from '@shared/services/logger.service';

@Injectable()
export class ContractFactoryService {
  constructor(
    private readonly abiService: AbiService,
    private readonly logger: LoggerService,
  ) {}

  createFlashloanContract(
    address: string,
    signerOrProvider: ethers.Signer | ethers.Provider,
  ): ethers.Contract {
    try {
      const abi = this.abiService.getFlashloanArbitrageLightAbi();
      const contract = new ethers.Contract(address, abi, signerOrProvider);
      
      this.logger.debug(`Created flashloan contract instance`, 'ContractFactory', {
        address,
        hasProvider: !!signerOrProvider,
      });
      
      return contract;
    } catch (error) {
      this.logger.error(`Failed to create flashloan contract`, error, 'ContractFactory');
      throw error;
    }
  }

  createERC20Contract(
    address: string,
    signerOrProvider: ethers.Signer | ethers.Provider,
  ): ethers.Contract {
    try {
      const abi = this.abiService.getERC20Abi();
      const contract = new ethers.Contract(address, abi, signerOrProvider);
      
      this.logger.debug(`Created ERC20 contract instance`, 'ContractFactory', {
        address,
      });
      
      return contract;
    } catch (error) {
      this.logger.error(`Failed to create ERC20 contract`, error, 'ContractFactory');
      throw error;
    }
  }

  createUniswapV3QuoterContract(
    address: string,
    provider: ethers.Provider,
  ): ethers.Contract {
    try {
      const abi = this.abiService.getUniswapV3QuoterAbi();
      const contract = new ethers.Contract(address, abi, provider);
      
      this.logger.debug(`Created Uniswap V3 Quoter contract instance`, 'ContractFactory', {
        address,
      });
      
      return contract;
    } catch (error) {
      this.logger.error(`Failed to create Uniswap V3 Quoter contract`, error, 'ContractFactory');
      throw error;
    }
  }

  async validateContractAddress(
    address: string,
    provider: ethers.Provider,
  ): Promise<boolean> {
    try {
      const code = await provider.getCode(address);
      const isContract = code !== '0x';
      
      this.logger.debug(`Contract validation result`, 'ContractFactory', {
        address,
        isContract,
        codeLength: code.length,
      });
      
      return isContract;
    } catch (error) {
      this.logger.error(`Failed to validate contract address`, error, 'ContractFactory');
      return false;
    }
  }

  async getContractOwner(
    contract: ethers.Contract,
  ): Promise<string | null> {
    try {
      const owner = await contract.owner();
      return owner;
    } catch (error) {
      this.logger.warn(`Contract does not have owner() function`, 'ContractFactory');
      return null;
    }
  }

  async isContractPaused(
    contract: ethers.Contract,
  ): Promise<boolean | null> {
    try {
      const paused = await contract.paused();
      return paused;
    } catch (error) {
      this.logger.warn(`Contract does not have paused() function`, 'ContractFactory');
      return null;
    }
  }
}
