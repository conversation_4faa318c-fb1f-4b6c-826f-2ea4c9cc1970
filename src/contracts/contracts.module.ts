import { Module } from '@nestjs/common';
import { ConfigurationModule } from '@config/configuration.module';
import { FlashloanContractService } from './services/flashloan-contract.service';
import { ContractFactoryService } from './services/contract-factory.service';
import { AbiService } from './services/abi.service';

@Module({
  imports: [ConfigurationModule],
  providers: [
    FlashloanContractService,
    ContractFactoryService,
    AbiService,
  ],
  exports: [
    FlashloanContractService,
    ContractFactoryService,
    AbiService,
  ],
})
export class ContractsModule {}
