import { NestFactory } from '@nestjs/core';
import { ValidationPipe } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { AppModule } from './app.module';
import { Logger } from '@nestjs/common';

async function bootstrap() {
  const logger = new Logger('Bootstrap');
  
  try {
    const app = await NestFactory.create(AppModule, {
      logger: ['error', 'warn', 'log', 'debug', 'verbose'],
    });

    // Get configuration service
    const configService = app.get(ConfigService);
    const port = configService.get<number>('PORT', 3000);
    const nodeEnv = configService.get<string>('NODE_ENV', 'development');

    // Enable CORS for development
    if (nodeEnv === 'development') {
      app.enableCors({
        origin: true,
        credentials: true,
      });
    }

    // Global validation pipe
    app.useGlobalPipes(
      new ValidationPipe({
        transform: true,
        whitelist: true,
        forbidNonWhitelisted: true,
      }),
    );

    // Graceful shutdown
    app.enableShutdownHooks();

    await app.listen(port);
    
    logger.log(`🚀 Flashloan Arbitrage Bot started successfully`);
    logger.log(`🌐 GraphQL Playground: http://localhost:${port}/graphql`);
    logger.log(`📊 Dashboard: http://localhost:${port}/dashboard`);
    logger.log(`🔧 Environment: ${nodeEnv}`);
    
  } catch (error) {
    logger.error('Failed to start application', error);
    process.exit(1);
  }
}

bootstrap();
