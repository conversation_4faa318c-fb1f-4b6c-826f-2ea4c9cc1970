import { ethers } from 'ethers';
import { Token } from '../types';
import { config } from '../config';
import { logger } from '../utils/logger';

export class TokenRegistry {
  private provider: ethers.JsonRpcProvider;
  private cache: Map<string, Token> = new Map();

  constructor(provider?: ethers.JsonRpcProvider) {
    this.provider = provider || new ethers.JsonRpcProvider(config.rpcUrl);
  }

  async getToken(address: string): Promise<Token | null> {
    const key = address.toLowerCase();
    if (this.cache.has(key)) return this.cache.get(key)!;

    try {
      const erc20 = new ethers.Contract(address, [
        'function symbol() view returns (string)',
        'function decimals() view returns (uint8)',
        'function name() view returns (string)'
      ], this.provider);

      const [symbol, decimals, name] = await Promise.all([
        erc20.symbol(), erc20.decimals(), erc20.name()
      ]);

      const token: Token = { address: key, symbol, decimals, name };
      this.cache.set(key, token);
      return token;
    } catch (err) {
      logger.debug('TokenRegistry.getToken failed', err);
      return null;
    }
  }
}

