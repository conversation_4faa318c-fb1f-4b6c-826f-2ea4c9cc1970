import {EventEmitter} from 'events';
import {ethers} from 'ethers';
import {FlashbotsBundleProvider} from '@flashbots/ethers-provider-bundle';
import {RPCManager} from '../providers/rpcManager';
import {MempoolMonitor} from '../mempool/monitor';
import {BlockData, BlockEventMonitor} from '../events/blockEventMonitor';
import {SandwichStrategy} from '../strategies/sandwich';
import {ArbitrageStrategy} from '../strategies/arbitrage';
import {FlashloanStrategy} from '../strategies/flashloan';
import {MEVShareFlashloanStrategy} from '../strategies/mev-share-flashloan';
import {UniswapV3FlashSwapStrategy} from '../strategies/uniswap-v3-flash';
import {DynamicFlashloanStrategy} from '../strategies/dynamic-flashloan';
import {MEVShareEventMonitor} from '../mev-share/event-monitor';
import {MultiDexMonitor} from '../services/multi-dex-monitor';
import { MAINNET_ADDRESSES } from '../config';
import {FlashbotsBundleManager} from '../flashbots/bundle-provider';
import {AdvancedGasEstimator} from '../gas/advanced-estimator';
import {FlashbotsExecutor} from '../execution/flashbots-executor';
import {BundleSimulator} from '../simulation/simulator';
import {GasOptimizer} from '../gas/optimizer';
import {statusDashboard} from '../utils/statusDashboard';
import {config, validateConfig} from '../config';
import {logger} from '../utils/logger';
import {ContractVerificationService} from '../utils/contractVerification';
import {bundleAnalyzer} from '../utils/bundle-analyzer';

import {performanceMonitor} from '../utils/performance-monitor';
import {BotState, Bundle, MEVOpportunity, RiskMetrics, Transaction} from '../types';

export class MEVBot extends EventEmitter {
    private rpcManager: RPCManager;
    private provider: ethers.JsonRpcProvider;
    private wallet: ethers.Wallet;
    private flashbotsProvider: FlashbotsBundleProvider | null = null;

    private mempoolMonitor: MempoolMonitor;
    private blockEventMonitor: BlockEventMonitor;
    private sandwichStrategy: SandwichStrategy;
    private arbitrageStrategy: ArbitrageStrategy;
    private flashloanStrategy: FlashloanStrategy;
    private mevShareFlashloanStrategy: MEVShareFlashloanStrategy | null = null;
    private uniswapV3FlashSwapStrategy: UniswapV3FlashSwapStrategy;
    private dynamicFlashloanStrategy: DynamicFlashloanStrategy;
    private mevShareMonitor: MEVShareEventMonitor | null = null;
    private multiDexMonitor: MultiDexMonitor | null = null;
    private flashbotsManager: FlashbotsBundleManager;
    private advancedGasEstimator: AdvancedGasEstimator;
    private flashbotsExecutor: FlashbotsExecutor;
    private simulator: BundleSimulator;
    private gasOptimizer: GasOptimizer;

    private state: BotState;
    private riskMetrics: RiskMetrics;
    private opportunities: MEVOpportunity[] = [];
    private executedBundles: Bundle[] = [];
    private executingOpportunities = new Set<string>();
    private lastExecutionTime = 0;
    private processedOpportunitiesThisBlock = new Set<string>();
    private processedTransactionsThisBlock = new Set<string>();
    private currentBlockNumber = 0;

    private readonly MAX_OPPORTUNITIES = 1000;
    private readonly EXECUTION_COOLDOWN = 5000; // 5 seconds between executions

    constructor() {
        super();

        // Initialize RPC manager and providers
        this.rpcManager = new RPCManager(config.chainId);
        this.provider = this.rpcManager.getHttpProvider();
        this.wallet = new ethers.Wallet(config.privateKey, this.provider);

        // Initialize components
        this.mempoolMonitor = new MempoolMonitor();
        this.blockEventMonitor = new BlockEventMonitor();
        this.sandwichStrategy = new SandwichStrategy();
        this.arbitrageStrategy = new ArbitrageStrategy();
        this.flashloanStrategy = new FlashloanStrategy(this.provider);
        this.uniswapV3FlashSwapStrategy = new UniswapV3FlashSwapStrategy(this.provider, this.wallet);
        this.simulator = new BundleSimulator();
        this.gasOptimizer = new GasOptimizer();

        // Initialize Flashbots and advanced gas estimation
        this.flashbotsManager = new FlashbotsBundleManager(this.provider, this.wallet);
        this.advancedGasEstimator = new AdvancedGasEstimator(this.provider);
        this.flashbotsExecutor = new FlashbotsExecutor(
            this.provider,
            this.wallet,
            this.flashbotsManager,
            this.advancedGasEstimator,
            this.gasOptimizer
        );

        // Initialize dynamic flashloan strategy
        this.dynamicFlashloanStrategy = new DynamicFlashloanStrategy(
            this.provider,
            this.wallet,
            this.flashbotsManager,
            this.flashbotsExecutor,
            this.gasOptimizer
        );

        // Initialize MEV-Share if enabled
        if (config.enableMevShare) {
            this.mevShareMonitor = new MEVShareEventMonitor(this.provider);
            this.mevShareFlashloanStrategy = new MEVShareFlashloanStrategy(
                this.provider,
                this.mevShareMonitor,
                this.flashbotsManager
            );

            // Integrate MEV-Share strategy with dynamic strategy
            this.dynamicFlashloanStrategy.setMEVShareStrategy(this.mevShareFlashloanStrategy);
        }

        // Initialize Multi-DEX monitor if enabled
        if (config.enableMultiDexArbitrage) {
            this.multiDexMonitor = new MultiDexMonitor(this.provider, config);
            this.setupMultiDexEventListeners();
        }

        // Initialize state
        this.state = {
            isRunning: false,
            totalProfit: BigInt(0),
            successfulTrades: 0,
            failedTrades: 0,
            lastActivity: 0,
            emergencyStop: config.emergencyStop
        };

        this.riskMetrics = {
            maxDrawdown: BigInt(0),
            winRate: 0,
            averageProfit: BigInt(0),
            totalGasSpent: BigInt(0),
            profitFactor: 0
        };

        this.setupEventListeners();
        this.setupMempoolFilters();
    }


    private setupEventListeners(): void {
        // Listen for relevant transactions from mempool
        this.mempoolMonitor.on('pendingTransaction', async (transaction: any) => {
            await this.handleRelevantTransaction(transaction);
        });

        // Mempool status events
        this.mempoolMonitor.on('started', () => logger.system('Mempool monitoring started'));
        this.mempoolMonitor.on('stopped', () => logger.system('Mempool monitoring stopped'));

        // Error handling
        this.mempoolMonitor.on('error', (error) => {
            logger.logError(error, 'MempoolMonitor');
            this.handleError(error);
        });

        // Listen for opportunities found
        this.on('opportunityFound', async (opportunity: any) => {
            await this.handleOpportunityFound(opportunity);
        });

        this.on('bundleExecuted', this.handleBundleExecuted.bind(this));
        this.on('error', this.handleError.bind(this));
    }

    /**
     * Setup Multi-DEX event listeners
     */
    private setupMultiDexEventListeners(): void {
        if (!this.multiDexMonitor) return;

        // Listen for price updates
        this.multiDexMonitor.on('priceUpdate', (priceData: any) => {
            if (config.debugMode) {
                logger.debug('Multi-DEX price update', {
                    pair: priceData.pair,
                    dex: priceData.dex,
                    price: priceData.price.toFixed(6)
                });
            }
        });

        // Listen for arbitrage opportunities
        this.multiDexMonitor.on('arbitrageOpportunity', async (opportunity: any) => {
            logger.info('Multi-DEX arbitrage opportunity detected', {
                pair: opportunity.pair,
                buyDex: opportunity.buyDex,
                sellDex: opportunity.sellDex,
                profitBps: opportunity.profitBps,
                estimatedProfitUSD: opportunity.estimatedProfitUSD.toFixed(2),
                confidence: opportunity.confidence
            });

            // Execute the arbitrage opportunity using the dynamic flashloan strategy
            await this.executeMultiDexArbitrage(opportunity);
        });
    }

    /**
     * Setup meaningful mempool filters for flashloan attacks
     */
    private setupMempoolFilters(): void {
        // Get target tokens from config
        const targetTokens = this.getFlashloanTargetTokens();
        const targetPools = this.getFlashloanTargetPools();

        // Filter 1: High-value transactions for flashloan opportunities
        const highValueFilter = {
            minValue: ethers.parseEther('0.01'), // Reduced from 0.1 to 0.01 ETH
            maxGasPrice: ethers.parseUnits('300', 'gwei'), // Increased from 200 to 300 gwei
            targetTokens: targetTokens,
            targetPools: targetPools,
            excludeAddresses: [
                // Exclude known MEV bots and flashloan contracts to avoid competition
                '******************************************', // OpenSea
                '******************************************', // Uniswap V2 Router
                '******************************************', // Uniswap V3 Router
            ]
        };

        // Filter 2: DEX arbitrage opportunities (more permissive)
        const dexArbitrageFilter = {
            minValue: ethers.parseEther('0.001'), // Reduced from 0.01 to 0.001 ETH
            maxGasPrice: ethers.parseUnits('250', 'gwei'), // Increased from 150 to 250 gwei
            targetTokens: [
                // Major stablecoins and tokens for arbitrage
                '0xA0b86a33E6441b8C4505B8C4505B8C4505B8C4505', // USDC
                '******************************************', // DAI
                '******************************************', // USDT
                '******************************************', // WETH
                '******************************************', // WBTC
            ],
            targetPools: targetPools,
            excludeAddresses: []
        };

        // Filter 3: Large swap transactions that create arbitrage opportunities
        const largeSwapFilter = {
            minValue: ethers.parseEther('0.1'), // Reduced from 1.0 to 0.1 ETH
            maxGasPrice: ethers.parseUnits('400', 'gwei'), // Increased from 300 to 400 gwei
            targetTokens: targetTokens,
            targetPools: [
                // Major DEX pools
                '******************************************', // USDC/WETH 0.05%
                '******************************************', // USDC/WETH 0.3%
                '******************************************', // WBTC/WETH 0.3%
                '******************************************', // WBTC/WETH 0.05%
            ],
            excludeAddresses: []
        };

        // Add filters to mempool monitor
        this.mempoolMonitor.addFilter(highValueFilter);
        this.mempoolMonitor.addFilter(dexArbitrageFilter);
        this.mempoolMonitor.addFilter(largeSwapFilter);

        logger.system('Mempool filters configured for flashloan attacks', {
            filtersCount: 3,
            targetTokensCount: targetTokens.length,
            targetPoolsCount: targetPools.length,
            highValueMinETH: '0.01',
            arbitrageMinETH: '0.001',
            largeSwapMinETH: '0.1'
        });
    }

    /**
     * Get target tokens for flashloan attacks based on network
     */
    private getFlashloanTargetTokens(): string[] {
        const isMainnet = config.chainId === 1;

        if (isMainnet) {
            return [
                '0xA0b86a33E6441b8C4505B8C4505B8C4505B8C4505', // USDC
                '******************************************', // DAI
                '******************************************', // USDT
                '******************************************', // WETH
                '******************************************', // WBTC
                '******************************************', // UNI
                '******************************************', // AAVE
                '******************************************', // LINK
            ];
        } else {
            // Sepolia testnet tokens
            return [
                '******************************************', // UNI (if available)
                '******************************************', // LINK (Sepolia)
                '******************************************', // WETH (Sepolia)
            ];
        }
    }

    /**
     * Get target pools for flashloan attacks based on network
     */
    private getFlashloanTargetPools(): string[] {
        const isMainnet = config.chainId === 1;

        if (isMainnet) {
            return [
                // Uniswap V3 pools
                '******************************************', // USDC/WETH 0.05%
                '******************************************', // USDC/WETH 0.3%
                '******************************************', // WBTC/WETH 0.3%
                '******************************************', // WBTC/WETH 0.05%
                '******************************************', // DAI/USDC 0.01%
                '******************************************', // DAI/USDC 0.05%

                // Uniswap V2 pairs
                '******************************************', // USDC/WETH
                '******************************************', // DAI/WETH
                '******************************************', // WETH/USDT

                // Curve pools
                '******************************************', // 3Pool (DAI/USDC/USDT)
                '******************************************', // sUSD Pool

                // Balancer pools
                '******************************************', // BAL/WETH 80/20
                '******************************************', // WETH/USDC 50/50
            ];
        } else {
            // Sepolia testnet pools (limited)
            return [
                '******************************************', // WETH (Sepolia)
            ];
        }
    }

    async start(): Promise<void> {
        if (this.state.isRunning) {
            logger.warn('MEV Bot is already running');
            return;
        }

        try {
            logger.clear();
            logger.system('🚀 Starting Advanced MEV Bot...', {liquidity: '109.601634'});

            // Initialize status dashboard
            const networkName = config.chainId === 1 ? 'Mainnet' :
                config.chainId === 11155111 ? 'Sepolia' : 'Testnet';

            // Get current block number
            const currentBlock = await this.provider.getBlockNumber();
            this.currentBlockNumber = currentBlock; // Initialize current block number
            statusDashboard.updateNetworkStatus(currentBlock, networkName);
            statusDashboard.updateStrategyStatus(
                config.enableFlashloanAttacks,
                config.enableMevShare,
                config.enableArbitrage
            );

            // Update dashboard with current configuration
            // Use WETH as primary if user only has ETH, otherwise use configured token
            const primaryToken = config.flashloanPrimaryToken || 'WETH'; // Changed from USDC to WETH
            const defaultTargets = primaryToken === 'WETH' ? ['USDC', 'DAI', 'USDT'] : ['WETH', 'DAI', 'USDT'];

            statusDashboard.updateConfiguration({
                primaryToken: primaryToken,
                targetTokens: Array.isArray(config.flashloanTargetTokens)
                    ? config.flashloanTargetTokens
                    : (config.flashloanTargetTokens ? [config.flashloanTargetTokens] : defaultTargets),
                enableAllTokenPairs: config.enableAllTokenPairs,
                flashloanDexPairs: config.flashloanDexPairs || ['CURVE', 'UNISWAP_V3'],
                buyDex: config.flashloanBuyDex || 'CURVE',
                sellDex: config.flashloanSellDex || 'UNISWAP_V3',
                enableCrossDex: config.enableCrossDexArbitrage,
                minProfitWei: config.minProfitWei?.toString() || '0',
                minArbitrageSpread: (config.minArbitrageSpread || 0).toString() + '%',
                minBackrunProfitEth: config.minBackrunProfitEth?.toString() || '0.01',
                maxPositionSizeEth: config.maxPositionSizeEth?.toString() || '10',
                maxGasCostEth: config.maxGasCostEth?.toString() || '0.02',
                slippageTolerance: ((config.slippageTolerance || 0.01) * 100).toString(),
                arbitrageScanInterval: 'Event-driven (WebSocket)',
                flashloanScanInterval: 'Event-driven (WebSocket)',
                chainId: config.chainId,
                dryRun: config.dryRun
            });

            // Validate configuration
            logger.system('🔧 Validating configuration...');
            validateConfig();
            logger.system('✅ Configuration validation completed');

            // Verify flashloan contract deployments
            logger.system('🔍 Starting contract verification...');
            try {
                const contractVerifier = new ContractVerificationService(this.provider);
                await contractVerifier.verifyAllContracts();
            } catch (error) {
                logger.error('Contract verification failed', error);
            }

            // Initialize Flashbots
            await this.initializeFlashbots();

            // Initialize MEV-Share if enabled
            if (config.enableMevShare && this.mevShareMonitor && this.mevShareFlashloanStrategy) {
                logger.system('🔄 Initializing MEV-Share integration...');
                await this.initializeMEVShare();
            }

            // Check wallet balance
            await this.checkWalletBalance();

            // Validate pools and fetch liquidity on startup
            await this.validatePoolsAndLiquidity();

            // Start mempool monitoring
            logger.system('Starting mempool monitoring...');
            await this.mempoolMonitor.start();

            // Start block event monitoring
            logger.system('Starting block event monitoring...');
            await this.blockEventMonitor.initialize();
            await this.blockEventMonitor.start();

            // Setup event-driven arbitrage monitoring
            logger.system('Setting up event-driven arbitrage monitoring...');
            logger.system('   Trigger: WebSocket events (new blocks + pending transactions)');
            this.setupEventDrivenArbitrage();

            // Setup event-driven flashloan monitoring (independent of arbitrage)
            logger.system('Setting up event-driven flashloan monitoring...');
            logger.system('   Trigger: WebSocket events (new blocks + pending transactions)');
            this.setupEventDrivenFlashloan();

            // Start multi-DEX monitoring if enabled
            if (config.enableMultiDexArbitrage && this.multiDexMonitor) {
                logger.system('Starting multi-DEX price monitoring...');
                logger.system(`   Pairs: ${config.multiDexPairs.join(', ')}`);
                logger.system(`   DEXs: ${config.multiDexSources.join(', ')}`);
                await this.multiDexMonitor.startMonitoring();
            }

            // Setup event-driven block tracking
            this.setupEventDrivenBlockTracking();

            // Setup ETH balance monitoring
            this.setupBalanceMonitoring();

            // Setup bundle analysis reporting
            this.setupBundleAnalysisReporting();

            // Start performance monitoring
            logger.system('Starting performance monitoring...');
            performanceMonitor.start(2000); // Monitor every 2 seconds

            this.state.isRunning = true;
            this.state.lastActivity = Date.now();

            logger.info('MEV Bot started successfully');

            // Start dashboard after a short delay to let initial logs show
            statusDashboard.start();

            this.emit('started');
        } catch (error) {
            logger.error('Failed to start MEV Bot', error);
            throw error;
        }
    }

    async stop(): Promise<void> {
        if (!this.state.isRunning) {
            logger.warn('MEV Bot is not running');
            return;
        }

        logger.info('Shutting down MEV Bot...');

        // Stop performance monitoring
        performanceMonitor.stop();

        // Shutdown arbitrage strategy workers first
        if (this.arbitrageStrategy) {
            await this.arbitrageStrategy.shutdown();
        }

        try {
            logger.info('🛑 Stopping MEV Bot...');

            this.state.isRunning = false;

            // Stop status dashboard first
            try {
                statusDashboard.stop();
            } catch (error) {
                logger.debug('Error stopping status dashboard:', error);
            }

            // Stop mempool monitoring
            try {
                await Promise.race([
                    this.mempoolMonitor.stop(),
                    new Promise((_, reject) =>
                        setTimeout(() => reject(new Error('Mempool stop timeout')), 5000)
                    )
                ]);
            } catch (error) {
                logger.debug('Error stopping mempool monitor:', error);
            }

            // Stop MEV-Share monitoring if enabled
            if (this.mevShareFlashloanStrategy) {
                try {
                    await Promise.race([
                        this.mevShareFlashloanStrategy.stopMonitoring(),
                        new Promise((_, reject) =>
                            setTimeout(() => reject(new Error('MEV-Share stop timeout')), 3000)
                        )
                    ]);
                } catch (error) {
                    logger.debug('Error stopping MEV-Share monitoring:', error);
                }
            }

            // Stop multi-DEX monitoring if enabled
            if (this.multiDexMonitor) {
                try {
                    await Promise.race([
                        this.multiDexMonitor.stopMonitoring(),
                        new Promise((_, reject) =>
                            setTimeout(() => reject(new Error('Multi-DEX stop timeout')), 3000)
                        )
                    ]);
                } catch (error) {
                    logger.debug('Error stopping multi-DEX monitoring:', error);
                }
            }

            // Remove event listeners for arbitrage and block tracking
            // (Event listeners are automatically cleaned up when components stop)

            // Clear opportunities
            this.opportunities = [];

            // Force cleanup any remaining timers
            this.forceCleanup();

            logger.info('✅ MEV Bot stopped successfully');
            this.emit('stopped');
        } catch (error) {
            logger.logError(error as Error, 'MEVBot.stop');
            throw error; // Re-throw to allow caller to handle
        }
    }

    private forceCleanup(): void {
        try {
            // Clear any remaining intervals/timeouts
            const highestTimeoutId = setTimeout(() => {
            }, 0) as unknown as number;
            for (let i = 0; i <= highestTimeoutId; i++) {
                clearTimeout(i);
                clearInterval(i);
            }
        } catch (error) {
            logger.debug('Error during force cleanup:', error);
        }
    }

    private async initializeFlashbots(): Promise<void> {
        try {
            logger.system('🔧 Initializing Flashbots integration...');

            // Initialize Flashbots bundle manager
            await this.flashbotsManager.initialize();

            if (this.flashbotsManager.isAvailable()) {
                logger.system('✅ Flashbots provider initialized successfully');

                // Get execution stats
                const stats = await this.flashbotsExecutor.getExecutionStats();
                logger.system(`   Recommended Strategy: ${stats.recommendedStrategy}`);
                logger.system(`   Network Congestion: ${(stats.networkCongestion * 100).toFixed(1)}%`);
            } else {
                logger.system('⚠️  Flashbots not available (testnet or initialization failed)');
                logger.system('   Will use regular mempool execution');
            }

            // Display simulation mode status
            if (config.simulationMode) {
                logger.system('🎭 SIMULATION MODE ENABLED');
                logger.system('   Opportunities will be detected and analyzed');
                logger.system('   No real transactions will be executed');
                logger.system('   Perfect for mainnet monitoring without gas costs');
            }

        } catch (error) {
            logger.logError(error as Error, 'MEVBot.initializeFlashbots');
            logger.system('⚠️  Flashbots initialization failed, using mempool fallback');
        }
    }

    private async initializeMEVShare(): Promise<void> {
        try {
            if (!this.mevShareMonitor || !this.mevShareFlashloanStrategy) {
                logger.system('⚠️  MEV-Share components not initialized');
                return;
            }

            // Initialize MEV-Share monitor
            await this.mevShareMonitor.initialize();

            if (this.mevShareMonitor.getStatus().isAvailable) {
                // Start MEV-Share monitoring
                await this.mevShareFlashloanStrategy.startMonitoring();

                logger.system('MEV-Share integration initialized successfully');

                const status = this.mevShareFlashloanStrategy.getStatus();
                logger.system('MEV-Share configuration', {
                    gasProtection: status.gasProtection,
                    maxGasCost: status.maxGasCost
                });
            } else {
                logger.warn('MEV-Share not available (requires mainnet)');
            }

        } catch (error) {
            logger.logError(error as Error, 'MEVBot.initializeMEVShare');
            logger.error('MEV-Share initialization failed');
        }
    }

    private async checkWalletBalance(): Promise<void> {
        const balance = await this.wallet.provider!.getBalance(this.wallet.address);
        const balanceEth = Number(ethers.formatEther(balance));

        logger.walletBalance(balanceEth, 'ETH');

        if (balanceEth < 0.001) {
            logger.warn('Low wallet balance - consider adding more ETH');
        }

        if (balanceEth < 0.001) {
            throw new Error('Insufficient wallet balance for MEV operations');
        }
    }

    private async validatePoolsAndLiquidity(): Promise<void> {
        try {
            if (!config.enablePoolValidation && !config.enableLiquidityFetching) {
                logger.system('⏭️  Pool validation and liquidity fetching disabled');
                return;
            }

            logger.system('🔍 Validating pools and fetching liquidity...');

            // Import PoolManager to validate pools
            const {PoolManager} = require('../dex/pools');
            const poolManager = new PoolManager();

            // Validate that configured token pairs have available pools
            if (config.enablePoolValidation) {
                logger.system('🔍 Validating configured token pairs...');
                const validation = await poolManager.validateConfiguredPools();

                if (!validation.valid) {
                    logger.error('❌ Pool validation failed:');
                    validation.errors.forEach((error: string) => logger.error(`   • ${error}`));

                    if (config.failOnPoolValidationError || config.emergencyStop) {
                        throw new Error('Pool validation failed and failOnPoolValidationError is enabled');
                    }
                }

                if (validation.warnings.length > 0) {
                    logger.warn('⚠️  Pool validation warnings:');
                    validation.warnings.forEach((warning: string) => logger.warn(`   • ${warning}`));
                }
            }

            // Fetch Balancer liquidity for configured tokens if enabled and Balancer is available
            if (config.enableLiquidityFetching && poolManager.isProtocolAvailable('balancer')) {
                logger.system('💧 Fetching Balancer liquidity...');

                const {getConfiguredTokens} = require('../config');
                const configuredTokens = getConfiguredTokens();

                for (const token of configuredTokens) {
                    try {
                        const liquidityData = await poolManager.fetchBalancerLiquidity(token.address);

                        if (liquidityData.totalLiquidity > BigInt(0)) {
                            logger.system(
                                `   ${token.symbol}: ${ethers.formatUnits(liquidityData.totalLiquidity, token.decimals)} (${liquidityData.pools.length} pools)`
                            );
                        } else {
                            logger.warn(`   ${token.symbol}: No liquidity found`);
                        }
                    } catch (error) {
                        logger.warn(`   ${token.symbol}: Error fetching liquidity - ${(error as Error).message}`);
                    }
                }
            } else if (config.enableLiquidityFetching) {
                logger.warn('⚠️  Balancer not available on this network - skipping liquidity fetch');
            }

            logger.system('✅ Pool validation and liquidity check completed');

        } catch (error) {
            logger.error('Pool validation failed', error);
            logger.logError(error as Error, 'MEVBot.validatePoolsAndLiquidity');

            if (config.failOnPoolValidationError || config.emergencyStop) {
                throw error;
            }
        }
    }

    private async handlePendingTransaction(tx: Transaction): Promise<void> {
        if (!this.state.isRunning || this.state.emergencyStop) {
            return;
        }

        try {
            this.state.lastActivity = Date.now();

            // Analyze for sandwich opportunities
            if (config.enableSandwichAttacks) {
                const sandwichOpportunity = await this.sandwichStrategy.analyzeTransaction(tx);
                if (sandwichOpportunity) {
                    this.emit('opportunityFound', sandwichOpportunity);
                }
            }

            // Note: Front-running would be implemented similarly to sandwich
            // but without the back-run transaction
        } catch (error) {
            logger.debug('Error analyzing transaction', {
                hash: tx.hash,
                error: (error as Error).message
            });
        }
    }

    private async handleOpportunityFound(opportunity: MEVOpportunity): Promise<void> {
        try {
            const profitEth = ethers.formatEther(opportunity.estimatedProfit);

            // Create unique identifier for opportunity deduplication (include block number and transaction)
            const opportunityId = `${opportunity.type}-block${this.currentBlockNumber}-${opportunity.triggerTx || 'block'}-${profitEth}-${opportunity.confidence.toFixed(2)}`;

            // Check if we've already processed this opportunity in the current block
            if (this.processedOpportunitiesThisBlock.has(opportunityId)) {
                logger.debug('🚫 Skipping already processed opportunity in this block', {
                    id: opportunityId,
                    block: this.currentBlockNumber,
                    processedCount: this.processedOpportunitiesThisBlock.size
                });
                return;
            }

            // Check if we're already executing this opportunity
            if (this.executingOpportunities.has(opportunityId)) {
                logger.debug('Skipping duplicate opportunity execution', {id: opportunityId});
                return;
            }

            // Check execution cooldown
            const now = Date.now();
            if (now - this.lastExecutionTime < this.EXECUTION_COOLDOWN) {
                logger.debug('Execution cooldown active, skipping opportunity');
                return;
            }

            // Validate profit is realistic (max 10 ETH for arbitrage)
            const profitBigInt = BigInt(opportunity.estimatedProfit.toString());
            const maxRealisticProfit = ethers.parseEther('10');
            if (profitBigInt > maxRealisticProfit) {
                logger.warn('Unrealistic profit detected, skipping opportunity', {
                    profit: profitEth,
                    maxRealistic: ethers.formatEther(maxRealisticProfit)
                });
                return;
            }

            // Mark as processed for this block
            this.processedOpportunitiesThisBlock.add(opportunityId);
            logger.debug(`✅ Marked opportunity as processed: ${opportunityId} (block: ${this.currentBlockNumber})`);

            // Record opportunity in dashboard (already recorded in analysis methods)
            // statusDashboard.recordOpportunity(false, BigInt(opportunity.estimatedProfit.toString()));

            // Log all opportunities for visibility (only once per block now)
            logger.info(`MEV Opportunity: ${opportunity.type.toUpperCase()}`, {
                profit: profitEth,
                confidence: opportunity.confidence,
                block: this.currentBlockNumber
            });

            // Add to opportunities list
            this.opportunities.push(opportunity);

            // Keep only recent opportunities
            if (this.opportunities.length > this.MAX_OPPORTUNITIES) {
                this.opportunities = this.opportunities.slice(-this.MAX_OPPORTUNITIES);
            }

            // Execute if confidence is high enough
            if (opportunity.confidence >= 50) { // Lowered from 70 to 50
                logger.info('High confidence detected - executing opportunity...');

                // Mark as executing
                this.executingOpportunities.add(opportunityId);
                this.lastExecutionTime = now;

                try {
                    await this.executeOpportunity(opportunity);
                } finally {
                    // Remove from executing set after completion
                    this.executingOpportunities.delete(opportunityId);
                }
            }
        } catch (error) {
            logger.error('Error handling MEV opportunity', error);
        }
    }

    private async executeOpportunity(opportunity: MEVOpportunity): Promise<void> {
        try {
            let success = false;

            switch (opportunity.type) {
                case 'sandwich':
                    success = await this.sandwichStrategy.executeSandwich(opportunity);
                    break;
                case 'arbitrage':
                    // This would need to be implemented based on the opportunity structure
                    logger.info('Arbitrage execution not yet implemented for mempool opportunities');
                    break;
                case 'flashloan':
                    logger.info('Flashloan opportunities are handled in the scanning loop');
                    break;
                default:
                    logger.warn(`Unknown opportunity type: ${opportunity.type}`);
                    return;
            }

            if (success) {
                this.state.successfulTrades++;
                this.state.totalProfit = BigInt(this.state.totalProfit.toString()) + BigInt(opportunity.estimatedProfit.toString());

                // Record successful transaction in dashboard
                statusDashboard.recordSuccessfulTransaction({
                    timestamp: Date.now(),
                    type: opportunity.type as any,
                    profit: BigInt(opportunity.estimatedProfit.toString()),
                    gasUsed: BigInt(0), // Will be updated with actual gas used
                    confidence: opportunity.confidence,
                    details: `${opportunity.type} executed successfully`
                });

                logger.info('✅ MEV opportunity executed successfully');
            } else {
                this.state.failedTrades++;
                logger.warn('❌ MEV opportunity execution failed');
            }

            this.updateRiskMetrics();
        } catch (error) {
            this.state.failedTrades++;
            logger.logError(error as Error, 'MEVBot.executeOpportunity');
        }
    }

    /**
     * Execute arbitrage opportunity using FlashbotsExecutor
     */
    private async executeArbitrageOpportunity(opportunity: MEVOpportunity): Promise<boolean> {
        try {
            if (!opportunity.arbitrageRoute) {
                logger.error('Arbitrage opportunity missing route data');
                return false;
            }

            const route = opportunity.arbitrageRoute;

            logger.info('Executing arbitrage opportunity', {
                profit: ethers.formatEther(route.expectedProfit),
                confidence: route.confidence,
                pools: route.pools.length,
                tokens: route.tokens.map(t => t.symbol).join(' → ')
            });

            // Execute using FlashbotsExecutor
            const result = await this.flashbotsExecutor.executeArbitrage(route, {
                useFlashbots: true,
                urgency: 'fast',
                maxGasCostEth: 0.01,
                slippageTolerance: 0.5
            });

            if (result.success) {
                logger.info('🎉 Arbitrage executed successfully!');
                logger.system(`   Bundle Hash: ${result.bundleHash}`);
                logger.system(`   Resolution: ${result.resolution}`);
                logger.system(`   Expected Profit: ${ethers.formatEther(route.expectedProfit)} ETH`);
                logger.system(`   Execution Time: ${result.executionTime}ms`);

                if (result.profit && result.profit > 0n) {
                    logger.info(`   Actual Profit: ${ethers.formatEther(result.profit)} ETH`);
                }

                // Update bot state with successful execution
                this.state.successfulTrades++;
                const profitToAdd = result.profit || BigInt(route.expectedProfit.toString());
                this.state.totalProfit = BigInt(this.state.totalProfit.toString()) + profitToAdd;

                // Record successful transaction in dashboard
                statusDashboard.recordTransaction(true, profitToAdd);

                return true;
            } else {
                logger.warn('❌ Arbitrage execution failed');
                logger.warn(`   Error: ${result.error}`);
                logger.warn(`   Bundle Hash: ${result.bundleHash || 'N/A'}`);
                logger.warn(`   Resolution: ${result.resolution || 'N/A'}`);
                logger.warn(`   Expected Profit: ${ethers.formatEther(route.expectedProfit)} ETH`);

                // Update failed trades counter
                this.state.failedTrades++;

                return false;
            }

        } catch (error) {
            logger.logError(error as Error, 'MEVBot.executeArbitrageOpportunity');
            return false;
        }
    }

    /**
     * Execute multi-DEX arbitrage opportunity using dynamic flashloan strategy
     */
    private async executeMultiDexArbitrage(opportunity: any): Promise<boolean> {
        try {
            logger.info('🎯 Executing multi-DEX arbitrage', {
                pair: opportunity.pair,
                buyDex: opportunity.buyDex,
                sellDex: opportunity.sellDex,
                profitBps: opportunity.profitBps,
                estimatedProfitUSD: opportunity.estimatedProfitUSD,
                flashloanAmount: opportunity.flashloanAmount
            });

            // Convert opportunity to flashloan parameters
            const flashloanParams = this.convertMultiDexOpportunityToFlashloanParams(opportunity);

            if (!flashloanParams) {
                logger.error('Failed to convert multi-DEX opportunity to flashloan parameters');
                return false;
            }

            // Execute using dynamic flashloan strategy
            // Convert flashloan params to route format
            const route = this.convertFlashloanParamsToRoute(flashloanParams);
            const success = await this.dynamicFlashloanStrategy.executeBestOpportunity([route], 'multi-dex-arbitrage');

            if (success) {
                logger.info('🎉 Multi-DEX arbitrage executed successfully!', {
                    pair: opportunity.pair,
                    buyDex: opportunity.buyDex,
                    sellDex: opportunity.sellDex,
                    estimatedProfit: opportunity.estimatedProfitUSD
                });

                // Update bot state
                this.state.successfulTrades++;
                statusDashboard.recordTransaction(true, route.profitEstimate);

                return true;
            } else {
                logger.warn('❌ Multi-DEX arbitrage execution failed', {
                    pair: opportunity.pair
                });

                this.state.failedTrades++;
                return false;
            }

        } catch (error) {
            logger.logError(error as Error, 'MEVBot.executeMultiDexArbitrage');
            return false;
        }
    }

    /**
     * Convert multi-DEX opportunity to flashloan parameters
     */
    private convertMultiDexOpportunityToFlashloanParams(opportunity: any): any {
        try {
            // Get token addresses from pair
            const [tokenASymbol, tokenBSymbol] = opportunity.pair.split('/');
            const tokenMap: { [key: string]: string } = {
                'WETH': MAINNET_ADDRESSES.WETH,
                'USDC': MAINNET_ADDRESSES.USDC,
                'DAI': MAINNET_ADDRESSES.DAI,
                'USDT': MAINNET_ADDRESSES.USDT
            };

            const tokenA = tokenMap[tokenASymbol];
            const tokenB = tokenMap[tokenBSymbol];

            if (!tokenA || !tokenB) {
                logger.error('Unknown tokens in pair', { pair: opportunity.pair });
                return null;
            }

            // Map DEX names to router addresses
            const dexMap: { [key: string]: string } = {
                'UNISWAP_V3': MAINNET_ADDRESSES.UNISWAP_V3_ROUTER,
                'SUSHISWAP': MAINNET_ADDRESSES.SUSHISWAP_ROUTER,
                'CURVE': MAINNET_ADDRESSES.CURVE_3POOL // Use 3Pool for USDC/DAI
            };

            const buyDexAddress = dexMap[opportunity.buyDex];
            const sellDexAddress = dexMap[opportunity.sellDex];

            if (!buyDexAddress || !sellDexAddress) {
                logger.error('Unknown DEX in opportunity', {
                    buyDex: opportunity.buyDex,
                    sellDex: opportunity.sellDex
                });
                return null;
            }

            // Build flashloan parameters
            return {
                asset: tokenA, // Use tokenA as the flashloan asset
                amount: ethers.parseEther(opportunity.flashloanAmount),
                buyPath: [tokenA, tokenB],
                sellPath: [tokenB, tokenA],
                buyDex: buyDexAddress,
                sellDex: sellDexAddress,
                v3Fees: opportunity.buyDex === 'UNISWAP_V3' ? [3000] : [], // 0.3% fee for V3
                minProfit: ethers.parseEther('0.01'), // 0.01 ETH minimum profit
                provider: 0, // Use Aave
                slippageToleranceBps: config.multiDexSlippageToleranceBps,
                maxGasCostWei: ethers.parseUnits(config.multiDexMaxGasCostGwei.toString(), 'gwei')
            };

        } catch (error) {
            logger.error('Error converting multi-DEX opportunity to flashloan params', error);
            return null;
        }
    }

    /**
     * Convert flashloan params to route format for dynamic strategy
     */
    private convertFlashloanParamsToRoute(params: any): any {
        return {
            strategy: 'aave',
            asset: params.asset,
            amount: params.amount,
            buyPath: params.buyPath,
            sellPath: params.sellPath,
            buyDex: params.buyDex,
            sellDex: params.sellDex,
            v3Fees: params.v3Fees,
            minProfit: params.minProfit,
            confidence: 80, // Default confidence for multi-DEX arbitrage
            gasEstimate: ethers.parseUnits('300000', 'wei'), // Default gas estimate
            profitEstimate: params.minProfit
        };
    }

    /**
     * Handle new block - reset processed opportunities for the new block
     */
    private handleNewBlock(blockNumber: number): void {
        if (blockNumber > this.currentBlockNumber) {
            const previousBlock = this.currentBlockNumber;
            this.currentBlockNumber = blockNumber;
            const clearedOpportunities = this.processedOpportunitiesThisBlock.size;
            const clearedTransactions = this.processedTransactionsThisBlock.size;

            this.processedOpportunitiesThisBlock.clear();
            this.processedTransactionsThisBlock.clear();

            // Also clear flashloan strategy opportunities
            if (this.flashloanStrategy) {
                this.flashloanStrategy.handleNewBlock(blockNumber);
            }

            logger.debug(`New block ${blockNumber} (prev: ${previousBlock}) - cleared ${clearedOpportunities} opportunities, ${clearedTransactions} transactions`);
        }
    }

    private setupEventDrivenArbitrage(): void {
        if (!config.enableArbitrage) {
            return;
        }

        // Listen for new blocks to trigger arbitrage analysis
        this.blockEventMonitor.on('newBlock', async (blockData: BlockData) => {
            if (!this.state.isRunning || this.state.emergencyStop) {
                return;
            }

            logger.debug(`📦 Block event received: ${blockData.number}`);

            // Handle new block for opportunity deduplication
            this.handleNewBlock(blockData.number);

            await this.analyzeArbitrageOpportunities('newBlock', null, blockData);
        });

        // Listen for relevant transactions to trigger immediate arbitrage analysis
        this.mempoolMonitor.on('pendingTransaction', async (transaction) => {
            if (!this.state.isRunning || this.state.emergencyStop) {
                return;
            }
            await this.analyzeArbitrageOpportunities('pendingTransaction', transaction);
        });

        logger.system('Event-driven arbitrage monitoring started');
    }

    /**
     * Setup independent flashloan attack monitoring
     * This runs independently of arbitrage and can be enabled/disabled separately
     */
    private setupEventDrivenFlashloan(): void {
        if (!config.enableFlashloanAttacks) {
            return;
        }

        // Listen for new blocks to trigger flashloan analysis (primary trigger)
        this.blockEventMonitor.on('newBlock', async (blockData: BlockData) => {
            if (!this.state.isRunning || this.state.emergencyStop) {
                return;
            }

            logger.debug(`🔗 Block event received for flashloan analysis: ${blockData.number}`);
            await this.analyzeFlashloanOpportunities('newBlock', null, blockData);
        });

        // Listen for high-value transactions only to trigger immediate flashloan analysis
        // This reduces spam by only analyzing flashloans for significant transactions
        this.mempoolMonitor.on('relevantTransaction', async (transaction) => {
            if (!this.state.isRunning || this.state.emergencyStop) {
                return;
            }

            // Only analyze flashloans for high-value transactions to reduce noise
            const txValue = parseFloat(transaction.value || '0');
            if (txValue >= 0.1) { // Only for transactions >= 0.1 ETH
                await this.analyzeFlashloanOpportunities('relevantTransaction', transaction);
            }
        });

        logger.system('Event-driven flashloan monitoring started');
        logger.system('   Primary trigger: New blocks');
        logger.system('   Secondary trigger: High-value transactions (>= 0.1 ETH)');
    }

    private async analyzeArbitrageOpportunities(trigger: string, transaction?: any, blockData?: BlockData): Promise<void> {
        try {
            const startTime = Date.now();

            // Scan for regular arbitrage opportunities
            const routes = await this.arbitrageStrategy.scanForArbitrageOpportunities();

            const scanTime = Date.now() - startTime;

            // Update performance metrics
            performanceMonitor.updateArbitrageMetrics(
                scanTime,
                routes.length > 0 ? routes.length * 10 : 0, // Estimate pairs processed
                routes.length,
                this.arbitrageStrategy.isUsingWorkers()
            );

            // Update worker metrics if using workers
            if (this.arbitrageStrategy.isUsingWorkers()) {
                const workerStats = this.arbitrageStrategy.getWorkerStats();
                performanceMonitor.updateWorkerMetrics(workerStats);
            }

            for (const route of routes) {
                if (route.confidence >= 80) { // Higher threshold for arbitrage
                    const success = await this.arbitrageStrategy.executeArbitrage(route);

                    if (success) {
                        this.state.successfulTrades++;
                        this.state.totalProfit = BigInt(this.state.totalProfit.toString()) + BigInt(route.expectedProfit.toString());
                    } else {
                        this.state.failedTrades++;
                    }
                }
            }


            // Scan for Uniswap V3 flash swap opportunities
            if (process.env.ENABLE_UNISWAP_V3_FLASH_SWAPS === 'true') {
                const flashSwapOpportunities = await this.uniswapV3FlashSwapStrategy.scanForOpportunities();

                for (const opportunity of flashSwapOpportunities) {
                    if (opportunity.route.confidence >= 70) { // 70% confidence threshold
                        logger.info(`⚡ Uniswap V3 flash swap opportunity detected (${opportunity.route.confidence}%)`);

                        const result = await this.uniswapV3FlashSwapStrategy.executeFlashSwap(opportunity);

                        if (result.success) {
                            this.state.successfulTrades++;
                            this.state.totalProfit = BigInt(this.state.totalProfit.toString()) + (result.profit || 0n);

                            // Record detailed successful transaction
                            statusDashboard.recordSuccessfulTransaction({
                                timestamp: Date.now(),
                                type: 'uniswap-v3-flash',
                                profit: result.profit || 0n,
                                gasUsed: opportunity.route.gasEstimate,
                                txHash: result.txHash,
                                bundleHash: undefined,
                                confidence: opportunity.route.confidence,
                                details: `${opportunity.route.tokenA.symbol}/${opportunity.route.tokenB.symbol} (${opportunity.route.borrowPool.fee}→${opportunity.route.sellPool.fee})`
                            });

                            logger.info('✅ Uniswap V3 flash swap executed successfully', {
                                profit: ethers.formatEther(result.profit || 0n),
                                txHash: result.txHash,
                                pair: `${opportunity.route.tokenA.symbol}/${opportunity.route.tokenB.symbol}`
                            });
                        } else {
                            this.state.failedTrades++;
                            logger.error(`❌ Uniswap V3 flash swap execution failed: ${result.error}`);
                        }
                    }
                }
            }

            this.updateRiskMetrics();
        } catch (error) {
            logger.debug('Error in event-driven arbitrage analysis', {
                trigger,
                error: (error as Error).message
            });
        }
    }

    /**
     * Analyze flashloan opportunities independently from arbitrage
     * This method runs when ENABLE_FLASHLOAN_ATTACKS=true regardless of ENABLE_ARBITRAGE setting
     */
    private async analyzeFlashloanOpportunities(trigger: string, transaction?: any, blockData?: BlockData): Promise<void> {
        try {
            // Create unique identifier for this analysis to prevent duplicates
            const analysisId = `flashloan-analysis-${trigger}-${blockData?.number || 'tx'}-${transaction?.hash || 'block'}`;

            // Check if we've already processed this analysis in the current block
            if (this.processedOpportunitiesThisBlock.has(analysisId)) {
                logger.debug('🚫 Skipping duplicate flashloan analysis', {
                    id: analysisId,
                    trigger,
                    block: this.currentBlockNumber
                });
                return;
            }

            // Mark this analysis as processed to prevent duplicates
            this.processedOpportunitiesThisBlock.add(analysisId);

            logger.debug('🔍 Scanning with Dynamic Flashloan Strategy...', {
                trigger,
                block: blockData?.number,
                txHash: transaction?.hash
            });

            // Scan once and reuse results to avoid redundant scanning (optimization)
            const opportunities = await this.dynamicFlashloanStrategy.scanForOpportunities(transaction?.hash);

            // Log market conditions using pre-scanned opportunities
            const totalOpportunities = opportunities.length;
            const bestProfit = opportunities.length > 0 ? ethers.formatEther(opportunities[0].netProfit) : '0';
            const bestStrategy = opportunities.length > 0 ? opportunities[0].strategy : 'none';
            logger.debug(`Market: ${totalOpportunities} opportunities, best: ${bestProfit} ETH (${bestStrategy})`);

            // Check if any strategy is profitable using pre-scanned opportunities
            const isProfitable = opportunities.length > 0 && opportunities[0].netProfit > BigInt(0);
            const marketConditions = await this.dynamicFlashloanStrategy.getMarketConditionsFrom(opportunities);

            if (isProfitable) {
                // Create unique opportunity identifier for execution deduplication
                const opportunityId = `flashloan-exec-block${this.currentBlockNumber}-${marketConditions.bestStrategy}-${marketConditions.bestProfit}`;

                // Check if we've already executed this opportunity in the current block
                if (this.processedOpportunitiesThisBlock.has(opportunityId)) {
                    logger.debug('🚫 Skipping already executed flashloan opportunity', {
                        id: opportunityId,
                        block: this.currentBlockNumber
                    });
                    return;
                }

                // Mark opportunity as processed
                this.processedOpportunitiesThisBlock.add(opportunityId);

                logger.system('🎯 Profitable flashloan opportunities detected, executing best strategy...', {
                    strategy: marketConditions.bestStrategy,
                    profit: marketConditions.bestProfit,
                    block: this.currentBlockNumber
                });

                // Check execution conditions
                const executionFavorable = await this.flashbotsExecutor.isExecutionFavorable({
                    useFlashbots: true,
                    urgency: 'fast',
                    maxGasCostEth: 0.005,
                    slippageTolerance: 0.3
                });

                if (!executionFavorable) {
                    logger.system('⚠️  Execution conditions unfavorable, skipping');
                } else {
                    // Execute best opportunity with MEV protection using pre-scanned opportunities
                    const success = await this.dynamicFlashloanStrategy.executeBestOpportunity(opportunities, transaction?.hash);

                    if (success) {
                        this.state.successfulTrades++;

                        // Get strategy performance report
                        const report = this.dynamicFlashloanStrategy.getStrategyReport();
                        const bestStrategy = this.dynamicFlashloanStrategy.getBestStrategy();
                        logger.system(`📈 Best performing flashloan strategy: ${bestStrategy}`);

                        // Record successful transaction
                        statusDashboard.recordSuccessfulTransaction({
                            timestamp: Date.now(),
                            type: 'dynamic-flashloan',
                            profit: BigInt(0), // Profit will be calculated by strategy
                            gasUsed: BigInt(0),
                            txHash: 'dynamic-execution',
                            bundleHash: undefined,
                            confidence: 90,
                            details: `Dynamic strategy: ${bestStrategy}`
                        });

                        logger.success('✅ Dynamic flashloan executed successfully');
                    } else {
                        this.state.failedTrades++;
                        logger.system('❌ Dynamic flashloan execution failed');
                    }
                }
            } else {
                // Only log "no opportunities" once per block, not for every transaction
                if (trigger === 'newBlock') {
                    logger.system(`Block ${blockData?.number}: No profitable flashloan opportunities found`);
                }
            }

        } catch (error) {
            logger.debug('Error in flashloan opportunity analysis', {
                trigger,
                error: (error as Error).message
            });
        }
    }

    private setupEventDrivenBlockTracking(): void {
        // Listen for new blocks via websocket events instead of polling
        this.blockEventMonitor.on('newBlock', async (blockData: BlockData) => {
            if (!this.state.isRunning) {
                return;
            }

            // Handle new block for opportunity deduplication
            this.handleNewBlock(blockData.number);

            try {
                const networkName = config.chainId === 1 ? 'Mainnet' :
                    config.chainId === 11155111 ? 'Sepolia' : 'Testnet';
                statusDashboard.updateNetworkStatus(blockData.number, networkName);
            } catch (error) {
                logger.debug('Error updating block status', {error: (error as Error).message});
            }
        });

        logger.system('Event-driven block tracking started');
    }

    /**
     * Handle relevant transaction detected by mempool monitor
     */
    private async handleRelevantTransaction(transaction: any): Promise<void> {
        try {
            // Check if we've already processed this transaction in the current block
            if (this.processedTransactionsThisBlock.has(transaction.hash)) {
                logger.debug('🚫 Skipping already processed transaction in this block', {
                    txHash: transaction.hash,
                    block: this.currentBlockNumber
                });
                return;
            }

            // Mark transaction as processed for this block
            this.processedTransactionsThisBlock.add(transaction.hash);

            // Check if this transaction creates immediate arbitrage opportunities
            if (config.enableArbitrage) {
                await this.analyzeTransactionForArbitrage(transaction);
            }

            // Check if this transaction creates flashloan opportunities
            if (config.enableFlashloanAttacks) {
                await this.analyzeTransactionForFlashloan(transaction);
            }

            // Check for sandwich opportunities
            if (config.enableSandwichAttacks) {
                await this.analyzeTransactionForSandwich(transaction);
            }

        } catch (error) {
            logger.debug('Error analyzing relevant transaction', {
                hash: transaction.hash,
                error: (error as Error).message
            });
        }
    }

    /**
     * Analyze transaction for arbitrage opportunities
     */
    private async analyzeTransactionForArbitrage(transaction: any): Promise<void> {
        try {
            // Quick arbitrage scan triggered by this transaction
            const routes = await this.arbitrageStrategy.scanForArbitrageOpportunities();

            for (const route of routes.slice(0, 3)) { // Check top 3 opportunities
                // Record ALL opportunities found (for dashboard stats)
                statusDashboard.recordOpportunity(false, BigInt(route.expectedProfit.toString()));

                // Only execute high-confidence opportunities
                if (route.confidence >= 50) { // Lowered from 70 to 50
                    const opportunity: MEVOpportunity = {
                        type: 'arbitrage' as const,
                        estimatedProfit: route.expectedProfit,
                        confidence: route.confidence,
                        gasEstimate: route.gasEstimate,
                        timestamp: Date.now(),
                        arbitrageRoute: route,
                        triggerTx: transaction.hash
                    };

                    this.emit('opportunityFound', opportunity);
                }
            }
        } catch (error) {
            logger.debug('Error analyzing arbitrage for transaction', {
                hash: transaction.hash,
                error: (error as Error).message
            });
        }
    }

    /**
     * Analyze transaction for flashloan opportunities
     */
    private async analyzeTransactionForFlashloan(transaction: any): Promise<void> {
        try {
            // Quick flashloan scan triggered by this transaction
            const routes = await this.flashloanStrategy.scanForFlashloanOpportunities(transaction.hash);

            for (const route of routes.slice(0, 2)) { // Check top 2 opportunities
                // Record ALL opportunities found (for dashboard stats)
                statusDashboard.recordOpportunity(false, BigInt(route.expectedProfit.toString()));

                // Only execute high-confidence opportunities
                if (route.confidence >= 50) { // Lowered from 70 to 50
                    const opportunity = {
                        type: 'flashloan' as const,
                        estimatedProfit: route.expectedProfit,
                        confidence: route.confidence,
                        gasEstimate: route.gasEstimate,
                        triggerTx: transaction.hash
                    };

                    this.emit('opportunityFound', opportunity);
                }
            }
        } catch (error) {
            logger.debug('Error analyzing flashloan for transaction', {
                hash: transaction.hash,
                error: (error as Error).message
            });
        }
    }

    /**
     * Analyze transaction for sandwich opportunities
     */
    private async analyzeTransactionForSandwich(transaction: any): Promise<void> {
        try {
            // Check if this transaction is sandwichable
            const isSandwichable = await this.sandwichStrategy.isTransactionSandwichable(transaction);

            if (isSandwichable) {
                const estimatedProfit = await this.sandwichStrategy.estimateSandwichProfit(transaction);

                if (estimatedProfit && BigInt(estimatedProfit.toString()) > ethers.parseEther('0.001')) {
                    statusDashboard.recordOpportunity(false, BigInt(estimatedProfit.toString()));

                    const opportunity = {
                        type: 'sandwich' as const,
                        estimatedProfit: estimatedProfit,
                        confidence: 85, // Sandwich attacks typically have high confidence
                        gasEstimate: ethers.parseEther('0.01'), // Estimated gas cost
                        triggerTx: transaction.hash
                    };

                    this.emit('opportunityFound', opportunity);
                }
            }
        } catch (error) {
            logger.debug('Error analyzing sandwich for transaction', {
                hash: transaction.hash,
                error: (error as Error).message
            });
        }
    }

    private async handleBundleExecuted(bundle: Bundle): Promise<void> {
        this.executedBundles.push(bundle);
        logger.logBundle(bundle.transactions[0]?.hash || 'unknown', bundle.transactions.length);
    }

    private handleError(error: Error): void {
        logger.logError(error, 'MEVBot');

        // Record error in dashboard
        statusDashboard.recordError(error.message);


        // Implement emergency stop if critical error
        if (error.message.includes('insufficient funds') ||
            error.message.includes('nonce too low')) {
            this.emergencyStop();
        }
    }

    private updateRiskMetrics(): void {
        const totalTrades = this.state.successfulTrades + this.state.failedTrades;

        if (totalTrades > 0) {
            this.riskMetrics.winRate = (this.state.successfulTrades / totalTrades) * 100;
            this.riskMetrics.averageProfit = BigInt(this.state.totalProfit.toString()) / BigInt(totalTrades);
        }

        // Calculate profit factor (gross profit / gross loss)
        // This would need more detailed tracking of individual trade results
        this.riskMetrics.profitFactor = this.riskMetrics.winRate / 100;
    }

    emergencyStop(): void {
        logger.warn('🚨 EMERGENCY STOP ACTIVATED');
        this.state.emergencyStop = true;
        this.stop();
        this.emit('emergencyStop');
    }

    getState(): BotState {
        return {...this.state};
    }

    getRiskMetrics(): RiskMetrics {
        return {...this.riskMetrics};
    }

    getRecentOpportunities(count: number = 10): MEVOpportunity[] {
        return this.opportunities.slice(-count);
    }

    getStats(): {
        state: BotState;
        riskMetrics: RiskMetrics;
        mempoolStatus: any;
        gasStats: any;
        mevShareStatus?: any;
    } {
        const stats = {
            state: this.getState(),
            riskMetrics: this.getRiskMetrics(),
            mempoolStatus: this.mempoolMonitor.getStatus(),
            gasStats: this.gasOptimizer.getGasStats()
        };

        // Add MEV-Share status if available
        if (this.mevShareFlashloanStrategy) {
            (stats as any).mevShareStatus = this.mevShareFlashloanStrategy.getStatus();
        }

        return stats;
    }

    async getWalletInfo(): Promise<{
        address: string;
        balance: string;
        nonce: number;
    }> {
        const balance = await this.wallet.provider!.getBalance(this.wallet.address);
        const nonce = await this.wallet.getNonce();

        return {
            address: this.wallet.address,
            balance: ethers.formatEther(balance),
            nonce
        };
    }

    /**
     * Setup ETH balance monitoring
     */
    private setupBalanceMonitoring(): void {
        // Update balance immediately
        this.updateEthBalance();

        // Update balance on every new block (event-driven)
        this.blockEventMonitor.on('newBlock', async (blockData: BlockData) => {
            if (this.state.isRunning) {
                // Handle new block for opportunity deduplication
                this.handleNewBlock(blockData.number);

                await this.updateEthBalance();
            }
        });

        logger.system('ETH balance monitoring started');
    }

    /**
     * Setup periodic bundle analysis reporting
     */
    private setupBundleAnalysisReporting(): void {
        // Report bundle statistics every 50 submissions or 10 minutes, whichever comes first
        let lastReportTime = Date.now();
        let lastSubmissionCount = 0;

        const checkAndReport = () => {
            const stats = bundleAnalyzer.getInclusionStats();
            const now = Date.now();
            const timeSinceLastReport = now - lastReportTime;
            const submissionsSinceLastReport = stats.totalSubmissions - lastSubmissionCount;

            // Report if 50+ new submissions OR 10+ minutes passed (and we have some data)
            if ((submissionsSinceLastReport >= 50) ||
                (timeSinceLastReport >= 600000 && stats.totalSubmissions > 0)) {

                logger.system('');
                logger.system('📊 BUNDLE PERFORMANCE REPORT');
                logger.system('============================');
                logger.system(`Submissions since last report: ${submissionsSinceLastReport}`);
                logger.system(`Total submissions: ${stats.totalSubmissions}`);
                logger.system(`Inclusion rate: ${stats.inclusionRate.toFixed(1)}%`);
                logger.system(`Average priority fee: ${stats.averagePriorityFee} gwei`);

                // Provide quick recommendations
                if (stats.inclusionRate < 50) {
                    logger.system('⚠️  LOW inclusion rate - consider increasing priority fees');
                    logger.system('   Run: npm run analyze:bundles for detailed recommendations');
                } else if (stats.inclusionRate >= 80) {
                    logger.system('✅ EXCELLENT inclusion rate - current settings working well');
                } else {
                    logger.system('📈 MODERATE inclusion rate - monitor and adjust as needed');
                }
                logger.system('');

                lastReportTime = now;
                lastSubmissionCount = stats.totalSubmissions;
            }
        };

        // Check every 2 minutes
        setInterval(checkAndReport, 120000);

        logger.system('Bundle analysis reporting started');
    }

    /**
     * Update ETH balance and display in dashboard
     */
    private async updateEthBalance(): Promise<void> {
        try {
            const balance = await this.provider.getBalance(this.wallet.address);
            statusDashboard.updateEthBalance(balance);

            // Log low balance warning
            if (balance < ethers.parseEther('0.01')) {
                logger.warn(`Low ETH balance: ${ethers.formatEther(balance)} ETH - consider adding more funds`);
            }
        } catch (error) {
            logger.debug('Error updating ETH balance', {error: (error as Error).message});
        }
    }

    /**
     * Get mempool monitor for testing purposes
     */
    getMempoolMonitor(): MempoolMonitor {
        return this.mempoolMonitor;
    }
}
