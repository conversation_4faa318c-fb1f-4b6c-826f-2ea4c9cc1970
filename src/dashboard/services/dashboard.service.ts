import { Injectable } from '@nestjs/common';
import { readFileSync } from 'fs';
import { join } from 'path';

@Injectable()
export class DashboardService {
  async getDashboardHtml(): Promise<string> {
    try {
      // Copy the dashboard from old folder
      const dashboardPath = join(process.cwd(), 'old', 'dashboard', 'index.html');
      return readFileSync(dashboardPath, 'utf8');
    } catch (error) {
      return '<h1>Dashboard not found</h1><p>Please copy the dashboard from the old folder.</p>';
    }
  }

  getStatus() {
    return {
      isRunning: true,
      uptime: Date.now(),
      stats: {
        totalOpportunities: 0,
        successfulArbitrages: 0,
        successRate: 0,
        totalProfit: '0',
        averageProfit: '0',
      },
      pairs: [],
      monitoring: {
        cachedPrices: 0,
      },
      execution: {
        consecutiveFailures: 0,
        dailyLoss: '0',
        isExecuting: false,
      },
    };
  }
}
