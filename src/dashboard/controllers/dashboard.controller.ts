import { Controller, Get, Res } from '@nestjs/common';
import { Response } from 'express';
import { DashboardService } from '../services/dashboard.service';

@Controller('dashboard')
export class DashboardController {
  constructor(private readonly dashboardService: DashboardService) {}

  @Get()
  async getDashboard(@Res() res: Response) {
    const html = await this.dashboardService.getDashboardHtml();
    res.setHeader('Content-Type', 'text/html');
    res.send(html);
  }

  @Get('api/status')
  getStatus() {
    return this.dashboardService.getStatus();
  }
}
