#!/usr/bin/env ts-node

/**
 * Test script to demonstrate enhanced logger formatting for Wei values
 */

import { logger } from '../utils/logger';

async function testLoggerFormatting() {
  console.log('🧪 Testing Enhanced Logger Wei Formatting with USD Values\n');

  // Wait a moment for ETH price to be fetched
  console.log('📊 Fetching current ETH price...');
  await new Promise(resolve => setTimeout(resolve, 2000));

  // Test the original error message you encountered
  const originalError = new Error('insufficient funds for gas * price + value: have 3800073449395503 want 8989122000000000');
  logger.error('Transaction failed', originalError);

  console.log('\n' + '='.repeat(80) + '\n');

  // Test insufficient funds method with your specific values
  await logger.insufficientFunds('3800073449395503', '8989122000000000', 'Gas estimation for MEV transaction');

  console.log('\n' + '='.repeat(80) + '\n');

  // Test gas estimation logging
  await logger.gasEstimation('300000', '20000000000', '6000000000000000'); // 20 gwei, 0.006 ETH

  console.log('\n' + '='.repeat(80) + '\n');

  // Test various Wei values in different log levels
  logger.info('Balance check: wallet has 1500000000000000000 wei available');
  logger.warn('Low balance detected: only 500000000000000000 wei remaining');
  logger.error('Transaction failed: required 2000000000000000000 wei but only have 1000000000000000000 wei');

  console.log('\n' + '='.repeat(80) + '\n');

  // Test with BigInt values
  const largeWei = BigInt('1000000000000000000'); // 1 ETH
  const smallWei = BigInt('100000000000000'); // 0.0001 ETH

  await logger.insufficientFunds(smallWei, largeWei, 'Flashloan execution');

  console.log('\n' + '='.repeat(80) + '\n');

  // Test edge cases
  logger.info('Small value: 1000000000000000 wei should be formatted');
  logger.info('Very small value: 1000000000000 wei should NOT be formatted');
  logger.info('Regular number: 12345 should not be formatted');

  console.log('\n' + '='.repeat(80) + '\n');

  // Test synchronous versions (when async is not possible)
  console.log('🔄 Testing synchronous versions:');
  logger.insufficientFundsSync('5000000000000000000', '10000000000000000000', 'Sync test');
  logger.gasEstimationSync('250000', '30000000000', '7500000000000000');

  console.log('\n✅ Logger formatting test completed!');
}

// Run the test if this file is executed directly
if (require.main === module) {
  testLoggerFormatting().catch(console.error);
}

export { testLoggerFormatting };
