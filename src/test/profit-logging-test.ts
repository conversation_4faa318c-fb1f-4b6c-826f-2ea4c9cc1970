#!/usr/bin/env ts-node

/**
 * Test script to demonstrate enhanced profit logging with USD values
 */

import { ethers } from 'ethers';
import { logger } from '../utils/logger';

async function testProfitLogging() {
  console.log('💰 Testing Enhanced Profit Logging with USD Values\n');

  // Wait for ETH price to be fetched
  console.log('📊 Fetching current ETH price...');
  await new Promise(resolve => setTimeout(resolve, 2000));

  console.log('='.repeat(80));
  console.log('🔍 PROFITABILITY ANALYSIS EXAMPLES');
  console.log('='.repeat(80));

  // Example 1: Profitable MEV opportunity
  console.log('\n1️⃣ Profitable MEV Opportunity:');
  const expectedProfit1 = ethers.parseEther('0.05'); // 0.05 ETH
  const gasCost1 = ethers.parseEther('0.01'); // 0.01 ETH
  const netProfit1 = expectedProfit1 - gasCost1; // 0.04 ETH

  await logger.profitabilityAnalysis(
    expectedProfit1,
    gasCost1,
    netProfit1,
    'Arbitrage Opportunity'
  );

  console.log('\n' + '-'.repeat(60));

  // Example 2: Marginal opportunity
  console.log('\n2️⃣ Marginal MEV Opportunity:');
  const expectedProfit2 = ethers.parseEther('0.008'); // 0.008 ETH
  const gasCost2 = ethers.parseEther('0.006'); // 0.006 ETH
  const netProfit2 = expectedProfit2 - gasCost2; // 0.002 ETH

  await logger.profitabilityAnalysis(
    expectedProfit2,
    gasCost2,
    netProfit2,
    'Flashloan Opportunity'
  );

  console.log('\n' + '-'.repeat(60));

  // Example 3: Unprofitable opportunity (high gas)
  console.log('\n3️⃣ Unprofitable Opportunity (High Gas):');
  const expectedProfit3 = ethers.parseEther('0.005'); // 0.005 ETH
  const gasCost3 = ethers.parseEther('0.012'); // 0.012 ETH (high gas)
  const netProfit3 = expectedProfit3 - gasCost3; // -0.007 ETH (loss)

  await logger.profitabilityAnalysis(
    expectedProfit3,
    gasCost3,
    netProfit3,
    'High Gas Scenario'
  );

  console.log('\n' + '='.repeat(80));
  console.log('📊 INDIVIDUAL PROFIT COMPONENTS');
  console.log('='.repeat(80));

  // Example 4: Individual component logging
  console.log('\n4️⃣ Individual Component Logging:');
  
  await logger.expectedProfit(ethers.parseEther('0.025'), 'Sandwich Attack');
  await logger.gasCost(ethers.parseEther('0.008'), 'Sandwich Attack');
  await logger.netProfit(ethers.parseEther('0.017'), 'Sandwich Attack');

  console.log('\n' + '-'.repeat(60));

  // Example 5: Large profit opportunity
  console.log('\n5️⃣ Large Profit Opportunity:');
  const largeProfit = ethers.parseEther('0.5'); // 0.5 ETH
  const largeCost = ethers.parseEther('0.02'); // 0.02 ETH
  const largeNet = largeProfit - largeCost; // 0.48 ETH

  await logger.profitabilityAnalysis(
    largeProfit,
    largeCost,
    largeNet,
    'Large Arbitrage'
  );

  console.log('\n' + '-'.repeat(60));

  // Example 6: Small amounts (testing precision)
  console.log('\n6️⃣ Small Amount Precision Test:');
  const smallProfit = ethers.parseEther('0.001'); // 0.001 ETH
  const smallCost = ethers.parseEther('0.0005'); // 0.0005 ETH
  const smallNet = smallProfit - smallCost; // 0.0005 ETH

  await logger.profitabilityAnalysis(
    smallProfit,
    smallCost,
    smallNet,
    'Micro Arbitrage'
  );

  console.log('\n' + '='.repeat(80));
  console.log('🔄 LEGACY PROFIT CALCULATION (Enhanced)');
  console.log('='.repeat(80));

  // Example 7: Legacy method with enhancement
  console.log('\n7️⃣ Legacy Profit Calculation Method:');
  await logger.profitCalculation(ethers.parseEther('0.03'), true);
  await logger.profitCalculation(ethers.parseEther('-0.005'), false);

  console.log('\n' + '='.repeat(80));
  console.log('⚡ REAL-TIME SCENARIOS');
  console.log('='.repeat(80));

  // Example 8: Simulating real MEV scenarios
  console.log('\n8️⃣ Real MEV Scenarios:');
  
  // Uniswap V2/V3 arbitrage
  console.log('\n📈 Uniswap V2/V3 Arbitrage:');
  await logger.profitabilityAnalysis(
    ethers.parseEther('0.015'),
    ethers.parseEther('0.004'),
    ethers.parseEther('0.011'),
    'Uniswap V2/V3'
  );

  // Balancer flashloan
  console.log('\n🏊 Balancer Flashloan:');
  await logger.profitabilityAnalysis(
    ethers.parseEther('0.08'),
    ethers.parseEther('0.015'),
    ethers.parseEther('0.065'),
    'Balancer Flashloan'
  );

  // MEV-Share opportunity
  console.log('\n🤝 MEV-Share Opportunity:');
  await logger.profitabilityAnalysis(
    ethers.parseEther('0.035'),
    ethers.parseEther('0.009'),
    ethers.parseEther('0.026'),
    'MEV-Share Bundle'
  );

  console.log('\n✅ Profit logging test completed!');
  console.log('\n💡 All profit values now include real-time USD conversion!');
}

// Run the test if this file is executed directly
if (require.main === module) {
  testProfitLogging().catch(console.error);
}

export { testProfitLogging };
