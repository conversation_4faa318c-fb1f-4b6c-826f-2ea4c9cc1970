import { ethers } from 'ethers';
import { EventEmitter } from 'events';
import { Config } from '../types';
import { MAINNET_ADDRESSES } from '../config';

interface PriceData {
  pair: string;
  dex: string;
  price: number;
  timestamp: number;
  liquidity?: number;
  gasEstimate?: number;
}

interface ArbitrageOpportunity {
  pair: string;
  buyDex: string;
  sellDex: string;
  buyPrice: number;
  sellPrice: number;
  priceSpread: number;
  profitBps: number;
  estimatedProfitUSD: number;
  confidence: number;
  timestamp: number;
  flashloanAmount: string;
}

export class MultiDexMonitor extends EventEmitter {
  private provider: ethers.JsonRpcProvider;
  private config: Config;
  private priceCache: Map<string, PriceData> = new Map();
  private isMonitoring: boolean = false;
  private monitoringInterval?: NodeJS.Timeout;
  private uniswapV3Quoter!: ethers.Contract;
  private curveContracts: Map<string, ethers.Contract> = new Map();
  private sushiswapRouter!: ethers.Contract;

  constructor(provider: ethers.JsonRpcProvider, config: Config) {
    super();
    this.provider = provider;
    this.config = config;
    
    // Initialize DEX contracts
    this.initializeContracts();
  }

  private initializeContracts() {
    // Uniswap V3 Quoter
    this.uniswapV3Quoter = new ethers.Contract(
      MAINNET_ADDRESSES.UNISWAP_V3_QUOTER,
      [
        'function quoteExactInputSingle(address tokenIn, address tokenOut, uint24 fee, uint256 amountIn, uint160 sqrtPriceLimitX96) external returns (uint256 amountOut)'
      ],
      this.provider
    );

    // SushiSwap Router (V2 compatible)
    this.sushiswapRouter = new ethers.Contract(
      MAINNET_ADDRESSES.SUSHISWAP_ROUTER,
      [
        'function getAmountsOut(uint amountIn, address[] calldata path) external view returns (uint[] memory amounts)'
      ],
      this.provider
    );

    // Curve pools
    const curveABI = [
      'function get_dy(int128 i, int128 j, uint256 dx) external view returns (uint256)',
      'function coins(uint256 i) external view returns (address)',
      'function balances(uint256 i) external view returns (uint256)'
    ];

    this.curveContracts.set('3POOL', new ethers.Contract(
      MAINNET_ADDRESSES.CURVE_3POOL,
      curveABI,
      this.provider
    ));
  }

  async startMonitoring(): Promise<void> {
    if (this.isMonitoring) {
      console.log('🔄 Multi-DEX monitoring already running');
      return;
    }

    console.log('🚀 Starting Multi-DEX Price Monitoring');
    console.log(`📊 Monitoring pairs: ${this.config.multiDexPairs.join(', ')}`);
    console.log(`🏪 DEX sources: ${this.config.multiDexSources.join(', ')}`);
    
    this.isMonitoring = true;
    
    // Initial price fetch
    await this.fetchAllPrices();
    
    // Start monitoring interval
    this.monitoringInterval = setInterval(async () => {
      try {
        await this.fetchAllPrices();
        await this.scanForArbitrageOpportunities();
      } catch (error) {
        console.error('❌ Error in multi-DEX monitoring cycle:', error);
      }
    }, this.config.multiDexScanIntervalMs);

    console.log('✅ Multi-DEX monitoring started');
  }

  async stopMonitoring(): Promise<void> {
    if (!this.isMonitoring) return;

    console.log('⏹️ Stopping Multi-DEX monitoring');
    this.isMonitoring = false;
    
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = undefined;
    }

    console.log('✅ Multi-DEX monitoring stopped');
  }

  private async fetchAllPrices(): Promise<void> {
    const fetchPromises: Promise<void>[] = [];

    for (const pair of this.config.multiDexPairs) {
      for (const dex of this.config.multiDexSources) {
        fetchPromises.push(this.fetchPriceForDexPair(pair, dex));
      }
    }

    await Promise.allSettled(fetchPromises);
  }

  private async fetchPriceForDexPair(pair: string, dex: string): Promise<void> {
    try {
      const [tokenA, tokenB] = this.getPairTokens(pair);
      if (!tokenA || !tokenB) return;

      const amount = ethers.parseUnits('1', 18); // 1 unit for price calculation
      let price: number = 0;

      switch (dex) {
        case 'UNISWAP_V3':
          price = await this.fetchUniswapV3Price(tokenA, tokenB, amount);
          break;
        case 'SUSHISWAP':
          price = await this.fetchSushiswapPrice(tokenA, tokenB, amount);
          break;
        case 'CURVE':
          price = await this.fetchCurvePrice(tokenA, tokenB, amount);
          break;
      }

      if (price > 0) {
        const priceData: PriceData = {
          pair,
          dex,
          price,
          timestamp: Date.now()
        };

        const cacheKey = `${pair}-${dex}`;
        this.priceCache.set(cacheKey, priceData);

        this.emit('priceUpdate', priceData);
      }

    } catch (error) {
      // Silently handle errors to avoid spam
      if (this.config.debugMode) {
        console.error(`❌ Error fetching price for ${pair} on ${dex}:`, error instanceof Error ? error.message : String(error));
      }
    }
  }

  private getPairTokens(pair: string): [string, string] | [null, null] {
    const tokenMap: { [key: string]: string } = {
      'WETH': MAINNET_ADDRESSES.WETH,
      'USDC': MAINNET_ADDRESSES.USDC,
      'DAI': MAINNET_ADDRESSES.DAI,
      'USDT': MAINNET_ADDRESSES.USDT
    };

    const [symbolA, symbolB] = pair.split('/');
    const tokenA = tokenMap[symbolA];
    const tokenB = tokenMap[symbolB];

    return tokenA && tokenB ? [tokenA, tokenB] : [null, null];
  }

  private async fetchUniswapV3Price(tokenA: string, tokenB: string, amount: bigint): Promise<number> {
    try {
      // Try different fee tiers
      const feeTiers = [100, 500, 3000, 10000]; // 0.01%, 0.05%, 0.3%, 1%
      
      for (const fee of feeTiers) {
        try {
          const result = await this.provider.call({
            to: this.uniswapV3Quoter.target,
            data: this.uniswapV3Quoter.interface.encodeFunctionData('quoteExactInputSingle', [
              tokenA,
              tokenB,
              fee,
              amount,
              0
            ])
          });

          if (result && result !== '0x') {
            const amountOut = ethers.AbiCoder.defaultAbiCoder().decode(['uint256'], result)[0];
            return Number(ethers.formatUnits(amountOut, 18));
          }
        } catch (feeError) {
          continue; // Try next fee tier
        }
      }
      return 0;
    } catch (error) {
      return 0;
    }
  }

  private async fetchSushiswapPrice(tokenA: string, tokenB: string, amount: bigint): Promise<number> {
    try {
      const path = [tokenA, tokenB];
      const amounts = await this.sushiswapRouter.getAmountsOut(amount, path);
      return Number(ethers.formatUnits(amounts[1], 18));
    } catch (error) {
      return 0;
    }
  }

  private async fetchCurvePrice(tokenA: string, tokenB: string, amount: bigint): Promise<number> {
    try {
      // Only handle USDC/DAI on Curve 3Pool for now
      if (!this.isCurvePairSupported(tokenA, tokenB)) {
        return 0;
      }

      const curveContract = this.curveContracts.get('3POOL');
      if (!curveContract) return 0;

      const indices = this.getCurveIndices(tokenA, tokenB);
      if (indices.i === -1 || indices.j === -1) return 0;

      const amountOut = await curveContract.get_dy(indices.i, indices.j, amount);
      return Number(ethers.formatUnits(amountOut, 18));
    } catch (error) {
      return 0;
    }
  }

  private isCurvePairSupported(tokenA: string, tokenB: string): boolean {
    const supportedTokens = [
      MAINNET_ADDRESSES.DAI,
      MAINNET_ADDRESSES.USDC,
      MAINNET_ADDRESSES.USDT
    ];
    return supportedTokens.includes(tokenA) && supportedTokens.includes(tokenB);
  }

  private getCurveIndices(tokenA: string, tokenB: string): { i: number; j: number } {
    const tokenIndices: { [key: string]: number } = {
      [MAINNET_ADDRESSES.DAI]: 0,
      [MAINNET_ADDRESSES.USDC]: 1,
      [MAINNET_ADDRESSES.USDT]: 2
    };

    return {
      i: tokenIndices[tokenA] ?? -1,
      j: tokenIndices[tokenB] ?? -1
    };
  }

  private async scanForArbitrageOpportunities(): Promise<void> {
    for (const pair of this.config.multiDexPairs) {
      const opportunities = this.findArbitrageOpportunities(pair);
      
      for (const opportunity of opportunities) {
        if (opportunity.profitBps >= this.config.multiDexPriceDeviationThresholdBps &&
            opportunity.confidence >= this.config.multiDexConfidenceThreshold) {
          this.emit('arbitrageOpportunity', opportunity);
        }
      }
    }
  }

  private findArbitrageOpportunities(pair: string): ArbitrageOpportunity[] {
    const opportunities: ArbitrageOpportunity[] = [];
    const currentTime = Date.now();

    // Get current prices for all DEXs for this pair
    const prices: { dex: string; price: number; data: PriceData }[] = [];
    
    for (const dex of this.config.multiDexSources) {
      const cacheKey = `${pair}-${dex}`;
      const priceData = this.priceCache.get(cacheKey);
      
      if (priceData && (currentTime - priceData.timestamp) < this.config.multiDexMaxPriceAgeMs) {
        prices.push({
          dex,
          price: priceData.price,
          data: priceData
        });
      }
    }

    // Find arbitrage opportunities between all DEX pairs
    for (let i = 0; i < prices.length; i++) {
      for (let j = i + 1; j < prices.length; j++) {
        const dex1 = prices[i];
        const dex2 = prices[j];

        // Calculate price spread
        const priceDiff = Math.abs(dex1.price - dex2.price);
        const avgPrice = (dex1.price + dex2.price) / 2;
        const spreadBps = (priceDiff / avgPrice) * 10000;

        if (spreadBps >= this.config.multiDexPriceDeviationThresholdBps) {
          // Determine buy/sell direction
          const buyDex = dex1.price < dex2.price ? dex1 : dex2;
          const sellDex = dex1.price < dex2.price ? dex2 : dex1;

          // Estimate profit
          const estimatedProfitUSD = this.estimateProfitUSD(
            buyDex.price,
            sellDex.price,
            this.config.multiDexMinProfitUSD
          );

          const opportunity: ArbitrageOpportunity = {
            pair,
            buyDex: buyDex.dex,
            sellDex: sellDex.dex,
            buyPrice: buyDex.price,
            sellPrice: sellDex.price,
            priceSpread: priceDiff,
            profitBps: spreadBps,
            estimatedProfitUSD,
            confidence: this.calculateConfidence(spreadBps, estimatedProfitUSD),
            timestamp: currentTime,
            flashloanAmount: this.determineOptimalFlashloanAmount(estimatedProfitUSD)
          };

          opportunities.push(opportunity);
        }
      }
    }

    return opportunities.sort((a, b) => b.profitBps - a.profitBps);
  }

  private estimateProfitUSD(buyPrice: number, sellPrice: number, minProfitUSD: number): number {
    const profitPerUnit = sellPrice - buyPrice;
    const profitPercentage = profitPerUnit / buyPrice;
    
    // Estimate with a reasonable trade size (e.g., $10,000)
    const tradeSize = 10000;
    return tradeSize * profitPercentage;
  }

  private calculateConfidence(spreadBps: number, estimatedProfitUSD: number): number {
    let confidence = Math.min(spreadBps / 10, 100); // Base confidence from spread
    
    // Boost confidence for higher estimated profits
    if (estimatedProfitUSD > 50) confidence += 20;
    else if (estimatedProfitUSD > 20) confidence += 10;
    
    return Math.min(confidence, 100);
  }

  private determineOptimalFlashloanAmount(estimatedProfitUSD: number): string {
    // Choose flashloan amount based on estimated profit
    if (estimatedProfitUSD > 100) return '50'; // 50 ETH for high profit
    if (estimatedProfitUSD > 50) return '25';  // 25 ETH for medium profit
    if (estimatedProfitUSD > 20) return '10';  // 10 ETH for low profit
    return '5'; // 5 ETH for minimal profit
  }

  // Public methods for external access
  getCurrentPrices(pairName?: string): PriceData[] {
    const prices: PriceData[] = [];
    
    for (const [key, priceData] of this.priceCache.entries()) {
      if (!pairName || key.startsWith(pairName)) {
        prices.push(priceData);
      }
    }
    
    return prices.sort((a, b) => a.price - b.price);
  }

  getMonitoringStats() {
    return {
      isMonitoring: this.isMonitoring,
      cachedPrices: this.priceCache.size,
      monitoredPairs: this.config.multiDexPairs.length,
      totalDexes: this.config.multiDexSources.length,
      lastUpdate: Math.max(...Array.from(this.priceCache.values()).map(p => p.timestamp))
    };
  }
}

export default MultiDexMonitor;
