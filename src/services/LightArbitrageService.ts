import { ethers } from 'ethers';
import { Logger } from '../utils/logger';

export interface TradeStep {
  dex: string;                    // DEX router/pool address
  dexType: number;               // 0=V2, 1=V3, 2=CURVE, 3=BALANCER_V2
  tokenIn: string;               // Input token address
  tokenOut: string;              // Output token address
  slippageToleranceBps: number;  // Slippage tolerance in basis points
  v3Fee: number;                 // V3 fee (0 for non-V3)
  balancerPoolId: string;        // Balancer pool ID (0x0 for non-Balancer)
}

export interface ArbitrageParams {
  tradeSteps: TradeStep[];       // Array of trade steps
  minProfit: string;             // Minimum profit in wei
  provider: number;              // 0=AAVE, 1=BALANCER
  maxGasCostWei: string;         // Maximum gas cost in wei
}

export enum FlashloanProvider {
  AAVE = 0,
  BALANCER = 1
}

export enum DEXType {
  V2 = 0,
  V3 = 1,
  CURVE = 2,
  BALANCER_V2 = 3
}

export class LightArbitrageService {
  private logger = new Logger();
  private contract: ethers.Contract;
  private provider: ethers.Provider;
  private wallet: ethers.Wallet;

  // DEX addresses (extracted from HybridFlashloanArbitrageFixed.sol)
  private readonly DEX_ADDRESSES = {
    // Uniswap routers
    UNISWAP_V2: '******************************************',
    UNISWAP_V3: '******************************************',
    UNISWAP_V3_QUOTER: '******************************************',

    // V2 forks
    SUSHISWAP: '******************************************',

    // Curve pools (major stablecoin and ETH pools)
    CURVE_3POOL: '******************************************', // DAI/USDC/USDT
    CURVE_TRICRYPTO2: '******************************************', // USDT/WBTC/WETH
    CURVE_GUSD: '******************************************', // GUSD Metapool
    CURVE_STECRV: '******************************************', // stETH/ETH

    // Balancer V2
    BALANCER_V2: '******************************************',

    // Flashloan providers
    AAVE_POOL: '******************************************',
    BALANCER_VAULT: '******************************************'
  };

  // Token addresses (mainnet)
  private readonly TOKENS = {
    WETH: '******************************************',
    USDC: '******************************************',
    DAI: '******************************************',
    USDT: '******************************************',
    WBTC: '******************************************',
    BAL: '******************************************'
  };

  // Balancer V2 pool IDs (real mainnet pools from HybridFlashloanArbitrageFixed.sol)
  private readonly BALANCER_POOLS = {
    'WETH-USDC': '0x96646936b91d6b9d7d0c47c496afbf3d6ec7b6f8000200000000000000000019', // 80/20 Weighted Pool (1% fee)
    'WETH-DAI': '0x0b09dea16768f0799065c475be02919503cb2a3500020000000000000000001a',  // 80/20 Weighted Pool (1% fee)
    'USDC-DAI': '0x06df3b2bbb68adc8b0e302443692037ed9f91b42000000000000000000000063', // Stable Pool (0.1% fee)
    'BAL-WETH': '0x5c6ee304399dbdb9c8ef030ab642b10820db8f56000200000000000000000014'  // 80/20 Weighted Pool (1% fee)
  };

  // V3 fees for different pairs
  private readonly V3_FEES = {
    WETH_USDC: 3000,  // 0.3%
    WETH_DAI: 3000,   // 0.3%
    USDC_DAI: 100,    // 0.01%
    WETH_USDT: 3000,  // 0.3%
    WBTC_WETH: 3000   // 0.3%
  };

  constructor(
    contractAddress: string,
    provider: ethers.Provider,
    wallet: ethers.Wallet,
    contractABI: any[]
  ) {
    this.provider = provider;
    this.wallet = wallet;
    this.contract = new ethers.Contract(contractAddress, contractABI, wallet);
  }

  /**
   * Execute arbitrage with off-chain provided trade steps
   */
  async executeArbitrage(
    asset: string,
    amount: string,
    tradeSteps: TradeStep[],
    minProfit: string,
    provider: FlashloanProvider = FlashloanProvider.AAVE,
    maxGasCostWei: string = ethers.parseUnits('50', 'gwei').toString()
  ): Promise<ethers.TransactionResponse> {
    this.logger.info('🚀 Executing arbitrage with Light contract (off-chain architecture)');
    
    // Validate trade steps
    this.validateTradeSteps(tradeSteps, asset);
    
    // Create arbitrage parameters
    const arbParams: ArbitrageParams = {
      tradeSteps,
      minProfit,
      provider,
      maxGasCostWei
    };

    // Encode parameters
    const encodedParams = this.encodeArbitrageParams(arbParams);

    this.logger.info(`📋 Trade Steps: ${tradeSteps.length}`);
    this.logger.info(`💰 Min Profit: ${ethers.formatEther(minProfit)} ETH`);
    this.logger.info(`⛽ Max Gas Cost: ${ethers.formatUnits(maxGasCostWei, 'gwei')} gwei`);
    this.logger.info(`🏦 Provider: ${provider === FlashloanProvider.AAVE ? 'AAVE' : 'BALANCER'}`);

    // Execute flashloan
    try {
      const tx = await this.contract.executeOptimalFlashloan(
        asset,
        amount,
        encodedParams
      );

      this.logger.info(`✅ Transaction submitted: ${tx.hash}`);
      return tx;
    } catch (error) {
      this.logger.error('❌ Arbitrage execution failed:', error);
      throw error;
    }
  }

  /**
   * Create simple 2-step arbitrage: DEX1 -> DEX2
   */
  createSimpleArbitrage(
    tokenA: string,
    tokenB: string,
    dex1: string,
    dex1Type: DEXType,
    dex2: string,
    dex2Type: DEXType,
    slippage: number = 100 // 1%
  ): TradeStep[] {
    const tradeSteps: TradeStep[] = [
      {
        dex: dex1,
        dexType: dex1Type,
        tokenIn: tokenA,
        tokenOut: tokenB,
        slippageToleranceBps: slippage,
        v3Fee: dex1Type === DEXType.V3 ? 3000 : 0, // 0.3% for V3
        balancerPoolId: dex1Type === DEXType.BALANCER_V2 ? this.getBalancerPoolId(tokenA, tokenB) : '0x0000000000000000000000000000000000000000000000000000000000000000'
      },
      {
        dex: dex2,
        dexType: dex2Type,
        tokenIn: tokenB,
        tokenOut: tokenA,
        slippageToleranceBps: slippage,
        v3Fee: dex2Type === DEXType.V3 ? 3000 : 0,
        balancerPoolId: dex2Type === DEXType.BALANCER_V2 ? this.getBalancerPoolId(tokenB, tokenA) : '0x0000000000000000000000000000000000000000000000000000000000000000'
      }
    ];

    return tradeSteps;
  }

  /**
   * Create complex multi-DEX arbitrage route
   */
  createComplexArbitrage(
    tokens: string[],
    dexes: string[],
    dexTypes: DEXType[],
    slippage: number = 100
  ): TradeStep[] {
    if (tokens.length !== dexes.length + 1) {
      throw new Error('Invalid route: tokens.length must equal dexes.length + 1');
    }

    const tradeSteps: TradeStep[] = [];

    for (let i = 0; i < dexes.length; i++) {
      const tokenIn = tokens[i];
      const tokenOut = tokens[i + 1];
      const dex = dexes[i];
      const dexType = dexTypes[i];

      tradeSteps.push({
        dex,
        dexType,
        tokenIn,
        tokenOut,
        slippageToleranceBps: slippage,
        v3Fee: dexType === DEXType.V3 ? 3000 : 0,
        balancerPoolId: dexType === DEXType.BALANCER_V2 ? this.getBalancerPoolId(tokenIn, tokenOut) : '0x0000000000000000000000000000000000000000000000000000000000000000'
      });
    }

    return tradeSteps;
  }

  /**
   * Get Balancer pool ID for token pair (off-chain lookup)
   */
  private getBalancerPoolId(tokenA: string, tokenB: string): string {
    const key1 = this.getTokenSymbol(tokenA) + '-' + this.getTokenSymbol(tokenB);
    const key2 = this.getTokenSymbol(tokenB) + '-' + this.getTokenSymbol(tokenA);
    
    return this.BALANCER_POOLS[key1] || this.BALANCER_POOLS[key2] || '0x0000000000000000000000000000000000000000000000000000000000000000';
  }

  /**
   * Get token symbol from address (off-chain lookup)
   */
  private getTokenSymbol(address: string): string {
    const symbols = {
      [this.TOKENS.WETH]: 'WETH',
      [this.TOKENS.USDC]: 'USDC',
      [this.TOKENS.DAI]: 'DAI',
      [this.TOKENS.USDT]: 'USDT'
    };
    return symbols[address] || 'UNKNOWN';
  }

  /**
   * Validate trade steps
   */
  private validateTradeSteps(tradeSteps: TradeStep[], asset: string): void {
    if (tradeSteps.length === 0) {
      throw new Error('Trade steps cannot be empty');
    }

    if (tradeSteps[0].tokenIn !== asset) {
      throw new Error('First trade step tokenIn must match flashloan asset');
    }

    const lastStep = tradeSteps[tradeSteps.length - 1];
    if (lastStep.tokenOut !== asset) {
      throw new Error('Last trade step tokenOut must match flashloan asset');
    }

    // Validate step continuity
    for (let i = 0; i < tradeSteps.length - 1; i++) {
      if (tradeSteps[i].tokenOut !== tradeSteps[i + 1].tokenIn) {
        throw new Error(`Trade step ${i} tokenOut must match step ${i + 1} tokenIn`);
      }
    }
  }

  /**
   * Encode arbitrage parameters for contract call
   */
  private encodeArbitrageParams(params: ArbitrageParams): string {
    const abiCoder = ethers.AbiCoder.defaultAbiCoder();
    
    return abiCoder.encode(
      ['tuple(tuple(address dex, uint8 dexType, address tokenIn, address tokenOut, uint256 slippageToleranceBps, uint24 v3Fee, bytes32 balancerPoolId)[] tradeSteps, uint256 minProfit, uint8 provider, uint256 maxGasCostWei)'],
      [params]
    );
  }

  /**
   * Get DEX addresses for easy access
   */
  getDEXAddresses() {
    return this.DEX_ADDRESSES;
  }

  /**
   * Get token addresses for easy access
   */
  getTokenAddresses() {
    return this.TOKENS;
  }

  /**
   * Get Balancer pool IDs for easy access
   */
  getBalancerPools() {
    return this.BALANCER_POOLS;
  }
}
