import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
// import { GraphQLModule } from '@nestjs/graphql';
// import { ApolloDriver, ApolloDriverConfig } from '@nestjs/apollo';
import { ScheduleModule } from '@nestjs/schedule';
import { EventEmitterModule } from '@nestjs/event-emitter';
// import { join } from 'path';

// Import custom modules
import { ConfigurationModule } from './config/configuration.module';
import { BlockchainModule } from './blockchain/blockchain.module';
import { ContractsModule } from './contracts/contracts.module';
import { ArbitrageModule } from './arbitrage/arbitrage.module';
import { BundlesModule } from './bundles/bundles.module';
import { MonitoringModule } from './monitoring/monitoring.module';
import { GraphqlModule } from './graphql/graphql.module';
import { DashboardModule } from './dashboard/dashboard.module';
import { SharedModule } from './shared/shared.module';

@Module({
  imports: [
    // Core NestJS modules
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ['.env.local', '.env'],
    }),
    
    // GraphQL temporarily disabled for initial setup
    // GraphQLModule.forRoot<ApolloDriverConfig>({
    //   driver: ApolloDriver,
    //   autoSchemaFile: join(process.cwd(), 'src/schema.gql'),
    //   sortSchema: true,
    //   playground: true,
    //   introspection: true,
    // }),
    
    ScheduleModule.forRoot(),
    EventEmitterModule.forRoot(),
    
    // Custom application modules
    ConfigurationModule,
    SharedModule,
    BlockchainModule,
    ContractsModule,
    ArbitrageModule,
    BundlesModule,
    MonitoringModule,
    GraphqlModule,
    DashboardModule,
  ],
  controllers: [],
  providers: [],
})
export class AppModule {}
