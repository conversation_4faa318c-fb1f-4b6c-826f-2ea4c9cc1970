const { ethers } = require('ethers');

async function testRealError() {
    console.log('🔍 Testing contract without try-catch to see real error...');
    
    // Connect to Hardhat fork
    const provider = new ethers.JsonRpcProvider('http://127.0.0.1:8547');
    const wallet = new ethers.Wallet('0xac0974bec39a17e36ba4a6b4d238ff944bacb478cbed5efcae784d7bf4f2ff80', provider);
    
    console.log(`📋 Test Configuration:`);
    console.log(`   Provider: Hardhat fork (mainnet)`);
    console.log(`   Wallet: ${wallet.address}`);
    
    // Deploy the contract without try-catch
    console.log('\n🚀 Deploying contract without try-catch...');
    
    const aavePoolAddressesProvider = '******************************************';
    const balancerVault = '******************************************';
    
    const fs = require('fs');
    const contractArtifact = JSON.parse(fs.readFileSync('./artifacts/contracts/HybridFlashloanArbitrage.sol/HybridFlashloanArbitrage.json', 'utf8'));
    
    const contractFactory = new ethers.ContractFactory(
        contractArtifact.abi,
        contractArtifact.bytecode,
        wallet
    );
    
    const contract = await contractFactory.deploy(aavePoolAddressesProvider, balancerVault);
    await contract.waitForDeployment();
    const contractAddress = await contract.getAddress();
    
    console.log(`✅ Contract deployed at: ${contractAddress}`);
    
    // Test the exact same transaction that's failing
    console.log('\n🧪 Testing with real error messages...');
    
    const wethAddress = '******************************************';
    const usdcAddress = '******************************************';
    const v3Router = '******************************************';
    const v2Router = '******************************************';
    
    const testParams = ethers.AbiCoder.defaultAbiCoder().encode(
        ['address', 'address', 'address', 'address', 'uint24', 'uint256', 'uint8'],
        [wethAddress, usdcAddress, v3Router, v2Router, 3000, ethers.parseEther('0.0001'), 0]
    );
    
    // Test with different amounts to see the real error
    const testAmounts = [
        { amount: ethers.parseEther('1'), label: '1 ETH' },
        { amount: ethers.parseEther('0.1'), label: '0.1 ETH' },
        { amount: ethers.parseEther('20'), label: '20 ETH (original failing amount)' }
    ];
    
    for (const test of testAmounts) {
        console.log(`\n   Testing ${test.label}:`);
        
        try {
            await contract.executeOptimalFlashloan.staticCall(
                wethAddress,
                test.amount,
                testParams
            );
            
            console.log(`      ✅ ${test.label}: SUCCESS`);
            
        } catch (error) {
            console.log(`      ❌ ${test.label}: ${error.message.split('(')[0]}`);
            
            // Now we should see the real error from Aave
            if (error.message.includes('Invalid initiator')) {
                console.log(`         🎯 REAL ERROR: Invalid initiator`);
                console.log(`         💡 This means our contract is not properly set up as an Aave receiver`);
            } else if (error.message.includes('FLASHLOAN_PREMIUM_INVALID')) {
                console.log(`         🎯 REAL ERROR: Flashloan premium invalid`);
            } else if (error.message.includes('COLLATERAL_BALANCE_IS_ZERO')) {
                console.log(`         🎯 REAL ERROR: No collateral balance`);
            } else if (error.message.includes('RESERVE_LIQUIDITY_NOT_ZERO')) {
                console.log(`         🎯 REAL ERROR: Reserve liquidity issue`);
            } else if (error.message.includes('INSUFFICIENT_OUTPUT_AMOUNT')) {
                console.log(`         ✅ TRADE ERROR: This means flashloan works, trade fails`);
            } else {
                console.log(`         🔍 OTHER ERROR: ${error.message}`);
                
                // Print more details
                if (error.data) {
                    console.log(`         Error data: ${error.data}`);
                }
            }
        }
    }
    
    // Test Balancer too
    console.log('\n🧪 Testing Balancer flashloan...');
    
    const balancerParams = ethers.AbiCoder.defaultAbiCoder().encode(
        ['address', 'address', 'address', 'address', 'uint24', 'uint256', 'uint8'],
        [wethAddress, usdcAddress, v3Router, v2Router, 3000, ethers.parseEther('0.0001'), 1] // Balancer provider
    );
    
    try {
        await contract.executeOptimalFlashloan.staticCall(
            wethAddress,
            ethers.parseEther('1'),
            balancerParams
        );
        
        console.log(`   ✅ Balancer flashloan: SUCCESS`);
        
    } catch (error) {
        console.log(`   ❌ Balancer flashloan: ${error.message.split('(')[0]}`);
        
        if (error.message.includes('BAL#')) {
            console.log(`      🎯 BALANCER ERROR: ${error.message}`);
        } else if (error.message.includes('INSUFFICIENT_OUTPUT_AMOUNT')) {
            console.log(`      ✅ TRADE ERROR: Balancer flashloan works, trade fails`);
        }
    }
    
    // Check if the issue is with the contract's POOL address
    console.log('\n🔍 Checking contract\'s POOL address...');
    
    try {
        const poolAddress = await contract.POOL();
        console.log(`   Contract's POOL address: ${poolAddress}`);
        
        // Check if this matches the expected Aave V3 pool
        const expectedPool = '******************************************';
        if (poolAddress.toLowerCase() === expectedPool.toLowerCase()) {
            console.log(`   ✅ POOL address is correct`);
        } else {
            console.log(`   ❌ POOL address mismatch!`);
            console.log(`      Expected: ${expectedPool}`);
            console.log(`      Got: ${poolAddress}`);
        }
        
    } catch (error) {
        console.log(`   ❌ Error getting POOL address: ${error.message}`);
    }
    
    console.log('\n🏁 Real error testing completed!');
}

testRealError().catch(console.error);
