import { expect } from 'chai';
import { ethers } from 'ethers';
import { MEVShareFlashloanStrategy } from '../../src/strategies/mev-share-flashloan';
import { MEVShareEventMonitor, BackrunOpportunity } from '../../src/mev-share/event-monitor';
import { FlashbotsBundleManager } from '../../src/flashbots/bundle-provider';

describe('MEVShareFlashloanStrategy.executeBackrunOpportunity', () => {
  it('builds route and attempts execution (non-throw)', async () => {
    const provider = new ethers.JsonRpcProvider('http://localhost:8545');

    // Stub monitor
    class StubMonitor extends MEVShareEventMonitor {
      constructor(p: any) { super(p); }
      async initialize() {}
      async start() {}
    }

    // Stub bundle manager to simulate success
    class StubFBM extends FlashbotsBundleManager {
      constructor() { super({} as any, {} as any); }
      async simulateBundle() { return { success: true, simulation: { gasUsed: 300000n } } as any; }
      async submitBundle() { return { success: true, bundleHash: '0xBUNDLE' } as any; }
      async getCurrentBlock() { return 123; }
      createBundleTransaction(tx: any) { return { transaction: tx } as any; }
    }

    const monitor = new StubMonitor(provider as any);
    const fbm = new StubFBM();

    const strat = new MEVShareFlashloanStrategy(provider as any, monitor as any, fbm as any);

    const opp: BackrunOpportunity = {
      userTxHash: '0xUSER',
      targetPairs: [],
      estimatedProfit: ethers.parseEther('0.02'),
      gasEstimate: ethers.parseEther('0.005'),
      confidence: 80,
    };

    const res = await strat.executeBackrunOpportunity(opp);
    expect(res).to.be.a('boolean');
  });
});

