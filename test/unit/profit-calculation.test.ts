import { expect } from 'chai';
import { ethers } from 'ethers';
import { FlashbotsExecutor } from '../../src/execution/flashbots-executor';
import { AdvancedGasEstimator } from '../../src/gas/advanced-estimator';

describe('FlashbotsExecutor.calculateActualProfitFromBundle', () => {
  let executor: FlashbotsExecutor;
  let mockProvider: any;
  let mockWallet: any;
  let mockFlashbotsManager: any;
  let mockGasEstimator: any;
  let mockPriceCalculator: any;

  beforeEach(() => {
    // Mock provider
    mockProvider = {
      getBlock: async (blockNumber: number) => ({
        number: blockNumber,
        hash: '0xblock123',
        transactions: ['0xtx1', '0xtx2', '0xtx3']
      }),
      getTransaction: async (txHash: string) => ({
        hash: txHash,
        to: '0xcontract123',
        value: ethers.parseEther('1.0'),
        data: '0xdata123',
        from: '0xwallet123',
        nonce: 42
      }),
      getTransactionReceipt: async (txHash: string) => ({
        hash: txHash,
        status: 1,
        gasUsed: BigInt(300000),
        gasPrice: ethers.parseUnits('20', 'gwei'),
        logs: [
          {
            topics: [
              '0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', // Transfer event
              '0x000000000000000000000000' + '1234567890123456789012345678901234567890'.slice(2), // from
              '0x000000000000000000000000' + 'wallet123'.padStart(40, '0') // to (our wallet)
            ],
            data: ethers.toBeHex(ethers.parseEther('0.1')) // 0.1 ETH worth of tokens
          }
        ]
      })
    };

    // Mock wallet
    mockWallet = {
      address: '0xwallet123',
      getAddress: async () => '0xwallet123'
    };

    // Mock Flashbots manager
    mockFlashbotsManager = {
      simulateBundle: async () => ({ success: true, simulation: { gasUsed: BigInt(300000) } }),
      submitBundle: async () => ({ success: true, bundleHash: '0xbundle123' })
    };

    // Mock gas estimator
    mockGasEstimator = {
      estimateGas: async () => BigInt(300000),
      getOptimalGasPrice: async () => ethers.parseUnits('20', 'gwei')
    };

    // Mock price calculator
    mockPriceCalculator = {
      getTokenPrice: async () => 1.0
    };

    executor = new FlashbotsExecutor(mockProvider as any, mockWallet as any, mockFlashbotsManager as any, mockGasEstimator as any, mockPriceCalculator as any);
  });

  it('calculates profit from Transfer events', async () => {
    const bundleTransactions = [
      {
        signer: mockWallet,
        transaction: {
          to: '0xcontract123',
          value: ethers.parseEther('1.0'),
          data: '0xdata123'
        }
      }
    ];

    // Access private method for testing
    const result = await (executor as any).calculateActualProfitFromBundle(
      '0xbundle123',
      bundleTransactions,
      100
    );

    expect(result).to.not.be.undefined;
    expect(result).to.be.a('bigint');
    
    // Should calculate net profit (token inflow - gas costs)
    // Token inflow: 0.1 ETH / 1000 = 0.0001 ETH (simplified conversion)
    // Gas cost: 300000 * 20 gwei = 0.006 ETH
    // Net should be negative due to gas costs exceeding simplified token conversion
    expect(result < 0n).to.be.true;
  });

  it('handles missing transaction receipts gracefully', async () => {
    // Mock provider that returns null receipts
    const errorProvider = {
      ...mockProvider,
      getTransactionReceipt: async () => null
    };

    const errorExecutor = new FlashbotsExecutor(errorProvider as any, mockWallet as any, mockFlashbotsManager as any, mockGasEstimator as any, mockPriceCalculator as any);

    const bundleTransactions = [
      {
        signer: mockWallet,
        transaction: {
          to: '0xcontract123',
          value: ethers.parseEther('1.0'),
          data: '0xdata123'
        }
      }
    ];

    const result = await (errorExecutor as any).calculateActualProfitFromBundle(
      '0xbundle123',
      bundleTransactions,
      100
    );

    expect(result).to.be.undefined;
  });

  it('handles no matching transactions in block', async () => {
    // Mock provider with different transaction data
    const noMatchProvider = {
      ...mockProvider,
      getTransaction: async (txHash: string) => ({
        hash: txHash,
        to: '0xdifferentcontract',
        value: ethers.parseEther('2.0'),
        data: '0xdifferentdata',
        from: '0xdifferentwallet',
        nonce: 99
      })
    };

    const noMatchExecutor = new FlashbotsExecutor(noMatchProvider as any, mockWallet as any, mockFlashbotsManager as any, mockGasEstimator as any, mockPriceCalculator as any);

    const bundleTransactions = [
      {
        signer: mockWallet,
        transaction: {
          to: '0xcontract123',
          value: ethers.parseEther('1.0'),
          data: '0xdata123'
        }
      }
    ];

    const result = await (noMatchExecutor as any).calculateActualProfitFromBundle(
      '0xbundle123',
      bundleTransactions,
      100
    );

    expect(result).to.be.undefined;
  });

  it('calculates gas costs correctly', async () => {
    const bundleTransactions = [
      {
        signer: mockWallet,
        transaction: {
          to: '0xcontract123',
          value: ethers.parseEther('1.0'),
          data: '0xdata123'
        }
      }
    ];

    // Mock receipt with no transfer events (only gas costs)
    const gasOnlyProvider = {
      ...mockProvider,
      getTransactionReceipt: async (txHash: string) => ({
        hash: txHash,
        status: 1,
        gasUsed: BigInt(500000),
        gasPrice: ethers.parseUnits('30', 'gwei'),
        logs: [] // No transfer events
      })
    };

    const gasOnlyExecutor = new FlashbotsExecutor(gasOnlyProvider as any, mockWallet as any, mockFlashbotsManager as any, mockGasEstimator as any, mockPriceCalculator as any);

    const result = await (gasOnlyExecutor as any).calculateActualProfitFromBundle(
      '0xbundle123',
      bundleTransactions,
      100
    );

    // Should return negative value equal to gas costs
    // 500000 * 30 gwei = 0.015 ETH
    const expectedGasCost = BigInt(500000) * ethers.parseUnits('30', 'gwei');
    expect(result).to.equal(-expectedGasCost);
  });

  it('handles multiple transactions in bundle', async () => {
    const bundleTransactions = [
      {
        signer: mockWallet,
        transaction: {
          to: '0xcontract123',
          value: ethers.parseEther('1.0'),
          data: '0xdata123'
        }
      },
      {
        signer: mockWallet,
        transaction: {
          to: '0xcontract456',
          value: ethers.parseEther('0.5'),
          data: '0xdata456'
        }
      }
    ];

    // Mock provider that returns different transactions
    const multiTxProvider = {
      ...mockProvider,
      getTransaction: async (txHash: string) => {
        if (txHash === '0xtx1') {
          return {
            hash: txHash,
            to: '0xcontract123',
            value: ethers.parseEther('1.0'),
            data: '0xdata123',
            from: '0xwallet123',
            nonce: 42
          };
        } else if (txHash === '0xtx2') {
          return {
            hash: txHash,
            to: '0xcontract456',
            value: ethers.parseEther('0.5'),
            data: '0xdata456',
            from: '0xwallet123',
            nonce: 43
          };
        }
        return null;
      }
    };

    const multiTxExecutor = new FlashbotsExecutor(multiTxProvider as any, mockWallet as any, mockFlashbotsManager as any, mockGasEstimator as any, mockPriceCalculator as any);

    const result = await (multiTxExecutor as any).calculateActualProfitFromBundle(
      '0xbundle123',
      bundleTransactions,
      100
    );

    expect(result).to.not.be.undefined;
    expect(result).to.be.a('bigint');
    // Should account for gas costs from multiple transactions
  });
});
