import { expect } from 'chai';
import { ethers } from 'ethers';
import { FlashbotsExecutor } from '../../src/execution/flashbots-executor';
import { ArbitrageRoute } from '../../src/types';

describe('FlashbotsExecutor.validateArbitrageRoute wei normalization', () => {
  it('rejects when wei-normalized profit < gas', async () => {
    const provider = new ethers.JsonRpcProvider('http://localhost:8545');
    const wallet = ethers.Wallet.createRandom().connect(provider);

    // Minimal stubs; this test exercises validation only
    const gasEstimator = {
      isGasFavorable: async () => true,
      getOptimalGasPrice: async () => ({ maxFeePerGas: ethers.parseUnits('20', 'gwei') }),
    } as any;
    const gasOptimizer = {} as any;
    const fbm = {} as any;

    const exec = new FlashbotsExecutor(provider as any, wallet as any, fbm, gasEstimator, gasOptimizer);

    const route: ArbitrageRoute = {
      pools: [{ address: wallet.address, protocol: 'uniswap-v2', token0: { address: wallet.address, symbol: 'WETH', decimals: 18, name: 'WETH' }, token1: { address: wallet.address, symbol: 'USDC', decimals: 6, name: 'USDC' } } as any],
      tokens: [{ address: wallet.address, symbol: 'WETH', decimals: 18, name: 'WETH' }, { address: wallet.address, symbol: 'USDC', decimals: 6, name: 'USDC' }],
      expectedProfit: ethers.parseEther('0.00001'),
      gasEstimate: 300000n,
      confidence: 60,
    };

    const validated = await (exec as any).validateArbitrageRoute(route);
    expect(validated).to.equal(null);
  });
});

