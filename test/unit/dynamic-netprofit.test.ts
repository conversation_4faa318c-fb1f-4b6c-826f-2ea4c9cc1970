import { expect } from 'chai';
import { ethers } from 'ethers';
import { DynamicFlashloanStrategy } from '../../src/strategies/dynamic-flashloan';
import { FlashloanRoute } from '../../src/types';
import { GasOptimizer } from '../../src/gas/optimizer';
import { ADDRESSES } from '../../src/config';

// Lightweight test using a stub strategy instance
class StubGasOptimizer extends GasOptimizer {
  async getCurrentGasStrategy(): Promise<any> {
    return { maxFeePerGas: ethers.parseUnits('20', 'gwei') };
  }
}

describe('DynamicFlashloanStrategy netProfit in wei', () => {
  it('computes netProfit using wei and gas estimator', async () => {
    const provider = new ethers.JsonRpcProvider('http://localhost:8545');
    const pk = ethers.hexlify(ethers.randomBytes(32));
    const wallet = new ethers.Wallet(pk, provider);
    const anyManager = { isAvailable: () => false } as any;
    const anyExecutor = {} as any;
    const strat = new DynamicFlashloanStrategy(provider, wallet as any, anyManager, anyExecutor, new StubGasOptimizer() as any);

    const route: FlashloanRoute = {
      flashloanToken: { address: ADDRESSES.WETH, symbol: 'WETH', decimals: 18, name: 'WETH' },
      flashloanAmount: ethers.parseEther('1'),
      flashloanPremium: 0n,
      arbitrageRoute: { pools: [], tokens: [], expectedProfit: 0n, gasEstimate: 0n, confidence: 50 },
      expectedProfit: ethers.parseEther('0.01'),
      gasEstimate: 300000n,
      confidence: 50,
    };

    // @ts-ignore access private method via any
    const enhanced = await (strat as any).enhanceRoute(route, 'aave');

    expect(typeof enhanced.netProfit).to.equal('bigint');
    // At 20 gwei * 400k = 0.008 ETH; netProfit should be ~0.002 ETH
    expect(enhanced.netProfit > 0n).to.equal(true);
  });
});

