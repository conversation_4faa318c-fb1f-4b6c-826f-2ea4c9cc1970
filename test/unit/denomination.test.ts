import { expect } from 'chai';
import { ethers } from 'ethers';
import { tokenAmountToWei } from '../../src/utils/denomination';
import { ADDRESSES } from '../../src/config';

// Minimal Token interface
const WETH = { address: ADDRESSES.WETH, symbol: 'WETH', decimals: 18, name: 'Wrapped Ether' };
const USDC = { address: ADDRESSES.USDC, symbol: 'USDC', decimals: 6, name: 'USD Coin' };

describe('tokenAmountToWei', () => {
  it('returns amount as-is for WETH', async () => {
    const oneEth = ethers.parseEther('1');
    const out = await tokenAmountToWei(WETH as any, oneEth);
    expect(out).to.equal(oneEth);
  });

  it('returns 0 for unknown tokens when pricing unavailable', async () => {
    const unknown = { address: '******************************************', symbol: 'UNK', decimals: 18, name: 'Unknown' };
    const out = await tokenAmountToWei(unknown as any, 1000000000000000000n);
    expect(out).to.be.a('bigint');
  });

  it('handles USDC amounts (non-throwing)', async () => {
    const out = await tokenAmountToWei(USDC as any, 1_000_000n); // 1 USDC
    expect(out).to.be.a('bigint');
  });
});

