import { expect } from 'chai';
import { config } from '../../src/config';

describe('🎯 Confidence Threshold Configuration', function() {
  
  describe('Trading Mode Configuration', function() {
    it('should use correct confidence thresholds for different trading modes', function() {
      // Test the constants we added
      const MAINNET_MIN_CONFIDENCE = 50;      // Balanced approach for mainnet
      const AGGRESSIVE_THRESHOLD = 45;        // High-frequency, smaller profits
      const CONSERVATIVE_THRESHOLD = 60;      // Fewer, higher-confidence trades
      const TESTNET_MIN_CONFIDENCE = 40;      // Lower threshold for testing

      // Verify the thresholds make sense
      expect(AGGRESSIVE_THRESHOLD).to.be.lessThan(MAINNET_MIN_CONFIDENCE);
      expect(MAINNET_MIN_CONFIDENCE).to.be.lessThan(CONSERVATIVE_THRESHOLD);
      expect(TESTNET_MIN_CONFIDENCE).to.be.lessThan(MAINNET_MIN_CONFIDENCE);

      console.log('✅ Confidence Thresholds:');
      console.log(`   🚀 Aggressive: ${AGGRESSIVE_THRESHOLD}% (high frequency)`);
      console.log(`   ⚖️  Balanced: ${MAINNET_MIN_CONFIDENCE}% (recommended)`);
      console.log(`   🛡️  Conservative: ${CONSERVATIVE_THRESHOLD}% (safer)`);
      console.log(`   🧪 Testnet: ${TESTNET_MIN_CONFIDENCE}% (testing)`);
    });

    it('should explain how to use different trading modes', function() {
      console.log('\n📋 How to Use Trading Modes:');
      console.log('');
      console.log('1. 🚀 AGGRESSIVE MODE (45% confidence):');
      console.log('   - Set TRADING_MODE=aggressive in .env');
      console.log('   - Captures more opportunities (including your 65% ones)');
      console.log('   - Higher frequency, smaller individual profits');
      console.log('   - Good for stable market conditions');
      console.log('');
      console.log('2. ⚖️  BALANCED MODE (50% confidence) - RECOMMENDED:');
      console.log('   - Set TRADING_MODE=balanced in .env (default)');
      console.log('   - Perfect for your 65% confidence opportunities');
      console.log('   - Good balance of frequency and safety');
      console.log('   - Recommended for most users');
      console.log('');
      console.log('3. 🛡️  CONSERVATIVE MODE (60% confidence):');
      console.log('   - Set TRADING_MODE=conservative in .env');
      console.log('   - Fewer but higher-confidence opportunities');
      console.log('   - Lower frequency, larger individual profits');
      console.log('   - Good for volatile market conditions');
      console.log('');
      console.log('4. 🧪 TESTNET MODE (40% confidence):');
      console.log('   - Automatically used on testnets');
      console.log('   - Very low threshold for testing purposes');
      console.log('   - Not recommended for mainnet');

      expect(true).to.be.true; // Test always passes, just informational
    });

    it('should show current configuration', function() {
      console.log('\n⚙️  Current Configuration:');
      console.log(`   Network: ${config.chainId === 1 ? 'Mainnet' : 'Testnet'}`);
      console.log(`   Trading Mode: ${process.env.TRADING_MODE || 'balanced (default)'}`);
      console.log(`   Min Profit: ${config.minProfitWei} wei`);
      console.log(`   Min Spread: ${config.minArbitrageSpread * 100}%`);
      
      // Show what confidence threshold would be used
      let expectedThreshold;
      const mode = process.env.TRADING_MODE?.toLowerCase();
      
      if (config.chainId !== 1) {
        expectedThreshold = 40; // Testnet
      } else {
        switch (mode) {
          case 'aggressive':
            expectedThreshold = 45;
            break;
          case 'conservative':
            expectedThreshold = 60;
            break;
          case 'balanced':
          default:
            expectedThreshold = 50;
            break;
        }
      }
      
      console.log(`   Expected Confidence Threshold: ${expectedThreshold}%`);
      
      if (expectedThreshold <= 50) {
        console.log('   ✅ This should capture your 65% confidence opportunities!');
      } else {
        console.log('   ⚠️  This might still miss your 65% confidence opportunities');
        console.log('   💡 Consider using "balanced" or "aggressive" mode');
      }

      expect(expectedThreshold).to.be.a('number');
    });
  });

  describe('Opportunity Capture Analysis', function() {
    it('should analyze which opportunities would be captured', function() {
      const testOpportunities = [
        { confidence: 75, description: 'High confidence' },
        { confidence: 65, description: 'Your typical opportunity' },
        { confidence: 55, description: 'Medium confidence' },
        { confidence: 45, description: 'Lower confidence' },
        { confidence: 35, description: 'Very low confidence' }
      ];

      const modes = [
        { name: 'aggressive', threshold: 45 },
        { name: 'balanced', threshold: 50 },
        { name: 'conservative', threshold: 60 }
      ];

      console.log('\n📊 Opportunity Capture Analysis:');
      console.log('');

      for (const mode of modes) {
        const captured = testOpportunities.filter(op => op.confidence >= mode.threshold);
        const missed = testOpportunities.filter(op => op.confidence < mode.threshold);

        console.log(`${mode.name.toUpperCase()} MODE (${mode.threshold}% threshold):`);
        console.log(`   ✅ Captured: ${captured.length}/5 opportunities`);
        captured.forEach(op => console.log(`      - ${op.confidence}% (${op.description})`));
        
        if (missed.length > 0) {
          console.log(`   ❌ Missed: ${missed.length}/5 opportunities`);
          missed.forEach(op => console.log(`      - ${op.confidence}% (${op.description})`));
        }
        
        // Special note for your 65% opportunities
        if (mode.threshold <= 65) {
          console.log(`   🎯 CAPTURES your 65% opportunities!`);
        } else {
          console.log(`   ⚠️  MISSES your 65% opportunities`);
        }
        console.log('');
      }

      expect(testOpportunities).to.have.length(5);
    });
  });
});
