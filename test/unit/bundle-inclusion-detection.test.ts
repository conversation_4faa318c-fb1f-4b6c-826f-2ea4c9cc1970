import { expect } from 'chai';
import { ethers } from 'ethers';
import { BundleTracker } from '../../src/flashbots/bundle-tracker';

describe('BundleTracker.checkIfBundleInBlock', () => {
  let tracker: BundleTracker;
  let mockProvider: any;
  let mockFlashbotsProvider: any;

  beforeEach(() => {
    // Mock provider
    mockProvider = {
      getBlockNumber: async () => 100,
      getBlock: async (blockNumber: number) => ({
        number: blockNumber,
        hash: '0xblock123',
        transactions: ['0xtx1', '0xtx2', '0xtx3']
      }),
      getTransaction: async (txHash: string) => ({
        hash: txHash,
        to: '0xcontract123',
        value: ethers.parseEther('1.0'),
        data: '0xdata123',
        nonce: 42,
        maxPriorityFeePerGas: ethers.parseUnits('20', 'gwei')
      })
    };

    // Mock Flashbots provider
    mockFlashbotsProvider = {
      getBundleStats: async () => ({ bundleGasPrice: '20000000000' })
    };

    tracker = new BundleTracker(mockProvider as any, mockFlashbotsProvider as any);
  });

  it('detects bundle inclusion via transaction hash matching', async () => {
    // Track a bundle with specific transaction hashes
    tracker.trackBundle(
      '0xbundle123',
      100,
      ethers.parseUnits('20', 'gwei'),
      ethers.parseUnits('15', 'gwei'),
      false,
      ['0xtx1', '0xtx2'], // Bundle contains tx1 and tx2
      undefined
    );

    // Get the tracked bundle
    const bundle = tracker.getBundleStatus('0xbundle123');
    expect(bundle).to.not.be.undefined;

    // Mock block contains tx1, tx2, tx3 - so our bundle (tx1, tx2) should be detected
    const block = await mockProvider.getBlock(100);
    
    // Access private method for testing
    const result = await (tracker as any).checkIfBundleInBlock(bundle!, block);
    expect(result).to.be.true;
  });

  it('detects partial bundle inclusion', async () => {
    // Track a bundle with transaction hashes where only some are included
    tracker.trackBundle(
      '0xbundle456',
      100,
      ethers.parseUnits('20', 'gwei'),
      ethers.parseUnits('15', 'gwei'),
      false,
      ['0xtx1', '0xtx4'], // Bundle contains tx1 (included) and tx4 (not included)
      undefined
    );

    const bundle = tracker.getBundleStatus('0xbundle456');
    const block = await mockProvider.getBlock(100);
    
    const result = await (tracker as any).checkIfBundleInBlock(bundle!, block);
    expect(result).to.be.false; // Should be false since not all transactions are included
  });

  it('detects bundle inclusion via pattern matching', async () => {
    // Track a bundle with transaction patterns (no hashes)
    tracker.trackBundle(
      '0xbundle789',
      100,
      ethers.parseUnits('20', 'gwei'),
      ethers.parseUnits('15', 'gwei'),
      false,
      undefined, // No transaction hashes
      [
        {
          to: '0xcontract123',
          value: ethers.parseEther('1.0'),
          data: '0xdata123',
          nonce: 42
        }
      ]
    );

    const bundle = tracker.getBundleStatus('0xbundle789');
    const block = await mockProvider.getBlock(100);
    
    const result = await (tracker as any).checkIfBundleInBlock(bundle!, block);
    expect(result).to.be.true; // Should match the pattern from mock transaction
  });

  it('returns false when no bundle data available', async () => {
    // Track a bundle with no transaction data
    tracker.trackBundle(
      '0xbundle000',
      100,
      ethers.parseUnits('20', 'gwei'),
      ethers.parseUnits('15', 'gwei'),
      false,
      undefined,
      undefined
    );

    const bundle = tracker.getBundleStatus('0xbundle000');
    const block = await mockProvider.getBlock(100);
    
    const result = await (tracker as any).checkIfBundleInBlock(bundle!, block);
    expect(result).to.be.false; // Should be false with heuristic matching
  });

  it('handles errors gracefully', async () => {
    // Mock provider that throws errors
    const errorProvider = {
      getTransaction: async () => { throw new Error('Network error'); }
    };

    const errorTracker = new BundleTracker(errorProvider as any, mockFlashbotsProvider as any);
    
    errorTracker.trackBundle(
      '0xbundleError',
      100,
      ethers.parseUnits('20', 'gwei'),
      ethers.parseUnits('15', 'gwei'),
      false,
      undefined,
      [{ to: '0xcontract123', value: ethers.parseEther('1.0') }]
    );

    const bundle = errorTracker.getBundleStatus('0xbundleError');
    const block = { number: 100, transactions: ['0xtx1'] };
    
    const result = await (errorTracker as any).checkIfBundleInBlock(bundle!, block);
    expect(result).to.be.false; // Should handle errors gracefully
  });
});
