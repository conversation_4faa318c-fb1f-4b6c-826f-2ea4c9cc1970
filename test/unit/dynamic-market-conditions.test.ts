import { expect } from 'chai';
import { ethers } from 'ethers';
import { DynamicFlashloanStrategy } from '../../src/strategies/dynamic-flashloan';

function makeEnhanced(netProfitEth: string, strategy: any = 'aave', confidence = 60) {
  return {
    strategy,
    netProfit: ethers.parseEther(netProfitEth),
    confidence,
  } as any;
}

describe('DynamicFlashloanStrategy.getMarketConditionsFrom', () => {
  it('summarizes from pre-scanned opportunities', async () => {
    // Partial construction; methods used in summary won't access these
    const strat = Object.create(DynamicFlashloanStrategy.prototype);
    (strat as any).flashbotsManager = { isAvailable: () => true };

    const opportunities = [
      makeEnhanced('0.01', 'aave', 70),
      makeEnhanced('0.008', 'balancer', 65),
      makeEnhanced('0.006', 'uniswap-v3', 55),
    ];

    const res = await (strat as any).getMarketConditionsFrom(opportunities);
    expect(res.totalOpportunities).to.equal(3);
    expect(res.bestStrategy).to.equal('aave');
    expect(res.bestProfit).to.equal('0.01');
    expect(res.averageConfidence).to.be.greaterThan(0);
    expect(res.flashbotsAvailable).to.equal(true);
  });
});

