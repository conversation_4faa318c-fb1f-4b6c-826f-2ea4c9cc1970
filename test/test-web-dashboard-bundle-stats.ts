#!/usr/bin/env ts-node

import { bundleAnalyzer } from '../src/utils/bundle-analyzer';
import { statusDashboard } from '../src/utils/statusDashboard';
import { logger } from '../src/utils/logger';

/**
 * Test Web Dashboard Bundle Statistics Integration
 * 
 * This script tests that bundle statistics are properly integrated
 * into the web dashboard data structure.
 */

async function main() {
    try {
        logger.system('🧪 TESTING WEB DASHBOARD BUNDLE STATS INTEGRATION');
        logger.system('=================================================');

        // First, populate some test data (if not already present)
        const stats = bundleAnalyzer.getInclusionStats();
        if (stats.totalSubmissions === 0) {
            logger.system('📝 Adding test bundle data...');
            
            // Add some test data
            const testData = [
                { included: true, priorityFee: '30', baseFee: '20' },
                { included: false, priorityFee: '25', baseFee: '22', error: 'Block passed without inclusion' },
                { included: true, priorityFee: '35', baseFee: '18' },
                { included: true, priorityFee: '40', baseFee: '21' },
                { included: false, priorityFee: '20', baseFee: '25', error: 'Block passed without inclusion' }
            ];

            testData.forEach((data, index) => {
                const targetBlock = 2000000 + index;
                const priorityFee = BigInt(Math.floor(parseFloat(data.priorityFee) * 1e9));
                const baseFee = BigInt(Math.floor(parseFloat(data.baseFee) * 1e9));
                
                bundleAnalyzer.recordBundleSubmission(
                    targetBlock,
                    data.included,
                    priorityFee,
                    baseFee,
                    data.error
                );
            });
            
            logger.system('✅ Test data added');
        } else {
            logger.system(`📊 Using existing bundle data (${stats.totalSubmissions} submissions)`);
        }

        // Get bundle statistics
        const bundleStats = bundleAnalyzer.getInclusionStats();
        logger.system('');
        logger.system('📊 CURRENT BUNDLE STATISTICS');
        logger.system('============================');
        logger.system(`Total Submissions: ${bundleStats.totalSubmissions}`);
        logger.system(`Successful Inclusions: ${bundleStats.successfulInclusions}`);
        logger.system(`Inclusion Rate: ${bundleStats.inclusionRate.toFixed(1)}%`);
        logger.system(`Average Priority Fee: ${bundleStats.averagePriorityFee} gwei`);
        logger.system('');

        // Test status dashboard integration
        logger.system('🔧 TESTING STATUS DASHBOARD INTEGRATION');
        logger.system('=======================================');

        // Trigger bundle stats update in status dashboard
        statusDashboard['updateBundleStats']();

        // Access the internal stats (for testing purposes)
        const dashboardStats = statusDashboard['stats'];
        
        logger.system(`Dashboard Bundle Submissions: ${dashboardStats.bundleSubmissions}`);
        logger.system(`Dashboard Bundle Inclusions: ${dashboardStats.bundleInclusions}`);
        logger.system(`Dashboard Inclusion Rate: ${dashboardStats.bundleInclusionRate.toFixed(1)}%`);
        logger.system(`Dashboard Avg Priority Fee: ${dashboardStats.avgPriorityFee} gwei`);
        logger.system('');

        // Verify data consistency
        logger.system('🔍 VERIFICATION');
        logger.system('===============');
        
        const isConsistent = 
            dashboardStats.bundleSubmissions === bundleStats.totalSubmissions &&
            dashboardStats.bundleInclusions === bundleStats.successfulInclusions &&
            Math.abs(dashboardStats.bundleInclusionRate - bundleStats.inclusionRate) < 0.1 &&
            dashboardStats.avgPriorityFee === bundleStats.averagePriorityFee;

        if (isConsistent) {
            logger.system('✅ Bundle statistics are properly integrated into dashboard');
        } else {
            logger.system('❌ Data inconsistency detected:');
            logger.system(`  Bundle Analyzer: ${bundleStats.totalSubmissions} submissions, ${bundleStats.inclusionRate.toFixed(1)}% rate`);
            logger.system(`  Dashboard: ${dashboardStats.bundleSubmissions} submissions, ${dashboardStats.bundleInclusionRate.toFixed(1)}% rate`);
        }
        logger.system('');

        // Test web dashboard data structure
        logger.system('🌐 TESTING WEB DASHBOARD DATA STRUCTURE');
        logger.system('=======================================');

        // Simulate the data that would be sent to web dashboard
        const webDashboardData = {
            bundleSubmissions: dashboardStats.bundleSubmissions,
            bundleInclusions: dashboardStats.bundleInclusions,
            bundleInclusionRate: dashboardStats.bundleInclusionRate,
            avgPriorityFee: dashboardStats.avgPriorityFee,
            // Other required fields for web dashboard
            currentBlock: 0,
            networkName: 'Test',
            ethBalance: BigInt(0),
            lastBalanceUpdate: 0,
            isRunning: false,
            uptime: 0,
            lastActivity: 0,
            flashloanEnabled: false,
            mevShareEnabled: false,
            arbitrageEnabled: false,
            totalTransactions: 0,
            relevantTransactions: 0,
            opportunitiesFound: 0,
            opportunitiesExecuted: 0,
            totalProfit: BigInt(0),
            avgGasPrice: BigInt(0),
            configuration: {
                tokenPairs: [],
                dexes: [],
                minProfitThreshold: '0',
                maxGasPrice: '0'
            },
            successfulTransactions: [],
            errors: 0
        };

        logger.system('Web Dashboard Data Structure:');
        logger.system(`  bundleSubmissions: ${webDashboardData.bundleSubmissions}`);
        logger.system(`  bundleInclusions: ${webDashboardData.bundleInclusions}`);
        logger.system(`  bundleInclusionRate: ${webDashboardData.bundleInclusionRate.toFixed(1)}%`);
        logger.system(`  avgPriorityFee: ${webDashboardData.avgPriorityFee} gwei`);
        logger.system('');

        // Test serialization (like web dashboard does)
        const serializedData = {
            ...webDashboardData,
            ethBalance: webDashboardData.ethBalance.toString(),
            totalProfit: webDashboardData.totalProfit.toString(),
            avgGasPrice: webDashboardData.avgGasPrice.toString(),
        };

        logger.system('📡 SERIALIZED DATA (for JSON transmission)');
        logger.system('==========================================');
        logger.system(`Bundle Submissions: ${serializedData.bundleSubmissions}`);
        logger.system(`Bundle Inclusions: ${serializedData.bundleInclusions}`);
        logger.system(`Bundle Inclusion Rate: ${serializedData.bundleInclusionRate}%`);
        logger.system(`Avg Priority Fee: ${serializedData.avgPriorityFee} gwei`);
        logger.system('');

        logger.system('✅ WEB DASHBOARD INTEGRATION TEST COMPLETED');
        logger.system('');
        logger.system('🎯 NEXT STEPS:');
        logger.system('1. Start the MEV bot with WEB_DASHBOARD=true');
        logger.system('2. Open http://localhost:3000 in your browser');
        logger.system('3. Look for the "Bundle Inclusion" section in the dashboard');
        logger.system('4. The statistics should update in real-time as bundles are submitted');
        logger.system('');

    } catch (error) {
        logger.error('Web dashboard bundle stats test failed:', error);
        process.exit(1);
    }
}

// Run the test
if (require.main === module) {
    main().catch(error => {
        logger.error('Script failed:', error);
        process.exit(1);
    });
}

export { main as testWebDashboardBundleStats };
