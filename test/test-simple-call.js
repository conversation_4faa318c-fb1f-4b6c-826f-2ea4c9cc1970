const { ethers } = require('ethers');

async function testSimpleCall() {
    console.log('🔍 Testing simple contract call...');
    
    // Connect to Hardhat fork
    const provider = new ethers.JsonRpcProvider('http://127.0.0.1:8547');
    
    // Contract address from deployment
    const contractAddress = '******************************************';
    
    console.log(`📋 Test Configuration:`);
    console.log(`   Provider: http://127.0.0.1:8547`);
    console.log(`   Contract: ${contractAddress}`);
    
    try {
        // Check if contract exists
        const code = await provider.getCode(contractAddress);
        console.log(`   Contract code length: ${code.length} bytes`);
        
        if (code === '0x') {
            console.log('   ❌ No contract code found at this address!');
            return;
        } else {
            console.log('   ✅ Contract code found');
        }
        
        // Try a simple call to get the current block number
        const blockNumber = await provider.getBlockNumber();
        console.log(`   Current block: ${blockNumber}`);
        
        // Try to call a simple function manually
        const ownerSelector = '0x8da5cb5b'; // owner() function selector
        
        const result = await provider.call({
            to: contractAddress,
            data: ownerSelector
        });
        
        console.log(`   Raw result: ${result}`);
        
        if (result && result !== '0x') {
            const owner = ethers.AbiCoder.defaultAbiCoder().decode(['address'], result)[0];
            console.log(`   ✅ Owner: ${owner}`);
        } else {
            console.log('   ❌ Empty result from owner() call');
        }
        
    } catch (error) {
        console.log(`❌ Error: ${error.message}`);
    }
    
    // Try to test the actual failing transaction directly
    console.log('\n🧪 Testing the failing transaction directly...');
    
    try {
        // The exact transaction data that was failing
        const failingTxData = "0x9bc62f7a000000000000000000000000c02aaa39b223fe8d0a0e5c4f27ead9083c756cc20000000000000000000000000000000000000000000000001bc16d674ec800000000000000000000000000000000000000000000000000000000000000000060000000000000000000000000000000000000000000000000000000000000022000000000000000000000000000000000000000000000000000000000000001200000000000000000000000000000000000000000000000000000000000000180000000000000000000000000e592427a0aece92de3edee1f18e0157c058615640000000000000000000000007a250d5630b4cf539739df2c5dacb4c659f2488d00000000000000000000000000000000000000000000000000000000000001e0000000000000000000000000000000000000000000000000000009184e72a000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000003e80000000000000000000000000000000000000000000000000000002e90edd0000000000000000000000000000000000000000000000000000000000000000002000000000000000000000000c02aaa39b223fe8d0a0e5c4f27ead9083c756cc20000000000000000000000006b175474e89094c44da98b954eedeac495271d0f00000000000000000000000000000000000000000000000000000000000000020000000000000000000000006b175474e89094c44da98b954eedeac495271d0f000000000000000000000000c02aaa39b223fe8d0a0e5c4f27ead9083c756cc200000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000000000bb8";
        
        const wallet = new ethers.Wallet('0xac0974bec39a17e36ba4a6b4d238ff944bacb478cbed5efcae784d7bf4f2ff80', provider);
        
        const result = await provider.call({
            to: contractAddress,
            data: failingTxData,
            from: wallet.address
        });
        
        console.log(`   ✅ Transaction call succeeded: ${result}`);
        
    } catch (error) {
        const errorMsg = error.message;
        console.log(`   Result: ${errorMsg.split('(')[0]}`);
        
        if (errorMsg.includes('require(false)')) {
            console.log('   ❌ Still getting require(false) - validation issue remains');
        } else if (errorMsg.includes('Arbitrage resulted in loss')) {
            console.log('   ✅ Fixed! Now getting proper arbitrage error instead of require(false)');
        } else if (errorMsg.includes('Invalid V3 fees')) {
            console.log('   ❌ V3 fees validation still failing');
        } else {
            console.log('   💡 Different error - investigating...');
        }
    }
    
    console.log('\n🏁 Simple call testing completed!');
}

testSimpleCall().catch(console.error);
