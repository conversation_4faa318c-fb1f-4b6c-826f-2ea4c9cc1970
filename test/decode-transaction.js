const { ethers } = require('ethers');

async function decodeTransaction() {
    console.log('🔍 Decoding failed transaction data...');
    
    // Transaction data from the error
    const txData = "0x9bc62f7a000000000000000000000000c02aaa39b223fe8d0a0e5c4f27ead9083c756cc2000000000000000000000000000000000000000000000001158e460913d00000000000000000000000000000000000000000000000000000000000000000006000000000000000000000000000000000000000000000000000000000000000e0000000000000000000000000c02aaa39b223fe8d0a0e5c4f27ead9083c756cc2000000000000000000000000a0b86991c6218b36c1d19d4a2e9eb0ce3606eb48000000000000000000000000e592427a0aece92de3edee1f18e0157c058615640000000000000000000000007a250d5630b4cf539739df2c5dacb4c659f2488d0000000000000000000000000000000000000000000000000000000000000bb800000000000000000000000000000000000000000000000000005af3107a40000000000000000000000000000000000000000000000000000000000000000000";
    
    // Contract interface for executeOptimalFlashloan
    const contractInterface = new ethers.Interface([
        'function executeOptimalFlashloan(address asset, uint256 amount, bytes calldata params) external'
    ]);
    
    // Decode the function call
    const decoded = contractInterface.parseTransaction({ data: txData });
    
    console.log('📋 Decoded Transaction:');
    console.log(`   Function: ${decoded.name}`);
    console.log(`   Asset: ${decoded.args.asset}`);
    console.log(`   Amount: ${ethers.formatEther(decoded.args.amount)} ETH`);
    
    // Decode the params (ArbitrageParams struct)
    const paramsData = decoded.args.params;
    console.log(`   Params Data: ${paramsData}`);
    
    // Decode ArbitrageParams struct
    const arbitrageParams = ethers.AbiCoder.defaultAbiCoder().decode(
        ['address', 'address', 'address', 'address', 'uint24', 'uint256', 'uint8'],
        paramsData
    );
    
    console.log('\n📊 ArbitrageParams:');
    console.log(`   tokenA: ${arbitrageParams[0]} (WETH)`);
    console.log(`   tokenB: ${arbitrageParams[1]} (USDC)`);
    console.log(`   buyDex: ${arbitrageParams[2]}`);
    console.log(`   sellDex: ${arbitrageParams[3]}`);
    console.log(`   v3Fee: ${arbitrageParams[4]}`);
    console.log(`   minProfit: ${ethers.formatEther(arbitrageParams[5])} ETH`);
    console.log(`   provider: ${arbitrageParams[6]} (${arbitrageParams[6] === 0n ? 'AAVE' : 'BALANCER'})`);
    
    // Check if addresses are correct
    console.log('\n🔍 Address Verification:');
    console.log(`   WETH: ${arbitrageParams[0] === '******************************************' ? '✅' : '❌'}`);
    console.log(`   USDC: ${arbitrageParams[1] === '******************************************' ? '✅' : '❌'}`);
    console.log(`   V3 Router: ${arbitrageParams[2] === '******************************************' ? '✅' : '❌'}`);
    console.log(`   V2 Router: ${arbitrageParams[3] === '******************************************' ? '✅' : '❌'}`);
    
    // Analyze the amounts
    console.log('\n💰 Amount Analysis:');
    const flashloanAmount = decoded.args.amount;
    const minProfit = arbitrageParams[5];
    
    console.log(`   Flashloan Amount: ${ethers.formatEther(flashloanAmount)} ETH`);
    console.log(`   Min Profit: ${ethers.formatEther(minProfit)} ETH`);
    console.log(`   Amount in Wei: ${flashloanAmount.toString()}`);
    
    // Check if amount meets minimum requirements
    const minimumAmount = ethers.parseEther('0.001'); // 0.001 ETH minimum we set
    console.log(`   Meets minimum (0.001 ETH): ${flashloanAmount >= minimumAmount ? '✅' : '❌'}`);
    
    if (flashloanAmount < minimumAmount) {
        console.log(`   ⚠️  Amount ${ethers.formatEther(flashloanAmount)} ETH is below minimum 0.001 ETH`);
    }
    
    // Check provider
    console.log('\n🏦 Provider Analysis:');
    const provider = arbitrageParams[6];
    console.log(`   Provider: ${provider === 0n ? 'AAVE (0)' : provider === 1n ? 'BALANCER (1)' : `INVALID (${provider})`}`);
    
    if (provider !== 0n && provider !== 1n) {
        console.log(`   ❌ Invalid provider value: ${provider}`);
    }
    
    return {
        asset: decoded.args.asset,
        amount: flashloanAmount,
        tokenA: arbitrageParams[0],
        tokenB: arbitrageParams[1],
        buyDex: arbitrageParams[2],
        sellDex: arbitrageParams[3],
        v3Fee: arbitrageParams[4],
        minProfit: arbitrageParams[5],
        provider: arbitrageParams[6]
    };
}

decodeTransaction().catch(console.error);
