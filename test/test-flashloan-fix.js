const { ethers } = require('ethers');

async function testFlashloanFix() {
    console.log('🧪 Testing flashloan fix with correct router addresses...');
    
    // Connect to your ETH RPC node
    const provider = new ethers.JsonRpcProvider('http://************:8545');
    
    // Your wallet
    const wallet = new ethers.Wallet('0x1521be785ecf5cb125cc42f1211789ef1a3f7fe4f33460601e56e52f58febd45', provider);
    
    // Contract addresses
    const hybridContractAddress = '******************************************';
    const wethAddress = '******************************************';
    const usdcAddress = '******************************************'; // Use USDC instead of DAI
    
    // CORRECT MAINNET ROUTER ADDRESSES (not Sepolia!)
    const mainnetV2Router = '******************************************'; // Mainnet Uniswap V2
    const mainnetV3Router = '******************************************'; // Mainnet Uniswap V3
    
    console.log(`📋 Test Configuration:`);
    console.log(`   Contract: ${hybridContractAddress}`);
    console.log(`   Wallet: ${wallet.address}`);
    console.log(`   V2 Router: ${mainnetV2Router}`);
    console.log(`   V3 Router: ${mainnetV3Router}`);
    
    // Check network
    const network = await provider.getNetwork();
    console.log(`   Network: ${network.name} (Chain ID: ${network.chainId})`);
    
    if (network.chainId !== 1n) {
        console.log('❌ Not on mainnet! This test is for mainnet only.');
        return;
    }
    
    // Contract interface
    const hybridContractInterface = new ethers.Interface([
        'function executeOptimalFlashloan(address asset, uint256 amount, bytes calldata params) external'
    ]);
    
    // Test with the EXACT same parameters from the failed transaction but with CORRECT router addresses
    const flashloanAmount = ethers.parseEther('0.02'); // 0.02 WETH (from the log)
    
    // Encode arbitrage parameters with CORRECT MAINNET router addresses
    // ArbitrageParams struct: tokenA, tokenB, buyDex, sellDex, v3Fee, minProfit, provider
    const arbitrageParams = ethers.AbiCoder.defaultAbiCoder().encode(
        ['address', 'address', 'address', 'address', 'uint24', 'uint256', 'uint8'],
        [
            wethAddress,      // tokenA (WETH)
            usdcAddress,      // tokenB (USDC) - test with working token
            mainnetV2Router,  // buyDex (CORRECT mainnet V2 router)
            mainnetV3Router,  // sellDex (CORRECT mainnet V3 router)
            3000,             // v3Fee (0.3%)
            ethers.parseEther('0.0001'), // minProfit
            0                 // FlashloanProvider.AAVE (try AAVE instead)
        ]
    );
    
    // Encode the function call
    const flashloanData = hybridContractInterface.encodeFunctionData('executeOptimalFlashloan', [
        wethAddress,
        flashloanAmount,
        arbitrageParams
    ]);
    
    console.log('\n🧪 Testing static call with CORRECT router addresses...');
    
    try {
        const result = await provider.call({
            to: hybridContractAddress,
            data: flashloanData,
            from: wallet.address
        });
        
        console.log('✅ SUCCESS! Static call passed with correct router addresses');
        console.log('🎉 The flashloan transaction validation should now work!');
        console.log(`   Result: ${result}`);
        
        return true;
        
    } catch (error) {
        console.log('❌ Static call failed:', error.message);
        
        // Check if it's still the same error or a different one
        if (error.message.includes('OVERFLOW(17)') || error.message.includes('enum conversion')) {
            console.log('   ⚠️  Still getting enum conversion error - there might be another issue');
        } else if (error.message.includes('INSUFFICIENT_OUTPUT_AMOUNT')) {
            console.log('   ✅ Different error! This means the router addresses are now correct');
            console.log('      The "INSUFFICIENT_OUTPUT_AMOUNT" error is expected for small amounts');
            console.log('      This confirms the fix is working!');
            return true;
        } else {
            console.log('   ❓ Different error - need to investigate further');
        }
        
        // Try to decode the error
        if (error.data) {
            console.log('   Error data:', error.data);
        }
        
        return false;
    }
}

// Test with different amounts to see the behavior
async function testDifferentAmounts() {
    console.log('\n🔬 Testing with different flashloan amounts...');
    
    const provider = new ethers.JsonRpcProvider('http://************:8545');
    const wallet = new ethers.Wallet('0x1521be785ecf5cb125cc42f1211789ef1a3f7fe4f33460601e56e52f58febd45', provider);
    
    const hybridContractAddress = '******************************************';
    const wethAddress = '******************************************';
    const daiAddress = '******************************************';
    const mainnetV2Router = '******************************************';
    const mainnetV3Router = '******************************************';
    
    const hybridContractInterface = new ethers.Interface([
        'function executeOptimalFlashloan(address asset, uint256 amount, bytes calldata params) external'
    ]);
    
    const testAmounts = [
        { amount: ethers.parseUnits('1', 'wei'), label: '1 wei' },
        { amount: ethers.parseEther('0.001'), label: '0.001 WETH' },
        { amount: ethers.parseEther('0.1'), label: '0.1 WETH' },
        { amount: ethers.parseEther('1'), label: '1 WETH' }
    ];
    
    for (const test of testAmounts) {
        console.log(`\n   Testing with ${test.label}...`);
        
        const arbitrageParams = ethers.AbiCoder.defaultAbiCoder().encode(
            ['address', 'address', 'address', 'address', 'uint24', 'uint256', 'uint8'],
            [
                wethAddress,
                usdcAddress,
                mainnetV2Router,
                mainnetV3Router,
                3000,
                ethers.parseEther('0.0001'),
                0 // AAVE
            ]
        );
        
        const flashloanData = hybridContractInterface.encodeFunctionData('executeOptimalFlashloan', [
            wethAddress,
            test.amount,
            arbitrageParams
        ]);
        
        try {
            await provider.call({
                to: hybridContractAddress,
                data: flashloanData,
                from: wallet.address
            });
            
            console.log(`   ✅ ${test.label}: SUCCESS`);
            
        } catch (error) {
            const errorMsg = error.message.split('(')[0];
            console.log(`   ❌ ${test.label}: ${errorMsg}`);
            
            if (errorMsg.includes('INSUFFICIENT_OUTPUT_AMOUNT')) {
                console.log(`      ✅ This is expected for small amounts - router addresses are correct!`);
            }
        }
    }
}

// Run the tests
async function runTests() {
    try {
        const mainTestPassed = await testFlashloanFix();
        
        if (mainTestPassed) {
            await testDifferentAmounts();
        }
        
        console.log('\n🏁 Test completed!');
        
    } catch (error) {
        console.error('❌ Test failed:', error);
    }
}

runTests();
