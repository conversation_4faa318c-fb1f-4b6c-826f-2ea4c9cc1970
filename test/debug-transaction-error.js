const { ethers } = require('ethers');

async function debugTransactionError() {
    console.log('🔍 Debugging transaction error...');
    
    // The failing transaction data from the error log
    const txData = "0x9bc62f7a000000000000000000000000c02aaa39b223fe8d0a0e5c4f27ead9083c756cc20000000000000000000000000000000000000000000000001bc16d674ec800000000000000000000000000000000000000000000000000000000000000000060000000000000000000000000000000000000000000000000000000000000022000000000000000000000000000000000000000000000000000000000000001200000000000000000000000000000000000000000000000000000000000000180000000000000000000000000e592427a0aece92de3edee1f18e0157c058615640000000000000000000000007a250d5630b4cf539739df2c5dacb4c659f2488d00000000000000000000000000000000000000000000000000000000000001e0000000000000000000000000000000000000000000000000000009184e72a000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000003e80000000000000000000000000000000000000000000000000000002e90edd0000000000000000000000000000000000000000000000000000000000000000002000000000000000000000000c02aaa39b223fe8d0a0e5c4f27ead9083c756cc20000000000000000000000006b175474e89094c44da98b954eedeac495271d0f00000000000000000000000000000000000000000000000000000000000000020000000000000000000000006b175474e89094c44da98b954eedeac495271d0f000000000000000000000000c02aaa39b223fe8d0a0e5c4f27ead9083c756cc200000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000000000bb8";
    
    // Contract interface
    const contractInterface = new ethers.Interface([
        'function executeOptimalFlashloan(address asset, uint256 amount, bytes calldata params) external'
    ]);
    
    // Decode the function call
    const decoded = contractInterface.parseTransaction({ data: txData });
    
    console.log('📋 Transaction Analysis:');
    console.log(`   Function: ${decoded.name}`);
    console.log(`   Asset: ${decoded.args.asset} (WETH)`);
    console.log(`   Amount: ${ethers.formatEther(decoded.args.amount)} ETH`);
    
    // Decode the enhanced arbitrage parameters (9 fields now)
    const paramsData = decoded.args.params;
    console.log(`   Params data length: ${paramsData.length} bytes`);
    
    try {
        const arbitrageParams = ethers.AbiCoder.defaultAbiCoder().decode(
            ['address[]', 'address[]', 'address', 'address', 'uint24[]', 'uint256', 'uint8', 'uint256', 'uint256'],
            paramsData
        );
        
        console.log('\n📊 Enhanced Arbitrage Parameters:');
        console.log(`   buyPath: [${arbitrageParams[0].map(addr => addr.slice(0,6) + '...' + addr.slice(-4)).join(', ')}]`);
        console.log(`   sellPath: [${arbitrageParams[1].map(addr => addr.slice(0,6) + '...' + addr.slice(-4)).join(', ')}]`);
        console.log(`   buyDex: ${arbitrageParams[2]} (${arbitrageParams[2] === '0xe592427a0aece92de3edee1f18e0157c058615640' ? 'Uniswap V3' : 'Unknown'})`);
        console.log(`   sellDex: ${arbitrageParams[3]} (${arbitrageParams[3] === '******************************************' ? 'Uniswap V2' : 'Unknown'})`);
        console.log(`   v3Fees: [${arbitrageParams[4].map(fee => Number(fee)/10000 + '%').join(', ')}]`);
        console.log(`   minProfit: ${ethers.formatEther(arbitrageParams[5])} ETH`);
        console.log(`   provider: ${arbitrageParams[6]} (${arbitrageParams[6] === 0n ? 'AAVE' : 'BALANCER'})`);
        console.log(`   slippageToleranceBps: ${arbitrageParams[7]} (${Number(arbitrageParams[7])/100}%)`);
        console.log(`   maxGasCostWei: ${ethers.formatUnits(arbitrageParams[8], 'gwei')} gwei`);
        
        console.log('\n🔍 VALIDATION ANALYSIS:');
        
        // Check path validation
        const buyPath = arbitrageParams[0];
        const sellPath = arbitrageParams[1];
        
        console.log(`   Buy path length: ${buyPath.length} (${buyPath.length >= 2 ? '✅ Valid' : '❌ Too short'})`);
        console.log(`   Sell path length: ${sellPath.length} (${sellPath.length >= 2 ? '✅ Valid' : '❌ Too short'})`);
        console.log(`   Buy path length: ${buyPath.length} (${buyPath.length <= 5 ? '✅ Valid' : '❌ Too long'})`);
        console.log(`   Sell path length: ${sellPath.length} (${sellPath.length <= 5 ? '✅ Valid' : '❌ Too long'})`);
        
        // Check if paths form a loop
        const firstBuyToken = buyPath[0];
        const lastSellToken = sellPath[sellPath.length - 1];
        const formsLoop = firstBuyToken.toLowerCase() === lastSellToken.toLowerCase();
        console.log(`   Forms complete loop: ${formsLoop ? '✅ Valid' : '❌ Invalid'}`);
        console.log(`     First buy token: ${firstBuyToken}`);
        console.log(`     Last sell token: ${lastSellToken}`);
        
        // Check asset match
        const assetMatch = decoded.args.asset.toLowerCase() === firstBuyToken.toLowerCase();
        console.log(`   Asset matches first buy token: ${assetMatch ? '✅ Valid' : '❌ Invalid'}`);
        
        // Check for zero addresses
        let hasZeroAddress = false;
        for (let i = 0; i < buyPath.length; i++) {
            if (buyPath[i] === ethers.ZeroAddress) {
                console.log(`   ❌ Zero address in buy path at index ${i}`);
                hasZeroAddress = true;
            }
        }
        for (let i = 0; i < sellPath.length; i++) {
            if (sellPath[i] === ethers.ZeroAddress) {
                console.log(`   ❌ Zero address in sell path at index ${i}`);
                hasZeroAddress = true;
            }
        }
        if (!hasZeroAddress) {
            console.log(`   Zero addresses: ✅ None found`);
        }
        
        // Check DEX addresses
        const buyDexValid = arbitrageParams[2] !== ethers.ZeroAddress;
        const sellDexValid = arbitrageParams[3] !== ethers.ZeroAddress;
        console.log(`   Buy DEX address: ${buyDexValid ? '✅ Valid' : '❌ Zero address'}`);
        console.log(`   Sell DEX address: ${sellDexValid ? '✅ Valid' : '❌ Zero address'}`);
        
        // Check other parameters
        const minProfitValid = arbitrageParams[5] > 0n;
        const slippageValid = Number(arbitrageParams[7]) <= 5000;
        const gasCostValid = arbitrageParams[8] > 0n;
        
        console.log(`   Min profit > 0: ${minProfitValid ? '✅ Valid' : '❌ Invalid'}`);
        console.log(`   Slippage <= 50%: ${slippageValid ? '✅ Valid' : '❌ Invalid'}`);
        console.log(`   Gas cost > 0: ${gasCostValid ? '✅ Valid' : '❌ Invalid'}`);
        
        // Check V3 fees array
        const v3Fees = arbitrageParams[4];
        const v3FeesValid = v3Fees.length === 0 || 
                          v3Fees.length === buyPath.length - 1 || 
                          v3Fees.length === sellPath.length - 1;
        console.log(`   V3 fees array length: ${v3FeesValid ? '✅ Valid' : '❌ Invalid'}`);
        console.log(`     V3 fees length: ${v3Fees.length}`);
        console.log(`     Buy path hops: ${buyPath.length - 1}`);
        console.log(`     Sell path hops: ${sellPath.length - 1}`);
        
        console.log('\n💡 LIKELY ISSUE:');
        if (!formsLoop) {
            console.log('   ❌ The arbitrage path does not form a complete loop!');
            console.log('   ❌ This is likely causing the require(false) error.');
            console.log('   💡 Fix: Ensure sellPath ends with the same token as buyPath starts with.');
        } else if (!assetMatch) {
            console.log('   ❌ The flashloan asset does not match the first token in buy path!');
            console.log('   💡 Fix: Ensure asset parameter matches buyPath[0].');
        } else if (!v3FeesValid) {
            console.log('   ❌ V3 fees array length is invalid!');
            console.log('   💡 Fix: V3 fees array should have length = path.length - 1 for each path.');
        } else {
            console.log('   ❓ All basic validations pass. The issue might be in DEX router validation.');
            console.log('   💡 Check if the DEX addresses are in the supportedRouters mapping.');
        }
        
    } catch (error) {
        console.log('\n❌ Failed to decode parameters:');
        console.log(`   Error: ${error.message}`);
        console.log('   💡 This suggests the parameter encoding is incorrect.');
    }
}

debugTransactionError().catch(console.error);
