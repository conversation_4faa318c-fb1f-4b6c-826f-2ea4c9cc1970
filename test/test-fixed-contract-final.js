const { ethers } = require('ethers');

async function testFixedContractFinal() {
    console.log('🔍 Testing final fixed contract without dependency conflicts...');
    
    // Connect to Hardhat fork
    const provider = new ethers.JsonRpcProvider('http://127.0.0.1:8547');
    const wallet = new ethers.Wallet('0xac0974bec39a17e36ba4a6b4d238ff944bacb478cbed5efcae784d7bf4f2ff80', provider);
    
    console.log(`📋 Test Configuration:`);
    console.log(`   Provider: Hardhat fork (mainnet)`);
    console.log(`   Wallet: ${wallet.address}`);
    console.log(`   Balance: ${ethers.formatEther(await provider.getBalance(wallet.address))} ETH`);
    
    // Deploy the fixed contract
    console.log('\n🚀 Deploying fixed contract...');
    
    const aavePool = '******************************************'; // Aave V3 Pool
    const balancerVault = '******************************************'; // Balancer V2 Vault
    
    const fs = require('fs');
    const contractArtifact = JSON.parse(fs.readFileSync('./artifacts/contracts/HybridFlashloanArbitrageFixed.sol/HybridFlashloanArbitrageFixed.json', 'utf8'));
    
    const contractFactory = new ethers.ContractFactory(
        contractArtifact.abi,
        contractArtifact.bytecode,
        wallet
    );
    
    const contract = await contractFactory.deploy(aavePool, balancerVault);
    await contract.waitForDeployment();
    const contractAddress = await contract.getAddress();
    
    console.log(`✅ Fixed contract deployed at: ${contractAddress}`);
    
    // Verify contract state
    const owner = await contract.owner();
    const chainId = await contract.CHAIN_ID();
    const v2Router = await contract.UNISWAP_V2_ROUTER();
    const v3Router = await contract.UNISWAP_V3_ROUTER();
    const aavePoolAddr = await contract.AAVE_POOL();
    const balancerVaultAddr = await contract.BALANCER_VAULT();
    
    console.log(`   Owner: ${owner}`);
    console.log(`   Chain ID: ${chainId}`);
    console.log(`   V2 Router: ${v2Router}`);
    console.log(`   V3 Router: ${v3Router}`);
    console.log(`   Aave Pool: ${aavePoolAddr}`);
    console.log(`   Balancer Vault: ${balancerVaultAddr}`);
    
    // Test the exact same transaction that was failing
    console.log('\n🧪 Testing with exact same failing parameters...');
    
    const wethAddress = '******************************************';
    const usdcAddress = '******************************************';
    
    const testParams = ethers.AbiCoder.defaultAbiCoder().encode(
        ['address', 'address', 'address', 'address', 'uint24', 'uint256', 'uint8'],
        [wethAddress, usdcAddress, v3Router, v2Router, 3000, ethers.parseEther('0.0001'), 0]
    );
    
    // Test with the exact same amounts that were failing
    const testAmounts = [
        { amount: ethers.parseEther('1'), label: '1 ETH' },
        { amount: ethers.parseEther('0.1'), label: '0.1 ETH' },
        { amount: ethers.parseEther('20'), label: '20 ETH (original failing amount)' }
    ];
    
    for (const test of testAmounts) {
        console.log(`\n   Testing ${test.label}:`);
        
        try {
            await contract.executeOptimalFlashloan.staticCall(
                wethAddress,
                test.amount,
                testParams
            );
            
            console.log(`      ✅ ${test.label}: SUCCESS - No enum conversion error!`);
            
        } catch (error) {
            const errorMsg = error.message.split('(')[0];
            console.log(`      ❌ ${test.label}: ${errorMsg}`);
            
            if (error.data && error.data.startsWith('0x4e487b71')) {
                const panicCode = parseInt(error.data.slice(10, 74), 16);
                if (panicCode === 17) {
                    console.log(`         🚨 ENUM_CONVERSION_ERROR still present!`);
                    console.log(`         💡 This suggests a deeper Solidity compiler issue`);
                } else {
                    console.log(`         📊 Different panic code: ${panicCode}`);
                }
            } else if (errorMsg.includes('Profit below minimum')) {
                console.log(`         ✅ Business logic error - flashloan execution works!`);
            } else if (errorMsg.includes('Invalid initiator')) {
                console.log(`         🔍 Aave initiator error - external contract issue`);
            } else if (errorMsg.includes('BAL#')) {
                console.log(`         🔍 Balancer error - external contract issue`);
            } else {
                console.log(`         🔍 Other error: ${errorMsg}`);
            }
        }
    }
    
    // Test with both providers
    console.log('\n🧪 Testing with different providers...');
    
    const providerTests = [
        { provider: 0, name: 'AAVE' },
        { provider: 1, name: 'BALANCER' }
    ];
    
    for (const providerTest of providerTests) {
        console.log(`\n   Testing ${providerTest.name} provider:`);
        
        const providerParams = ethers.AbiCoder.defaultAbiCoder().encode(
            ['address', 'address', 'address', 'address', 'uint24', 'uint256', 'uint8'],
            [wethAddress, usdcAddress, v3Router, v2Router, 3000, ethers.parseEther('0.0001'), providerTest.provider]
        );
        
        try {
            await contract.executeOptimalFlashloan.staticCall(
                wethAddress,
                ethers.parseEther('1'),
                providerParams
            );
            
            console.log(`      ✅ ${providerTest.name}: SUCCESS`);
            
        } catch (error) {
            const errorMsg = error.message.split('(')[0];
            console.log(`      ❌ ${providerTest.name}: ${errorMsg}`);
            
            if (error.data && error.data.startsWith('0x4e487b71')) {
                const panicCode = parseInt(error.data.slice(10, 74), 16);
                if (panicCode === 17) {
                    console.log(`         🚨 ENUM_CONVERSION_ERROR persists with ${providerTest.name}!`);
                }
            } else if (errorMsg.includes('Profit below minimum')) {
                console.log(`         ✅ Business logic error - ${providerTest.name} flashloan works!`);
            }
        }
    }
    
    // Test with amounts below minimum
    console.log('\n🧪 Testing minimum amount validation...');
    
    try {
        await contract.executeOptimalFlashloan.staticCall(
            wethAddress,
            ethers.parseUnits('1', 'gwei'), // Below minimum
            testParams
        );
        
        console.log(`   ❌ Should have failed with minimum amount error`);
        
    } catch (error) {
        const errorMsg = error.message.split('(')[0];
        console.log(`   ✅ Minimum validation: ${errorMsg}`);
        
        if (errorMsg.includes('Amount too small')) {
            console.log(`      ✅ Correct minimum amount validation`);
        } else if (error.data && error.data.startsWith('0x4e487b71')) {
            const panicCode = parseInt(error.data.slice(10, 74), 16);
            if (panicCode === 17) {
                console.log(`      🚨 ENUM_CONVERSION_ERROR even with minimum validation!`);
            }
        }
    }
    
    console.log('\n🏁 Fixed contract testing completed!');
    
    console.log('\n📊 FINAL ANALYSIS:');
    console.log('   If this fixed contract works, we can deploy it to mainnet.');
    console.log('   If it still fails, the issue is deeper in the Solidity compiler or EVM.');
}

testFixedContractFinal().catch(console.error);
