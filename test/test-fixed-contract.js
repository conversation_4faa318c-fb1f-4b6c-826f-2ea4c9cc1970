const { ethers } = require('ethers');

async function testFixedContract() {
    console.log('🔍 Testing fixed contract with enhanced error handling...');
    
    // Connect to Hardhat fork
    const provider = new ethers.JsonRpcProvider('http://127.0.0.1:8547');
    const wallet = new ethers.Wallet('0xac0974bec39a17e36ba4a6b4d238ff944bacb478cbed5efcae784d7bf4f2ff80', provider);
    
    console.log(`📋 Test Configuration:`);
    console.log(`   Provider: Hardhat fork (mainnet)`);
    console.log(`   Wallet: ${wallet.address}`);
    console.log(`   Balance: ${ethers.formatEther(await provider.getBalance(wallet.address))} ETH`);
    
    // Compile the fixed contract
    console.log('\n🔨 Compiling fixed contract...');
    const { execSync } = require('child_process');
    try {
        execSync('npx hardhat compile', { stdio: 'inherit' });
    } catch (error) {
        console.log('❌ Compilation failed');
        return;
    }
    
    // Deploy the fixed contract
    console.log('\n🚀 Deploying fixed contract...');
    
    const aavePoolAddressesProvider = '******************************************';
    const balancerVault = '******************************************';
    
    const fs = require('fs');
    const contractArtifact = JSON.parse(fs.readFileSync('./artifacts/contracts/HybridFlashloanArbitrageFixed.sol/HybridFlashloanArbitrageFixed.json', 'utf8'));
    
    const contractFactory = new ethers.ContractFactory(
        contractArtifact.abi,
        contractArtifact.bytecode,
        wallet
    );
    
    const contract = await contractFactory.deploy(aavePoolAddressesProvider, balancerVault);
    await contract.waitForDeployment();
    const contractAddress = await contract.getAddress();
    
    console.log(`✅ Fixed contract deployed at: ${contractAddress}`);
    
    // Verify contract state
    console.log('\n📋 Contract State Verification:');
    const owner = await contract.owner();
    const chainId = await contract.CHAIN_ID();
    const v2Router = await contract.UNISWAP_V2_ROUTER();
    const v3Router = await contract.UNISWAP_V3_ROUTER();
    
    console.log(`   Owner: ${owner}`);
    console.log(`   Chain ID: ${chainId}`);
    console.log(`   V2 Router: ${v2Router}`);
    console.log(`   V3 Router: ${v3Router}`);
    
    // Test parameters
    const wethAddress = '******************************************';
    const usdcAddress = '******************************************';
    
    const testParams = ethers.AbiCoder.defaultAbiCoder().encode(
        ['address', 'address', 'address', 'address', 'uint24', 'uint256', 'uint8'],
        [wethAddress, usdcAddress, v2Router, v3Router, 3000, ethers.parseEther('0.0001'), 0]
    );
    
    console.log('\n🧪 Testing with the problematic amounts...');
    
    const testAmounts = [
        { amount: ethers.parseUnits('1000', 'wei'), label: '1000 wei (working)' },
        { amount: ethers.parseUnits('224030315', 'wei'), label: '224030315 wei (threshold)' },
        { amount: ethers.parseUnits('1', 'gwei'), label: '1 gwei (failing)' },
        { amount: ethers.parseEther('0.02'), label: '0.02 ETH (bot amount)' }
    ];
    
    for (const test of testAmounts) {
        console.log(`\n   Testing ${test.label}:`);
        
        try {
            await contract.executeOptimalFlashloan.staticCall(
                wethAddress,
                test.amount,
                testParams
            );
            
            console.log(`      ✅ SUCCESS - No enum conversion error!`);
            
        } catch (error) {
            const errorMsg = error.message.split('(')[0];
            console.log(`      ❌ ${errorMsg}`);
            
            // Check for specific error types
            if (error.data && error.data.startsWith('0x4e487b71')) {
                const panicCode = parseInt(error.data.slice(10, 74), 16);
                if (panicCode === 17) {
                    console.log(`         🚨 ENUM_CONVERSION_ERROR still present`);
                } else {
                    console.log(`         📊 Different panic code: ${panicCode}`);
                }
            } else if (errorMsg.includes('Buy execution failed') || errorMsg.includes('Sell execution failed')) {
                console.log(`         ✅ Expected error - arbitrage logic not implemented`);
            } else if (errorMsg.includes('Failed to decode')) {
                console.log(`         🔍 Decode error - parameter issue`);
            } else {
                console.log(`         🔍 Other error type`);
            }
        }
    }
    
    // Test the external decode function
    console.log('\n🧪 Testing external decode function...');
    
    try {
        const decoded = await contract.decodeParams(testParams);
        console.log(`   ✅ External decode works - Provider: ${decoded.provider}`);
    } catch (error) {
        console.log(`   ❌ External decode failed: ${error.message.split('(')[0]}`);
    }
    
    // Test with different providers
    console.log('\n🧪 Testing with different providers...');
    
    const providerTests = [
        { provider: 0, name: 'AAVE' },
        { provider: 1, name: 'BALANCER' }
    ];
    
    for (const providerTest of providerTests) {
        console.log(`\n   Testing ${providerTest.name} provider:`);
        
        const providerParams = ethers.AbiCoder.defaultAbiCoder().encode(
            ['address', 'address', 'address', 'address', 'uint24', 'uint256', 'uint8'],
            [wethAddress, usdcAddress, v2Router, v3Router, 3000, ethers.parseEther('0.0001'), providerTest.provider]
        );
        
        try {
            await contract.executeOptimalFlashloan.staticCall(
                wethAddress,
                ethers.parseUnits('1', 'gwei'),
                providerParams
            );
            
            console.log(`      ✅ ${providerTest.name}: SUCCESS`);
            
        } catch (error) {
            const errorMsg = error.message.split('(')[0];
            console.log(`      ❌ ${providerTest.name}: ${errorMsg}`);
            
            if (error.data && error.data.startsWith('0x4e487b71')) {
                const panicCode = parseInt(error.data.slice(10, 74), 16);
                if (panicCode === 17) {
                    console.log(`         🚨 ENUM_CONVERSION_ERROR persists`);
                }
            }
        }
    }
    
    console.log('\n🏁 Fixed contract testing completed!');
    
    // Summary
    console.log('\n📊 SUMMARY:');
    console.log('   If the enum conversion error persists, it suggests the issue is deeper');
    console.log('   than parameter handling and might be in the Solidity compiler or EVM.');
    console.log('   If it\'s resolved, the enhanced error handling fixed the issue.');
}

testFixedContract().catch(console.error);
