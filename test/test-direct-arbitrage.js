const { ethers } = require('ethers');

async function testDirectArbitrage() {
    console.log('🔍 Testing direct arbitrage contract (without external flashloans)...');
    
    // Connect to Hardhat fork
    const provider = new ethers.JsonRpcProvider('http://127.0.0.1:8547');
    const wallet = new ethers.Wallet('0xac0974bec39a17e36ba4a6b4d238ff944bacb478cbed5efcae784d7bf4f2ff80', provider);
    
    console.log(`📋 Test Configuration:`);
    console.log(`   Provider: Hardhat fork (mainnet)`);
    console.log(`   Wallet: ${wallet.address}`);
    
    // Deploy the direct arbitrage contract
    console.log('\n🚀 Deploying direct arbitrage contract...');
    
    const fs = require('fs');
    const contractArtifact = JSON.parse(fs.readFileSync('./artifacts/contracts/DirectArbitrageContract.sol/DirectArbitrageContract.json', 'utf8'));
    
    const contractFactory = new ethers.ContractFactory(
        contractArtifact.abi,
        contractArtifact.bytecode,
        wallet
    );
    
    const contract = await contractFactory.deploy();
    await contract.waitForDeployment();
    const contractAddress = await contract.getAddress();
    
    console.log(`✅ Direct arbitrage contract deployed at: ${contractAddress}`);
    
    // Verify contract state
    const chainId = await contract.CHAIN_ID();
    const v2Router = await contract.UNISWAP_V2_ROUTER();
    const v3Router = await contract.UNISWAP_V3_ROUTER();
    
    console.log(`   Chain ID: ${chainId}`);
    console.log(`   V2 Router: ${v2Router}`);
    console.log(`   V3 Router: ${v3Router}`);
    
    // Test parameters (same as original contract)
    const wethAddress = '******************************************';
    const usdcAddress = '******************************************';
    
    const testParams = ethers.AbiCoder.defaultAbiCoder().encode(
        ['address', 'address', 'address', 'address', 'uint24', 'uint256', 'uint8'],
        [wethAddress, usdcAddress, v2Router, v3Router, 3000, ethers.parseEther('0.0001'), 0]
    );
    
    console.log('\n🧪 Testing direct arbitrage with problematic amounts...');
    
    const testAmounts = [
        { amount: ethers.parseUnits('1000', 'wei'), label: '1000 wei (working)' },
        { amount: ethers.parseUnits('224030315', 'wei'), label: '224030315 wei (threshold)' },
        { amount: ethers.parseUnits('1', 'gwei'), label: '1 gwei (failing in original)' },
        { amount: ethers.parseEther('0.02'), label: '0.02 ETH (bot amount)' }
    ];
    
    for (const test of testAmounts) {
        console.log(`\n   Testing ${test.label}:`);
        
        try {
            const tx = await contract.executeDirectArbitrage(wethAddress, test.amount, testParams);
            console.log(`      ✅ Direct arbitrage: SUCCESS`);
            
            // Check for events
            const receipt = await tx.wait();
            const events = receipt.logs.filter(log => {
                try {
                    return contract.interface.parseLog(log);
                } catch {
                    return false;
                }
            });
            
            if (events.length > 0) {
                const parsedEvent = contract.interface.parseLog(events[0]);
                console.log(`         Event: ArbitrageExecuted`);
                console.log(`         Amount: ${ethers.formatEther(parsedEvent.args.amountIn)} ETH`);
                console.log(`         Profit: ${ethers.formatEther(parsedEvent.args.profit)} ETH`);
                console.log(`         Provider: ${parsedEvent.args.provider === 0n ? 'AAVE' : 'BALANCER'}`);
            }
            
        } catch (error) {
            console.log(`      ❌ Direct arbitrage: ${error.message.split('(')[0]}`);
            
            if (error.data && error.data.startsWith('0x4e487b71')) {
                const panicCode = parseInt(error.data.slice(10, 74), 16);
                if (panicCode === 17) {
                    console.log(`         🚨 ENUM_CONVERSION_ERROR reproduced!`);
                }
            }
        }
    }
    
    console.log('\n🧪 Testing original flow reproduction...');
    
    for (const test of testAmounts) {
        console.log(`\n   Testing original flow with ${test.label}:`);
        
        try {
            await contract.testOriginalFlow(wethAddress, test.amount, testParams);
            console.log(`      ✅ Original flow: SUCCESS`);
            
        } catch (error) {
            console.log(`      ❌ Original flow: ${error.message.split('(')[0]}`);
            
            if (error.data && error.data.startsWith('0x4e487b71')) {
                const panicCode = parseInt(error.data.slice(10, 74), 16);
                if (panicCode === 17) {
                    console.log(`         🚨 ENUM_CONVERSION_ERROR in original flow!`);
                }
            }
        }
    }
    
    console.log('\n🧪 Testing with different providers...');
    
    const providerTests = [
        { provider: 0, name: 'AAVE' },
        { provider: 1, name: 'BALANCER' }
    ];
    
    for (const providerTest of providerTests) {
        console.log(`\n   Testing ${providerTest.name} provider with 1 gwei:`);
        
        const providerParams = ethers.AbiCoder.defaultAbiCoder().encode(
            ['address', 'address', 'address', 'address', 'uint24', 'uint256', 'uint8'],
            [wethAddress, usdcAddress, v2Router, v3Router, 3000, ethers.parseEther('0.0001'), providerTest.provider]
        );
        
        try {
            await contract.testOriginalFlow(
                wethAddress, 
                ethers.parseUnits('1', 'gwei'), 
                providerParams
            );
            console.log(`      ✅ ${providerTest.name}: SUCCESS`);
            
        } catch (error) {
            console.log(`      ❌ ${providerTest.name}: ${error.message.split('(')[0]}`);
            
            if (error.data && error.data.startsWith('0x4e487b71')) {
                const panicCode = parseInt(error.data.slice(10, 74), 16);
                if (panicCode === 17) {
                    console.log(`         🚨 ENUM_CONVERSION_ERROR with ${providerTest.name}!`);
                }
            }
        }
    }
    
    console.log('\n🏁 Direct arbitrage testing completed!');
    
    console.log('\n📊 FINAL ANALYSIS:');
    console.log('   If this direct contract works with all amounts, it confirms');
    console.log('   that the issue is in the EXTERNAL FLASHLOAN CONTRACT CALLS');
    console.log('   (Aave POOL.flashLoanSimple or Balancer VAULT.flashLoan)');
    console.log('   and NOT in the arbitrage logic itself.');
}

testDirectArbitrage().catch(console.error);
