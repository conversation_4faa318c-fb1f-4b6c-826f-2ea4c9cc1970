const { ethers } = require('ethers');

async function debugStructLayout() {
    console.log('🔍 Debugging struct layout for deployed contract...');
    
    const provider = new ethers.JsonRpcProvider('http://************:8545');
    const wallet = new ethers.Wallet('0x1521be785ecf5cb125cc42f1211789ef1a3f7fe4f33460601e56e52f58febd45', provider);
    
    const hybridContractAddress = '******************************************';
    const wethAddress = '******************************************';
    const daiAddress = '******************************************';
    const mainnetV2Router = '******************************************';
    const mainnetV3Router = '******************************************';
    
    const hybridContractInterface = new ethers.Interface([
        'function executeOptimalFlashloan(address asset, uint256 amount, bytes calldata params) external'
    ]);
    
    const flashloanAmount = ethers.parseEther('0.02');
    
    // Test different struct layouts to find the correct one
    const testLayouts = [
        {
            name: 'Current Layout (provider last)',
            types: ['address', 'address', 'address', 'address', 'uint24', 'uint256', 'uint8'],
            values: [wethAddress, daiAddress, mainnetV2Router, mainnetV3Router, 3000, ethers.parseEther('0.0001'), 0]
        },
        {
            name: 'Provider First',
            types: ['uint8', 'address', 'address', 'address', 'address', 'uint24', 'uint256'],
            values: [0, wethAddress, daiAddress, mainnetV2Router, mainnetV3Router, 3000, ethers.parseEther('0.0001')]
        },
        {
            name: 'No minProfit field',
            types: ['address', 'address', 'address', 'address', 'uint24', 'uint8'],
            values: [wethAddress, daiAddress, mainnetV2Router, mainnetV3Router, 3000, 0]
        },
        {
            name: 'Different order (v3Fee after routers)',
            types: ['address', 'address', 'uint24', 'address', 'address', 'uint256', 'uint8'],
            values: [wethAddress, daiAddress, 3000, mainnetV2Router, mainnetV3Router, ethers.parseEther('0.0001'), 0]
        },
        {
            name: 'Old layout (no v3Fee)',
            types: ['address', 'address', 'address', 'address', 'uint256', 'uint8'],
            values: [wethAddress, daiAddress, mainnetV2Router, mainnetV3Router, ethers.parseEther('0.0001'), 0]
        },
        {
            name: 'Minimal (just tokens and provider)',
            types: ['address', 'address', 'uint8'],
            values: [wethAddress, daiAddress, 0]
        }
    ];
    
    for (let i = 0; i < testLayouts.length; i++) {
        const layout = testLayouts[i];
        console.log(`\n${i + 1}. Testing: ${layout.name}`);
        console.log(`   Types: [${layout.types.join(', ')}]`);
        
        try {
            const arbitrageParams = ethers.AbiCoder.defaultAbiCoder().encode(layout.types, layout.values);
            
            const flashloanData = hybridContractInterface.encodeFunctionData('executeOptimalFlashloan', [
                wethAddress,
                flashloanAmount,
                arbitrageParams
            ]);
            
            await provider.call({
                to: hybridContractAddress,
                data: flashloanData,
                from: wallet.address
            });
            
            console.log(`   ✅ SUCCESS! Found working layout: ${layout.name}`);
            console.log(`   🎉 This is the correct struct layout for the deployed contract!`);
            return layout;
            
        } catch (error) {
            const errorMsg = error.message.split('(')[0];
            console.log(`   ❌ Failed: ${errorMsg}`);
            
            if (errorMsg.includes('INSUFFICIENT_OUTPUT_AMOUNT')) {
                console.log(`   ✅ PARTIAL SUCCESS! Struct layout works, but trade fails (expected for small amounts)`);
                console.log(`   🎉 This is likely the correct struct layout!`);
                return layout;
            }
        }
    }
    
    console.log('\n❌ None of the tested layouts worked. The issue might be elsewhere.');
    return null;
}

async function testWithWorkingLayout() {
    console.log('\n🧪 Testing with potentially working layouts...');
    
    const provider = new ethers.JsonRpcProvider('http://************:8545');
    const wallet = new ethers.Wallet('0x1521be785ecf5cb125cc42f1211789ef1a3f7fe4f33460601e56e52f58febd45', provider);
    
    const hybridContractAddress = '******************************************';
    const wethAddress = '******************************************';
    const daiAddress = '******************************************';
    const mainnetV2Router = '******************************************';
    const mainnetV3Router = '******************************************';
    
    const hybridContractInterface = new ethers.Interface([
        'function executeOptimalFlashloan(address asset, uint256 amount, bytes calldata params) external'
    ]);
    
    // Test with very small amount that might work
    const smallAmount = ethers.parseUnits('1', 'wei');
    
    console.log('   Testing with 1 wei (minimal amount)...');
    
    const arbitrageParams = ethers.AbiCoder.defaultAbiCoder().encode(
        ['address', 'address', 'address', 'address', 'uint24', 'uint256', 'uint8'],
        [wethAddress, daiAddress, mainnetV2Router, mainnetV3Router, 3000, ethers.parseEther('0.0001'), 0]
    );
    
    const flashloanData = hybridContractInterface.encodeFunctionData('executeOptimalFlashloan', [
        wethAddress,
        smallAmount,
        arbitrageParams
    ]);
    
    try {
        await provider.call({
            to: hybridContractAddress,
            data: flashloanData,
            from: wallet.address
        });
        
        console.log('   ✅ 1 wei test passed!');
        
    } catch (error) {
        const errorMsg = error.message.split('(')[0];
        console.log(`   ❌ 1 wei test failed: ${errorMsg}`);
        
        if (errorMsg.includes('INSUFFICIENT_OUTPUT_AMOUNT')) {
            console.log('   ✅ This confirms the struct layout is correct!');
            console.log('   💡 The issue is with trade amounts, not struct encoding');
            return true;
        }
    }
    
    return false;
}

// Check if the issue might be with the contract itself
async function checkContractState() {
    console.log('\n🔍 Checking contract state...');
    
    const provider = new ethers.JsonRpcProvider('http://************:8545');
    
    const hybridContractAddress = '******************************************';
    
    try {
        const contract = new ethers.Contract(hybridContractAddress, [
            'function owner() view returns (address)',
            'function CHAIN_ID() view returns (uint256)',
            'function UNISWAP_V2_ROUTER() view returns (address)',
            'function UNISWAP_V3_ROUTER() view returns (address)'
        ], provider);
        
        const owner = await contract.owner();
        const chainId = await contract.CHAIN_ID();
        const v2Router = await contract.UNISWAP_V2_ROUTER();
        const v3Router = await contract.UNISWAP_V3_ROUTER();
        
        console.log(`   Owner: ${owner}`);
        console.log(`   Chain ID: ${chainId}`);
        console.log(`   V2 Router: ${v2Router}`);
        console.log(`   V3 Router: ${v3Router}`);
        
        // Check if the router addresses match what we expect
        const expectedV2 = '******************************************';
        const expectedV3 = '******************************************';
        
        if (v2Router.toLowerCase() !== expectedV2.toLowerCase()) {
            console.log(`   ⚠️  V2 Router mismatch! Expected: ${expectedV2}, Got: ${v2Router}`);
        } else {
            console.log(`   ✅ V2 Router matches expected address`);
        }
        
        if (v3Router.toLowerCase() !== expectedV3.toLowerCase()) {
            console.log(`   ⚠️  V3 Router mismatch! Expected: ${expectedV3}, Got: ${v3Router}`);
        } else {
            console.log(`   ✅ V3 Router matches expected address`);
        }
        
        return { v2Router, v3Router };
        
    } catch (error) {
        console.log(`   ❌ Error checking contract state: ${error.message}`);
        return null;
    }
}

async function runDebug() {
    try {
        // First check the contract state
        const contractState = await checkContractState();
        
        if (contractState) {
            // Test with the actual router addresses from the contract
            console.log('\n🧪 Testing with contract\'s actual router addresses...');
            
            const provider = new ethers.JsonRpcProvider('http://************:8545');
            const wallet = new ethers.Wallet('0x1521be785ecf5cb125cc42f1211789ef1a3f7fe4f33460601e56e52f58febd45', provider);
            
            const hybridContractAddress = '******************************************';
            const wethAddress = '******************************************';
            const daiAddress = '******************************************';
            
            const hybridContractInterface = new ethers.Interface([
                'function executeOptimalFlashloan(address asset, uint256 amount, bytes calldata params) external'
            ]);
            
            const arbitrageParams = ethers.AbiCoder.defaultAbiCoder().encode(
                ['address', 'address', 'address', 'address', 'uint24', 'uint256', 'uint8'],
                [
                    wethAddress, 
                    daiAddress, 
                    contractState.v2Router, // Use actual contract's V2 router
                    contractState.v3Router, // Use actual contract's V3 router
                    3000, 
                    ethers.parseEther('0.0001'), 
                    0
                ]
            );
            
            const flashloanData = hybridContractInterface.encodeFunctionData('executeOptimalFlashloan', [
                wethAddress,
                ethers.parseUnits('1', 'wei'),
                arbitrageParams
            ]);
            
            try {
                await provider.call({
                    to: hybridContractAddress,
                    data: flashloanData,
                    from: wallet.address
                });
                
                console.log('   ✅ SUCCESS with contract\'s router addresses!');
                
            } catch (error) {
                const errorMsg = error.message.split('(')[0];
                console.log(`   ❌ Failed with contract's addresses: ${errorMsg}`);
                
                if (errorMsg.includes('INSUFFICIENT_OUTPUT_AMOUNT')) {
                    console.log('   ✅ This is progress! The struct encoding works now.');
                }
            }
        }
        
        // Try different struct layouts
        const workingLayout = await debugStructLayout();
        
        if (!workingLayout) {
            await testWithWorkingLayout();
        }
        
        console.log('\n🏁 Debug completed!');
        
    } catch (error) {
        console.error('❌ Debug failed:', error);
    }
}

runDebug();
