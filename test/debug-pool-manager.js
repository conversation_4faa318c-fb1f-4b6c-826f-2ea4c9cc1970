#!/usr/bin/env node

/**
 * Debug PoolManager Implementation
 * 
 * This test debugs the PoolManager class to see why it's not returning V2 pool data
 */

const { ethers } = require('ethers');
const { PoolManager } = require('../dist/dex/pools');

async function debugPoolManager() {
  console.log('🔍 Debugging PoolManager Implementation');
  console.log('=' .repeat(50));

  const provider = new ethers.JsonRpcProvider(process.env.RPC_URL || 'http://localhost:8545');
  const poolManager = new PoolManager();
  
  // Test tokens
  const USDC = '******************************************';
  const WETH = '******************************************';
  
  console.log(`USDC: ${USDC}`);
  console.log(`WETH: ${WETH}`);
  
  try {
    console.log('\n1. Testing PoolManager.getPool() for Uniswap V2...');
    
    // Enable debug logging temporarily
    const originalLogLevel = process.env.LOG_LEVEL;
    process.env.LOG_LEVEL = 'debug';
    
    const v2Pool = await poolManager.getPool(USDC, WETH, 'uniswap-v2');
    
    // Restore log level
    process.env.LOG_LEVEL = originalLogLevel;
    
    if (v2Pool) {
      console.log('   ✅ V2 Pool found!');
      console.log(`   Address: ${v2Pool.address}`);
      console.log(`   Token0: ${v2Pool.token0.symbol} (${v2Pool.token0.address})`);
      console.log(`   Token1: ${v2Pool.token1.symbol} (${v2Pool.token1.address})`);
      console.log(`   Protocol: ${v2Pool.protocol}`);
      
      if (v2Pool.reserves) {
        console.log(`   Reserve0: ${ethers.formatUnits(v2Pool.reserves.reserve0, v2Pool.token0.decimals)}`);
        console.log(`   Reserve1: ${ethers.formatUnits(v2Pool.reserves.reserve1, v2Pool.token1.decimals)}`);
        
        // Calculate price
        const reserve0 = Number(ethers.formatUnits(v2Pool.reserves.reserve0, v2Pool.token0.decimals));
        const reserve1 = Number(ethers.formatUnits(v2Pool.reserves.reserve1, v2Pool.token1.decimals));
        
        if (v2Pool.token0.symbol === 'USDC') {
          console.log(`   Price: 1 WETH = ${(reserve0 / reserve1).toFixed(2)} USDC`);
        } else {
          console.log(`   Price: 1 USDC = ${(reserve1 / reserve0).toFixed(6)} WETH`);
        }
      } else {
        console.log('   ❌ No reserves data');
      }
    } else {
      console.log('   ❌ V2 Pool NOT found by PoolManager');
      
      // Let's debug step by step
      console.log('\n2. Debugging PoolManager internals...');
      
      // Test if the issue is in getUniswapV2PoolAddress
      console.log('   Testing getUniswapV2PoolAddress manually...');
      
      // We need to access the private method, so let's recreate the logic
      const { config, ADDRESSES } = require('../dist/config');
      
      console.log(`   Chain ID: ${config.chainId}`);
      console.log(`   V2 Factory: ${ADDRESSES.UNISWAP_V2_FACTORY}`);
      
      if (!ADDRESSES.UNISWAP_V2_FACTORY || ADDRESSES.UNISWAP_V2_FACTORY === '') {
        console.log('   ❌ V2 Factory address not available');
      } else {
        console.log('   ✅ V2 Factory address available');
        
        // Test factory call manually
        const factoryABI = ['function getPair(address tokenA, address tokenB) view returns (address pair)'];
        const factory = new ethers.Contract(ADDRESSES.UNISWAP_V2_FACTORY, factoryABI, provider);
        
        const pairAddress = await factory.getPair(USDC, WETH);
        console.log(`   Manual factory call result: ${pairAddress}`);
        
        if (pairAddress === ethers.ZeroAddress) {
          console.log('   ❌ Factory returned zero address');
        } else {
          console.log('   ✅ Factory returned valid address');
          
          // Test if the issue is in loadPoolData
          console.log('\n3. Testing loadPoolData manually...');
          
          const pairABI = [
            'function getReserves() view returns (uint112 reserve0, uint112 reserve1, uint32 blockTimestampLast)',
            'function token0() view returns (address)',
            'function token1() view returns (address)'
          ];
          
          const pair = new ethers.Contract(pairAddress, pairABI, provider);
          
          try {
            const [reserves, token0Address, token1Address] = await Promise.all([
              pair.getReserves(),
              pair.token0(),
              pair.token1()
            ]);
            
            console.log(`   Reserves: ${reserves[0]}, ${reserves[1]}`);
            console.log(`   Token0: ${token0Address}`);
            console.log(`   Token1: ${token1Address}`);
            
            // Test getTokenInfo
            console.log('\n4. Testing getTokenInfo...');
            
            // We'll need to test this with a fresh PoolManager instance
            const testPoolManager = new PoolManager();
            
            // Try to get token info
            try {
              const token0Info = await testPoolManager.getTokenInfo(token0Address);
              const token1Info = await testPoolManager.getTokenInfo(token1Address);
              
              console.log(`   Token0 Info: ${token0Info.symbol} (${token0Info.decimals} decimals)`);
              console.log(`   Token1 Info: ${token1Info.symbol} (${token1Info.decimals} decimals)`);
              
              if (token0Info.symbol === 'UNKNOWN' || token1Info.symbol === 'UNKNOWN') {
                console.log('   ⚠️  Token info lookup failed');
              } else {
                console.log('   ✅ Token info lookup successful');
              }
              
            } catch (tokenError) {
              console.log(`   ❌ Token info error: ${tokenError.message}`);
            }
            
          } catch (pairError) {
            console.log(`   ❌ Pair contract error: ${pairError.message}`);
          }
        }
      }
    }
    
    console.log('\n5. Testing PoolManager.getPool() for Uniswap V3...');
    const v3Pool = await poolManager.getPool(USDC, WETH, 'uniswap-v3', 3000);
    
    if (v3Pool) {
      console.log('   ✅ V3 Pool found!');
      console.log(`   Address: ${v3Pool.address}`);
      console.log(`   Liquidity: ${v3Pool.liquidity}`);
      console.log(`   Tick: ${v3Pool.tick}`);
    } else {
      console.log('   ❌ V3 Pool NOT found');
    }
    
  } catch (error) {
    console.error('❌ Error:', error.message);
    console.error('Stack:', error.stack);
  }
}

// Run the debug
if (require.main === module) {
  debugPoolManager().catch(console.error);
}

module.exports = { debugPoolManager };
