const { splitScreenDashboard } = require('../dist/utils/splitScreenDashboard');
const { logger } = require('../dist/utils/logger');
const { logger } = require('../dist/utils/logger');
const { ethers } = require('ethers');

console.log('🧪 Testing Split Screen Dashboard...\n');

// Set environment variable to enable split screen
process.env.SPLIT_SCREEN_DASHBOARD = 'true';

// Initialize dashboard with test data
const testData = {
  currentBlock: 18500000,
  networkName: 'Sepolia',
  isRunning: true,
  uptime: Date.now(),
  lastActivity: Date.now(),
  flashloanEnabled: true,
  mevShareEnabled: false,
  arbitrageEnabled: true,
  totalTransactions: 1250,
  relevantTransactions: 89,
  opportunitiesFound: 23,
  opportunitiesExecuted: 8,
  totalProfit: ethers.parseEther('0.0456'),
  avgGasPrice: ethers.parseUnits('25', 'gwei'),
  configuration: {
    tokenPairs: ['USDC/WETH', 'DAI/USDC', 'WETH/USDT'],
    dexes: ['Uniswap V3', 'Curve', 'Sushiswap'],
    minProfitThreshold: '0.01',
    maxGasPrice: '50'
  },
  successfulTransactions: [
    {
      timestamp: Date.now() - 30000,
      type: 'flashloan',
      profit: ethers.parseEther('0.0123'),
      gasUsed: ethers.parseEther('0.0045'),
      txHash: '******************************************',
      confidence: 95,
      details: 'USDC → WETH'
    },
    {
      timestamp: Date.now() - 60000,
      type: 'arbitrage',
      profit: ethers.parseEther('0.0089'),
      gasUsed: ethers.parseEther('0.0032'),
      txHash: '******************************************',
      confidence: 87,
      details: 'Cross-DEX'
    },
    {
      timestamp: Date.now() - 120000,
      type: 'sandwich',
      profit: ethers.parseEther('0.0234'),
      gasUsed: ethers.parseEther('0.0067'),
      txHash: '******************************************',
      confidence: 92,
      details: 'Live backrun'
    }
  ],
  errors: 2,
  lastError: 'Gas price too high'
};

// Start the split screen dashboard
splitScreenDashboard.updateDashboardData(testData);
splitScreenDashboard.start();

console.log('Split screen dashboard started! Use the following keys:');
console.log('- Press "r" to refresh');
console.log('- Press "q", "Escape", or "Ctrl+C" to quit');
console.log('- Use arrow keys to scroll in the log panel');

// Simulate live activity
let transactionCount = 0;
let opportunityCount = 0;

const simulateActivity = () => {
  const activities = [
    () => {
      logger.systemStatus('Mempool monitoring active', { liquidity: '109.601634' });
    },
    () => {
      logger.info('New block detected', { blockNumber: testData.currentBlock + Math.floor(Math.random() * 10) });
    },
    () => {
      logger.success('MEV opportunity detected');
      opportunityCount++;
    },
    () => {
      logger.warn('High gas price detected', { gasPrice: '45 gwei' });
    },
    () => {
      logger.error('RPC connection timeout', new Error('Connection timeout after 5s'));
    },
    () => {
      const profit = (Math.random() * 0.05 + 0.001).toFixed(4);
      logger.info('Flashloan executed successfully', { 
        profit: `${profit} ETH`,
        gasUsed: '0.0032 ETH',
        type: 'USDC → WETH'
      });
      transactionCount++;
    },
    () => {
      logger.bundleSubmission(testData.currentBlock + 1, 3);
    },
    () => {
      logger.debug('Gas optimization completed', { 
        oldPrice: '30 gwei',
        newPrice: '25 gwei',
        savings: '16.7%'
      });
    },
    () => {
      logger.info('Arbitrage opportunity detected', {
        tokenA: 'USDC',
        tokenB: 'WETH',
        amount: '1000',
        dexA: 'Uniswap V3',
        dexB: 'Curve'
      });
    },
    () => {
      logger.info('Mempool transaction analyzed', {
        hash: `0x${Math.random().toString(16).slice(2, 18)}${'0'.repeat(40)}`,
        relevant: Math.random() > 0.7,
        gasPrice: `${Math.floor(Math.random() * 20 + 20)} gwei`
      });
    }
  ];

  // Execute random activity
  const activity = activities[Math.floor(Math.random() * activities.length)];
  activity();

  // Update dashboard stats occasionally
  if (Math.random() > 0.7) {
    testData.totalTransactions += Math.floor(Math.random() * 5);
    testData.relevantTransactions += Math.floor(Math.random() * 2);
    testData.opportunitiesFound += opportunityCount;
    testData.opportunitiesExecuted += transactionCount;
    testData.lastActivity = Date.now();
    testData.currentBlock += Math.floor(Math.random() * 2);
    
    // Add new successful transaction occasionally
    if (transactionCount > 0 && Math.random() > 0.8) {
      const types = ['flashloan', 'arbitrage', 'sandwich', 'mev-share'];
      const newTx = {
        timestamp: Date.now(),
        type: types[Math.floor(Math.random() * types.length)],
        profit: ethers.parseEther((Math.random() * 0.05 + 0.001).toFixed(4)),
        gasUsed: ethers.parseEther((Math.random() * 0.01 + 0.001).toFixed(4)),
        txHash: `0x${Math.random().toString(16).slice(2, 18)}${'0'.repeat(40)}`,
        confidence: Math.floor(Math.random() * 30 + 70),
        details: Math.random() > 0.5 ? 'USDC → WETH' : 'Cross-DEX'
      };
      
      testData.successfulTransactions.push(newTx);
      if (testData.successfulTransactions.length > 10) {
        testData.successfulTransactions = testData.successfulTransactions.slice(-10);
      }
      
      transactionCount = 0; // Reset counter
    }
    
    opportunityCount = 0; // Reset counter
    splitScreenDashboard.updateDashboardData(testData);
  }
};

// Start simulation
const activityInterval = setInterval(simulateActivity, 1500);

// Handle graceful shutdown
process.on('SIGINT', () => {
  clearInterval(activityInterval);
  splitScreenDashboard.stop();
  console.log('\n👋 Split screen dashboard test completed!');
  process.exit(0);
});

// Keep the process running
process.stdin.resume();
