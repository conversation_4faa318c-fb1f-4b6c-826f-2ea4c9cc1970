const { ethers } = require('ethers');

async function debugAbiDecode() {
    console.log('🔍 Debugging abi.decode operation...');
    
    // Connect to Hardhat fork
    const provider = new ethers.JsonRpcProvider('http://127.0.0.1:8547');
    const wallet = new ethers.Wallet('0xac0974bec39a17e36ba4a6b4d238ff944bacb478cbed5efcae784d7bf4f2ff80', provider);
    
    // Deploy a simple test contract to isolate the abi.decode issue
    console.log('\n🚀 Deploying test contract...');
    
    const testContractCode = `
        // SPDX-License-Identifier: MIT
        pragma solidity ^0.8.19;
        
        contract AbiDecodeTest {
            enum FlashloanProvider { AAVE, BALANCER }
            
            struct ArbitrageParams {
                address tokenA;
                address tokenB;
                address buyDex;
                address sellDex;
                uint24 v3Fee;
                uint256 minProfit;
                FlashloanProvider provider;
            }
            
            function testDecode(bytes calldata params) external pure returns (ArbitrageParams memory) {
                return abi.decode(params, (ArbitrageParams));
            }
            
            function testDecodeWithAmount(uint256 amount, bytes calldata params) external pure returns (ArbitrageParams memory, uint256) {
                ArbitrageParams memory arbParams = abi.decode(params, (ArbitrageParams));
                return (arbParams, amount);
            }
        }
    `;
    
    // Write and compile the test contract
    const fs = require('fs');
    fs.writeFileSync('./contracts/AbiDecodeTest.sol', testContractCode);
    
    // Compile the test contract
    const { execSync } = require('child_process');
    try {
        execSync('npx hardhat compile', { stdio: 'inherit' });
    } catch (error) {
        console.log('Compilation error, but continuing...');
    }
    
    // Deploy the test contract
    const testArtifact = JSON.parse(fs.readFileSync('./artifacts/contracts/AbiDecodeTest.sol/AbiDecodeTest.json', 'utf8'));
    
    const testFactory = new ethers.ContractFactory(
        testArtifact.abi,
        testArtifact.bytecode,
        wallet
    );
    
    const testContract = await testFactory.deploy();
    await testContract.waitForDeployment();
    const testAddress = await testContract.getAddress();
    
    console.log(`✅ Test contract deployed at: ${testAddress}`);
    
    // Test parameters
    const wethAddress = '******************************************';
    const usdcAddress = '******************************************';
    const v2Router = '******************************************';
    const v3Router = '******************************************';
    
    console.log('\n🧪 Testing abi.decode with different scenarios...');
    
    // Test 1: Basic decode without amount
    console.log('\n   Test 1: Basic abi.decode (no amount involved)');
    const basicParams = ethers.AbiCoder.defaultAbiCoder().encode(
        ['address', 'address', 'address', 'address', 'uint24', 'uint256', 'uint8'],
        [wethAddress, usdcAddress, v2Router, v3Router, 3000, ethers.parseEther('0.0001'), 0]
    );
    
    try {
        const result = await testContract.testDecode(basicParams);
        console.log(`      ✅ Basic decode works`);
        console.log(`         Provider: ${result.provider} (${result.provider === 0n ? 'AAVE' : 'BALANCER'})`);
    } catch (error) {
        console.log(`      ❌ Basic decode failed: ${error.message}`);
    }
    
    // Test 2: Decode with working amount
    console.log('\n   Test 2: Decode with working amount (1000 wei)');
    try {
        const result = await testContract.testDecodeWithAmount(ethers.parseUnits('1000', 'wei'), basicParams);
        console.log(`      ✅ Decode with working amount succeeds`);
        console.log(`         Provider: ${result[0].provider}`);
        console.log(`         Amount: ${result[1]}`);
    } catch (error) {
        console.log(`      ❌ Decode with working amount failed: ${error.message}`);
    }
    
    // Test 3: Decode with failing amount
    console.log('\n   Test 3: Decode with failing amount (1 gwei)');
    try {
        const result = await testContract.testDecodeWithAmount(ethers.parseUnits('1', 'gwei'), basicParams);
        console.log(`      ✅ Decode with failing amount succeeds`);
        console.log(`         Provider: ${result[0].provider}`);
        console.log(`         Amount: ${result[1]}`);
    } catch (error) {
        console.log(`      ❌ Decode with failing amount failed: ${error.message}`);
    }
    
    // Test 4: Test with different enum values
    console.log('\n   Test 4: Testing different enum values');
    
    const enumTests = [
        { value: 0, name: 'AAVE (0)' },
        { value: 1, name: 'BALANCER (1)' },
        { value: 2, name: 'Invalid (2)' },
        { value: 255, name: 'Invalid (255)' }
    ];
    
    for (const enumTest of enumTests) {
        const testParams = ethers.AbiCoder.defaultAbiCoder().encode(
            ['address', 'address', 'address', 'address', 'uint24', 'uint256', 'uint8'],
            [wethAddress, usdcAddress, v2Router, v3Router, 3000, ethers.parseEther('0.0001'), enumTest.value]
        );
        
        try {
            const result = await testContract.testDecode(testParams);
            console.log(`      ✅ ${enumTest.name}: SUCCESS`);
        } catch (error) {
            console.log(`      ❌ ${enumTest.name}: ${error.message.split('(')[0]}`);
            if (error.data && error.data.startsWith('0x4e487b71')) {
                const panicCode = parseInt(error.data.slice(10, 74), 16);
                if (panicCode === 17) {
                    console.log(`         🚨 ENUM_CONVERSION_ERROR as expected`);
                }
            }
        }
    }
    
    // Test 5: Test if the issue is in the original contract's logic
    console.log('\n   Test 5: Testing original contract with isolated decode');
    
    // Deploy the original contract and test just the decode part
    const aavePoolAddressesProvider = '******************************************';
    const balancerVault = '******************************************';
    
    const originalArtifact = JSON.parse(fs.readFileSync('./artifacts/contracts/HybridFlashloanArbitrage.sol/HybridFlashloanArbitrage.json', 'utf8'));
    
    const originalFactory = new ethers.ContractFactory(
        originalArtifact.abi,
        originalArtifact.bytecode,
        wallet
    );
    
    const originalContract = await originalFactory.deploy(aavePoolAddressesProvider, balancerVault);
    await originalContract.waitForDeployment();
    
    console.log(`      Original contract deployed for comparison`);
    
    // Test the working amount
    try {
        await originalContract.executeOptimalFlashloan.staticCall(
            wethAddress,
            ethers.parseUnits('1000', 'wei'),
            basicParams
        );
        console.log(`      ✅ Original contract works with 1000 wei`);
    } catch (error) {
        console.log(`      ❌ Original contract fails with 1000 wei: ${error.message.split('(')[0]}`);
    }
    
    // Test the failing amount
    try {
        await originalContract.executeOptimalFlashloan.staticCall(
            wethAddress,
            ethers.parseUnits('1', 'gwei'),
            basicParams
        );
        console.log(`      ✅ Original contract works with 1 gwei`);
    } catch (error) {
        console.log(`      ❌ Original contract fails with 1 gwei: ${error.message.split('(')[0]}`);
    }
    
    console.log('\n🏁 ABI decode debugging completed!');
    
    // Clean up
    try {
        fs.unlinkSync('./contracts/AbiDecodeTest.sol');
    } catch (e) {}
}

debugAbiDecode().catch(console.error);
