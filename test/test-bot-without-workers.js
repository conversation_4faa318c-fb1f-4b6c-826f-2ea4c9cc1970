#!/usr/bin/env node

/**
 * Test bot functionality without workers (single-threaded mode)
 */

// Disable dashboard and workers
process.env.SPLIT_SCREEN_DASHBOARD = 'false';
process.env.DISABLE_DASHBOARD = 'true';
process.env.ENABLE_WORKERS = 'false';

const { ArbitrageStrategy } = require('../dist/strategies/arbitrage');

async function testBotWithoutWorkers() {
  console.log('🤖 Testing Bot Without Workers (Single-threaded)\n');

  let arbitrageStrategy;

  try {
    // Initialize arbitrage strategy
    console.log('⏳ Initializing arbitrage strategy...');
    arbitrageStrategy = new ArbitrageStrategy();
    
    // Wait for initialization
    await new Promise(resolve => setTimeout(resolve, 2000));

    console.log(`✅ Workers enabled: ${arbitrageStrategy.isUsingWorkers()}`);
    console.log(`✅ Mode: ${arbitrageStrategy.isUsingWorkers() ? 'Multi-threaded' : 'Single-threaded'}\n`);

    // Test arbitrage scan
    console.log('🔍 Running arbitrage scan...');
    const start = Date.now();
    const opportunities = await arbitrageStrategy.scanForArbitrageOpportunities();
    const duration = Date.now() - start;

    console.log(`✅ Scan completed in ${duration}ms`);
    console.log(`✅ Found ${opportunities.length} opportunities`);
    console.log(`✅ Mode confirmed: ${arbitrageStrategy.isUsingWorkers() ? 'Multi-threaded' : 'Single-threaded'}\n`);

    // Test multiple scans to ensure stability
    console.log('🔄 Running 3 consecutive scans for stability test...');
    const scanTimes = [];
    
    for (let i = 0; i < 3; i++) {
      const iterStart = Date.now();
      const iterOpportunities = await arbitrageStrategy.scanForArbitrageOpportunities();
      const iterTime = Date.now() - iterStart;
      
      scanTimes.push(iterTime);
      console.log(`   Scan ${i + 1}: ${iterTime}ms (${iterOpportunities.length} opportunities)`);
      
      // Small delay
      await new Promise(resolve => setTimeout(resolve, 500));
    }

    const avgTime = scanTimes.reduce((sum, time) => sum + time, 0) / scanTimes.length;
    console.log(`\n📊 Average scan time: ${avgTime.toFixed(2)}ms`);
    console.log(`📊 Performance: ${avgTime < 5000 ? '✅ Good' : avgTime < 10000 ? '⚠️ Moderate' : '❌ Slow'}`);

    console.log('\n✅ Single-threaded mode test completed successfully!');
    console.log('✅ Bot is working correctly without workers');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error(error.stack);
  } finally {
    // Cleanup
    if (arbitrageStrategy) {
      console.log('\n🧹 Cleaning up...');
      try {
        await arbitrageStrategy.shutdown();
        console.log('✅ Cleanup completed');
      } catch (cleanupError) {
        console.error('⚠️  Cleanup error:', cleanupError.message);
      }
    }
  }
}

// Handle graceful shutdown
process.on('SIGINT', async () => {
  console.log('\n🛑 Test interrupted');
  process.exit(0);
});

process.on('SIGTERM', async () => {
  console.log('\n🛑 Test terminated');
  process.exit(0);
});

// Run the test
if (require.main === module) {
  testBotWithoutWorkers().catch(error => {
    console.error('Fatal error:', error);
    process.exit(1);
  });
}

module.exports = { testBotWithoutWorkers };
