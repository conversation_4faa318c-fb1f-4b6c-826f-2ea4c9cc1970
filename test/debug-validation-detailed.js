const { ethers } = require('ethers');

async function debugValidationDetailed() {
    console.log('🔍 Detailed validation debugging...');
    
    // Connect to the deployed contract on fork
    const provider = new ethers.JsonRpcProvider('http://127.0.0.1:8547');
    const contractAddress = '******************************************';
    
    // Token addresses
    const wethAddress = '******************************************';
    const daiAddress = '******************************************';
    
    // Get router addresses from contract
    const contract = new ethers.Contract(contractAddress, [
        'function UNISWAP_V2_ROUTER() external view returns (address)',
        'function UNISWAP_V3_ROUTER() external view returns (address)'
    ], provider);
    
    const v2Router = await contract.UNISWAP_V2_ROUTER();
    const v3Router = await contract.UNISWAP_V3_ROUTER();
    
    console.log('📋 Debug Configuration:');
    console.log(`   Contract: ${contractAddress}`);
    console.log(`   WETH: ${wethAddress}`);
    console.log(`   DAI: ${daiAddress}`);
    console.log(`   V2 Router: ${v2Router}`);
    console.log(`   V3 Router: ${v3Router}`);
    
    // Test different parameter combinations to isolate the issue
    const testCases = [
        {
            name: 'Original failing case',
            buyPath: [wethAddress, daiAddress],
            sellPath: [daiAddress, wethAddress],
            buyDex: v3Router,
            sellDex: v2Router,
            v3Fees: [3000],
            minProfit: ethers.parseEther('0.00001'),
            provider: 0,
            slippage: 1000,
            gasCost: ethers.parseUnits('200', 'gwei')
        },
        {
            name: 'Both V2 (no V3 fees needed)',
            buyPath: [wethAddress, daiAddress],
            sellPath: [daiAddress, wethAddress],
            buyDex: v2Router,
            sellDex: v2Router,
            v3Fees: [],
            minProfit: ethers.parseEther('0.00001'),
            provider: 0,
            slippage: 1000,
            gasCost: ethers.parseUnits('200', 'gwei')
        },
        {
            name: 'Both V3 (need 2 V3 fees)',
            buyPath: [wethAddress, daiAddress],
            sellPath: [daiAddress, wethAddress],
            buyDex: v3Router,
            sellDex: v3Router,
            v3Fees: [3000, 3000],
            minProfit: ethers.parseEther('0.00001'),
            provider: 0,
            slippage: 1000,
            gasCost: ethers.parseUnits('200', 'gwei')
        },
        {
            name: 'Higher minimum profit',
            buyPath: [wethAddress, daiAddress],
            sellPath: [daiAddress, wethAddress],
            buyDex: v3Router,
            sellDex: v2Router,
            v3Fees: [3000],
            minProfit: ethers.parseEther('0.1'),
            provider: 0,
            slippage: 1000,
            gasCost: ethers.parseUnits('200', 'gwei')
        },
        {
            name: 'Lower slippage',
            buyPath: [wethAddress, daiAddress],
            sellPath: [daiAddress, wethAddress],
            buyDex: v3Router,
            sellDex: v2Router,
            v3Fees: [3000],
            minProfit: ethers.parseEther('0.00001'),
            provider: 0,
            slippage: 100, // 1%
            gasCost: ethers.parseUnits('200', 'gwei')
        }
    ];
    
    const wallet = new ethers.Wallet('0xac0974bec39a17e36ba4a6b4d238ff944bacb478cbed5efcae784d7bf4f2ff80', provider);
    const executeContract = new ethers.Contract(contractAddress, [
        'function executeOptimalFlashloan(address asset, uint256 amount, bytes calldata params) external'
    ], wallet);
    
    for (const testCase of testCases) {
        console.log(`\n🧪 Testing: ${testCase.name}`);
        
        const params = ethers.AbiCoder.defaultAbiCoder().encode(
            ['address[]', 'address[]', 'address', 'address', 'uint24[]', 'uint256', 'uint8', 'uint256', 'uint256'],
            [
                testCase.buyPath,
                testCase.sellPath,
                testCase.buyDex,
                testCase.sellDex,
                testCase.v3Fees,
                testCase.minProfit,
                testCase.provider,
                testCase.slippage,
                testCase.gasCost
            ]
        );
        
        console.log(`   Buy path: ${testCase.buyPath.map(addr => addr.slice(0,6) + '...' + addr.slice(-4)).join(' → ')}`);
        console.log(`   Sell path: ${testCase.sellPath.map(addr => addr.slice(0,6) + '...' + addr.slice(-4)).join(' → ')}`);
        console.log(`   Buy DEX: ${testCase.buyDex === v3Router ? 'V3' : 'V2'}`);
        console.log(`   Sell DEX: ${testCase.sellDex === v3Router ? 'V3' : 'V2'}`);
        console.log(`   V3 fees: [${testCase.v3Fees.map(fee => Number(fee)/10000 + '%').join(', ')}]`);
        
        try {
            await executeContract.executeOptimalFlashloan.staticCall(
                wethAddress,
                ethers.parseEther('1.0'), // 1 ETH
                params
            );
            
            console.log(`   ✅ PASSED: Validation successful!`);
            
        } catch (error) {
            const errorMsg = error.message;
            console.log(`   ❌ FAILED: ${errorMsg.split('(')[0]}`);
            
            if (errorMsg.includes('execution reverted') && !errorMsg.includes('Arbitrage resulted in loss')) {
                console.log(`      💡 This is likely the require(false) issue`);
            } else if (errorMsg.includes('Arbitrage resulted in loss')) {
                console.log(`      ✅ Validation passed - got expected arbitrage error`);
            } else if (errorMsg.includes('Invalid V3 fees')) {
                console.log(`      💡 V3 fees validation issue`);
            } else if (errorMsg.includes('Buy path too short')) {
                console.log(`      💡 Path validation issue`);
            } else if (errorMsg.includes('Unsupported')) {
                console.log(`      💡 DEX support issue`);
            } else if (errorMsg.includes('complete loop')) {
                console.log(`      💡 Arbitrage loop validation issue`);
            }
        }
    }
    
    // Test with manual validation checks
    console.log('\n🔍 Manual validation checks:');
    
    const originalCase = testCases[0];
    
    // Check arbitrage loop
    const firstBuyToken = originalCase.buyPath[0];
    const lastSellToken = originalCase.sellPath[originalCase.sellPath.length - 1];
    const formsLoop = firstBuyToken.toLowerCase() === lastSellToken.toLowerCase();
    
    console.log(`   Arbitrage loop: ${formsLoop ? '✅' : '❌'} (${firstBuyToken} → ${lastSellToken})`);
    
    // Check path lengths
    console.log(`   Buy path length: ${originalCase.buyPath.length >= 2 && originalCase.buyPath.length <= 5 ? '✅' : '❌'} (${originalCase.buyPath.length})`);
    console.log(`   Sell path length: ${originalCase.sellPath.length >= 2 && originalCase.sellPath.length <= 5 ? '✅' : '❌'} (${originalCase.sellPath.length})`);
    
    // Check V3 fees
    const maxPossibleFees = (originalCase.buyPath.length - 1) + (originalCase.sellPath.length - 1);
    const v3FeesValid = originalCase.v3Fees.length <= maxPossibleFees && originalCase.v3Fees.length >= 1;
    console.log(`   V3 fees: ${v3FeesValid ? '✅' : '❌'} (${originalCase.v3Fees.length} fees, max ${maxPossibleFees})`);
    
    // Check other parameters
    console.log(`   Min profit > 0: ${originalCase.minProfit > 0n ? '✅' : '❌'}`);
    console.log(`   Slippage <= 50%: ${originalCase.slippage <= 5000 ? '✅' : '❌'} (${originalCase.slippage/100}%)`);
    console.log(`   Gas cost > 0: ${originalCase.gasCost > 0n ? '✅' : '❌'}`);
    
    console.log('\n💡 If all manual checks pass but contract still fails,');
    console.log('   the issue might be in the external DEX calls or gas estimation.');
}

debugValidationDetailed().catch(console.error);
