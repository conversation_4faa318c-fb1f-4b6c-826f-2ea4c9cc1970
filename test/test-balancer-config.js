const { ethers } = require('ethers');

// Load configuration
require('dotenv').config();

/**
 * Test the new configurable Balancer flashloan parameters
 */
async function testBalancerConfig() {
  console.log('🔧 Testing Configurable Balancer Flashloan Parameters');
  console.log('=' .repeat(60));

  try {
    // Import the config after building
    const { config } = require('../dist/config/index.js');
    const { BalancerFlashloanStrategy } = require('../dist/strategies/balancer-flashloan.js');

    console.log('✅ Successfully imported updated config and strategy');

    // 1. Test Configuration Loading
    console.log('\n1. ⚙️  Testing Configuration Loading');
    console.log('-'.repeat(40));

    console.log('   📋 Environment Variables:');
    console.log(`      BALANCER_MIN_PROFIT_THRESHOLD: ${process.env.BALANCER_MIN_PROFIT_THRESHOLD}`);
    console.log(`      BALANCER_MAX_FLASHLOAN_AMOUNT: ${process.env.BALANCER_MAX_FLASHLOAN_AMOUNT}`);

    console.log('\n   📋 Parsed Config Values:');
    console.log(`      balancerMinProfitThreshold: ${config.balancerMinProfitThreshold} ETH`);
    console.log(`      balancerMaxFlashloanAmount: ${config.balancerMaxFlashloanAmount} USDC`);

    // Validate the values are correctly parsed
    const expectedMinProfit = parseFloat(process.env.BALANCER_MIN_PROFIT_THRESHOLD || '0.003');
    const expectedMaxAmount = parseInt(process.env.BALANCER_MAX_FLASHLOAN_AMOUNT || '50000');

    if (config.balancerMinProfitThreshold === expectedMinProfit) {
      console.log('   ✅ Min profit threshold correctly loaded');
    } else {
      console.log(`   ❌ Min profit threshold mismatch: ${config.balancerMinProfitThreshold} vs ${expectedMinProfit}`);
    }

    if (config.balancerMaxFlashloanAmount === expectedMaxAmount) {
      console.log('   ✅ Max flashloan amount correctly loaded');
    } else {
      console.log(`   ❌ Max flashloan amount mismatch: ${config.balancerMaxFlashloanAmount} vs ${expectedMaxAmount}`);
    }

    // 2. Test Strategy Initialization
    console.log('\n2. 🏦 Testing Strategy Initialization');
    console.log('-'.repeat(40));

    const provider = new ethers.JsonRpcProvider(process.env.RPC_URL);
    const balancerStrategy = new BalancerFlashloanStrategy(provider);

    console.log('   ✅ BalancerFlashloanStrategy initialized successfully');
    console.log('   📊 Strategy should use configurable values from .env');

    // 3. Test Different Configuration Scenarios
    console.log('\n3. 🔄 Testing Different Configuration Scenarios');
    console.log('-'.repeat(40));

    const testScenarios = [
      {
        name: 'Conservative (Current)',
        minProfit: '0.003',
        maxAmount: '50000',
        description: 'Lower profit threshold, moderate max amount'
      },
      {
        name: 'Aggressive',
        minProfit: '0.001',
        maxAmount: '100000',
        description: 'Very low profit threshold, high max amount'
      },
      {
        name: 'Conservative High',
        minProfit: '0.01',
        maxAmount: '25000',
        description: 'Higher profit threshold, lower max amount'
      }
    ];

    for (const scenario of testScenarios) {
      console.log(`\n   📊 Scenario: ${scenario.name}`);
      console.log(`      Min Profit: ${scenario.minProfit} ETH`);
      console.log(`      Max Amount: ${scenario.maxAmount} USDC`);
      console.log(`      Description: ${scenario.description}`);
      
      // Calculate implications
      const minProfitEth = parseFloat(scenario.minProfit);
      const maxAmountUsdc = parseInt(scenario.maxAmount);
      
      // Estimate minimum spread needed (assuming 5 ETH flashloan)
      const flashloanAmountEth = 5;
      const minSpreadPercent = (minProfitEth / flashloanAmountEth) * 100;
      
      console.log(`      Implications:`);
      console.log(`        - Min spread needed: ~${minSpreadPercent.toFixed(3)}% (for 5 ETH flashloan)`);
      console.log(`        - Max flashloan: ${maxAmountUsdc.toLocaleString()} USDC`);
      console.log(`        - Risk level: ${minProfitEth < 0.005 ? 'Higher' : 'Lower'} (lower threshold = more opportunities but higher risk)`);
    }

    // 4. Test Real-time Configuration Changes
    console.log('\n4. 🔧 Testing Configuration Flexibility');
    console.log('-'.repeat(40));

    console.log('   💡 Configuration Benefits:');
    console.log('      ✅ Adjustable profit thresholds for market conditions');
    console.log('      ✅ Configurable max amounts based on available capital');
    console.log('      ✅ Easy optimization without code changes');
    console.log('      ✅ Different settings for mainnet vs testnet');

    console.log('\n   📋 Usage Examples:');
    console.log('      • Bull market: Lower thresholds for more opportunities');
    console.log('      • Bear market: Higher thresholds for safer trades');
    console.log('      • High gas: Increase min profit to cover costs');
    console.log('      • Low liquidity: Reduce max amounts');

    // 5. Test Validation Logic
    console.log('\n5. ✅ Testing Validation Logic');
    console.log('-'.repeat(40));

    // Test edge cases
    const edgeCases = [
      { minProfit: 0, maxAmount: 50000, valid: false, reason: 'Zero profit threshold' },
      { minProfit: 0.001, maxAmount: 0, valid: false, reason: 'Zero max amount' },
      { minProfit: 1, maxAmount: 50000, valid: false, reason: 'Unrealistic profit threshold' },
      { minProfit: 0.005, maxAmount: 1000000, valid: false, reason: 'Exceeds Balancer liquidity' },
      { minProfit: 0.003, maxAmount: 50000, valid: true, reason: 'Reasonable values' }
    ];

    for (const testCase of edgeCases) {
      const status = testCase.valid ? '✅ Valid' : '⚠️  Invalid';
      console.log(`   ${status}: Min=${testCase.minProfit} ETH, Max=${testCase.maxAmount} USDC`);
      console.log(`      Reason: ${testCase.reason}`);
    }

    // 6. Test Current Market Suitability
    console.log('\n6. 📈 Testing Current Market Suitability');
    console.log('-'.repeat(40));

    const currentMinProfit = config.balancerMinProfitThreshold;
    const currentMaxAmount = config.balancerMaxFlashloanAmount;

    console.log(`   Current Configuration Analysis:`);
    console.log(`      Min Profit: ${currentMinProfit} ETH`);
    console.log(`      Max Amount: ${currentMaxAmount.toLocaleString()} USDC`);

    // Analyze suitability
    if (currentMinProfit <= 0.005) {
      console.log(`   ✅ Profit threshold suitable for current market efficiency`);
    } else {
      console.log(`   ⚠️  Profit threshold may be too high for current market`);
    }

    if (currentMaxAmount <= 100000) {
      console.log(`   ✅ Max amount within reasonable Balancer liquidity limits`);
    } else {
      console.log(`   ⚠️  Max amount may exceed typical Balancer pool liquidity`);
    }

    // Summary
    console.log('\n' + '='.repeat(60));
    console.log('📋 BALANCER CONFIGURATION TEST SUMMARY');
    console.log('='.repeat(60));
    
    console.log('✅ CONFIGURATION FEATURES IMPLEMENTED:');
    console.log('   • BALANCER_MIN_PROFIT_THRESHOLD configurable via .env');
    console.log('   • BALANCER_MAX_FLASHLOAN_AMOUNT configurable via .env');
    console.log('   • Values properly loaded into config object');
    console.log('   • Strategy uses configurable values instead of hardcoded');
    console.log('   • Logging shows configurable status');
    
    console.log('\n🎯 CURRENT SETTINGS:');
    console.log(`   Min Profit Threshold: ${currentMinProfit} ETH`);
    console.log(`   Max Flashloan Amount: ${currentMaxAmount.toLocaleString()} USDC`);
    
    console.log('\n💡 OPTIMIZATION TIPS:');
    console.log('   • Lower min profit = more opportunities, higher risk');
    console.log('   • Higher max amount = larger potential profits');
    console.log('   • Adjust based on market volatility and gas costs');
    console.log('   • Monitor success rates and adjust accordingly');
    
    console.log('\n🚀 READY FOR CUSTOMIZATION:');
    console.log('   Edit .env file → npm run build → npm run dev');

  } catch (error) {
    console.error('❌ Configuration test failed:', error.message);
    
    if (error.message.includes('Cannot find module')) {
      console.log('\n🔧 TROUBLESHOOTING:');
      console.log('1. Build the project: npm run build');
      console.log('2. Ensure .env file has the new variables');
      console.log('3. Restart the test');
    }
  }
}

// Run test
testBalancerConfig().catch(console.error);
