#!/usr/bin/env node

/**
 * Test SendRawTransaction Implementation
 * Verifies that the new non-blocking transaction execution works correctly
 */

const { ethers } = require("hardhat");
const chalk = require('chalk');

console.log(chalk.blue.bold('\n🧪 Testing SendRawTransaction Implementation\n'));

async function main() {
  try {
    // Get signers
    const [deployer, account1, account2] = await ethers.getSigners();
    const provider = ethers.provider;
    
    console.log(chalk.yellow('📋 Test Configuration:'));
    console.log(`   Network: ${await provider.getNetwork().then(n => n.name)}`);
    console.log(`   Chain ID: ${await provider.getNetwork().then(n => n.chainId)}`);
    console.log(`   Deployer: ${deployer.address}`);
    console.log(`   Balance: ${ethers.formatEther(await provider.getBalance(deployer.address))} ETH`);
    
    // Test 1: Traditional sendTransaction (blocking)
    console.log(chalk.cyan('\n1. 🐌 Testing Traditional sendTransaction (blocking)'));
    console.log('─'.repeat(60));
    
    const startTime1 = Date.now();
    
    const tx1 = await deployer.sendTransaction({
      to: account1.address,
      value: ethers.parseEther("0.01"),
      gasPrice: ethers.parseUnits("20", "gwei")
    });
    
    console.log(`   📤 Transaction sent: ${tx1.hash}`);
    console.log(`   ⏳ Waiting for confirmation...`);
    
    const receipt1 = await tx1.wait();
    const endTime1 = Date.now();
    
    console.log(`   ✅ Transaction confirmed in block ${receipt1.blockNumber}`);
    console.log(`   ⏱️  Total time: ${endTime1 - startTime1}ms`);
    console.log(`   ⛽ Gas used: ${receipt1.gasUsed}`);
    
    // Test 2: sendRawTransaction (non-blocking)
    console.log(chalk.cyan('\n2. 🚀 Testing sendRawTransaction (non-blocking)'));
    console.log('─'.repeat(60));
    
    const startTime2 = Date.now();
    
    // Prepare transaction
    const txRequest = {
      to: account2.address,
      value: ethers.parseEther("0.01"),
      gasPrice: ethers.parseUnits("20", "gwei"),
      gasLimit: 21000,
      nonce: await provider.getTransactionCount(deployer.address)
    };
    
    // Sign transaction
    const signedTx = await deployer.signTransaction(txRequest);
    console.log(`   ✍️  Transaction signed`);
    
    // Send raw transaction
    const txHash2 = await provider.send('eth_sendRawTransaction', [signedTx]);
    const endTime2 = Date.now();
    
    console.log(`   🚀 Raw transaction sent: ${txHash2}`);
    console.log(`   ⚡ Immediate return (non-blocking)`);
    console.log(`   ⏱️  Execution time: ${endTime2 - startTime2}ms`);
    
    // Optional: Monitor transaction status (non-blocking)
    console.log(`   📊 Starting non-blocking monitoring...`);
    
    const monitorStartTime = Date.now();
    let confirmed = false;
    let attempts = 0;
    const maxAttempts = 60; // 5 minutes max
    
    while (!confirmed && attempts < maxAttempts) {
      try {
        const receipt = await provider.getTransactionReceipt(txHash2);
        if (receipt) {
          const monitorEndTime = Date.now();
          console.log(`   ✅ Transaction confirmed in block ${receipt.blockNumber}`);
          console.log(`   ⏱️  Monitoring time: ${monitorEndTime - monitorStartTime}ms`);
          console.log(`   ⛽ Gas used: ${receipt.gasUsed}`);
          confirmed = true;
        } else {
          // Still pending
          if (attempts % 10 === 0) { // Log every 10 attempts (50 seconds)
            console.log(`   ⏳ Still pending... (${attempts * 5}s)`);
          }
        }
      } catch (error) {
        // Transaction might not be found yet
      }
      
      if (!confirmed) {
        await new Promise(resolve => setTimeout(resolve, 5000)); // Wait 5 seconds
        attempts++;
      }
    }
    
    if (!confirmed) {
      console.log(`   ⏰ Monitoring timeout after ${maxAttempts * 5} seconds`);
    }
    
    // Test 3: Performance Comparison
    console.log(chalk.cyan('\n3. 📊 Performance Comparison'));
    console.log('─'.repeat(60));
    
    const traditionalTime = endTime1 - startTime1;
    const rawTransactionTime = endTime2 - startTime2;
    const speedImprovement = Math.round((traditionalTime / rawTransactionTime) * 100) / 100;
    
    console.log(`   Traditional method: ${traditionalTime}ms`);
    console.log(`   Raw transaction:    ${rawTransactionTime}ms`);
    console.log(`   Speed improvement:  ${speedImprovement}x faster`);
    
    if (speedImprovement > 10) {
      console.log(chalk.green(`   🎉 Excellent performance improvement!`));
    } else if (speedImprovement > 5) {
      console.log(chalk.yellow(`   👍 Good performance improvement`));
    } else {
      console.log(chalk.orange(`   ⚠️  Modest improvement (network dependent)`));
    }
    
    // Test 4: MEV Scenario Simulation
    console.log(chalk.cyan('\n4. 🎯 MEV Scenario Simulation'));
    console.log('─'.repeat(60));
    
    console.log(`   Simulating rapid opportunity processing...`);
    
    const opportunities = 5;
    const mevStartTime = Date.now();
    const txHashes = [];
    
    for (let i = 0; i < opportunities; i++) {
      const opportunityStart = Date.now();
      
      const mevTxRequest = {
        to: account1.address,
        value: ethers.parseEther("0.001"),
        gasPrice: ethers.parseUnits("25", "gwei"),
        gasLimit: 21000,
        nonce: await provider.getTransactionCount(deployer.address)
      };
      
      const mevSignedTx = await deployer.signTransaction(mevTxRequest);
      const mevTxHash = await provider.send('eth_sendRawTransaction', [mevSignedTx]);
      
      const opportunityEnd = Date.now();
      txHashes.push(mevTxHash);
      
      console.log(`   📤 Opportunity ${i + 1}: ${mevTxHash.slice(0, 10)}... (${opportunityEnd - opportunityStart}ms)`);
      
      // Small delay to avoid nonce conflicts
      await new Promise(resolve => setTimeout(resolve, 100));
    }
    
    const mevEndTime = Date.now();
    const totalMevTime = mevEndTime - mevStartTime;
    const avgTimePerOpportunity = Math.round(totalMevTime / opportunities);
    
    console.log(`   ✅ Processed ${opportunities} opportunities in ${totalMevTime}ms`);
    console.log(`   📈 Average time per opportunity: ${avgTimePerOpportunity}ms`);
    console.log(`   🚀 Theoretical throughput: ${Math.round(60000 / avgTimePerOpportunity)} opportunities/minute`);
    
    // Test 5: Error Handling
    console.log(chalk.cyan('\n5. 🛡️  Error Handling Test'));
    console.log('─'.repeat(60));
    
    try {
      // Try to send invalid transaction
      const invalidTx = "0xinvalidtransactiondata";
      await provider.send('eth_sendRawTransaction', [invalidTx]);
    } catch (error) {
      console.log(`   ✅ Error handling works: ${error.message.slice(0, 50)}...`);
    }
    
    // Summary
    console.log(chalk.green.bold('\n🎉 SendRawTransaction Test Summary'));
    console.log('═'.repeat(60));
    console.log(`✅ Traditional execution: ${traditionalTime}ms`);
    console.log(`✅ Raw transaction execution: ${rawTransactionTime}ms`);
    console.log(`✅ Performance improvement: ${speedImprovement}x`);
    console.log(`✅ MEV throughput: ${Math.round(60000 / avgTimePerOpportunity)} ops/min`);
    console.log(`✅ Error handling: Working`);
    console.log(`✅ Non-blocking execution: Confirmed`);
    
    console.log(chalk.blue('\n💡 Recommendations:'));
    console.log(`   • Use sendRawTransaction for MEV operations`);
    console.log(`   • Enable transaction monitoring for status tracking`);
    console.log(`   • Set USE_RAW_TRANSACTION_EXECUTION=true in .env`);
    console.log(`   • Monitor gas prices to avoid overpaying`);
    
  } catch (error) {
    console.error(chalk.red(`❌ Test failed: ${error.message}`));
    process.exit(1);
  }
}

// Run the test
main()
  .then(() => {
    console.log(chalk.green('\n✅ All tests completed successfully!'));
    process.exit(0);
  })
  .catch((error) => {
    console.error(chalk.red(`❌ Test suite failed: ${error.message}`));
    process.exit(1);
  });
