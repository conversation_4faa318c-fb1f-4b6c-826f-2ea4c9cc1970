#!/usr/bin/env node

/**
 * Simple arbitrage performance test without dashboard interference
 */

const { ArbitrageStrategy } = require('../dist/strategies/arbitrage');

async function testArbitrageSimple() {
  console.log('🚀 Simple Arbitrage Test\n');

  try {
    // Initialize arbitrage strategy
    const arbitrageStrategy = new ArbitrageStrategy();
    
    // Wait for worker initialization
    console.log('⏳ Initializing workers...');
    await new Promise(resolve => setTimeout(resolve, 3000));

    console.log(`✅ Workers Enabled: ${arbitrageStrategy.isUsingWorkers()}`);
    console.log(`✅ Worker Count: ${arbitrageStrategy.getWorkerStats().length}\n`);

    // Test single scan
    console.log('🔍 Running single arbitrage scan...');
    const startTime = Date.now();
    const opportunities = await arbitrageStrategy.scanForArbitrageOpportunities();
    const scanTime = Date.now() - startTime;

    console.log(`✅ Scan completed in ${scanTime}ms`);
    console.log(`✅ Found ${opportunities.length} opportunities`);
    console.log(`✅ Using workers: ${arbitrageStrategy.isUsingWorkers()}\n`);

    // Test multiple scans
    console.log('🔄 Running 3 consecutive scans...');
    const scanTimes = [];
    
    for (let i = 0; i < 3; i++) {
      const iterStart = Date.now();
      const iterOpportunities = await arbitrageStrategy.scanForArbitrageOpportunities();
      const iterTime = Date.now() - iterStart;
      
      scanTimes.push(iterTime);
      console.log(`   Scan ${i + 1}: ${iterTime}ms (${iterOpportunities.length} opportunities)`);
      
      // Small delay
      await new Promise(resolve => setTimeout(resolve, 500));
    }

    const avgTime = scanTimes.reduce((sum, time) => sum + time, 0) / scanTimes.length;
    console.log(`\n📊 Average scan time: ${avgTime.toFixed(2)}ms`);

    // Worker stats
    if (arbitrageStrategy.isUsingWorkers()) {
      const workerStats = arbitrageStrategy.getWorkerStats();
      console.log('\n👷 Worker Statistics:');
      workerStats.forEach(worker => {
        console.log(`   Worker ${worker.workerId}: ${worker.tasksProcessed} tasks, ${worker.averageProcessingTime.toFixed(2)}ms avg`);
      });
    }

    // Cleanup
    console.log('\n🧹 Cleaning up...');
    await arbitrageStrategy.shutdown();
    
    console.log('✅ Test completed successfully!');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error(error.stack);
    process.exit(1);
  }
}

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log('\n🛑 Test interrupted');
  process.exit(0);
});

// Run the test
if (require.main === module) {
  testArbitrageSimple().catch(console.error);
}

module.exports = { testArbitrageSimple };
