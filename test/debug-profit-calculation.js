const { ethers } = require('ethers');

async function debugProfitCalculation() {
    console.log('🔍 Debugging profit calculation vs actual execution...');
    
    // Decode the failing transaction data
    const txData = "0x9bc62f7a000000000000000000000000c02aaa39b223fe8d0a0e5c4f27ead9083c756cc2000000000000000000000000000000000000000000000001158e460913d00000000000000000000000000000000000000000000000000000000000000000006000000000000000000000000000000000000000000000000000000000000000e0000000000000000000000000c02aaa39b223fe8d0a0e5c4f27ead9083c756cc20000000000000000000000002260fac5e5542a773aa44fbcfedf7c193bc2c599000000000000000000000000e592427a0aece92de3edee1f18e0157c058615640000000000000000000000007a250d5630b4cf539739df2c5dacb4c659f2488d0000000000000000000000000000000000000000000000000000000000000bb800000000000000000000000000000000000000000000000000005af3107a40000000000000000000000000000000000000000000000000000000000000000000";
    
    // Contract interface
    const contractInterface = new ethers.Interface([
        'function executeOptimalFlashloan(address asset, uint256 amount, bytes calldata params) external'
    ]);
    
    // Decode the function call
    const decoded = contractInterface.parseTransaction({ data: txData });
    
    console.log('📋 Transaction Analysis:');
    console.log(`   Function: ${decoded.name}`);
    console.log(`   Asset: ${decoded.args.asset}`);
    console.log(`   Amount: ${ethers.formatEther(decoded.args.amount)} ETH`);
    
    // Decode the arbitrage parameters
    const paramsData = decoded.args.params;
    const arbitrageParams = ethers.AbiCoder.defaultAbiCoder().decode(
        ['address', 'address', 'address', 'address', 'uint24', 'uint256', 'uint8'],
        paramsData
    );
    
    console.log('\n📊 Arbitrage Parameters:');
    console.log(`   tokenA: ${arbitrageParams[0]} (WETH)`);
    console.log(`   tokenB: ${arbitrageParams[1]} (WBTC)`);
    console.log(`   buyDex: ${arbitrageParams[2]} (V3 Router)`);
    console.log(`   sellDex: ${arbitrageParams[3]} (V2 Router)`);
    console.log(`   v3Fee: ${arbitrageParams[4]} (${Number(arbitrageParams[4])/10000}%)`);
    console.log(`   minProfit: ${ethers.formatEther(arbitrageParams[5])} ETH`);
    console.log(`   provider: ${arbitrageParams[6]} (${arbitrageParams[6] === 0n ? 'AAVE' : 'BALANCER'})`);

    console.log('\n🎯 ISSUE IDENTIFIED:');
    console.log('   The bot is calculating profit based on CURRENT prices,');
    console.log('   but by the time the transaction executes, prices have changed!');
    console.log('');
    console.log('   This is a classic MEV timing issue:');
    console.log('   1. Bot detects arbitrage opportunity');
    console.log('   2. Bot calculates profit based on current state');
    console.log('   3. Bot submits transaction');
    console.log('   4. By execution time, prices changed → loss');
    
    console.log('\n💡 SOLUTIONS:');
    console.log('   1. Add slippage protection to profit calculations');
    console.log('   2. Use more conservative profit thresholds');
    console.log('   3. Implement dynamic slippage based on volatility');
    console.log('   4. Add price impact calculations');
    console.log('   5. Use Flashbots for MEV protection');
    
    return {
        asset: decoded.args.asset,
        amount: decoded.args.amount,
        tokenA: arbitrageParams[0],
        tokenB: arbitrageParams[1],
        buyDex: arbitrageParams[2],
        sellDex: arbitrageParams[3],
        v3Fee: arbitrageParams[4],
        minProfit: arbitrageParams[5],
        provider: arbitrageParams[6]
    };
}

debugProfitCalculation().catch(console.error);
