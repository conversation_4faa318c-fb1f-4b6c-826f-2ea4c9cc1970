#!/usr/bin/env node

/**
 * Debug Uniswap V2 Pool Fetching
 * 
 * This test specifically debugs why Uniswap V2 pool data is not being fetched
 */

const { ethers } = require('ethers');

async function debugV2Pool() {
  console.log('🔍 Debugging Uniswap V2 Pool Fetching');
  console.log('=' .repeat(50));

  // Use the same RPC as the bot
  const provider = new ethers.JsonRpcProvider(process.env.RPC_URL || 'http://************:8545');
  
  // Mainnet addresses
  const USDC = '******************************************';
  const WETH = '******************************************';
  const UNISWAP_V2_FACTORY = '******************************************';
  
  console.log(`RPC URL: ${process.env.RPC_URL || 'http://localhost:8545'}`);
  console.log(`USDC: ${USDC}`);
  console.log(`WETH: ${WETH}`);
  console.log(`V2 Factory: ${UNISWAP_V2_FACTORY}`);
  
  try {
    // Test RPC connection
    console.log('\n1. Testing RPC Connection...');
    const blockNumber = await provider.getBlockNumber();
    console.log(`   ✅ Connected! Latest block: ${blockNumber}`);
    
    // Test factory contract
    console.log('\n2. Testing Uniswap V2 Factory...');
    const factoryABI = ['function getPair(address tokenA, address tokenB) view returns (address pair)'];
    const factory = new ethers.Contract(UNISWAP_V2_FACTORY, factoryABI, provider);
    
    // Try to get the pair address
    console.log('   Calling getPair(USDC, WETH)...');
    const pairAddress = await factory.getPair(USDC, WETH);
    console.log(`   Pair address: ${pairAddress}`);
    
    if (pairAddress === ethers.ZeroAddress) {
      console.log('   ❌ No pair found for USDC/WETH on Uniswap V2');
      
      // Try reverse order
      console.log('   Trying reverse order: getPair(WETH, USDC)...');
      const reversePairAddress = await factory.getPair(WETH, USDC);
      console.log(`   Reverse pair address: ${reversePairAddress}`);
      
      if (reversePairAddress === ethers.ZeroAddress) {
        console.log('   ❌ No pair found in either direction');
        
        // Try other popular pairs
        console.log('\n3. Testing Other Popular V2 Pairs...');
        const DAI = '******************************************';
        const USDT = '******************************************';
        
        const pairs = [
          { name: 'WETH/DAI', tokenA: WETH, tokenB: DAI },
          { name: 'USDC/DAI', tokenA: USDC, tokenB: DAI },
          { name: 'USDC/USDT', tokenA: USDC, tokenB: USDT }
        ];
        
        for (const pair of pairs) {
          try {
            const addr = await factory.getPair(pair.tokenA, pair.tokenB);
            console.log(`   ${pair.name}: ${addr === ethers.ZeroAddress ? 'Not found' : addr}`);
          } catch (error) {
            console.log(`   ${pair.name}: Error - ${error.message}`);
          }
        }
      } else {
        console.log('   ✅ Found pair in reverse order!');
        await testPairContract(provider, reversePairAddress);
      }
    } else {
      console.log('   ✅ Found USDC/WETH pair!');
      await testPairContract(provider, pairAddress);
    }
    
  } catch (error) {
    console.error('❌ Error:', error.message);
    
    if (error.message.includes('could not detect network')) {
      console.log('\n💡 Suggestions:');
      console.log('   - Check if your local ETH node is running on port 8545');
      console.log('   - Verify the node is synced to mainnet');
      console.log('   - Try using a different RPC provider (Alchemy, Infura)');
    }
  }
}

async function testPairContract(provider, pairAddress) {
  console.log('\n4. Testing Pair Contract...');
  
  const pairABI = [
    'function getReserves() view returns (uint112 reserve0, uint112 reserve1, uint32 blockTimestampLast)',
    'function token0() view returns (address)',
    'function token1() view returns (address)',
    'function totalSupply() view returns (uint256)'
  ];
  
  try {
    const pair = new ethers.Contract(pairAddress, pairABI, provider);
    
    const [reserves, token0, token1, totalSupply] = await Promise.all([
      pair.getReserves(),
      pair.token0(),
      pair.token1(),
      pair.totalSupply()
    ]);
    
    console.log(`   Token0: ${token0}`);
    console.log(`   Token1: ${token1}`);
    console.log(`   Reserve0: ${ethers.formatUnits(reserves[0], 6)} (assuming USDC)`);
    console.log(`   Reserve1: ${ethers.formatEther(reserves[1])} (assuming WETH)`);
    console.log(`   Total Supply: ${ethers.formatEther(totalSupply)}`);
    
    // Calculate price
    const reserve0 = Number(ethers.formatUnits(reserves[0], 6));
    const reserve1 = Number(ethers.formatEther(reserves[1]));
    const price = reserve0 / reserve1;
    
    console.log(`   Price: 1 WETH = ${price.toFixed(2)} USDC`);
    
    if (reserve0 > 0 && reserve1 > 0) {
      console.log('   ✅ Pool has liquidity and should work!');
    } else {
      console.log('   ❌ Pool has no liquidity');
    }
    
  } catch (error) {
    console.error('   ❌ Error testing pair contract:', error.message);
  }
}

// Alternative test with known working pair
async function testKnownWorkingPair() {
  console.log('\n5. Testing Known Working V2 Pair...');
  
  // WETH/USDT pair (known to exist and have liquidity)
  const WETH_USDT_PAIR = '******************************************';
  
  try {
    const provider = new ethers.JsonRpcProvider(process.env.RPC_URL || 'http://localhost:8545');
    await testPairContract(provider, WETH_USDT_PAIR);
  } catch (error) {
    console.error('   ❌ Error with known pair:', error.message);
  }
}

// Run the debug
if (require.main === module) {
  debugV2Pool()
    .then(() => testKnownWorkingPair())
    .catch(console.error);
}

module.exports = { debugV2Pool };
