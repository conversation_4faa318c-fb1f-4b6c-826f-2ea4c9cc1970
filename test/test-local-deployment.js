const { ethers } = require('ethers');

async function testLocalDeployment() {
    console.log('🧪 Testing contract deployment and execution on local Hardhat fork...');
    
    // Connect to local Hardhat fork
    const provider = new ethers.JsonRpcProvider('http://127.0.0.1:8547');
    
    // Use Hardhat's first account
    const wallet = new ethers.Wallet('0xac0974bec39a17e36ba4a6b4d238ff944bacb478cbed5efcae784d7bf4f2ff80', provider);
    
    console.log(`📋 Test Configuration:`);
    console.log(`   Provider: Local Hardhat fork`);
    console.log(`   Wallet: ${wallet.address}`);
    
    // Check network
    const network = await provider.getNetwork();
    console.log(`   Network: Chain ID ${network.chainId}`);
    
    // Get wallet balance
    const balance = await provider.getBalance(wallet.address);
    console.log(`   Balance: ${ethers.formatEther(balance)} ETH`);
    
    // Deploy the HybridFlashloanArbitrage contract
    console.log('\n🚀 Deploying HybridFlashloanArbitrage contract...');
    
    // Contract addresses for mainnet (since we're forking mainnet)
    const aavePoolAddressesProvider = '******************************************';
    const balancerVault = '******************************************';
    const uniswapV3Router = '******************************************';
    const uniswapV2Router = '******************************************';
    const uniswapV3Factory = '******************************************';
    
    try {
        // Read the contract artifact
        const fs = require('fs');
        const contractArtifact = JSON.parse(fs.readFileSync('./artifacts/contracts/HybridFlashloanArbitrage.sol/HybridFlashloanArbitrage.json', 'utf8'));
        
        const contractFactory = new ethers.ContractFactory(
            contractArtifact.abi,
            contractArtifact.bytecode,
            wallet
        );
        
        const contract = await contractFactory.deploy(
            aavePoolAddressesProvider,
            balancerVault
        );
        
        await contract.waitForDeployment();
        const contractAddress = await contract.getAddress();
        
        console.log(`✅ Contract deployed at: ${contractAddress}`);
        
        // Test the contract with the same parameters that were failing
        console.log('\n🧪 Testing executeOptimalFlashloan...');
        
        const wethAddress = '******************************************';
        const daiAddress = '******************************************';
        const flashloanAmount = ethers.parseEther('0.02');
        
        // Encode arbitrage parameters with the correct struct layout
        const arbitrageParams = ethers.AbiCoder.defaultAbiCoder().encode(
            ['address', 'address', 'address', 'address', 'uint24', 'uint256', 'uint8'],
            [
                wethAddress,      // tokenA
                daiAddress,       // tokenB
                uniswapV2Router,  // buyDex
                uniswapV3Router,  // sellDex
                3000,             // v3Fee
                ethers.parseEther('0.0001'), // minProfit
                0                 // FlashloanProvider.AAVE
            ]
        );
        
        // Check contract owner first
        const owner = await contract.owner();
        console.log(`   Contract owner: ${owner}`);
        console.log(`   Caller: ${wallet.address}`);

        if (owner.toLowerCase() !== wallet.address.toLowerCase()) {
            console.log('   ⚠️  Ownership mismatch! This will cause onlyOwner modifier to fail');
        }

        // Test static call first
        try {
            const result = await contract.executeOptimalFlashloan.staticCall(
                wethAddress,
                flashloanAmount,
                arbitrageParams
            );
            
            console.log('✅ Static call succeeded!');
            console.log('🎉 The contract and parameters are working correctly!');
            
            return { success: true, contractAddress, error: null };
            
        } catch (error) {
            console.log('❌ Static call failed:', error.message);
            
            if (error.message.includes('OVERFLOW(17)')) {
                console.log('   ⚠️  Still getting enum conversion error even with fresh deployment');
                console.log('   🔍 This suggests there might be a bug in the contract code itself');
            } else if (error.message.includes('INSUFFICIENT_OUTPUT_AMOUNT')) {
                console.log('   ✅ Different error! This means the struct encoding is working');
                console.log('   💡 The issue is with trade execution, not parameter encoding');
                return { success: true, contractAddress, error: 'trade_execution' };
            }
            
            return { success: false, contractAddress, error: error.message };
        }
        
    } catch (deployError) {
        console.log('❌ Deployment failed:', deployError.message);
        return { success: false, contractAddress: null, error: deployError.message };
    }
}

async function testWithDifferentParameters() {
    console.log('\n🔬 Testing with different parameter combinations...');
    
    const provider = new ethers.JsonRpcProvider('http://127.0.0.1:8547');
    const wallet = new ethers.Wallet('0xac0974bec39a17e36ba4a6b4d238ff944bacb478cbed5efcae784d7bf4f2ff80', provider);
    
    // Use the existing deployed contract on mainnet for comparison
    const existingContractAddress = '******************************************';
    
    const contract = new ethers.Contract(existingContractAddress, [
        'function executeOptimalFlashloan(address asset, uint256 amount, bytes calldata params) external'
    ], wallet);
    
    const wethAddress = '******************************************';
    const usdcAddress = '******************************************';
    const daiAddress = '******************************************';
    const uniswapV2Router = '******************************************';
    const uniswapV3Router = '******************************************';
    
    const testCases = [
        {
            name: 'WETH → USDC (small amount)',
            tokenA: wethAddress,
            tokenB: usdcAddress,
            amount: ethers.parseEther('0.001'),
            provider: 0 // AAVE
        },
        {
            name: 'WETH → DAI (small amount)',
            tokenA: wethAddress,
            tokenB: daiAddress,
            amount: ethers.parseEther('0.001'),
            provider: 0 // AAVE
        },
        {
            name: 'WETH → USDC (tiny amount)',
            tokenA: wethAddress,
            tokenB: usdcAddress,
            amount: ethers.parseUnits('1', 'wei'),
            provider: 1 // BALANCER
        }
    ];
    
    for (const testCase of testCases) {
        console.log(`\n   Testing: ${testCase.name}`);
        
        const arbitrageParams = ethers.AbiCoder.defaultAbiCoder().encode(
            ['address', 'address', 'address', 'address', 'uint24', 'uint256', 'uint8'],
            [
                testCase.tokenA,
                testCase.tokenB,
                uniswapV2Router,
                uniswapV3Router,
                3000,
                ethers.parseEther('0.0001'),
                testCase.provider
            ]
        );
        
        try {
            await contract.executeOptimalFlashloan.staticCall(
                testCase.tokenA,
                testCase.amount,
                arbitrageParams
            );
            
            console.log(`   ✅ ${testCase.name}: SUCCESS`);
            
        } catch (error) {
            const errorMsg = error.message.split('(')[0];
            console.log(`   ❌ ${testCase.name}: ${errorMsg}`);
            
            if (errorMsg.includes('INSUFFICIENT_OUTPUT_AMOUNT')) {
                console.log(`      ✅ This is expected - the struct encoding is working!`);
            }
        }
    }
}

async function runTests() {
    try {
        const deploymentResult = await testLocalDeployment();
        
        if (deploymentResult.success) {
            console.log('\n✅ Local deployment test passed!');
            
            // Test with the existing contract too
            await testWithDifferentParameters();
        } else {
            console.log('\n❌ Local deployment test failed');
        }
        
        console.log('\n🏁 All tests completed!');
        
    } catch (error) {
        console.error('❌ Test suite failed:', error);
    }
}

runTests();
