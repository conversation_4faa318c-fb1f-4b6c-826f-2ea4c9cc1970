const { ethers } = require('ethers');

async function testMinimalFlashloan() {
    console.log('🔍 Testing minimal flashloan contract...');
    
    // Connect to Hardhat fork
    const provider = new ethers.JsonRpcProvider('http://127.0.0.1:8547');
    const wallet = new ethers.Wallet('0xac0974bec39a17e36ba4a6b4d238ff944bacb478cbed5efcae784d7bf4f2ff80', provider);
    
    console.log(`📋 Test Configuration:`);
    console.log(`   Provider: Hardhat fork (mainnet)`);
    console.log(`   Wallet: ${wallet.address}`);
    
    // Deploy the minimal contract
    console.log('\n🚀 Deploying minimal flashloan contract...');
    
    const fs = require('fs');
    const contractArtifact = JSON.parse(fs.readFileSync('./artifacts/contracts/MinimalFlashloanTest.sol/MinimalFlashloanTest.json', 'utf8'));
    
    const contractFactory = new ethers.ContractFactory(
        contractArtifact.abi,
        contractArtifact.bytecode,
        wallet
    );
    
    const contract = await contractFactory.deploy();
    await contract.waitForDeployment();
    const contractAddress = await contract.getAddress();
    
    console.log(`✅ Minimal contract deployed at: ${contractAddress}`);
    
    // Test the exact same transaction that's failing in the original contract
    console.log('\n🧪 Testing with exact same parameters...');
    
    const wethAddress = '******************************************';
    const usdcAddress = '******************************************';
    const v3Router = '******************************************';
    const v2Router = '******************************************';
    
    const testParams = ethers.AbiCoder.defaultAbiCoder().encode(
        ['address', 'address', 'address', 'address', 'uint24', 'uint256', 'uint8'],
        [wethAddress, usdcAddress, v3Router, v2Router, 3000, ethers.parseEther('0.0001'), 0]
    );
    
    // Test with the exact same amounts that are failing
    const testAmounts = [
        { amount: ethers.parseEther('1'), label: '1 ETH' },
        { amount: ethers.parseEther('0.1'), label: '0.1 ETH' },
        { amount: ethers.parseEther('20'), label: '20 ETH (original failing amount)' }
    ];
    
    for (const test of testAmounts) {
        console.log(`\n   Testing ${test.label}:`);
        
        try {
            await contract.executeOptimalFlashloan.staticCall(
                wethAddress,
                test.amount,
                testParams
            );
            
            console.log(`      ✅ ${test.label}: SUCCESS - No enum conversion error!`);
            
        } catch (error) {
            console.log(`      ❌ ${test.label}: ${error.message.split('(')[0]}`);
            
            if (error.data && error.data.startsWith('0x4e487b71')) {
                const panicCode = parseInt(error.data.slice(10, 74), 16);
                if (panicCode === 17) {
                    console.log(`         🚨 ENUM_CONVERSION_ERROR still present in minimal contract!`);
                    console.log(`         💡 This means the issue is in the basic contract logic, not external calls`);
                } else {
                    console.log(`         📊 Different panic code: ${panicCode}`);
                }
            } else if (error.message.includes('Amount too small')) {
                console.log(`         ✅ Expected minimum amount error - contract logic works!`);
            }
        }
    }
    
    // Test with both providers
    console.log('\n🧪 Testing with different providers...');
    
    const providerTests = [
        { provider: 0, name: 'AAVE' },
        { provider: 1, name: 'BALANCER' }
    ];
    
    for (const providerTest of providerTests) {
        console.log(`\n   Testing ${providerTest.name} provider:`);
        
        const providerParams = ethers.AbiCoder.defaultAbiCoder().encode(
            ['address', 'address', 'address', 'address', 'uint24', 'uint256', 'uint8'],
            [wethAddress, usdcAddress, v3Router, v2Router, 3000, ethers.parseEther('0.0001'), providerTest.provider]
        );
        
        try {
            await contract.executeOptimalFlashloan.staticCall(
                wethAddress,
                ethers.parseEther('1'),
                providerParams
            );
            
            console.log(`      ✅ ${providerTest.name}: SUCCESS`);
            
        } catch (error) {
            console.log(`      ❌ ${providerTest.name}: ${error.message.split('(')[0]}`);
            
            if (error.data && error.data.startsWith('0x4e487b71')) {
                const panicCode = parseInt(error.data.slice(10, 74), 16);
                if (panicCode === 17) {
                    console.log(`         🚨 ENUM_CONVERSION_ERROR with ${providerTest.name}!`);
                }
            }
        }
    }
    
    // Test with amounts below minimum
    console.log('\n🧪 Testing with amounts below minimum...');
    
    const smallAmounts = [
        { amount: ethers.parseUnits('1000', 'wei'), label: '1000 wei' },
        { amount: ethers.parseUnits('1', 'gwei'), label: '1 gwei' }
    ];
    
    for (const test of smallAmounts) {
        console.log(`\n   Testing ${test.label}:`);
        
        try {
            await contract.executeOptimalFlashloan.staticCall(
                wethAddress,
                test.amount,
                testParams
            );
            
            console.log(`      ❌ ${test.label}: Should have failed with minimum amount error`);
            
        } catch (error) {
            console.log(`      ✅ ${test.label}: ${error.message.split('(')[0]}`);
            
            if (error.message.includes('Amount too small')) {
                console.log(`         ✅ Correct minimum amount validation`);
            } else if (error.data && error.data.startsWith('0x4e487b71')) {
                const panicCode = parseInt(error.data.slice(10, 74), 16);
                if (panicCode === 17) {
                    console.log(`         🚨 ENUM_CONVERSION_ERROR even with small amounts!`);
                }
            }
        }
    }
    
    console.log('\n🏁 Minimal flashloan testing completed!');
    
    console.log('\n📊 ANALYSIS:');
    console.log('   If the minimal contract works, the issue is in the external dependencies.');
    console.log('   If the minimal contract fails, the issue is in the basic contract logic.');
}

testMinimalFlashloan().catch(console.error);
