#!/usr/bin/env node

/**
 * Balancer Flashloan Strategy Test
 * 
 * This test specifically validates the Balancer flashloan strategy
 * with real market data and identifies why opportunities aren't being found.
 */

const { ethers } = require('ethers');
const { config } = require('../dist/config');
const { BalancerFlashloanStrategy } = require('../dist/strategies/balancer-flashloan');
const { PoolManager } = require('../dist/dex/pools');
const { logger } = require('../dist/utils/logger');

class BalancerFlashloanTester {
  constructor() {
    this.provider = new ethers.JsonRpcProvider(config.rpcUrl);
    this.strategy = new BalancerFlashloanStrategy(this.provider);
    this.poolManager = new PoolManager();
    
    // Test tokens
    this.tokens = {
      USDC: { address: '******************************************', decimals: 6, symbol: 'USDC' },
      WETH: { address: '******************************************', decimals: 18, symbol: 'WETH' },
      USDT: { address: '******************************************', decimals: 6, symbol: 'USDT' },
      DAI: { address: '******************************************', decimals: 18, symbol: 'DAI' }
    };
  }

  async runTest() {
    console.log('🔵 Balancer Flashloan Strategy Test');
    console.log('=' .repeat(50));
    console.log(`Network: ${config.chainId === 1 ? 'Mainnet' : 'Testnet'}`);
    console.log(`RPC: ${config.rpcUrl}`);
    console.log(`Balancer Contract: ${config.balancerFlashloanContract || 'Not set'}`);
    console.log('=' .repeat(50));

    try {
      // Step 1: Test configuration
      await this.testConfiguration();
      
      // Step 2: Test Balancer liquidity
      await this.testBalancerLiquidity();
      
      // Step 3: Test pool data fetching
      await this.testPoolDataFetching();
      
      // Step 4: Test price calculations
      await this.testPriceCalculations();
      
      // Step 5: Test arbitrage detection
      await this.testArbitrageDetection();
      
      // Step 6: Test opportunity scanning
      await this.testOpportunityScanning();
      
      // Step 7: Create synthetic opportunity
      await this.testSyntheticOpportunity();
      
    } catch (error) {
      console.error('❌ Test failed:', error);
    }
  }

  async testConfiguration() {
    console.log('\n1. ⚙️  Testing Configuration');
    console.log('-'.repeat(30));
    
    console.log(`   Min Profit Threshold: ${config.balancerMinProfitThreshold} ETH`);
    console.log(`   Max Flashloan Amount: ${config.balancerMaxFlashloanAmount} USDC`);
    console.log(`   Min Arbitrage Spread: ${config.minArbitrageSpread}%`);
    
    // Check if thresholds are too high
    const minProfitUsd = config.balancerMinProfitThreshold * 3000; // Assume $3000/ETH
    console.log(`   Min Profit in USD: $${minProfitUsd.toFixed(2)}`);
    
    if (minProfitUsd > 10) {
      console.log('   ⚠️  WARNING: Minimum profit threshold may be too high for finding opportunities');
      console.log('   💡 Consider lowering BALANCER_MIN_PROFIT_THRESHOLD in .env');
    } else {
      console.log('   ✅ Profit threshold looks reasonable');
    }
  }

  async testBalancerLiquidity() {
    console.log('\n2. 🏦 Testing Balancer Liquidity');
    console.log('-'.repeat(30));
    
    try {
      // Test USDC liquidity
      const usdcLiquidity = await this.strategy.checkBalancerLiquidity(this.tokens.USDC);
      console.log(`   USDC Liquidity: ${ethers.formatUnits(usdcLiquidity, 6)} USDC`);
      
      if (usdcLiquidity > ethers.parseUnits('1000', 6)) {
        console.log('   ✅ Sufficient USDC liquidity');
      } else {
        console.log('   ❌ Insufficient USDC liquidity');
      }
      
      // Test WETH liquidity
      const wethLiquidity = await this.strategy.checkBalancerLiquidity(this.tokens.WETH);
      console.log(`   WETH Liquidity: ${ethers.formatEther(wethLiquidity)} WETH`);
      
    } catch (error) {
      console.log(`   ❌ Liquidity check failed: ${error.message}`);
    }
  }

  async testPoolDataFetching() {
    console.log('\n3. 🔍 Testing Pool Data Fetching');
    console.log('-'.repeat(30));
    
    try {
      // Test Uniswap V2 USDC/WETH
      console.log('   Fetching Uniswap V2 USDC/WETH pool...');
      const v2Pool = await this.poolManager.getPool(
        this.tokens.USDC.address,
        this.tokens.WETH.address,
        'uniswap-v2'
      );
      
      if (v2Pool) {
        console.log(`   ✅ V2 Pool found: ${v2Pool.address}`);
        console.log(`   Token0: ${v2Pool.token0.symbol}, Token1: ${v2Pool.token1.symbol}`);

        if (v2Pool.reserves && v2Pool.reserves.reserve0 > 0) {
          const reserve0 = Number(ethers.formatUnits(v2Pool.reserves.reserve0, v2Pool.token0.decimals));
          const reserve1 = Number(ethers.formatUnits(v2Pool.reserves.reserve1, v2Pool.token1.decimals));

          console.log(`   Reserve0: ${reserve0} ${v2Pool.token0.symbol}`);
          console.log(`   Reserve1: ${reserve1} ${v2Pool.token1.symbol}`);

          if (v2Pool.token0.symbol === 'USDC') {
            console.log(`   V2 Price: 1 WETH = ${(reserve0 / reserve1).toFixed(2)} USDC`);
          } else {
            console.log(`   V2 Price: 1 USDC = ${(reserve1 / reserve0).toFixed(6)} WETH`);
          }
        } else {
          console.log('   ❌ V2 Pool has no reserves data');
        }
      } else {
        console.log('   ❌ V2 Pool data not found or invalid');
      }
      
      // Test Uniswap V3 USDC/WETH
      console.log('   Fetching Uniswap V3 USDC/WETH pool...');
      const v3Pool = await this.poolManager.getPool(
        this.tokens.USDC.address,
        this.tokens.WETH.address,
        'uniswap-v3',
        3000
      );
      
      if (v3Pool && v3Pool.liquidity > 0) {
        console.log(`   ✅ V3 Pool: Liquidity ${v3Pool.liquidity}, Fee ${v3Pool.fee}`);
      } else {
        console.log('   ❌ V3 Pool data not found or invalid');
      }
      
    } catch (error) {
      console.log(`   ❌ Pool data fetching failed: ${error.message}`);
    }
  }

  async testPriceCalculations() {
    console.log('\n4. 💰 Testing Price Calculations');
    console.log('-'.repeat(30));
    
    try {
      // Get pools
      const v2Pool = await this.poolManager.getPool(
        this.tokens.USDC.address,
        this.tokens.WETH.address,
        'uniswap-v2'
      );
      
      const v3Pool = await this.poolManager.getPool(
        this.tokens.USDC.address,
        this.tokens.WETH.address,
        'uniswap-v3',
        3000
      );
      
      if (v2Pool && v3Pool) {
        // Calculate prices using the strategy's method
        const v2Price = this.strategy.calculatePoolPrice(v2Pool, this.tokens.USDC, this.tokens.WETH);
        const v3Price = this.strategy.calculatePoolPrice(v3Pool, this.tokens.USDC, this.tokens.WETH);
        
        console.log(`   V2 Price: ${v2Price?.toFixed(6) || 'N/A'}`);
        console.log(`   V3 Price: ${v3Price?.toFixed(6) || 'N/A'}`);
        
        if (v2Price && v3Price) {
          const priceDiff = Math.abs(v2Price - v3Price) / Math.min(v2Price, v3Price);
          console.log(`   Price Difference: ${(priceDiff * 100).toFixed(3)}%`);
          console.log(`   Min Required Spread: ${config.minArbitrageSpread}%`);
          
          if (priceDiff * 100 >= config.minArbitrageSpread) {
            console.log('   ✅ Price difference exceeds minimum spread!');
          } else {
            console.log('   ❌ Price difference below minimum spread');
          }
        } else {
          console.log('   ❌ Could not calculate prices');
        }
      } else {
        console.log('   ❌ Missing pool data for price calculation');
      }
      
    } catch (error) {
      console.log(`   ❌ Price calculation failed: ${error.message}`);
    }
  }

  async testArbitrageDetection() {
    console.log('\n5. ⚡ Testing Arbitrage Detection');
    console.log('-'.repeat(30));
    
    try {
      // Test the findArbitrageOpportunity method directly
      const arbitrageRoute = await this.strategy.findArbitrageOpportunity(
        this.tokens.USDC,
        this.tokens.WETH
      );
      
      if (arbitrageRoute) {
        console.log('   ✅ Arbitrage opportunity found!');
        console.log(`   Expected Profit: ${ethers.formatEther(arbitrageRoute.expectedProfit)} ETH`);
        console.log(`   Confidence: ${arbitrageRoute.confidence}%`);
        console.log(`   Gas Estimate: ${arbitrageRoute.gasEstimate.toString()}`);
      } else {
        console.log('   ❌ No arbitrage opportunity found');
        console.log('   💡 This could be due to:');
        console.log('      - Insufficient price difference');
        console.log('      - High gas costs');
        console.log('      - Missing pool data');
        console.log('      - Profit below threshold');
      }
      
    } catch (error) {
      console.log(`   ❌ Arbitrage detection failed: ${error.message}`);
    }
  }

  async testOpportunityScanning() {
    console.log('\n6. 🔍 Testing Full Opportunity Scanning');
    console.log('-'.repeat(30));
    
    try {
      console.log('   Running scanForBalancerFlashloanOpportunities...');
      const opportunities = await this.strategy.scanForBalancerFlashloanOpportunities();
      
      console.log(`   Found ${opportunities.length} opportunities`);
      
      if (opportunities.length > 0) {
        console.log('   ✅ Opportunities detected!');
        opportunities.forEach((opp, i) => {
          console.log(`   ${i + 1}. Token: ${opp.flashloanToken.symbol}`);
          console.log(`      Amount: ${ethers.formatUnits(opp.flashloanAmount, opp.flashloanToken.decimals)}`);
          console.log(`      Profit: ${ethers.formatEther(opp.expectedProfit)} ETH`);
          console.log(`      Confidence: ${opp.confidence}%`);
        });
      } else {
        console.log('   ❌ No opportunities found');
        console.log('   🔍 Debugging suggestions:');
        console.log('      1. Lower BALANCER_MIN_PROFIT_THRESHOLD');
        console.log('      2. Lower MIN_ARBITRAGE_SPREAD');
        console.log('      3. Check if pools have sufficient liquidity');
        console.log('      4. Verify price calculation logic');
      }
      
    } catch (error) {
      console.log(`   ❌ Opportunity scanning failed: ${error.message}`);
    }
  }

  async testSyntheticOpportunity() {
    console.log('\n7. 🧪 Testing with Synthetic Opportunity');
    console.log('-'.repeat(30));
    
    try {
      // Create a synthetic arbitrage opportunity for testing
      console.log('   Creating synthetic opportunity with guaranteed profit...');
      
      // Mock pools with price difference
      const mockV2Pool = {
        address: '******************************************',
        token0: this.tokens.USDC.address,
        token1: this.tokens.WETH.address,
        reserve0: ethers.parseUnits('1000000', 6), // 1M USDC
        reserve1: ethers.parseEther('300'), // 300 WETH (price: 3333 USDC/WETH)
        protocol: 'uniswap-v2',
        fee: 3000
      };
      
      const mockV3Pool = {
        address: '******************************************',
        token0: this.tokens.USDC.address,
        token1: this.tokens.WETH.address,
        liquidity: '1000000000000000000000',
        sqrtPriceX96: '1000000000000000000000000',
        protocol: 'uniswap-v3',
        fee: 3000
      };
      
      // Calculate prices
      const v2Price = Number(ethers.formatUnits(mockV2Pool.reserve0, 6)) / Number(ethers.formatEther(mockV2Pool.reserve1));
      const v3Price = v2Price * 1.01; // 1% higher price on V3
      
      console.log(`   Mock V2 Price: ${v2Price.toFixed(2)} USDC/WETH`);
      console.log(`   Mock V3 Price: ${v3Price.toFixed(2)} USDC/WETH`);
      console.log(`   Price Difference: ${((v3Price - v2Price) / v2Price * 100).toFixed(2)}%`);
      
      // This should create a profitable opportunity
      const priceDiff = Math.abs(v3Price - v2Price) / Math.min(v2Price, v3Price);
      if (priceDiff > config.minArbitrageSpread / 100) {
        console.log('   ✅ Synthetic opportunity should be profitable');
        console.log('   💡 If real scanning still finds no opportunities, the issue is likely:');
        console.log('      - Real pool data fetching');
        console.log('      - Price calculation implementation');
        console.log('      - Profit calculation logic');
      } else {
        console.log('   ❌ Even synthetic opportunity not profitable with current thresholds');
      }
      
    } catch (error) {
      console.log(`   ❌ Synthetic opportunity test failed: ${error.message}`);
    }
  }
}

// Run test if called directly
if (require.main === module) {
  const tester = new BalancerFlashloanTester();
  tester.runTest().catch(console.error);
}

module.exports = { BalancerFlashloanTester };
