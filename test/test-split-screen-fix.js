#!/usr/bin/env node

const { splitScreenDashboard } = require('./dist/utils/splitScreenDashboard');
const { logger } = require('./dist/utils/logger');
const { ethers } = require('ethers');

console.log('🧪 Testing Split Screen Dashboard Artifact Fixes...\n');

// Set environment variable to enable split screen
process.env.SPLIT_SCREEN_DASHBOARD = 'true';

// Initialize dashboard with test data
const testData = {
  currentBlock: 18500000,
  networkName: 'Sepolia',
  ethBalance: ethers.parseEther('1.5'),
  lastBalanceUpdate: Date.now(),
  isRunning: true,
  uptime: Date.now(),
  lastActivity: Date.now(),
  flashloanEnabled: true,
  mevShareEnabled: false,
  arbitrageEnabled: true,
  totalTransactions: 1250,
  relevantTransactions: 89,
  opportunitiesFound: 23,
  opportunitiesExecuted: 8,
  totalProfit: ethers.parseEther('0.0456'),
  avgGasPrice: ethers.parseUnits('25', 'gwei'),
  configuration: {
    tokenPairs: ['USDC/WETH', 'DAI/USDC', 'WETH/USDT'],
    dexes: ['Uniswap V3', 'Curve', 'Sushiswap'],
    minProfitThreshold: '0.01',
    maxGasPrice: '50'
  },
  successfulTransactions: [
    {
      timestamp: Date.now() - 30000,
      type: 'flashloan',
      profit: ethers.parseEther('0.0123'),
      gasUsed: ethers.parseEther('0.0032'),
      confidence: 95,
      details: 'USDC → WETH arbitrage'
    },
    {
      timestamp: Date.now() - 60000,
      type: 'arbitrage',
      profit: ethers.parseEther('0.0089'),
      gasUsed: ethers.parseEther('0.0028'),
      confidence: 87,
      details: 'DAI → USDC → WETH'
    }
  ],
  errors: 2,
  lastError: 'RPC timeout after 5s'
};

// Start the split screen dashboard
splitScreenDashboard.updateDashboardData(testData);
splitScreenDashboard.start();

console.log('✅ Split screen dashboard started with artifact fixes!');
console.log('📋 Testing improvements:');
console.log('   • Reduced render frequency to prevent artifacts');
console.log('   • Added render throttling and batching');
console.log('   • Improved mouse interaction handling');
console.log('   • Enhanced terminal state management');
console.log('   • Better error handling for screen operations');
console.log('');
console.log('🎮 Controls:');
console.log('   • Press "r" to refresh');
console.log('   • Press "m" to toggle mouse/text mode');
console.log('   • Press "tab" to switch between panels');
console.log('   • Use arrow keys or mouse wheel to scroll');
console.log('   • Press "q", "Escape", or "Ctrl+C" to quit');
console.log('');

// Simulate activity with controlled frequency to test throttling
let logCount = 0;
const simulateActivity = () => {
  const activities = [
    () => {
      logger.info('Mempool monitoring active', { liquidity: '109.601634' });
    },
    () => {
      logger.info('New block detected', { blockNumber: testData.currentBlock + Math.floor(Math.random() * 10) });
    },
    () => {
      logger.warn('High gas price detected', { gasPrice: '45 gwei' });
    },
    () => {
      logger.error('RPC connection timeout', { error: 'Connection timeout after 5s' });
    },
    () => {
      const profit = (Math.random() * 0.05 + 0.001).toFixed(4);
      logger.info('Flashloan executed successfully', { 
        profit: `${profit} ETH`,
        gasUsed: '0.0032 ETH',
        type: 'USDC → WETH'
      });
    },
    () => {
      logger.debug('Gas optimization completed', { 
        oldPrice: '30 gwei',
        newPrice: '25 gwei',
        savings: '16.7%'
      });
    }
  ];

  // Execute random activity
  const activity = activities[Math.floor(Math.random() * activities.length)];
  activity();
  
  logCount++;
  
  // Update dashboard data periodically
  if (logCount % 5 === 0) {
    testData.totalTransactions += Math.floor(Math.random() * 3) + 1;
    testData.relevantTransactions += Math.floor(Math.random() * 2);
    testData.currentBlock += 1;
    testData.lastActivity = Date.now();
    
    splitScreenDashboard.updateDashboardData(testData);
  }
};

// Start simulation with moderate frequency to test throttling
const activityInterval = setInterval(simulateActivity, 800); // Slower than before

// Handle graceful shutdown
process.on('SIGINT', () => {
  clearInterval(activityInterval);
  splitScreenDashboard.stop();
  console.log('\n👋 Split screen dashboard artifact fix test completed!');
  console.log('✅ If you didn\'t see artifacts, the fixes are working properly.');
  process.exit(0);
});

// Keep the process running
process.stdin.resume();
