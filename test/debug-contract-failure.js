const { ethers } = require('ethers');

async function debugContractFailure() {
    console.log('🔍 Debugging contract failure step by step...');
    
    const provider = new ethers.JsonRpcProvider('http://127.0.0.1:8547');
    const wallet = new ethers.Wallet('0xac0974bec39a17e36ba4a6b4d238ff944bacb478cbed5efcae784d7bf4f2ff80', provider);
    
    // Deploy a fresh contract
    console.log('\n🚀 Deploying fresh contract...');
    
    const aavePoolAddressesProvider = '******************************************';
    const balancerVault = '******************************************';
    
    const fs = require('fs');
    const contractArtifact = JSON.parse(fs.readFileSync('./artifacts/contracts/HybridFlashloanArbitrage.sol/HybridFlashloanArbitrage.json', 'utf8'));
    
    const contractFactory = new ethers.ContractFactory(
        contractArtifact.abi,
        contractArtifact.bytecode,
        wallet
    );
    
    const contract = await contractFactory.deploy(
        aavePoolAddressesProvider,
        balancerVault
    );
    
    await contract.waitForDeployment();
    const contractAddress = await contract.getAddress();
    
    console.log(`✅ Contract deployed at: ${contractAddress}`);
    
    // Test contract state
    console.log('\n📋 Checking contract state...');
    
    const owner = await contract.owner();
    const chainId = await contract.CHAIN_ID();
    const v2Router = await contract.UNISWAP_V2_ROUTER();
    const v3Router = await contract.UNISWAP_V3_ROUTER();
    const balancerVaultAddr = await contract.BALANCER_VAULT();
    
    console.log(`   Owner: ${owner}`);
    console.log(`   Chain ID: ${chainId}`);
    console.log(`   V2 Router: ${v2Router}`);
    console.log(`   V3 Router: ${v3Router}`);
    console.log(`   Balancer Vault: ${balancerVaultAddr}`);
    
    // Test with minimal parameters first
    console.log('\n🧪 Testing with minimal parameters...');
    
    const wethAddress = '******************************************';
    const daiAddress = '******************************************';
    
    // Test 1: Empty bytes
    console.log('\n   Test 1: Empty bytes...');
    try {
        await contract.executeOptimalFlashloan.staticCall(
            wethAddress,
            ethers.parseUnits('1', 'wei'),
            '0x'
        );
        console.log('   ✅ Empty bytes worked');
    } catch (error) {
        console.log(`   ❌ Empty bytes failed: ${error.message.split('(')[0]}`);
    }
    
    // Test 2: Just enum value
    console.log('\n   Test 2: Just enum value...');
    try {
        const enumOnly = ethers.AbiCoder.defaultAbiCoder().encode(['uint8'], [0]);
        await contract.executeOptimalFlashloan.staticCall(
            wethAddress,
            ethers.parseUnits('1', 'wei'),
            enumOnly
        );
        console.log('   ✅ Enum only worked');
    } catch (error) {
        console.log(`   ❌ Enum only failed: ${error.message.split('(')[0]}`);
    }
    
    // Test 3: Minimal struct (just tokens)
    console.log('\n   Test 3: Minimal struct (just tokens)...');
    try {
        const minimalStruct = ethers.AbiCoder.defaultAbiCoder().encode(
            ['address', 'address'],
            [wethAddress, daiAddress]
        );
        await contract.executeOptimalFlashloan.staticCall(
            wethAddress,
            ethers.parseUnits('1', 'wei'),
            minimalStruct
        );
        console.log('   ✅ Minimal struct worked');
    } catch (error) {
        console.log(`   ❌ Minimal struct failed: ${error.message.split('(')[0]}`);
    }
    
    // Test 4: Full struct with correct router addresses from contract
    console.log('\n   Test 4: Full struct with contract router addresses...');
    try {
        const fullStruct = ethers.AbiCoder.defaultAbiCoder().encode(
            ['address', 'address', 'address', 'address', 'uint24', 'uint256', 'uint8'],
            [
                wethAddress,  // tokenA
                daiAddress,   // tokenB
                v2Router,     // buyDex (from contract)
                v3Router,     // sellDex (from contract)
                3000,         // v3Fee
                ethers.parseEther('0.0001'), // minProfit
                0             // FlashloanProvider.AAVE
            ]
        );
        await contract.executeOptimalFlashloan.staticCall(
            wethAddress,
            ethers.parseUnits('1', 'wei'),
            fullStruct
        );
        console.log('   ✅ Full struct worked');
    } catch (error) {
        console.log(`   ❌ Full struct failed: ${error.message.split('(')[0]}`);
        
        // Try to get more details about the error
        if (error.data) {
            console.log(`   Error data: ${error.data}`);
        }
    }
    
    // Test 5: Different token pair
    console.log('\n   Test 5: Different token pair (WETH → USDC)...');
    try {
        const usdcAddress = '******************************************';
        const differentPairStruct = ethers.AbiCoder.defaultAbiCoder().encode(
            ['address', 'address', 'address', 'address', 'uint24', 'uint256', 'uint8'],
            [
                wethAddress,  // tokenA
                usdcAddress,  // tokenB (USDC instead of DAI)
                v2Router,     // buyDex
                v3Router,     // sellDex
                3000,         // v3Fee
                ethers.parseEther('0.0001'), // minProfit
                0             // FlashloanProvider.AAVE
            ]
        );
        await contract.executeOptimalFlashloan.staticCall(
            wethAddress,
            ethers.parseUnits('1', 'wei'),
            differentPairStruct
        );
        console.log('   ✅ Different token pair worked');
    } catch (error) {
        console.log(`   ❌ Different token pair failed: ${error.message.split('(')[0]}`);
    }
    
    // Test 6: Try with BALANCER provider
    console.log('\n   Test 6: BALANCER provider...');
    try {
        const balancerStruct = ethers.AbiCoder.defaultAbiCoder().encode(
            ['address', 'address', 'address', 'address', 'uint24', 'uint256', 'uint8'],
            [
                wethAddress,  // tokenA
                daiAddress,   // tokenB
                v2Router,     // buyDex
                v3Router,     // sellDex
                3000,         // v3Fee
                ethers.parseEther('0.0001'), // minProfit
                1             // FlashloanProvider.BALANCER
            ]
        );
        await contract.executeOptimalFlashloan.staticCall(
            wethAddress,
            ethers.parseUnits('1', 'wei'),
            balancerStruct
        );
        console.log('   ✅ BALANCER provider worked');
    } catch (error) {
        console.log(`   ❌ BALANCER provider failed: ${error.message.split('(')[0]}`);
    }
    
    // Test 7: Check if the issue is with the flashloan amount
    console.log('\n   Test 7: Different flashloan amounts...');
    
    const amounts = [
        { amount: ethers.parseUnits('0', 'wei'), label: '0 wei' },
        { amount: ethers.parseUnits('1', 'wei'), label: '1 wei' },
        { amount: ethers.parseEther('0.001'), label: '0.001 ETH' },
        { amount: ethers.parseEther('1'), label: '1 ETH' }
    ];
    
    for (const test of amounts) {
        try {
            const testStruct = ethers.AbiCoder.defaultAbiCoder().encode(
                ['address', 'address', 'address', 'address', 'uint24', 'uint256', 'uint8'],
                [wethAddress, daiAddress, v2Router, v3Router, 3000, ethers.parseEther('0.0001'), 0]
            );
            
            await contract.executeOptimalFlashloan.staticCall(
                wethAddress,
                test.amount,
                testStruct
            );
            
            console.log(`   ✅ ${test.label}: SUCCESS`);
            
        } catch (error) {
            const errorMsg = error.message.split('(')[0];
            console.log(`   ❌ ${test.label}: ${errorMsg}`);
        }
    }
    
    console.log('\n🏁 Debug completed!');
}

debugContractFailure().catch(console.error);
