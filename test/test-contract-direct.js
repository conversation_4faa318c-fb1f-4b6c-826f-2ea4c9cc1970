const { ethers } = require('ethers');

async function testContractDirect() {
    // Connect to your local node
    const provider = new ethers.JsonRpcProvider('http://************:8545');
    
    // Your wallet
    const wallet = new ethers.Wallet('0x1521be785ecf5cb125cc42f1211789ef1a3f7fe4f33460601e56e52f58febd45', provider);
    
    // Contract address
    const hybridContractAddress = '******************************************';
    
    console.log('🧪 Testing contract with minimal parameters...');
    
    // Try to call a simple view function first to make sure the contract is working
    try {
        const contract = new ethers.Contract(hybridContractAddress, [
            'function owner() view returns (address)',
            'function CHAIN_ID() view returns (uint256)'
        ], provider);
        
        const owner = await contract.owner();
        const chainId = await contract.CHAIN_ID();
        
        console.log(`✅ Contract is responsive:`);
        console.log(`   Owner: ${owner}`);
        console.log(`   Chain ID: ${chainId}`);
        
    } catch (error) {
        console.log('❌ Contract is not responsive:', error.message);
        return;
    }
    
    // Now let's try to understand what the executeOptimalFlashloan function expects
    // by trying with minimal valid parameters
    
    const wethAddress = '******************************************';
    const usdcAddress = '******************************************';
    const v2Router = '******************************************';
    const v3Router = '******************************************';
    
    const hybridContractInterface = new ethers.Interface([
        'function executeOptimalFlashloan(address asset, uint256 amount, bytes calldata params) external'
    ]);
    
    // Try with empty bytes first
    console.log('\n🧪 Testing with empty bytes...');
    try {
        const emptyData = hybridContractInterface.encodeFunctionData('executeOptimalFlashloan', [
            wethAddress,
            ethers.parseEther('0.1'),
            '0x'
        ]);
        
        await provider.call({
            to: hybridContractAddress,
            data: emptyData,
            from: wallet.address
        });
        
        console.log('✅ Empty bytes worked (unexpected!)');
    } catch (error) {
        console.log('❌ Empty bytes failed:', error.message.split('(')[0]);
    }
    
    // Try with just the enum value
    console.log('\n🧪 Testing with just enum value...');
    try {
        const enumOnlyParams = ethers.AbiCoder.defaultAbiCoder().encode(['uint8'], [0]);
        const enumOnlyData = hybridContractInterface.encodeFunctionData('executeOptimalFlashloan', [
            wethAddress,
            ethers.parseEther('0.1'),
            enumOnlyParams
        ]);
        
        await provider.call({
            to: hybridContractAddress,
            data: enumOnlyData,
            from: wallet.address
        });
        
        console.log('✅ Enum only worked (unexpected!)');
    } catch (error) {
        console.log('❌ Enum only failed:', error.message.split('(')[0]);
    }
    
    // Try with minimal struct (just addresses)
    console.log('\n🧪 Testing with minimal struct...');
    try {
        const minimalParams = ethers.AbiCoder.defaultAbiCoder().encode(
            ['address', 'address'],
            [wethAddress, usdcAddress]
        );
        const minimalData = hybridContractInterface.encodeFunctionData('executeOptimalFlashloan', [
            wethAddress,
            ethers.parseEther('0.1'),
            minimalParams
        ]);
        
        await provider.call({
            to: hybridContractAddress,
            data: minimalData,
            from: wallet.address
        });
        
        console.log('✅ Minimal struct worked (unexpected!)');
    } catch (error) {
        console.log('❌ Minimal struct failed:', error.message.split('(')[0]);
    }
    
    // Let's try to see if the issue is with the specific values we're using
    console.log('\n🧪 Testing with different token addresses...');
    try {
        // Use DAI instead of USDC
        const daiAddress = '******************************************';
        
        const differentTokenParams = ethers.AbiCoder.defaultAbiCoder().encode(
            ['address', 'address', 'address', 'address', 'uint24', 'uint256', 'uint8'],
            [
                wethAddress, // tokenA
                daiAddress, // tokenB (DAI instead of USDC)
                v2Router, // buyDex
                v3Router, // sellDex
                3000, // v3Fee
                ethers.parseEther('0.0001'), // minProfit
                0 // FlashloanProvider.AAVE
            ]
        );
        
        const differentTokenData = hybridContractInterface.encodeFunctionData('executeOptimalFlashloan', [
            wethAddress,
            ethers.parseEther('0.1'),
            differentTokenParams
        ]);
        
        await provider.call({
            to: hybridContractAddress,
            data: differentTokenData,
            from: wallet.address
        });
        
        console.log('✅ Different token addresses worked!');
    } catch (error) {
        console.log('❌ Different token addresses failed:', error.message.split('(')[0]);
    }
    
    // Let's try with a very small amount
    console.log('\n🧪 Testing with very small amount...');
    try {
        const smallAmountParams = ethers.AbiCoder.defaultAbiCoder().encode(
            ['address', 'address', 'address', 'address', 'uint24', 'uint256', 'uint8'],
            [
                wethAddress, // tokenA
                usdcAddress, // tokenB
                v2Router, // buyDex
                v3Router, // sellDex
                3000, // v3Fee
                ethers.parseEther('0.0001'), // minProfit
                0 // FlashloanProvider.AAVE
            ]
        );
        
        const smallAmountData = hybridContractInterface.encodeFunctionData('executeOptimalFlashloan', [
            wethAddress,
            ethers.parseUnits('1', 'wei'), // 1 wei
            smallAmountParams
        ]);
        
        await provider.call({
            to: hybridContractAddress,
            data: smallAmountData,
            from: wallet.address
        });
        
        console.log('✅ Very small amount worked!');
    } catch (error) {
        console.log('❌ Very small amount failed:', error.message.split('(')[0]);
    }
    
    // The "UniswapV2: INSUFFICIENT_OUTPUT_AMOUNT" error suggests the struct is working!
    // Let's try with a larger amount and better token pair
    console.log('\n🧪 Testing with larger amount and high-liquidity pair...');
    try {
        const largerAmountParams = ethers.AbiCoder.defaultAbiCoder().encode(
            ['address', 'address', 'address', 'address', 'uint24', 'uint256', 'uint8'],
            [
                wethAddress, // tokenA
                usdcAddress, // tokenB
                v2Router, // buyDex
                v3Router, // sellDex
                3000, // v3Fee
                ethers.parseEther('0.001'), // minProfit (higher)
                0 // FlashloanProvider.AAVE
            ]
        );

        const largerAmountData = hybridContractInterface.encodeFunctionData('executeOptimalFlashloan', [
            wethAddress,
            ethers.parseEther('1'), // 1 WETH (larger amount)
            largerAmountParams
        ]);

        await provider.call({
            to: hybridContractAddress,
            data: largerAmountData,
            from: wallet.address
        });

        console.log('✅ Larger amount worked! The flashloan transaction should succeed!');
    } catch (error) {
        console.log('❌ Larger amount failed:', error.message.split('(')[0]);

        // Check if it's still the enum error or a different error
        if (error.message.includes('OVERFLOW(17)')) {
            console.log('   Still getting enum conversion error - there might be a bug in the contract');
        } else {
            console.log('   Different error - the struct encoding is likely working');
        }
    }

    console.log('\n🔍 Analysis complete. The "UniswapV2: INSUFFICIENT_OUTPUT_AMOUNT" error suggests');
    console.log('    that the struct encoding is actually working, and the issue is with trade execution.');
}

// Run the test function
testContractDirect().catch(console.error);
