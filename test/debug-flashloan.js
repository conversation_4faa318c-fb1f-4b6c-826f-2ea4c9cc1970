const { ethers } = require('ethers');

async function debugFlashloanTransaction() {
    // Connect to your local node
    const provider = new ethers.JsonRpcProvider('http://************:8545');
    
    // Your wallet
    const wallet = new ethers.Wallet('0x1521be785ecf5cb125cc42f1211789ef1a3f7fe4f33460601e56e52f58febd45', provider);
    
    // Contract addresses
    const hybridContractAddress = '******************************************';
    const wethAddress = '******************************************';
    const usdcAddress = '******************************************';
    
    // Contract ABI for the hybrid contract
    const hybridContractInterface = new ethers.Interface([
        'function executeOptimalFlashloan(address asset, uint256 amount, bytes calldata params) external'
    ]);
    
    // Test parameters
    const flashloanAmount = ethers.parseEther('0.1'); // 0.1 WETH (smaller amount)
    
    console.log('🔍 Debugging flashloan transaction...');
    console.log(`Contract: ${hybridContractAddress}`);
    console.log(`Wallet: ${wallet.address}`);
    console.log(`Flashloan Amount: ${ethers.formatEther(flashloanAmount)} WETH`);
    
    // Check wallet balance
    const balance = await provider.getBalance(wallet.address);
    console.log(`Wallet Balance: ${ethers.formatEther(balance)} ETH`);
    
    // Check if contract exists
    const code = await provider.getCode(hybridContractAddress);
    console.log(`Contract Code Size: ${code.length} bytes`);
    
    if (code === '0x') {
        console.log('❌ Contract not found!');
        return;
    }
    
    // Get contract router addresses and other info
    try {
        const contractWithInterface = new ethers.Contract(hybridContractAddress, [
            'function owner() view returns (address)',
            'function CHAIN_ID() view returns (uint256)',
            'function UNISWAP_V2_ROUTER() view returns (address)',
            'function UNISWAP_V3_ROUTER() view returns (address)',
            'function BALANCER_VAULT() view returns (address)'
        ], provider);
        
        const owner = await contractWithInterface.owner();
        const chainId = await contractWithInterface.CHAIN_ID();
        const v2Router = await contractWithInterface.UNISWAP_V2_ROUTER();
        const v3Router = await contractWithInterface.UNISWAP_V3_ROUTER();
        const balancerVault = await contractWithInterface.BALANCER_VAULT();
        
        console.log(`\n📋 Contract Information:`);
        console.log(`Contract owner: ${owner}`);
        console.log(`Contract chain ID: ${chainId}`);
        console.log(`V2 Router: ${v2Router}`);
        console.log(`V3 Router: ${v3Router}`);
        console.log(`Balancer Vault: ${balancerVault}`);
        console.log(`Wallet address: ${wallet.address}`);
        
        if (owner.toLowerCase() !== wallet.address.toLowerCase()) {
            console.log('❌ OWNERSHIP ISSUE: Wallet is not the contract owner!');
            console.log('   This is likely the cause of the revert.');
            console.log('   The executeOptimalFlashloan function has onlyOwner modifier.');
            return;
        } else {
            console.log('✅ Wallet is the contract owner');
        }
        
        // Encode arbitrage parameters with the correct router addresses
        // ArbitrageParams struct: tokenA, tokenB, buyDex, sellDex, v3Fee, minProfit, provider
        // enum FlashloanProvider { AAVE, BALANCER } -> AAVE = 0, BALANCER = 1
        const arbitrageParams = ethers.AbiCoder.defaultAbiCoder().encode(
            ['address', 'address', 'address', 'address', 'uint24', 'uint256', 'uint8'],
            [
                wethAddress, // tokenA
                usdcAddress, // tokenB
                v2Router, // buyDex (use actual V2 router from contract)
                v3Router, // sellDex (use actual V3 router from contract)
                3000, // v3Fee (0.3% for most pairs)
                ethers.parseEther('0.0001'), // minProfit
                0 // FlashloanProvider.AAVE
            ]
        );
        
        // Encode the executeOptimalFlashloan function call
        const flashloanData = hybridContractInterface.encodeFunctionData('executeOptimalFlashloan', [
            wethAddress,
            flashloanAmount,
            arbitrageParams
        ]);
        
        // Test the static call with correct parameters
        console.log('\n🧪 Testing static call with correct router addresses...');
        try {
            const result = await provider.call({
                to: hybridContractAddress,
                data: flashloanData,
                from: wallet.address
            });
            console.log('✅ Static call succeeded:', result);
            console.log('🎉 The flashloan transaction should work!');
            return;
        } catch (staticError) {
            console.log('❌ Static call failed:', staticError.message);
            
            // Try to decode the revert reason
            if (staticError.data) {
                console.log('Error data:', staticError.data);
                
                // Common revert signatures
                const revertSignatures = {
                    '0x08c379a0': 'Error(string)', // Standard revert with message
                    '0x4e487b71': 'Panic(uint256)', // Panic errors
                };
                
                const errorSignature = staticError.data.slice(0, 10);
                if (revertSignatures[errorSignature]) {
                    console.log('Revert type:', revertSignatures[errorSignature]);
                    
                    if (errorSignature === '0x08c379a0') {
                        // Decode the error message
                        try {
                            const decoded = ethers.AbiCoder.defaultAbiCoder().decode(['string'], '0x' + staticError.data.slice(10));
                            console.log('Revert message:', decoded[0]);
                        } catch (decodeError) {
                            console.log('Could not decode error message');
                        }
                    } else if (errorSignature === '0x4e487b71') {
                        // Decode panic code
                        try {
                            const decoded = ethers.AbiCoder.defaultAbiCoder().decode(['uint256'], '0x' + staticError.data.slice(10));
                            console.log('Panic code:', decoded[0].toString());
                            
                            // Panic code meanings
                            const panicCodes = {
                                '1': 'ASSERT_FAILED',
                                '17': 'ENUM_CONVERSION_ERROR',
                                '18': 'DIVISION_BY_ZERO',
                                '33': 'ARRAY_ACCESS_OUT_OF_BOUNDS',
                                '34': 'BYTE_ARRAY_ACCESS_OUT_OF_BOUNDS',
                                '49': 'POP_EMPTY_ARRAY',
                                '50': 'ARRAY_INDEX_OUT_OF_BOUNDS',
                                '65': 'MEMORY_ALLOCATION_ERROR',
                                '81': 'ZERO_INITIALIZED_VARIABLE'
                            };
                            
                            const panicCode = decoded[0].toString();
                            if (panicCodes[panicCode]) {
                                console.log(`Panic meaning: ${panicCodes[panicCode]}`);
                                
                                if (panicCode === '17') {
                                    console.log('🔍 ENUM_CONVERSION_ERROR suggests the FlashloanProvider enum value is invalid');
                                    console.log('   Trying with BALANCER (1) instead of AAVE (0)...');
                                    
                                    // Try with BALANCER instead
                                    const balancerParams = ethers.AbiCoder.defaultAbiCoder().encode(
                                        ['address', 'address', 'address', 'address', 'uint24', 'uint256', 'uint8'],
                                        [
                                            wethAddress, // tokenA
                                            usdcAddress, // tokenB
                                            v2Router, // buyDex
                                            v3Router, // sellDex
                                            3000, // v3Fee
                                            ethers.parseEther('0.0001'), // minProfit
                                            1 // FlashloanProvider.BALANCER
                                        ]
                                    );
                                    
                                    const balancerFlashloanData = hybridContractInterface.encodeFunctionData('executeOptimalFlashloan', [
                                        wethAddress,
                                        flashloanAmount,
                                        balancerParams
                                    ]);
                                    
                                    try {
                                        const balancerResult = await provider.call({
                                            to: hybridContractAddress,
                                            data: balancerFlashloanData,
                                            from: wallet.address
                                        });
                                        console.log('✅ Static call with BALANCER succeeded:', balancerResult);
                                        console.log('🎉 The flashloan transaction should work with BALANCER provider!');
                                        return;
                                    } catch (balancerError) {
                                        console.log('❌ Static call with BALANCER also failed:', balancerError.message.split('(')[0]);

                                        // The enum conversion error persists, which suggests the struct layout might be different
                                        console.log('\n🔍 Trying different struct layouts...');

                                        // Maybe the enum is first in the struct?
                                        console.log('   Testing with enum first...');
                                        const enumFirstParams = ethers.AbiCoder.defaultAbiCoder().encode(
                                            ['uint8', 'address', 'address', 'address', 'address', 'uint24', 'uint256'],
                                            [
                                                0, // FlashloanProvider.AAVE first
                                                wethAddress, // tokenA
                                                usdcAddress, // tokenB
                                                v2Router, // buyDex
                                                v3Router, // sellDex
                                                3000, // v3Fee
                                                ethers.parseEther('0.0001') // minProfit
                                            ]
                                        );

                                        const enumFirstData = hybridContractInterface.encodeFunctionData('executeOptimalFlashloan', [
                                            wethAddress,
                                            flashloanAmount,
                                            enumFirstParams
                                        ]);

                                        try {
                                            const enumFirstResult = await provider.call({
                                                to: hybridContractAddress,
                                                data: enumFirstData,
                                                from: wallet.address
                                            });
                                            console.log('✅ Static call with enum first succeeded:', enumFirstResult);
                                            console.log('🎉 Found the correct struct layout!');
                                            return;
                                        } catch (enumFirstError) {
                                            console.log('❌ Enum first layout also failed:', enumFirstError.message.split('(')[0]);
                                        }

                                        // Maybe there are additional fields or different order?
                                        console.log('   The deployed contract might have a different struct definition than the source code.');
                                        console.log('   This suggests the contract was deployed with an older version of the code.');
                                        console.log('   Recommendation: Redeploy the contract or check the actual deployed contract ABI.');
                                    }
                                }
                            }
                        } catch (panicDecodeError) {
                            console.log('Could not decode panic code');
                        }
                    }
                }
            }
        }
        
    } catch (contractError) {
        console.log('❌ Error checking contract state:', contractError.message);
    }
}

// Run the debug function
debugFlashloanTransaction().catch(console.error);
