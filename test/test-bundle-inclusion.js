#!/usr/bin/env node

/**
 * Test Bundle Inclusion Performance
 * Analyzes current gas settings and provides recommendations for better Flashbots inclusion rates
 */

const { ethers } = require("hardhat");
const chalk = require('chalk');

console.log(chalk.blue.bold('\n🧪 Testing Bundle Inclusion Performance\n'));

async function main() {
  try {
    // Import required modules
    const { FlashbotsBundleManager } = require('../dist/flashbots/bundle-provider.js');
    const { AdvancedGasEstimator } = require('../dist/gas/advanced-estimator.js');
    const { config } = require('../dist/config/index.js');
    
    // Initialize provider and components
    const provider = new ethers.JsonRpcProvider(config.rpcUrl);
    const [deployer] = await ethers.getSigners();
    const flashbotsManager = new FlashbotsBundleManager(provider, deployer);
    const gasEstimator = new AdvancedGasEstimator(provider);
    
    console.log(chalk.yellow('📋 Current Configuration Analysis:'));
    console.log(`   Network: ${await provider.getNetwork().then(n => n.name)}`);
    console.log(`   Chain ID: ${config.chainId}`);
    console.log(`   Max Gas Price: ${config.maxGasPriceGwei} gwei`);
    console.log(`   Max Priority Fee: ${config.maxPriorityFeeGwei} gwei`);
    console.log(`   Gas Urgency: ${config.gasUrgency}`);
    console.log(`   Max Gas Cost: ${config.maxGasCostEth} ETH`);
    console.log(`   Min Profit: ${ethers.formatEther(config.minProfitWei)} ETH`);
    
    // Test 1: Network Congestion Analysis
    console.log(chalk.cyan('\n1. 🌐 Network Congestion Analysis'));
    console.log('─'.repeat(60));
    
    try {
      const congestion = await flashbotsManager.getNetworkCongestion();
      console.log(`   Congestion Level: ${congestion.congestionLevel.toUpperCase()}`);
      console.log(`   Gas Usage Ratio: ${(congestion.gasUsageRatio * 100).toFixed(1)}%`);
      console.log(`   Recommended Priority Fee: ${ethers.formatUnits(congestion.recommendedPriorityFee, 'gwei')} gwei`);
      
      // Analyze competitiveness
      const currentMaxPriority = config.maxPriorityFeeGwei;
      const recommendedPriority = Number(ethers.formatUnits(congestion.recommendedPriorityFee, 'gwei'));
      
      if (currentMaxPriority < recommendedPriority) {
        console.log(chalk.red(`   ⚠️  Your max priority fee (${currentMaxPriority} gwei) is below recommended (${recommendedPriority} gwei)`));
        console.log(chalk.yellow(`   💡 Increase MAX_PRIORITY_FEE_GWEI to at least ${Math.ceil(recommendedPriority * 2)} gwei`));
      } else {
        console.log(chalk.green(`   ✅ Your priority fee limit is competitive`));
      }
      
    } catch (error) {
      console.log(`   ❌ Congestion analysis failed: ${error.message}`);
    }
    
    // Test 2: Gas Price Competitiveness
    console.log(chalk.cyan('\n2. 💰 Gas Price Competitiveness Analysis'));
    console.log('─'.repeat(60));
    
    try {
      const currentBlock = await provider.getBlockNumber();
      const block = await provider.getBlock(currentBlock);
      const feeData = await provider.getFeeData();
      
      if (block && feeData) {
        const baseFeeGwei = Number(ethers.formatUnits(feeData.maxFeePerGas || feeData.gasPrice || 0, 'gwei'));
        const currentPriorityGwei = Number(ethers.formatUnits(feeData.maxPriorityFeePerGas || 0, 'gwei'));
        
        console.log(`   Current Base Fee: ${baseFeeGwei.toFixed(2)} gwei`);
        console.log(`   Current Priority Fee: ${currentPriorityGwei.toFixed(2)} gwei`);
        console.log(`   Your Max Gas Price: ${config.maxGasPriceGwei} gwei`);
        console.log(`   Your Max Priority Fee: ${config.maxPriorityFeeGwei} gwei`);
        
        // Calculate competitive thresholds
        const competitiveGasPrice = baseFeeGwei * 2; // 2x base fee for competitiveness
        const competitivePriorityFee = Math.max(currentPriorityGwei * 3, 20); // 3x current or 20 gwei minimum
        
        console.log(`\n   📊 Competitiveness Analysis:`);
        console.log(`   Competitive Gas Price: ${competitiveGasPrice.toFixed(0)} gwei`);
        console.log(`   Competitive Priority Fee: ${competitivePriorityFee.toFixed(0)} gwei`);
        
        // Provide recommendations
        if (config.maxGasPriceGwei < competitiveGasPrice) {
          console.log(chalk.red(`   ❌ Gas price limit too low for competition`));
          console.log(chalk.yellow(`   💡 Increase MAX_GAS_PRICE_GWEI to ${Math.ceil(competitiveGasPrice)} gwei`));
        } else {
          console.log(chalk.green(`   ✅ Gas price limit is competitive`));
        }
        
        if (config.maxPriorityFeeGwei < competitivePriorityFee) {
          console.log(chalk.red(`   ❌ Priority fee limit too low for MEV competition`));
          console.log(chalk.yellow(`   💡 Increase MAX_PRIORITY_FEE_GWEI to ${Math.ceil(competitivePriorityFee)} gwei`));
        } else {
          console.log(chalk.green(`   ✅ Priority fee limit is competitive`));
        }
      }
      
    } catch (error) {
      console.log(`   ❌ Gas analysis failed: ${error.message}`);
    }
    
    // Test 3: Profit Threshold Analysis
    console.log(chalk.cyan('\n3. 🎯 Profit Threshold Analysis'));
    console.log('─'.repeat(60));
    
    const minProfitEth = Number(ethers.formatEther(config.minProfitWei));
    const maxGasCostEth = config.maxGasCostEth;
    const profitToGasRatio = minProfitEth / maxGasCostEth;
    
    console.log(`   Minimum Profit: ${minProfitEth} ETH`);
    console.log(`   Maximum Gas Cost: ${maxGasCostEth} ETH`);
    console.log(`   Profit-to-Gas Ratio: ${profitToGasRatio.toFixed(1)}x`);
    
    if (profitToGasRatio < 2) {
      console.log(chalk.red(`   ⚠️  Low profit margin - gas costs could eat into profits`));
      console.log(chalk.yellow(`   💡 Consider lowering MIN_PROFIT_WEI or increasing MAX_GAS_COST_ETH`));
    } else if (profitToGasRatio > 10) {
      console.log(chalk.yellow(`   ⚠️  Very conservative - might miss profitable opportunities`));
      console.log(chalk.yellow(`   💡 Consider lowering MIN_PROFIT_WEI to capture more opportunities`));
    } else {
      console.log(chalk.green(`   ✅ Profit threshold is well balanced`));
    }
    
    // Test 4: Bundle Simulation
    console.log(chalk.cyan('\n4. 🧪 Bundle Simulation Test'));
    console.log('─'.repeat(60));
    
    try {
      // Create a test transaction for simulation
      const testTx = {
        to: deployer.address,
        value: ethers.parseEther('0.001'),
        gasLimit: 21000,
        maxFeePerGas: ethers.parseUnits('50', 'gwei'),
        maxPriorityFeePerGas: ethers.parseUnits('2', 'gwei'),
        type: 2
      };
      
      const bundleTransaction = {
        signer: deployer,
        transaction: testTx
      };
      
      const targetBlock = await provider.getBlockNumber() + 1;
      
      console.log(`   Simulating bundle for block ${targetBlock}...`);
      
      // Note: This would require actual Flashbots initialization on mainnet
      if (config.chainId === 1) {
        console.log(`   📝 Mainnet detected - bundle simulation would be performed`);
        console.log(`   💡 Run on mainnet to see actual simulation results`);
      } else {
        console.log(`   📝 Testnet detected - Flashbots simulation not available`);
        console.log(`   💡 Deploy to mainnet for full Flashbots testing`);
      }
      
    } catch (error) {
      console.log(`   ❌ Bundle simulation failed: ${error.message}`);
    }
    
    // Test 5: Recommendations Summary
    console.log(chalk.cyan('\n5. 📋 Optimization Recommendations'));
    console.log('─'.repeat(60));
    
    const recommendations = [];
    
    // Gas price recommendations
    if (config.maxGasPriceGwei < 200) {
      recommendations.push(`Increase MAX_GAS_PRICE_GWEI to 200-300 gwei for MEV competitiveness`);
    }
    
    if (config.maxPriorityFeeGwei < 20) {
      recommendations.push(`Increase MAX_PRIORITY_FEE_GWEI to 50-100 gwei for bundle inclusion`);
    }
    
    if (config.gasUrgency !== 'instant') {
      recommendations.push(`Set GAS_URGENCY=instant for maximum priority`);
    }
    
    if (minProfitEth > 0.05) {
      recommendations.push(`Lower MIN_PROFIT_WEI to 0.01-0.02 ETH to catch more opportunities`);
    }
    
    if (config.maxGasCostEth < 0.05) {
      recommendations.push(`Increase MAX_GAS_COST_ETH to 0.05-0.1 ETH for profitable opportunities`);
    }
    
    if (!config.pureProfitMode) {
      recommendations.push(`Enable PURE_PROFIT_MODE=true to execute all profitable opportunities`);
    }
    
    if (recommendations.length === 0) {
      console.log(chalk.green(`   ✅ Your configuration looks competitive!`));
      console.log(`   💡 Monitor bundle inclusion rates and adjust as needed`);
    } else {
      console.log(chalk.yellow(`   📝 Recommended optimizations:`));
      recommendations.forEach((rec, index) => {
        console.log(`   ${index + 1}. ${rec}`);
      });
    }
    
    // Summary
    console.log(chalk.green.bold('\n🎉 Bundle Inclusion Analysis Complete'));
    console.log('═'.repeat(60));
    console.log(`✅ Network congestion analyzed`);
    console.log(`✅ Gas price competitiveness evaluated`);
    console.log(`✅ Profit thresholds reviewed`);
    console.log(`✅ Optimization recommendations provided`);
    
    console.log(chalk.blue('\n💡 Next Steps:'));
    console.log(`   1. Update .env file with recommended gas settings`);
    console.log(`   2. Test on testnet first with new settings`);
    console.log(`   3. Monitor bundle inclusion rates on mainnet`);
    console.log(`   4. Adjust parameters based on performance`);
    console.log(`   5. Track profit vs gas cost ratios`);
    
    console.log(chalk.yellow('\n⚠️  Important Notes:'));
    console.log(`   • Higher gas limits increase costs but improve inclusion rates`);
    console.log(`   • Start conservative and increase limits gradually`);
    console.log(`   • Monitor profitability closely with new settings`);
    console.log(`   • Use emergency stop if losses exceed expectations`);
    
  } catch (error) {
    console.error(chalk.red(`❌ Test failed: ${error.message}`));
    console.error(error.stack);
    process.exit(1);
  }
}

// Run the test
main()
  .then(() => {
    console.log(chalk.green('\n✅ Bundle inclusion analysis completed successfully!'));
    process.exit(0);
  })
  .catch((error) => {
    console.error(chalk.red(`❌ Analysis failed: ${error.message}`));
    process.exit(1);
  });
