#!/usr/bin/env ts-node

import { ethers } from 'ethers';
import { bundleAnalyzer } from '../src/utils/bundle-analyzer';
import { logger } from '../src/utils/logger';

/**
 * Test Bundle Analyzer Functionality
 * 
 * This script tests the bundle analyzer by simulating some bundle submissions
 * and verifying that statistics are calculated correctly.
 */

function main() {
    try {
        logger.system('🧪 TESTING BUNDLE ANALYZER');
        logger.system('==========================');

        // Simulate some bundle submissions
        logger.system('Simulating bundle submissions...');

        // Simulate 10 bundle submissions with varying results
        const testData = [
            { included: true, priorityFee: '25', baseFee: '20' },
            { included: false, priorityFee: '15', baseFee: '22', error: 'Block passed without inclusion' },
            { included: true, priorityFee: '30', baseFee: '18' },
            { included: false, priorityFee: '20', baseFee: '25', error: 'Block passed without inclusion' },
            { included: true, priorityFee: '35', baseFee: '19' },
            { included: true, priorityFee: '28', baseFee: '21' },
            { included: false, priorityFee: '18', baseFee: '24', error: 'Account nonce too high' },
            { included: true, priorityFee: '32', baseFee: '20' },
            { included: false, priorityFee: '22', baseFee: '23', error: 'Block passed without inclusion' },
            { included: true, priorityFee: '40', baseFee: '17' }
        ];

        testData.forEach((data, index) => {
            const targetBlock = 1000000 + index;
            const priorityFee = ethers.parseUnits(data.priorityFee, 'gwei');
            const baseFee = ethers.parseUnits(data.baseFee, 'gwei');
            
            bundleAnalyzer.recordBundleSubmission(
                targetBlock,
                data.included,
                priorityFee,
                baseFee,
                data.error
            );
            
            logger.debug(`Recorded: Block ${targetBlock}, Included: ${data.included}, Fee: ${data.priorityFee} gwei`);
        });

        logger.system('✅ Test data recorded');
        logger.system('');

        // Test statistics calculation
        logger.system('📊 CALCULATED STATISTICS');
        logger.system('========================');

        const stats = bundleAnalyzer.getInclusionStats();
        
        logger.system(`Total Submissions: ${stats.totalSubmissions}`);
        logger.system(`Successful Inclusions: ${stats.successfulInclusions}`);
        logger.system(`Inclusion Rate: ${stats.inclusionRate.toFixed(1)}%`);
        logger.system(`Average Priority Fee: ${stats.averagePriorityFee} gwei`);
        logger.system(`Average Base Fee: ${stats.averageBaseFee} gwei`);
        logger.system('');

        // Verify calculations
        const expectedSubmissions = testData.length;
        const expectedInclusions = testData.filter(d => d.included).length;
        const expectedRate = (expectedInclusions / expectedSubmissions) * 100;

        logger.system('🔍 VERIFICATION');
        logger.system('===============');
        logger.system(`Expected submissions: ${expectedSubmissions}, Got: ${stats.totalSubmissions}`);
        logger.system(`Expected inclusions: ${expectedInclusions}, Got: ${stats.successfulInclusions}`);
        logger.system(`Expected rate: ${expectedRate.toFixed(1)}%, Got: ${stats.inclusionRate.toFixed(1)}%`);

        const isCorrect = stats.totalSubmissions === expectedSubmissions &&
                         stats.successfulInclusions === expectedInclusions &&
                         Math.abs(stats.inclusionRate - expectedRate) < 0.1;

        if (isCorrect) {
            logger.system('✅ All calculations are correct!');
        } else {
            logger.system('❌ Calculation mismatch detected!');
        }
        logger.system('');

        // Test failure analysis
        logger.system('🔍 FAILURE ANALYSIS');
        logger.system('===================');
        
        const failures = bundleAnalyzer.analyzeRecentFailures();
        logger.system(`Recent failures: ${failures.recentFailures}`);
        logger.system('Common errors:');
        failures.commonErrors.forEach(error => {
            logger.system(`  • ${error}`);
        });
        logger.system('');

        // Test recommendations
        logger.system('💡 RECOMMENDATIONS');
        logger.system('==================');
        
        const feeRec = bundleAnalyzer.getOptimalPriorityFeeRecommendation();
        logger.system(`Recommended min priority fee: ${feeRec.recommendedMinGwei} gwei`);
        logger.system(`Recommended max priority fee: ${feeRec.recommendedMaxGwei} gwei`);
        logger.system(`Reasoning: ${feeRec.reasoning}`);
        logger.system('');

        // Test full report
        logger.system('📋 FULL ANALYSIS REPORT');
        logger.system('=======================');
        bundleAnalyzer.printAnalysisReport();

        logger.system('🎉 Bundle analyzer test completed successfully!');

    } catch (error) {
        logger.error('Bundle analyzer test failed:', error);
        process.exit(1);
    }
}

// Run the test
if (require.main === module) {
    main();
}

export { main as testBundleAnalyzer };
