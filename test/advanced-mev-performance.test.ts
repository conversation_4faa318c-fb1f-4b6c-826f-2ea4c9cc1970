import { expect } from 'chai';
import { ethers } from 'hardhat';
import { FlashbotsExecutor } from '../src/execution/flashbots-executor';
import { FlashbotsBundleManager } from '../src/flashbots/bundle-provider';
import { AdvancedGasEstimator } from '../src/gas/advanced-estimator';
import { GasOptimizer } from '../src/gas/optimizer';
import { config } from '../src/config';

describe('Advanced MEV Performance Tests', function() {
    let flashbotsExecutor: FlashbotsExecutor;
    let flashbotsManager: FlashbotsBundleManager;
    let gasEstimator: AdvancedGasEstimator;
    let provider: ethers.JsonRpcProvider;
    let wallet: ethers.Wallet;

    before(async function() {
        [wallet] = await ethers.getSigners();
        provider = ethers.provider as ethers.JsonRpcProvider;
        
        flashbotsManager = new FlashbotsBundleManager(provider, wallet);
        gasEstimator = new AdvancedGasEstimator(provider);
        const gasOptimizer = new GasOptimizer();
        
        flashbotsExecutor = new FlashbotsExecutor(
            provider,
            wallet,
            flashbotsManager,
            gasEstimator,
            gasOptimizer
        );
    });

    describe('Gas Pricing Performance', function() {
        it('should calculate gas prices quickly with dynamic pricing', async function() {
            const iterations = 100;
            const startTime = Date.now();

            for (let i = 0; i < iterations; i++) {
                await gasEstimator.getOptimalGasPrice('fast', i % 3);
            }

            const endTime = Date.now();
            const avgTime = (endTime - startTime) / iterations;

            expect(avgTime).to.be.lessThan(100); // Should be under 100ms per calculation

            console.log(`   Gas pricing performance:`);
            console.log(`   ${iterations} calculations in ${endTime - startTime}ms`);
            console.log(`   Average: ${avgTime.toFixed(2)}ms per calculation`);
        });

        it('should handle concurrent gas price calculations', async function() {
            const concurrentRequests = 20;
            const startTime = Date.now();

            const promises = Array.from({ length: concurrentRequests }, (_, i) =>
                gasEstimator.getOptimalGasPrice('instant', i % 3)
            );

            const results = await Promise.all(promises);
            const endTime = Date.now();

            expect(results).to.have.length(concurrentRequests);
            results.forEach(result => {
                expect(result.gasPrice).to.be.greaterThan(0);
            });

            console.log(`   Concurrent gas pricing:`);
            console.log(`   ${concurrentRequests} concurrent requests in ${endTime - startTime}ms`);
            console.log(`   Average: ${((endTime - startTime) / concurrentRequests).toFixed(2)}ms per request`);
        });
    });

    describe('Block Timing Performance', function() {
        it('should calculate optimal target blocks quickly', async function() {
            const iterations = 50;
            const startTime = Date.now();

            for (let i = 0; i < iterations; i++) {
                await (flashbotsExecutor as any).calculateOptimalTargetBlock();
            }

            const endTime = Date.now();
            const avgTime = (endTime - startTime) / iterations;

            expect(avgTime).to.be.lessThan(200); // Should be under 200ms per calculation

            console.log(`   Block timing performance:`);
            console.log(`   ${iterations} calculations in ${endTime - startTime}ms`);
            console.log(`   Average: ${avgTime.toFixed(2)}ms per calculation`);
        });

        it('should handle rapid block timing requests', async function() {
            const rapidRequests = 10;
            const startTime = Date.now();

            const promises = Array.from({ length: rapidRequests }, () =>
                (flashbotsExecutor as any).calculateOptimalTargetBlock()
            );

            const results = await Promise.all(promises);
            const endTime = Date.now();

            expect(results).to.have.length(rapidRequests);
            const currentBlock = await provider.getBlockNumber();
            results.forEach(targetBlock => {
                expect(targetBlock).to.be.greaterThan(currentBlock);
            });

            console.log(`   Rapid block timing:`);
            console.log(`   ${rapidRequests} rapid requests in ${endTime - startTime}ms`);
        });
    });

    describe('Bundle Priority Fee Performance', function() {
        it('should calculate advanced priority fees efficiently', async function() {
            const currentBlock = await provider.getBlockNumber();
            const targetBlock = currentBlock + 1;
            const iterations = 50;
            const startTime = Date.now();

            for (let i = 0; i < iterations; i++) {
                await flashbotsManager.calculateAdvancedBundlePriorityFee(
                    targetBlock,
                    i % 2 === 0, // Alternate high priority
                    i % 3        // Vary retry count
                );
            }

            const endTime = Date.now();
            const avgTime = (endTime - startTime) / iterations;

            expect(avgTime).to.be.lessThan(150); // Should be under 150ms per calculation

            console.log(`   Priority fee performance:`);
            console.log(`   ${iterations} calculations in ${endTime - startTime}ms`);
            console.log(`   Average: ${avgTime.toFixed(2)}ms per calculation`);
        });

        it('should scale well with different strategies', async function() {
            const currentBlock = await provider.getBlockNumber();
            const targetBlock = currentBlock + 1;
            const strategies = ['conservative', 'balanced', 'aggressive'] as const;
            const originalStrategy = config.bundleSubmissionStrategy;

            try {
                const results: { [key: string]: number } = {};

                for (const strategy of strategies) {
                    (config as any).bundleSubmissionStrategy = strategy;
                    const startTime = Date.now();

                    for (let i = 0; i < 20; i++) {
                        await flashbotsManager.calculateAdvancedBundlePriorityFee(targetBlock, false, 0);
                    }

                    const endTime = Date.now();
                    results[strategy] = (endTime - startTime) / 20;
                }

                // All strategies should perform similarly
                Object.values(results).forEach(avgTime => {
                    expect(avgTime).to.be.lessThan(100);
                });

                console.log(`   Strategy performance comparison:`);
                strategies.forEach(strategy => {
                    console.log(`   ${strategy}: ${results[strategy].toFixed(2)}ms avg`);
                });

            } finally {
                (config as any).bundleSubmissionStrategy = originalStrategy;
            }
        });
    });

    describe('Memory and Resource Usage', function() {
        it('should not leak memory during repeated calculations', async function() {
            const initialMemory = process.memoryUsage().heapUsed;
            const iterations = 200;

            // Perform many calculations
            for (let i = 0; i < iterations; i++) {
                await gasEstimator.getOptimalGasPrice('fast', i % 3);
                await (flashbotsExecutor as any).calculateOptimalTargetBlock();
                
                // Force garbage collection periodically
                if (i % 50 === 0 && global.gc) {
                    global.gc();
                }
            }

            const finalMemory = process.memoryUsage().heapUsed;
            const memoryIncrease = finalMemory - initialMemory;
            const memoryIncreasePercent = (memoryIncrease / initialMemory) * 100;

            // Memory increase should be reasonable (less than 50% increase)
            expect(memoryIncreasePercent).to.be.lessThan(50);

            console.log(`   Memory usage test:`);
            console.log(`   Initial: ${(initialMemory / 1024 / 1024).toFixed(2)} MB`);
            console.log(`   Final: ${(finalMemory / 1024 / 1024).toFixed(2)} MB`);
            console.log(`   Increase: ${memoryIncreasePercent.toFixed(2)}%`);
        });

        it('should handle high-frequency requests without degradation', async function() {
            const batchSize = 10;
            const batches = 5;
            const timings: number[] = [];

            for (let batch = 0; batch < batches; batch++) {
                const startTime = Date.now();

                const promises = Array.from({ length: batchSize }, (_, i) =>
                    gasEstimator.getOptimalGasPrice('instant', i % 3)
                );

                await Promise.all(promises);
                const endTime = Date.now();
                timings.push(endTime - startTime);
            }

            // Performance should not degrade significantly across batches
            const firstBatchTime = timings[0];
            const lastBatchTime = timings[timings.length - 1];
            const degradation = (lastBatchTime - firstBatchTime) / firstBatchTime;

            expect(degradation).to.be.lessThan(0.5); // Less than 50% degradation

            console.log(`   High-frequency performance:`);
            timings.forEach((time, i) => {
                console.log(`   Batch ${i + 1}: ${time}ms`);
            });
            console.log(`   Performance degradation: ${(degradation * 100).toFixed(2)}%`);
        });
    });

    describe('Error Handling Performance', function() {
        it('should handle errors gracefully without performance impact', async function() {
            const iterations = 30;
            const startTime = Date.now();

            for (let i = 0; i < iterations; i++) {
                try {
                    // Test with invalid block numbers to trigger error paths
                    await flashbotsManager.calculateAdvancedBundlePriorityFee(-1, false, 0);
                } catch (error) {
                    // Expected to fail, but should fail quickly
                }
            }

            const endTime = Date.now();
            const avgTime = (endTime - startTime) / iterations;

            expect(avgTime).to.be.lessThan(50); // Error handling should be fast

            console.log(`   Error handling performance:`);
            console.log(`   ${iterations} error cases in ${endTime - startTime}ms`);
            console.log(`   Average: ${avgTime.toFixed(2)}ms per error`);
        });

        it('should recover quickly from network errors', async function() {
            // Simulate network issues by using invalid provider temporarily
            const originalProvider = (gasEstimator as any).provider;
            const invalidProvider = new ethers.JsonRpcProvider('http://invalid-url:8545');
            
            try {
                (gasEstimator as any).provider = invalidProvider;
                
                const startTime = Date.now();
                
                // This should fail quickly due to network error
                try {
                    await gasEstimator.getOptimalGasPrice('fast', 0);
                } catch (error) {
                    // Expected to fail
                }
                
                const errorTime = Date.now() - startTime;
                expect(errorTime).to.be.lessThan(5000); // Should timeout quickly
                
                // Restore valid provider
                (gasEstimator as any).provider = originalProvider;
                
                // Should work normally again
                const recoveryStart = Date.now();
                const result = await gasEstimator.getOptimalGasPrice('fast', 0);
                const recoveryTime = Date.now() - recoveryStart;
                
                expect(result.gasPrice).to.be.greaterThan(0);
                expect(recoveryTime).to.be.lessThan(1000); // Should recover quickly
                
                console.log(`   Network error recovery:`);
                console.log(`   Error timeout: ${errorTime}ms`);
                console.log(`   Recovery time: ${recoveryTime}ms`);
                
            } finally {
                (gasEstimator as any).provider = originalProvider;
            }
        });
    });

    describe('Scalability Tests', function() {
        it('should handle multiple simultaneous optimization requests', async function() {
            const concurrentUsers = 5;
            const requestsPerUser = 10;
            const startTime = Date.now();

            const userPromises = Array.from({ length: concurrentUsers }, async (_, userId) => {
                const userRequests = Array.from({ length: requestsPerUser }, async (_, requestId) => {
                    const retryCount = (userId + requestId) % 3;
                    return await gasEstimator.getOptimalGasPrice('fast', retryCount);
                });
                return Promise.all(userRequests);
            });

            const results = await Promise.all(userPromises);
            const endTime = Date.now();

            expect(results).to.have.length(concurrentUsers);
            results.forEach(userResults => {
                expect(userResults).to.have.length(requestsPerUser);
                userResults.forEach(result => {
                    expect(result.gasPrice).to.be.greaterThan(0);
                });
            });

            const totalRequests = concurrentUsers * requestsPerUser;
            const avgTimePerRequest = (endTime - startTime) / totalRequests;

            console.log(`   Scalability test:`);
            console.log(`   ${concurrentUsers} users × ${requestsPerUser} requests = ${totalRequests} total`);
            console.log(`   Total time: ${endTime - startTime}ms`);
            console.log(`   Average per request: ${avgTimePerRequest.toFixed(2)}ms`);
        });
    });
});
