#!/usr/bin/env node

/**
 * Real Opportunity Detection Test
 * 
 * This test creates real-world scenarios and validates that the MEV bot
 * can detect and execute profitable opportunities on mainnet.
 */

const { ethers } = require('ethers');
const { config } = require('../dist/config');
const { BalancerFlashloanStrategy } = require('../dist/strategies/balancer-flashloan');
const { FlashloanStrategy } = require('../dist/strategies/flashloan');
const { DynamicFlashloanStrategy } = require('../dist/strategies/dynamic-flashloan');
const { ArbitrageStrategy } = require('../dist/strategies/arbitrage');
const { PoolManager } = require('../dist/dex/pools');
const { logger } = require('../dist/utils/logger');

// Test configuration
const TEST_CONFIG = {
  // Use real mainnet data
  RPC_URL: process.env.RPC_URL || 'http://localhost:8545',
  CHAIN_ID: 1, // Mainnet
  
  // Test tokens (mainnet addresses)
  TOKENS: {
    USDC: { address: '******************************************', decimals: 6, symbol: 'USDC' },
    WETH: { address: '******************************************', decimals: 18, symbol: 'WETH' },
    USDT: { address: '******************************************', decimals: 6, symbol: 'USDT' },
    DAI: { address: '******************************************', decimals: 18, symbol: 'DAI' }
  },
  
  // Real DEX addresses
  UNISWAP_V2_FACTORY: '******************************************',
  UNISWAP_V3_FACTORY: '******************************************',
  BALANCER_VAULT: '******************************************',
  
  // Test parameters
  MIN_PROFIT_ETH: 0.001, // $3 at $3000/ETH
  MAX_FLASHLOAN_USDC: 50000,
  TEST_TIMEOUT: 60000 // 60 seconds
};

class RealOpportunityTester {
  constructor() {
    this.provider = new ethers.JsonRpcProvider(TEST_CONFIG.RPC_URL);
    this.results = {
      totalTests: 0,
      passedTests: 0,
      failedTests: 0,
      opportunities: [],
      errors: []
    };
  }

  async runAllTests() {
    console.log('🧪 Starting Real Opportunity Detection Tests');
    console.log('=' .repeat(60));
    console.log(`Network: Mainnet (Chain ID: ${TEST_CONFIG.CHAIN_ID})`);
    console.log(`RPC: ${TEST_CONFIG.RPC_URL}`);
    console.log(`Min Profit: ${TEST_CONFIG.MIN_PROFIT_ETH} ETH`);
    console.log('=' .repeat(60));

    try {
      // Test 1: Real Pool Data Fetching
      await this.testRealPoolDataFetching();
      
      // Test 2: Real Price Discovery
      await this.testRealPriceDiscovery();
      
      // Test 3: Balancer Liquidity Check
      await this.testBalancerLiquidityCheck();
      
      // Test 4: Real Arbitrage Detection
      await this.testRealArbitrageDetection();
      
      // Test 5: Flashloan Opportunity Scanning
      await this.testFlashloanOpportunityScanning();
      
      // Test 6: Dynamic Strategy Selection
      await this.testDynamicStrategySelection();
      
      // Test 7: Profit Threshold Validation
      await this.testProfitThresholdValidation();
      
      // Test 8: Gas Cost Estimation
      await this.testGasCostEstimation();

    } catch (error) {
      console.error('❌ Test suite failed:', error);
      this.results.errors.push(error.message);
    }

    this.printResults();
  }

  async testRealPoolDataFetching() {
    console.log('\n1. 🔍 Testing Real Pool Data Fetching');
    console.log('-'.repeat(40));
    
    try {
      const poolManager = new PoolManager();
      
      // Test Uniswap V2 USDC/WETH pool
      console.log('   Testing Uniswap V2 USDC/WETH pool...');
      const v2Pool = await poolManager.getPool(
        TEST_CONFIG.TOKENS.USDC.address,
        TEST_CONFIG.TOKENS.WETH.address,
        'uniswap-v2'
      );
      
      if (v2Pool && v2Pool.reserve0 > 0 && v2Pool.reserve1 > 0) {
        console.log(`   ✅ V2 Pool found: ${ethers.formatUnits(v2Pool.reserve0, 6)} USDC, ${ethers.formatEther(v2Pool.reserve1)} WETH`);
        this.recordSuccess('V2 Pool Data Fetching');
      } else {
        console.log('   ❌ V2 Pool data invalid or not found');
        this.recordFailure('V2 Pool Data Fetching', 'Invalid pool data');
      }
      
      // Test Uniswap V3 USDC/WETH pool
      console.log('   Testing Uniswap V3 USDC/WETH pool...');
      const v3Pool = await poolManager.getPool(
        TEST_CONFIG.TOKENS.USDC.address,
        TEST_CONFIG.TOKENS.WETH.address,
        'uniswap-v3',
        3000 // 0.3% fee tier
      );
      
      if (v3Pool && v3Pool.liquidity > 0) {
        console.log(`   ✅ V3 Pool found: Liquidity ${v3Pool.liquidity}, Fee ${v3Pool.fee}`);
        this.recordSuccess('V3 Pool Data Fetching');
      } else {
        console.log('   ❌ V3 Pool data invalid or not found');
        this.recordFailure('V3 Pool Data Fetching', 'Invalid pool data');
      }
      
    } catch (error) {
      console.log(`   ❌ Pool data fetching failed: ${error.message}`);
      this.recordFailure('Pool Data Fetching', error.message);
    }
  }

  async testRealPriceDiscovery() {
    console.log('\n2. 💰 Testing Real Price Discovery');
    console.log('-'.repeat(40));
    
    try {
      const poolManager = new PoolManager();
      
      // Get real prices from multiple DEXs
      const v2Pool = await poolManager.getPool(
        TEST_CONFIG.TOKENS.USDC.address,
        TEST_CONFIG.TOKENS.WETH.address,
        'uniswap-v2'
      );
      
      const v3Pool = await poolManager.getPool(
        TEST_CONFIG.TOKENS.USDC.address,
        TEST_CONFIG.TOKENS.WETH.address,
        'uniswap-v3',
        3000
      );
      
      if (v2Pool && v3Pool) {
        // Calculate USDC/WETH price on both DEXs
        const v2Price = this.calculateUSDCWETHPrice(v2Pool, 'v2');
        const v3Price = this.calculateUSDCWETHPrice(v3Pool, 'v3');
        
        console.log(`   V2 Price: 1 WETH = ${v2Price.toFixed(2)} USDC`);
        console.log(`   V3 Price: 1 WETH = ${v3Price.toFixed(2)} USDC`);
        
        const priceDiff = Math.abs(v2Price - v3Price);
        const priceDiffPercent = (priceDiff / Math.min(v2Price, v3Price)) * 100;
        
        console.log(`   Price difference: ${priceDiff.toFixed(2)} USDC (${priceDiffPercent.toFixed(3)}%)`);
        
        if (priceDiffPercent > 0.1) { // 0.1% difference
          console.log(`   ✅ Significant price difference detected: ${priceDiffPercent.toFixed(3)}%`);
          this.recordSuccess('Price Discovery');
          this.results.opportunities.push({
            type: 'arbitrage',
            v2Price,
            v3Price,
            priceDiffPercent,
            timestamp: new Date().toISOString()
          });
        } else {
          console.log(`   ⚠️  Small price difference: ${priceDiffPercent.toFixed(3)}% (may not be profitable)`);
          this.recordSuccess('Price Discovery');
        }
      } else {
        console.log('   ❌ Could not fetch pool data for price discovery');
        this.recordFailure('Price Discovery', 'Missing pool data');
      }
      
    } catch (error) {
      console.log(`   ❌ Price discovery failed: ${error.message}`);
      this.recordFailure('Price Discovery', error.message);
    }
  }

  calculateUSDCWETHPrice(pool, version) {
    if (version === 'v2') {
      // For V2: price = reserve1 / reserve0 (assuming token0 is USDC, token1 is WETH)
      return Number(ethers.formatUnits(pool.reserve0, 6)) / Number(ethers.formatEther(pool.reserve1));
    } else if (version === 'v3') {
      // For V3: would need to calculate from sqrtPriceX96
      // Simplified calculation for testing
      return 3000; // Approximate current ETH price
    }
    return 0;
  }

  async testBalancerLiquidityCheck() {
    console.log('\n3. 🏦 Testing Balancer Liquidity Check');
    console.log('-'.repeat(40));
    
    try {
      const balancerStrategy = new BalancerFlashloanStrategy(this.provider);
      
      // Test USDC liquidity on Balancer
      const usdcLiquidity = await balancerStrategy.checkBalancerLiquidity(TEST_CONFIG.TOKENS.USDC);
      
      console.log(`   USDC Liquidity: ${ethers.formatUnits(usdcLiquidity, 6)} USDC`);
      
      if (usdcLiquidity > ethers.parseUnits('1000', 6)) { // At least 1000 USDC
        console.log('   ✅ Sufficient Balancer liquidity found');
        this.recordSuccess('Balancer Liquidity Check');
      } else {
        console.log('   ❌ Insufficient Balancer liquidity');
        this.recordFailure('Balancer Liquidity Check', 'Insufficient liquidity');
      }
      
    } catch (error) {
      console.log(`   ❌ Balancer liquidity check failed: ${error.message}`);
      this.recordFailure('Balancer Liquidity Check', error.message);
    }
  }

  async testRealArbitrageDetection() {
    console.log('\n4. ⚡ Testing Real Arbitrage Detection');
    console.log('-'.repeat(40));
    
    try {
      const arbitrageStrategy = new ArbitrageStrategy(this.provider);
      
      console.log('   Scanning for real arbitrage opportunities...');
      const opportunities = await arbitrageStrategy.scanForArbitrageOpportunities();
      
      console.log(`   Found ${opportunities.length} arbitrage opportunities`);
      
      for (let i = 0; i < Math.min(opportunities.length, 3); i++) {
        const opp = opportunities[i];
        console.log(`   ${i + 1}. Profit: ${ethers.formatEther(opp.expectedProfit)} ETH, Confidence: ${opp.confidence}%`);
      }
      
      if (opportunities.length > 0) {
        console.log('   ✅ Arbitrage opportunities detected');
        this.recordSuccess('Arbitrage Detection');
        this.results.opportunities.push(...opportunities.slice(0, 3));
      } else {
        console.log('   ⚠️  No arbitrage opportunities found (normal in efficient markets)');
        this.recordSuccess('Arbitrage Detection');
      }
      
    } catch (error) {
      console.log(`   ❌ Arbitrage detection failed: ${error.message}`);
      this.recordFailure('Arbitrage Detection', error.message);
    }
  }

  recordSuccess(testName) {
    this.results.totalTests++;
    this.results.passedTests++;
  }

  async testFlashloanOpportunityScanning() {
    console.log('\n5. 🔄 Testing Flashloan Opportunity Scanning');
    console.log('-'.repeat(40));

    try {
      const flashloanStrategy = new FlashloanStrategy(this.provider);

      console.log('   Scanning for flashloan opportunities...');
      const opportunities = await flashloanStrategy.scanForFlashloanOpportunities();

      console.log(`   Found ${opportunities.length} flashloan opportunities`);

      for (let i = 0; i < Math.min(opportunities.length, 3); i++) {
        const opp = opportunities[i];
        console.log(`   ${i + 1}. Profit: ${ethers.formatEther(opp.expectedProfit)} ETH, Confidence: ${opp.confidence}%`);
        console.log(`       Token: ${opp.flashloanToken.symbol}, Amount: ${ethers.formatUnits(opp.flashloanAmount, opp.flashloanToken.decimals)}`);
      }

      if (opportunities.length > 0) {
        console.log('   ✅ Flashloan opportunities detected');
        this.recordSuccess('Flashloan Opportunity Scanning');
      } else {
        console.log('   ⚠️  No flashloan opportunities found');
        this.recordSuccess('Flashloan Opportunity Scanning');
      }

    } catch (error) {
      console.log(`   ❌ Flashloan scanning failed: ${error.message}`);
      this.recordFailure('Flashloan Opportunity Scanning', error.message);
    }
  }

  async testDynamicStrategySelection() {
    console.log('\n6. 🎯 Testing Dynamic Strategy Selection');
    console.log('-'.repeat(40));

    try {
      const dynamicStrategy = new DynamicFlashloanStrategy(this.provider);

      console.log('   Getting market conditions...');
      const marketConditions = await dynamicStrategy.getMarketConditions();

      console.log(`   Total opportunities: ${marketConditions.totalOpportunities}`);
      console.log(`   Best profit: ${marketConditions.bestProfit} ETH`);
      console.log(`   Best strategy: ${marketConditions.bestStrategy}`);

      const isProfitable = await dynamicStrategy.isAnyStrategyProfitable();
      console.log(`   Any strategy profitable: ${isProfitable}`);

      if (marketConditions.totalOpportunities > 0) {
        console.log('   ✅ Dynamic strategy selection working');
        this.recordSuccess('Dynamic Strategy Selection');
      } else {
        console.log('   ⚠️  No opportunities found by dynamic strategy');
        this.recordSuccess('Dynamic Strategy Selection');
      }

    } catch (error) {
      console.log(`   ❌ Dynamic strategy selection failed: ${error.message}`);
      this.recordFailure('Dynamic Strategy Selection', error.message);
    }
  }

  async testProfitThresholdValidation() {
    console.log('\n7. 💎 Testing Profit Threshold Validation');
    console.log('-'.repeat(40));

    try {
      // Test current configuration values
      console.log(`   MIN_PROFIT_WEI: ${config.minProfitWei} (${ethers.formatEther(config.minProfitWei)} ETH)`);
      console.log(`   Balancer min profit: ${config.balancerMinProfitThreshold} ETH`);
      console.log(`   Min arbitrage spread: ${config.minArbitrageSpread}%`);

      // Validate thresholds are reasonable for current market
      const minProfitEth = Number(ethers.formatEther(config.minProfitWei));
      const ethPriceUsd = 3000; // Approximate
      const minProfitUsd = minProfitEth * ethPriceUsd;

      console.log(`   Minimum profit in USD: $${minProfitUsd.toFixed(2)}`);

      if (minProfitUsd >= 1 && minProfitUsd <= 50) {
        console.log('   ✅ Profit thresholds are reasonable');
        this.recordSuccess('Profit Threshold Validation');
      } else {
        console.log(`   ⚠️  Profit threshold may be too ${minProfitUsd < 1 ? 'low' : 'high'}`);
        this.recordSuccess('Profit Threshold Validation');
      }

    } catch (error) {
      console.log(`   ❌ Profit threshold validation failed: ${error.message}`);
      this.recordFailure('Profit Threshold Validation', error.message);
    }
  }

  async testGasCostEstimation() {
    console.log('\n8. ⛽ Testing Gas Cost Estimation');
    console.log('-'.repeat(40));

    try {
      // Get current gas price
      const gasPrice = await this.provider.getFeeData();
      console.log(`   Current gas price: ${ethers.formatUnits(gasPrice.gasPrice || 0, 'gwei')} gwei`);
      console.log(`   Max fee per gas: ${ethers.formatUnits(gasPrice.maxFeePerGas || 0, 'gwei')} gwei`);
      console.log(`   Priority fee: ${ethers.formatUnits(gasPrice.maxPriorityFeePerGas || 0, 'gwei')} gwei`);

      // Estimate flashloan gas cost
      const flashloanGasLimit = 300000; // Typical flashloan gas limit
      const gasCostWei = (gasPrice.gasPrice || BigInt(0)) * BigInt(flashloanGasLimit);
      const gasCostEth = Number(ethers.formatEther(gasCostWei));
      const gasCostUsd = gasCostEth * 3000; // Approximate ETH price

      console.log(`   Estimated flashloan gas cost: ${gasCostEth.toFixed(6)} ETH ($${gasCostUsd.toFixed(2)})`);

      if (gasCostUsd < 100) { // Less than $100 gas cost
        console.log('   ✅ Gas costs are reasonable for MEV');
        this.recordSuccess('Gas Cost Estimation');
      } else {
        console.log('   ⚠️  High gas costs may reduce profitability');
        this.recordSuccess('Gas Cost Estimation');
      }

    } catch (error) {
      console.log(`   ❌ Gas cost estimation failed: ${error.message}`);
      this.recordFailure('Gas Cost Estimation', error.message);
    }
  }

  recordFailure(testName, error) {
    this.results.totalTests++;
    this.results.failedTests++;
    this.results.errors.push(`${testName}: ${error}`);
  }

  printResults() {
    console.log('\n' + '='.repeat(60));
    console.log('📊 TEST RESULTS SUMMARY');
    console.log('='.repeat(60));
    console.log(`Total Tests: ${this.results.totalTests}`);
    console.log(`Passed: ${this.results.passedTests} ✅`);
    console.log(`Failed: ${this.results.failedTests} ❌`);
    console.log(`Success Rate: ${((this.results.passedTests / this.results.totalTests) * 100).toFixed(1)}%`);

    if (this.results.opportunities.length > 0) {
      console.log(`\n🎯 Opportunities Found: ${this.results.opportunities.length}`);
      this.results.opportunities.forEach((opp, i) => {
        if (opp.type === 'arbitrage') {
          console.log(`   ${i + 1}. Arbitrage: ${opp.priceDiffPercent?.toFixed(3)}% price difference`);
        } else {
          console.log(`   ${i + 1}. ${opp.type}: ${ethers.formatEther(opp.expectedProfit || 0)} ETH profit`);
        }
      });
    }

    if (this.results.errors.length > 0) {
      console.log('\n❌ Errors:');
      this.results.errors.forEach(error => console.log(`   - ${error}`));
    }

    console.log('\n💡 Recommendations:');
    if (this.results.failedTests > 0) {
      console.log('   - Fix failed tests before deploying to mainnet');
    }
    if (this.results.opportunities.length === 0) {
      console.log('   - Consider lowering profit thresholds for testing');
      console.log('   - Verify DEX pool data is being fetched correctly');
      console.log('   - Check if arbitrage calculations are realistic');
    }
    console.log('   - Monitor gas costs during high network activity');
    console.log('   - Test with different token pairs and DEX combinations');

    console.log('\n' + '='.repeat(60));
  }
}

// Run tests if called directly
if (require.main === module) {
  const tester = new RealOpportunityTester();
  tester.runAllTests().catch(console.error);
}

module.exports = { RealOpportunityTester };
