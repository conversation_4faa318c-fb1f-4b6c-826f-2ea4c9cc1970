const { ethers } = require('ethers');

async function testContractBasic() {
    console.log('🔍 Testing basic contract functionality...');
    
    // Connect to Hardhat fork
    const provider = new ethers.JsonRpcProvider('http://127.0.0.1:8547');
    const wallet = new ethers.Wallet('0xac0974bec39a17e36ba4a6b4d238ff944bacb478cbed5efcae784d7bf4f2ff80', provider);
    
    // Fixed contract address
    const contractAddress = '******************************************';
    
    console.log(`📋 Test Configuration:`);
    console.log(`   Contract: ${contractAddress}`);
    console.log(`   Wallet: ${wallet.address}`);
    
    // Basic contract interface
    const contractInterface = [
        'function owner() external view returns (address)',
        'function CHAIN_ID() external view returns (uint256)',
        'function UNISWAP_V2_ROUTER() external view returns (address)',
        'function UNISWAP_V3_ROUTER() external view returns (address)',
        'function supportedRouters(address) external view returns (bool)',
        'function paused() external view returns (bool)'
    ];
    
    const contract = new ethers.Contract(contractAddress, contractInterface, provider);
    
    try {
        console.log('\n🧪 Testing basic contract state...');
        
        const owner = await contract.owner();
        const chainId = await contract.CHAIN_ID();
        const v2Router = await contract.UNISWAP_V2_ROUTER();
        const v3Router = await contract.UNISWAP_V3_ROUTER();
        const isPaused = await contract.paused();
        
        console.log(`   ✅ Owner: ${owner}`);
        console.log(`   ✅ Chain ID: ${chainId}`);
        console.log(`   ✅ V2 Router: ${v2Router}`);
        console.log(`   ✅ V3 Router: ${v3Router}`);
        console.log(`   ✅ Is Paused: ${isPaused}`);
        
        // Test router support
        const v2Supported = await contract.supportedRouters(v2Router);
        const v3Supported = await contract.supportedRouters(v3Router);
        
        console.log(`   ✅ V2 Router supported: ${v2Supported}`);
        console.log(`   ✅ V3 Router supported: ${v3Supported}`);
        
        console.log('\n✅ Basic contract functionality working!');
        
        // Now test the problematic function with minimal parameters
        console.log('\n🧪 Testing executeOptimalFlashloan with minimal validation...');
        
        const executeInterface = [
            'function executeOptimalFlashloan(address asset, uint256 amount, bytes calldata params) external'
        ];
        
        const executeContract = new ethers.Contract(contractAddress, executeInterface, wallet);
        
        // Token addresses
        const wethAddress = '******************************************';
        const daiAddress = '******************************************';
        
        // Create minimal valid parameters
        const minimalParams = ethers.AbiCoder.defaultAbiCoder().encode(
            ['address[]', 'address[]', 'address', 'address', 'uint24[]', 'uint256', 'uint8', 'uint256', 'uint256'],
            [
                [wethAddress, daiAddress],               // buyPath: WETH → DAI
                [daiAddress, wethAddress],               // sellPath: DAI → WETH
                v3Router,                                // buyDex (V3)
                v2Router,                                // sellDex (V2)
                [3000],                                  // v3Fees (0.3%)
                ethers.parseEther('0.001'),              // minProfit
                0,                                       // provider (AAVE)
                500,                                     // slippageToleranceBps (5%)
                ethers.parseUnits('100', 'gwei')         // maxGasCostWei
            ]
        );
        
        try {
            // Test with very small amount to minimize external calls
            await executeContract.executeOptimalFlashloan.staticCall(
                wethAddress,
                ethers.parseUnits('1', 'gwei'), // Very small amount
                minimalParams
            );
            
            console.log(`   ✅ Minimal validation passed!`);
            
        } catch (error) {
            const errorMsg = error.message;
            console.log(`   Result: ${errorMsg.split('(')[0]}`);
            
            if (errorMsg.includes('Amount too small')) {
                console.log(`   ✅ Amount validation working - try larger amount`);
                
                // Try with minimum required amount
                try {
                    await executeContract.executeOptimalFlashloan.staticCall(
                        wethAddress,
                        ethers.parseUnits('1', 'finney'), // 0.001 ETH
                        minimalParams
                    );
                    
                    console.log(`   ✅ Minimum amount validation passed!`);
                    
                } catch (error2) {
                    const errorMsg2 = error2.message;
                    console.log(`   Result with 0.001 ETH: ${errorMsg2.split('(')[0]}`);
                    
                    if (errorMsg2.includes('require(false)')) {
                        console.log(`   ❌ Still getting require(false) error`);
                    } else if (errorMsg2.includes('Arbitrage resulted in loss')) {
                        console.log(`   ✅ Validation working! Got expected arbitrage error`);
                    } else {
                        console.log(`   💡 Different error - investigating...`);
                    }
                }
                
            } else if (errorMsg.includes('require(false)')) {
                console.log(`   ❌ Still getting require(false) error - need more investigation`);
            } else if (errorMsg.includes('Arbitrage resulted in loss')) {
                console.log(`   ✅ Validation working! Got expected arbitrage error`);
            } else {
                console.log(`   💡 Different error: ${errorMsg}`);
            }
        }
        
    } catch (error) {
        console.log(`❌ Error testing contract: ${error.message}`);
    }
    
    console.log('\n🏁 Basic contract testing completed!');
}

testContractBasic().catch(console.error);
