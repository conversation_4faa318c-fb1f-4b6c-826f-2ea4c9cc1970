const { ethers } = require('ethers');

async function testMinimalSlippage() {
    console.log('🔍 Testing contract with minimal slippage protection...');
    
    // Connect to Hardhat fork
    const provider = new ethers.JsonRpcProvider('http://127.0.0.1:8547');
    const wallet = new ethers.Wallet('0xac0974bec39a17e36ba4a6b4d238ff944bacb478cbed5efcae784d7bf4f2ff80', provider);
    
    console.log(`📋 Test Configuration:`);
    console.log(`   Provider: Hardhat fork (mainnet)`);
    console.log(`   Wallet: ${wallet.address}`);
    console.log(`   Balance: ${ethers.formatEther(await provider.getBalance(wallet.address))} ETH`);
    
    // Deploy the improved contract
    console.log('\n🚀 Deploying contract with minimal slippage protection...');
    
    const aavePool = '******************************************';
    const balancerVault = '******************************************';
    
    const fs = require('fs');
    const contractArtifact = JSON.parse(fs.readFileSync('./artifacts/contracts/HybridFlashloanArbitrageFixed.sol/HybridFlashloanArbitrageFixed.json', 'utf8'));
    
    const contractFactory = new ethers.ContractFactory(
        contractArtifact.abi,
        contractArtifact.bytecode,
        wallet
    );
    
    const contract = await contractFactory.deploy(aavePool, balancerVault);
    await contract.waitForDeployment();
    const contractAddress = await contract.getAddress();
    
    console.log(`✅ Improved contract deployed at: ${contractAddress}`);
    
    // Get contract addresses
    const v2Router = await contract.UNISWAP_V2_ROUTER();
    const v3Router = await contract.UNISWAP_V3_ROUTER();
    
    // Token addresses
    const wethAddress = '******************************************';
    const usdcAddress = '******************************************';
    const wbtcAddress = '******************************************';
    
    console.log('\n🧪 Testing with the exact same failing parameters...');
    
    // Test the exact same transaction that was failing with "Too little received"
    const failingParams = ethers.AbiCoder.defaultAbiCoder().encode(
        ['address', 'address', 'address', 'address', 'uint24', 'uint256', 'uint8'],
        [
            wethAddress,                     // tokenA (WETH)
            wbtcAddress,                     // tokenB (WBTC) - from the failing transaction
            v3Router,                        // buyDex (Uniswap V3)
            v2Router,                        // sellDex (Uniswap V2)
            3000,                            // v3Fee (0.3%)
            ethers.parseEther('0.0001'),     // minProfit (0.0001 ETH)
            0                                // provider (AAVE)
        ]
    );
    
    const flashloanAmount = ethers.parseEther('20'); // Same as failing transaction
    
    console.log(`   Testing exact failing scenario: 20 ETH WETH → WBTC → WETH`);
    
    try {
        await contract.executeOptimalFlashloan.staticCall(
            wethAddress,
            flashloanAmount,
            failingParams
        );
        
        console.log(`      ✅ SUCCESS: Minimal slippage protection fixed the "Too little received" error!`);
        
    } catch (error) {
        const errorMsg = error.message.split('(')[0];
        console.log(`      ❌ Still failing: ${errorMsg}`);
        
        if (errorMsg.includes('Too little received')) {
            console.log(`         💡 Still getting slippage error - need even more flexible approach`);
        } else if (errorMsg.includes('Arbitrage resulted in loss')) {
            console.log(`         ✅ PROGRESS: No more "Too little received" - now getting loss protection!`);
            console.log(`         💡 This means trades are executing but unprofitable`);
        } else if (errorMsg.includes('Buy execution failed')) {
            console.log(`         💡 First trade failed - checking liquidity...`);
        } else if (errorMsg.includes('Sell execution failed')) {
            console.log(`         ✅ PROGRESS: First trade succeeded, second trade failed`);
        } else {
            console.log(`         🔍 Other error: ${errorMsg}`);
        }
    }
    
    console.log('\n🧪 Testing with smaller amounts...');
    
    // Test with more reasonable amounts
    const reasonableTests = [
        {
            name: 'WETH/USDC 0.1 ETH',
            tokenA: wethAddress,
            tokenB: usdcAddress,
            amount: ethers.parseEther('0.1'),
            minProfit: ethers.parseEther('0.00001')
        },
        {
            name: 'WETH/USDC 1 ETH',
            tokenA: wethAddress,
            tokenB: usdcAddress,
            amount: ethers.parseEther('1'),
            minProfit: ethers.parseEther('0.0001')
        },
        {
            name: 'WETH/WBTC 0.1 ETH',
            tokenA: wethAddress,
            tokenB: wbtcAddress,
            amount: ethers.parseEther('0.1'),
            minProfit: ethers.parseEther('0.00001')
        }
    ];
    
    for (const test of reasonableTests) {
        console.log(`\n   Testing ${test.name}:`);
        
        const testParams = ethers.AbiCoder.defaultAbiCoder().encode(
            ['address', 'address', 'address', 'address', 'uint24', 'uint256', 'uint8'],
            [
                test.tokenA,
                test.tokenB,
                v3Router,
                v2Router,
                3000,
                test.minProfit,
                0
            ]
        );
        
        try {
            await contract.executeOptimalFlashloan.staticCall(
                test.tokenA,
                test.amount,
                testParams
            );
            
            console.log(`      ✅ ${test.name}: SUCCESS with minimal slippage protection!`);
            
        } catch (error) {
            const errorMsg = error.message.split('(')[0];
            console.log(`      ❌ ${test.name}: ${errorMsg}`);
            
            if (errorMsg.includes('Too little received')) {
                console.log(`         💡 Still getting slippage error`);
            } else if (errorMsg.includes('Arbitrage resulted in loss')) {
                console.log(`         ✅ PROGRESS: Trades executing, just unprofitable`);
            } else if (errorMsg.includes('INSUFFICIENT_OUTPUT_AMOUNT')) {
                console.log(`         💡 Uniswap slippage error`);
            }
        }
    }
    
    console.log('\n🧪 Testing different DEX combinations...');
    
    const dexCombinations = [
        { name: 'V2 → V3', buy: v2Router, sell: v3Router },
        { name: 'V3 → V2', buy: v3Router, sell: v2Router },
        { name: 'V2 → V2', buy: v2Router, sell: v2Router },
        { name: 'V3 → V3', buy: v3Router, sell: v3Router }
    ];
    
    for (const combo of dexCombinations) {
        console.log(`\n   Testing ${combo.name} combination:`);
        
        const comboParams = ethers.AbiCoder.defaultAbiCoder().encode(
            ['address', 'address', 'address', 'address', 'uint24', 'uint256', 'uint8'],
            [
                wethAddress,
                usdcAddress,
                combo.buy,
                combo.sell,
                3000,
                ethers.parseEther('0.00001'),
                0
            ]
        );
        
        try {
            await contract.executeOptimalFlashloan.staticCall(
                wethAddress,
                ethers.parseEther('0.1'),
                comboParams
            );
            
            console.log(`      ✅ ${combo.name}: SUCCESS`);
            
        } catch (error) {
            const errorMsg = error.message.split('(')[0];
            console.log(`      ❌ ${combo.name}: ${errorMsg}`);
            
            if (errorMsg.includes('Too little received')) {
                console.log(`         💡 ${combo.name} still has slippage issues`);
            } else if (errorMsg.includes('Arbitrage resulted in loss')) {
                console.log(`         ✅ ${combo.name} trades executing successfully!`);
            }
        }
    }
    
    console.log('\n🏁 Minimal slippage protection testing completed!');
    
    console.log('\n📊 ANALYSIS:');
    console.log('   ✅ Minimal slippage protection implemented');
    console.log('   ✅ V2 trades: minAmountOut = 1 (minimal protection)');
    console.log('   ✅ V3 trades: minAmountOut = 1 (minimal protection)');
    console.log('   ✅ Profit validation at arbitrage level');
    console.log('');
    console.log('   💡 If we still see "Too little received", it means:');
    console.log('   💡 1. The price impact is extremely high');
    console.log('   💡 2. There might be liquidity issues');
    console.log('   💡 3. The trade amounts are too large for available liquidity');
}

testMinimalSlippage().catch(console.error);
