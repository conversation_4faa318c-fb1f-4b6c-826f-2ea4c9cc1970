import { expect } from 'chai';
import { ethers } from 'ethers';
import { FlashloanAttackAnalyzer, AttackVector } from '../../../src/attacks/flashloan/FlashloanAttackAnalyzer';
import { FlashloanAttackValidator } from '../../../src/attacks/flashloan/FlashloanAttackValidator';
import { FlashloanAttackMonitor } from '../../../src/attacks/flashloan/FlashloanAttackMonitor';
import { FlashloanAttackExecutor } from '../../../src/attacks/flashloan/FlashloanAttackExecutor';
import { FlashloanAttackOrchestrator } from '../../../src/attacks/flashloan/FlashloanAttackOrchestrator';
import { LightArbitrageService, DEXType, FlashloanProvider } from '../../../src/services/LightArbitrageService';

describe('Flashloan Attack System', function () {
  let provider: ethers.Provider;
  let wallet: ethers.Wallet;
  let lightArbitrageService: LightArbitrageService;
  let attackAnalyzer: FlashloanAttackAnalyzer;
  let attackValidator: FlashloanAttackValidator;
  let attackMonitor: FlashloanAttackMonitor;
  let attackExecutor: FlashloanAttackExecutor;
  let attackOrchestrator: FlashloanAttackOrchestrator;

  before(async function () {
    // Setup test environment
    provider = new ethers.JsonRpcProvider('http://localhost:8545');
    wallet = new ethers.Wallet('0xac0974bec39a17e36ba4a6b4d238ff944bacb478cbed5efcae784d7bf4f2ff80', provider);
    
    // Mock contract ABI for testing
    const mockABI = [
      "function executeOptimalFlashloan(address asset, uint256 amount, bytes calldata params) external",
      "function owner() view returns (address)"
    ];
    
    // Initialize services
    lightArbitrageService = new LightArbitrageService(
      '******************************************', // Mock address
      provider,
      wallet,
      mockABI
    );
    
    attackAnalyzer = new FlashloanAttackAnalyzer(provider);
    attackValidator = new FlashloanAttackValidator(provider);
    attackMonitor = new FlashloanAttackMonitor(provider);
    attackExecutor = new FlashloanAttackExecutor(lightArbitrageService, provider, wallet);
    
    const orchestratorConfig = {
      enabledVectors: ['weth-usdc-v3-sushi'],
      minProfitETH: '0.01',
      maxGasCostGwei: '50',
      defaultSlippage: 100,
      scanInterval: 1000,
      dryRun: true,
      useFlashbots: false,
      maxConcurrentAttacks: 1
    };
    
    attackOrchestrator = new FlashloanAttackOrchestrator(
      lightArbitrageService,
      provider,
      wallet,
      orchestratorConfig
    );
  });

  describe('FlashloanAttackAnalyzer', function () {
    it('should load attack vectors correctly', function () {
      const vectors = attackAnalyzer.getAttackVectors();
      
      expect(vectors).to.be.an('array');
      expect(vectors.length).to.be.greaterThan(0);
      
      // Check first vector structure
      const firstVector = vectors[0];
      expect(firstVector).to.have.property('id');
      expect(firstVector).to.have.property('name');
      expect(firstVector).to.have.property('tokenA');
      expect(firstVector).to.have.property('tokenB');
      expect(firstVector).to.have.property('dexBuy');
      expect(firstVector).to.have.property('dexSell');
      expect(firstVector).to.have.property('expectedProfitability');
      expect(firstVector).to.have.property('riskLevel');
    });

    it('should get attack vector by ID', function () {
      const vector = attackAnalyzer.getAttackVector('weth-usdc-v3-sushi');
      
      expect(vector).to.not.be.undefined;
      expect(vector?.id).to.equal('weth-usdc-v3-sushi');
      expect(vector?.name).to.include('WETH/USDC');
    });

    it('should return best attack vectors', function () {
      const bestVectors = attackAnalyzer.getBestAttackVectors(2);
      
      expect(bestVectors).to.be.an('array');
      expect(bestVectors.length).to.be.lessThanOrEqual(2);
      
      // Should be sorted by profitability
      if (bestVectors.length > 1) {
        expect(bestVectors[0].expectedProfitability).to.be.greaterThanOrEqual(
          bestVectors[1].expectedProfitability
        );
      }
    });

    it('should return low-risk attack vectors', function () {
      const lowRiskVectors = attackAnalyzer.getLowRiskAttackVectors();
      
      expect(lowRiskVectors).to.be.an('array');
      lowRiskVectors.forEach(vector => {
        expect(vector.riskLevel).to.be.lessThanOrEqual(3);
      });
    });

    it('should validate attack parameters', function () {
      const vector = attackAnalyzer.getAttackVector('weth-usdc-v3-sushi');
      expect(vector).to.not.be.undefined;
      
      if (vector) {
        const validation = attackAnalyzer.validateAttackParameters(
          vector,
          ethers.parseEther('1.0'),
          ethers.parseUnits('50', 'gwei')
        );
        
        expect(validation).to.have.property('valid');
        expect(validation).to.have.property('errors');
        expect(validation.errors).to.be.an('array');
      }
    });

    it('should calculate optimal amount', async function () {
      const vector = attackAnalyzer.getAttackVector('weth-usdc-v3-sushi');
      expect(vector).to.not.be.undefined;
      
      if (vector) {
        const optimalAmount = await attackAnalyzer.calculateOptimalAmount(vector);
        
        expect(optimalAmount).to.be.a('bigint');
        expect(optimalAmount).to.be.greaterThan(BigInt(0));
        expect(optimalAmount).to.be.greaterThanOrEqual(vector.minAmount);
        expect(optimalAmount).to.be.lessThanOrEqual(vector.maxAmount);
      }
    });

    it('should provide attack vector statistics', function () {
      const stats = attackAnalyzer.getAttackVectorStats();
      
      expect(stats).to.have.property('totalVectors');
      expect(stats).to.have.property('avgProfitability');
      expect(stats).to.have.property('avgRisk');
      expect(stats).to.have.property('lowRiskCount');
      expect(stats).to.have.property('highProfitCount');
      expect(stats).to.have.property('vectors');
      
      expect(stats.totalVectors).to.be.greaterThan(0);
      expect(stats.vectors).to.be.an('array');
    });
  });

  describe('FlashloanAttackValidator', function () {
    it('should validate network conditions', async function () {
      // This test may fail if no local network is running
      try {
        const vector = attackAnalyzer.getAttackVector('weth-usdc-v3-sushi');
        const mockOpportunity = {
          vector: vector!,
          currentPriceBuy: ethers.parseEther('2000'),
          currentPriceSell: ethers.parseEther('2005'),
          priceSpread: 0.25,
          estimatedProfit: ethers.parseEther('0.05'),
          confidence: 7,
          timestamp: Math.floor(Date.now() / 1000),
          blockNumber: 1000000
        };
        
        const validation = await attackValidator.validatePreExecution(
          vector!,
          ethers.parseEther('1.0'),
          mockOpportunity
        );
        
        expect(validation).to.have.property('valid');
        expect(validation).to.have.property('errors');
        expect(validation).to.have.property('warnings');
        expect(validation).to.have.property('riskScore');
        
      } catch (error) {
        // Expected if no local network
        console.log('Network validation test skipped (no local network)');
      }
    });

    it('should provide validation statistics', function () {
      const stats = attackValidator.getValidationStats();
      
      expect(stats).to.have.property('riskThresholds');
      expect(stats).to.have.property('validationChecks');
      expect(stats.validationChecks).to.be.an('array');
      expect(stats.validationChecks.length).to.be.greaterThan(0);
    });
  });

  describe('FlashloanAttackMonitor', function () {
    it('should initialize with empty statistics', function () {
      const stats = attackMonitor.getAttackStatistics();
      
      expect(stats.totalAttacks).to.equal(0);
      expect(stats.successfulAttacks).to.equal(0);
      expect(stats.failedAttacks).to.equal(0);
      expect(stats.successRate).to.equal(0);
      expect(stats.totalProfit).to.equal(BigInt(0));
      expect(stats.bestAttack).to.be.null;
      expect(stats.worstAttack).to.be.null;
    });

    it('should generate performance report', function () {
      const report = attackMonitor.generatePerformanceReport();
      
      expect(report).to.be.a('string');
      expect(report).to.include('FLASHLOAN ATTACK PERFORMANCE REPORT');
      expect(report).to.include('EXECUTION STATISTICS');
      expect(report).to.include('PROFIT ANALYSIS');
    });

    it('should handle monitoring lifecycle', function () {
      expect(() => attackMonitor.startMonitoring()).to.not.throw();
      expect(() => attackMonitor.stopMonitoring()).to.not.throw();
      expect(() => attackMonitor.clearHistory()).to.not.throw();
    });

    it('should export attack data', function () {
      const data = attackMonitor.exportAttackData();
      
      expect(data).to.be.an('array');
      // Should be empty initially
      expect(data.length).to.equal(0);
    });
  });

  describe('LightArbitrageService Integration', function () {
    it('should provide DEX addresses', function () {
      const dexAddresses = lightArbitrageService.getDEXAddresses();
      
      expect(dexAddresses).to.be.an('object');
      expect(dexAddresses).to.have.property('UNISWAP_V2');
      expect(dexAddresses).to.have.property('UNISWAP_V3');
      expect(dexAddresses).to.have.property('SUSHISWAP');
      expect(dexAddresses).to.have.property('BALANCER_V2');
      
      // Validate addresses
      expect(ethers.isAddress(dexAddresses.UNISWAP_V2)).to.be.true;
      expect(ethers.isAddress(dexAddresses.UNISWAP_V3)).to.be.true;
      expect(ethers.isAddress(dexAddresses.SUSHISWAP)).to.be.true;
      expect(ethers.isAddress(dexAddresses.BALANCER_V2)).to.be.true;
    });

    it('should provide token addresses', function () {
      const tokens = lightArbitrageService.getTokenAddresses();
      
      expect(tokens).to.be.an('object');
      expect(tokens).to.have.property('WETH');
      expect(tokens).to.have.property('USDC');
      expect(tokens).to.have.property('DAI');
      
      // Validate addresses
      expect(ethers.isAddress(tokens.WETH)).to.be.true;
      expect(ethers.isAddress(tokens.USDC)).to.be.true;
      expect(ethers.isAddress(tokens.DAI)).to.be.true;
    });

    it('should provide Balancer pool IDs', function () {
      const pools = lightArbitrageService.getBalancerPools();
      
      expect(pools).to.be.an('object');
      expect(pools).to.have.property('WETH-USDC');
      expect(pools).to.have.property('USDC-DAI');
      
      // Validate pool IDs (should be 32-byte hex strings)
      expect(pools['WETH-USDC']).to.match(/^0x[a-fA-F0-9]{64}$/);
      expect(pools['USDC-DAI']).to.match(/^0x[a-fA-F0-9]{64}$/);
    });

    it('should create simple arbitrage steps', function () {
      const tokens = lightArbitrageService.getTokenAddresses();
      const dexes = lightArbitrageService.getDEXAddresses();
      
      const steps = lightArbitrageService.createSimpleArbitrage(
        tokens.WETH,
        tokens.USDC,
        dexes.UNISWAP_V3,
        DEXType.V3,
        dexes.SUSHISWAP,
        DEXType.V2
      );
      
      expect(steps).to.be.an('array');
      expect(steps.length).to.equal(2);
      
      // Validate first step
      expect(steps[0].tokenIn).to.equal(tokens.WETH);
      expect(steps[0].tokenOut).to.equal(tokens.USDC);
      expect(steps[0].dexType).to.equal(DEXType.V3);
      
      // Validate second step
      expect(steps[1].tokenIn).to.equal(tokens.USDC);
      expect(steps[1].tokenOut).to.equal(tokens.WETH);
      expect(steps[1].dexType).to.equal(DEXType.V2);
    });

    it('should create complex arbitrage steps', function () {
      const tokens = lightArbitrageService.getTokenAddresses();
      const dexes = lightArbitrageService.getDEXAddresses();
      
      const steps = lightArbitrageService.createComplexArbitrage(
        [tokens.WETH, tokens.USDC, tokens.DAI, tokens.WETH],
        [dexes.UNISWAP_V3, dexes.BALANCER_V2, dexes.SUSHISWAP],
        [DEXType.V3, DEXType.BALANCER_V2, DEXType.V2]
      );
      
      expect(steps).to.be.an('array');
      expect(steps.length).to.equal(3);
      
      // Validate route continuity
      expect(steps[0].tokenIn).to.equal(tokens.WETH);
      expect(steps[0].tokenOut).to.equal(tokens.USDC);
      expect(steps[1].tokenIn).to.equal(tokens.USDC);
      expect(steps[1].tokenOut).to.equal(tokens.DAI);
      expect(steps[2].tokenIn).to.equal(tokens.DAI);
      expect(steps[2].tokenOut).to.equal(tokens.WETH);
    });
  });

  describe('FlashloanAttackOrchestrator', function () {
    it('should initialize with correct configuration', function () {
      const status = attackOrchestrator.getStatus();
      
      expect(status).to.have.property('isRunning');
      expect(status).to.have.property('config');
      expect(status).to.have.property('activeAttacks');
      expect(status).to.have.property('statistics');
      expect(status).to.have.property('availableVectors');
      
      expect(status.isRunning).to.be.false;
      expect(status.activeAttacks).to.equal(0);
      expect(status.config.dryRun).to.be.true;
    });

    it('should get best opportunities', async function () {
      const opportunities = await attackOrchestrator.getBestOpportunities();
      
      expect(opportunities).to.be.an('array');
      expect(opportunities.length).to.be.greaterThan(0);
      
      // Should be sorted by score (profitability - risk)
      if (opportunities.length > 1) {
        const score1 = opportunities[0].expectedProfitability - opportunities[0].riskLevel;
        const score2 = opportunities[1].expectedProfitability - opportunities[1].riskLevel;
        expect(score1).to.be.greaterThanOrEqual(score2);
      }
    });

    it('should update configuration', function () {
      const newConfig = {
        minProfitETH: '0.02',
        maxGasCostGwei: '60'
      };
      
      expect(() => attackOrchestrator.updateConfig(newConfig)).to.not.throw();
      
      const status = attackOrchestrator.getStatus();
      expect(status.config.minProfitETH).to.equal('0.02');
      expect(status.config.maxGasCostGwei).to.equal('60');
    });
  });

  describe('Integration Tests', function () {
    it('should execute dry run attack successfully', async function () {
      this.timeout(10000); // 10 second timeout
      
      try {
        const result = await attackOrchestrator.executeAttack(
          'weth-usdc-v3-sushi',
          ethers.parseEther('1.0')
        );
        
        expect(result).to.have.property('success');
        expect(result).to.have.property('executionTime');
        expect(result.executionTime).to.be.greaterThan(0);
        
        // In dry run mode, should succeed
        expect(result.success).to.be.true;
        
      } catch (error) {
        // Expected if no proper network setup
        console.log('Dry run test skipped (network setup required)');
      }
    });

    it('should handle invalid attack vector gracefully', async function () {
      try {
        const result = await attackOrchestrator.executeAttack('invalid-vector');
        expect(result.success).to.be.false;
        expect(result.error).to.include('Attack vector not found');
      } catch (error) {
        expect(error).to.be.instanceOf(Error);
      }
    });
  });
});
