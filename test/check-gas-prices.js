const { ethers } = require('ethers');

async function checkGasPrices() {
    console.log('⛽ Checking current gas prices for contract deployment...');
    
    // Connect to mainnet
    const provider = new ethers.JsonRpcProvider(process.env.RPC_URL || 'http://192.168.0.16:8545');
    
    try {
        // Get current gas prices
        const feeData = await provider.getFeeData();
        const gasPrice = feeData.gasPrice;
        const maxFeePerGas = feeData.maxFeePerGas;
        const maxPriorityFeePerGas = feeData.maxPriorityFeePerGas;

        console.log('\n📊 Current Gas Prices:');
        console.log(`   Gas Price: ${ethers.formatUnits(gasPrice || 0n, 'gwei')} gwei`);
        console.log(`   Max Fee Per Gas: ${ethers.formatUnits(maxFeePerGas || 0n, 'gwei')} gwei`);
        console.log(`   Max Priority Fee: ${ethers.formatUnits(maxPriorityFeePerGas || 0n, 'gwei')} gwei`);

        // Estimate deployment cost for our contract
        const estimatedGasUnits = 3200000n; // Conservative estimate for large contract
        const effectiveGasPrice = maxFeePerGas || gasPrice || 20000000000n;
        const estimatedCostWei = estimatedGasUnits * effectiveGasPrice;
        const estimatedCostEth = ethers.formatEther(estimatedCostWei);
        
        // Get current ETH price (fallback to conservative estimate)
        let ethPriceUsd = 3500; // Conservative fallback
        try {
            // Try to get real ETH price from a simple API
            const response = await fetch('https://api.coingecko.com/api/v3/simple/price?ids=ethereum&vs_currencies=usd');
            const data = await response.json();
            if (data.ethereum && data.ethereum.usd) {
                ethPriceUsd = data.ethereum.usd;
                console.log(`   Current ETH Price: $${ethPriceUsd}`);
            }
        } catch (error) {
            console.log(`   ETH Price: $${ethPriceUsd} (fallback estimate)`);
        }

        const estimatedCostUsd = parseFloat(estimatedCostEth) * ethPriceUsd;

        console.log('\n💰 Deployment Cost Estimate:');
        console.log(`   Gas Units: ${estimatedGasUnits.toLocaleString()}`);
        console.log(`   Effective Gas Price: ${ethers.formatUnits(effectiveGasPrice, 'gwei')} gwei`);
        console.log(`   Estimated Cost: ${estimatedCostEth} ETH`);
        console.log(`   Estimated USD Cost: $${estimatedCostUsd.toFixed(2)}`);

        // Check against our $2 limit
        const maxCostUsd = 2.0;
        const isAffordable = estimatedCostUsd <= maxCostUsd;

        console.log('\n🎯 Deployment Status:');
        if (isAffordable) {
            console.log(`   ✅ READY TO DEPLOY!`);
            console.log(`   💰 Cost ($${estimatedCostUsd.toFixed(2)}) is within budget ($${maxCostUsd})`);
            console.log(`   🚀 Run: npm run deploy:mainnet`);
        } else {
            console.log(`   ❌ TOO EXPENSIVE`);
            console.log(`   💸 Cost ($${estimatedCostUsd.toFixed(2)}) exceeds budget ($${maxCostUsd})`);
            console.log(`   ⏰ Wait for lower gas prices`);
            
            // Calculate what gas price we need
            const maxCostEth = maxCostUsd / ethPriceUsd;
            const maxGasPriceWei = BigInt(Math.floor(maxCostEth * 1e18)) / estimatedGasUnits;
            const maxGasPriceGwei = ethers.formatUnits(maxGasPriceWei, 'gwei');
            console.log(`   🎯 Target gas price: ≤ ${parseFloat(maxGasPriceGwei).toFixed(3)} gwei`);
        }

        // Gas price categories for reference
        console.log('\n📈 Gas Price Reference:');
        const currentGwei = parseFloat(ethers.formatUnits(effectiveGasPrice, 'gwei'));
        
        if (currentGwei < 10) {
            console.log('   🟢 Very Low (< 10 gwei) - Excellent time to deploy');
        } else if (currentGwei < 20) {
            console.log('   🟡 Low (10-20 gwei) - Good time to deploy');
        } else if (currentGwei < 50) {
            console.log('   🟠 Medium (20-50 gwei) - Consider waiting');
        } else if (currentGwei < 100) {
            console.log('   🔴 High (50-100 gwei) - Wait for lower prices');
        } else {
            console.log('   🚨 Very High (> 100 gwei) - Definitely wait');
        }

        console.log('\n💡 Tips for Lower Gas Prices:');
        console.log('   • Check gas prices during weekends');
        console.log('   • Early morning UTC (2-6 AM) often has lower prices');
        console.log('   • Use ethgasstation.info or similar trackers');
        console.log('   • Avoid times when major DeFi protocols are active');

        // Show next check time
        const nextCheckMinutes = 15;
        const nextCheckTime = new Date(Date.now() + nextCheckMinutes * 60 * 1000);
        console.log(`\n⏰ Check again at: ${nextCheckTime.toLocaleTimeString()}`);

    } catch (error) {
        console.error('❌ Error checking gas prices:', error.message);
    }
}

// Run the check
checkGasPrices().catch(console.error);

// If running as a monitoring script, check every 15 minutes
if (process.argv.includes('--monitor')) {
    console.log('\n🔄 Starting gas price monitoring (every 15 minutes)...');
    console.log('   Press Ctrl+C to stop');
    
    setInterval(() => {
        console.log('\n' + '='.repeat(60));
        checkGasPrices().catch(console.error);
    }, 15 * 60 * 1000); // 15 minutes
}
