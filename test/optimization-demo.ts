import { ethers } from 'ethers';
import { DynamicFlashloanStrategy } from '../src/strategies/dynamic-flashloan';
import { FlashbotsBundleManager } from '../src/flashbots/bundle-provider';
import { FlashbotsExecutor } from '../src/execution/flashbots-executor';
import { AdvancedGasEstimator } from '../src/gas/advanced-estimator';
import { GasOptimizer } from '../src/gas/optimizer';
import { config } from '../src/config';
import { logger } from '../src/utils/logger';

/**
 * Demonstration of the optimization fix for executeBestOpportunity
 * This shows the performance improvement from avoiding redundant scanning
 */
async function demonstrateOptimization() {
  logger.info('🚀 Demonstrating Optimization: Avoiding Redundant Scanning');
  logger.info('='.repeat(60));

  // Initialize components
  const provider = new ethers.JsonRpcProvider(config.rpcUrl);
  const wallet = new ethers.Wallet(config.privateKey, provider);
  const flashbotsManager = new FlashbotsBundleManager(provider, wallet);
  const gasEstimator = new AdvancedGasEstimator(provider);
  const gasOptimizer = new GasOptimizer();
  const flashbotsExecutor = new FlashbotsExecutor(provider, wallet, flashbotsManager, gasEstimator, gasOptimizer);
  
  const dynamicFlashloanStrategy = new DynamicFlashloanStrategy(
    provider,
    wallet,
    flashbotsManager,
    flashbotsExecutor,
    gasOptimizer
  );

  // Test 1: OLD WAY (simulated) - Multiple scans
  logger.info('\n📊 OLD WAY (Before Optimization):');
  logger.info('   This simulates the old behavior with redundant scanning');
  
  const oldWayStart = Date.now();
  
  // Simulate the old way: scan 3 times (like the old bot.ts did)
  logger.info('   1. getMarketConditions() -> scanForOpportunities()');
  const scan1Start = Date.now();
  const opportunities1 = await dynamicFlashloanStrategy.scanForOpportunities();
  const scan1Time = Date.now() - scan1Start;
  
  logger.info('   2. isAnyStrategyProfitable() -> scanForOpportunities()');
  const scan2Start = Date.now();
  const opportunities2 = await dynamicFlashloanStrategy.scanForOpportunities();
  const scan2Time = Date.now() - scan2Start;
  
  logger.info('   3. executeBestOpportunity() -> scanForOpportunities()');
  const scan3Start = Date.now();
  const opportunities3 = await dynamicFlashloanStrategy.scanForOpportunities();
  const scan3Time = Date.now() - scan3Start;
  
  const oldWayTotal = Date.now() - oldWayStart;
  
  logger.info(`   Results: Found ${opportunities1.length}, ${opportunities2.length}, ${opportunities3.length} opportunities`);
  logger.info(`   Scan times: ${scan1Time}ms, ${scan2Time}ms, ${scan3Time}ms`);
  logger.info(`   Total time: ${oldWayTotal}ms`);
  logger.info(`   ❌ INEFFICIENT: 3 separate scans for the same data!`);

  // Test 2: NEW WAY (optimized) - Single scan with reuse
  logger.info('\n🚀 NEW WAY (After Optimization):');
  logger.info('   This shows the optimized behavior with single scan + reuse');
  
  const newWayStart = Date.now();
  
  // New optimized way: scan once and reuse
  logger.info('   1. Single scanForOpportunities() call');
  const optimizedScanStart = Date.now();
  const opportunities = await dynamicFlashloanStrategy.scanForOpportunities();
  const optimizedScanTime = Date.now() - optimizedScanStart;
  
  logger.info('   2. Reuse opportunities for market conditions (no additional scan)');
  const totalOpportunities = opportunities.length;
  const bestProfit = opportunities.length > 0 ? ethers.formatEther(opportunities[0].netProfit) : '0';
  const bestStrategy = opportunities.length > 0 ? opportunities[0].strategy : 'none';
  
  logger.info('   3. Reuse opportunities for profitability check (no additional scan)');
  const isProfitable = opportunities.length > 0 && opportunities[0].netProfit > BigInt(0);
  
  logger.info('   4. Pass opportunities to executeBestOpportunity (no additional scan)');
  const executionStart = Date.now();
  // Note: We're not actually executing, just demonstrating the optimization
  // const success = await dynamicFlashloanStrategy.executeBestOpportunity(opportunities);
  const executionTime = Date.now() - executionStart;
  
  const newWayTotal = Date.now() - newWayStart;
  
  logger.info(`   Results: Found ${totalOpportunities} opportunities`);
  logger.info(`   Best profit: ${bestProfit} ETH (${bestStrategy})`);
  logger.info(`   Is profitable: ${isProfitable}`);
  logger.info(`   Scan time: ${optimizedScanTime}ms`);
  logger.info(`   Total time: ${newWayTotal}ms`);
  logger.info(`   ✅ EFFICIENT: 1 scan reused for all operations!`);

  // Performance comparison
  logger.info('\n📈 PERFORMANCE COMPARISON:');
  logger.info('='.repeat(40));
  logger.info(`   Old way total time: ${oldWayTotal}ms`);
  logger.info(`   New way total time: ${newWayTotal}ms`);
  
  const timeSaved = oldWayTotal - newWayTotal;
  const percentImprovement = ((timeSaved / oldWayTotal) * 100).toFixed(1);
  
  logger.info(`   Time saved: ${timeSaved}ms`);
  logger.info(`   Performance improvement: ${percentImprovement}%`);
  
  if (timeSaved > 0) {
    logger.info(`   🎉 SUCCESS: Optimization reduced scanning time by ${percentImprovement}%!`);
  } else {
    logger.info(`   ⚠️  Note: Improvement may vary based on network conditions`);
  }

  logger.info('\n🔧 TECHNICAL DETAILS:');
  logger.info('   - Modified executeBestOpportunity() to accept optional pre-scanned opportunities');
  logger.info('   - Updated bot.ts to scan once and pass results to execution');
  logger.info('   - Eliminated redundant calls to scanForOpportunities()');
  logger.info('   - Maintained backward compatibility for standalone calls');
  
  logger.info('\n✅ Optimization demonstration completed!');
}

// Run the demonstration
if (require.main === module) {
  demonstrateOptimization().catch(console.error);
}

export { demonstrateOptimization };
