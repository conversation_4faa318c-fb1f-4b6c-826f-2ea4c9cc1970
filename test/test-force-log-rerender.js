#!/usr/bin/env node

/**
 * Test script to demonstrate force log rerender functionality
 * This script tests the immediate rerendering of the right side panel after each log update
 */

const { splitScreenDashboard } = require('../dist/utils/splitScreenDashboard');
const { logger } = require('../dist/utils/logger');
const { logger } = require('../dist/utils/logger');
const { ethers } = require('ethers');

console.log('🧪 Testing Force Log Rerender Functionality');
console.log('This test will demonstrate immediate log panel updates');
console.log('Press Ctrl+C to exit\n');

// Initialize dashboard with force rerender enabled (default)
const testData = {
  currentBlock: 18500000,
  networkName: 'Sepolia Testnet',
  isRunning: true,
  uptime: Date.now(),
  lastActivity: Date.now(),
  flashloanEnabled: true,
  mevShareEnabled: false,
  arbitrageEnabled: true,
  totalTransactions: 0,
  relevantTransactions: 0,
  opportunitiesFound: 0,
  opportunitiesExecuted: 0,
  totalProfit: ethers.parseEther('0'),
  avgGasPrice: ethers.parseUnits('25', 'gwei'),
  configuration: {
    tokenPairs: ['USDC/WETH', 'DAI/USDC'],
    dexes: ['Uniswap V3', 'Curve'],
    minProfitThreshold: '0.01',
    maxGasPrice: '50'
  },
  successfulTransactions: [],
  errors: 0
};

// Start the dashboard
splitScreenDashboard.updateDashboardData(testData);
splitScreenDashboard.start();

// Display current configuration
console.log(`Force Log Rerender: ${splitScreenDashboard.getForceLogRerender()}`);

let testPhase = 1;
let logCount = 0;

const runTest = () => {
  switch (testPhase) {
    case 1:
      // Phase 1: Test rapid log updates with force rerender enabled
      logger.info('=== PHASE 1: Testing Force Rerender (ENABLED) ===');
      logger.info('Sending rapid log updates - should see immediate updates');
      
      // Send 5 rapid logs
      for (let i = 1; i <= 5; i++) {
        setTimeout(() => {
          logger.info(`Rapid log update #${i}`, { timestamp: Date.now() });
          logCount++;
        }, i * 100); // 100ms intervals
      }
      
      setTimeout(() => {
        testPhase = 2;
        runTest();
      }, 2000);
      break;
      
    case 2:
      // Phase 2: Test with force rerender disabled
      logger.info('=== PHASE 2: Testing Force Rerender (DISABLED) ===');
      logger.info('Disabling force rerender - updates should be slower');
      
      splitScreenDashboard.setForceLogRerender(false);
      
      // Send 5 rapid logs
      for (let i = 1; i <= 5; i++) {
        setTimeout(() => {
          logger.warn(`Slow update log #${i}`, { timestamp: Date.now() });
          logCount++;
        }, i * 100); // 100ms intervals
      }
      
      setTimeout(() => {
        testPhase = 3;
        runTest();
      }, 2000);
      break;
      
    case 3:
      // Phase 3: Test throttling
      logger.info('=== PHASE 3: Testing Throttling ===');
      logger.info('Re-enabling force rerender with different throttle settings');
      
      splitScreenDashboard.setForceLogRerender(true);
      splitScreenDashboard.setLogRenderThrottle(200); // 200ms throttle
      
      // Send very rapid logs
      for (let i = 1; i <= 10; i++) {
        setTimeout(() => {
          logger.debug(`Throttled log #${i}`, { throttle: '200ms' });
          logCount++;
        }, i * 50); // 50ms intervals (faster than throttle)
      }
      
      setTimeout(() => {
        testPhase = 4;
        runTest();
      }, 3000);
      break;
      
    case 4:
      // Phase 4: Test different log types
      logger.info('=== PHASE 4: Testing Different Log Types ===');
      
      splitScreenDashboard.setLogRenderThrottle(50); // Reset to default
      
      setTimeout(() => logger.success('Success message test'), 200);
      setTimeout(() => logger.error('Error message test', new Error('Test error')), 400);
      setTimeout(() => logger.systemStatus('System status test', { liquidity: '123.456' }), 600);
      setTimeout(() => logger.botStatus('Bot status test', { seconds: 5 }), 800);
      setTimeout(() => logger.warn('Warning message test', { gasPrice: '45 gwei' }), 1000);
      
      setTimeout(() => {
        testPhase = 5;
        runTest();
      }, 2000);
      break;
      
    case 5:
      // Phase 5: Performance test
      logger.info('=== PHASE 5: Performance Test ===');
      logger.info('Testing high-frequency log updates');
      
      let perfCount = 0;
      const perfInterval = setInterval(() => {
        logger.info(`Performance test log #${++perfCount}`, { 
          timestamp: Date.now(),
          performance: true 
        });
        logCount++;
        
        if (perfCount >= 20) {
          clearInterval(perfInterval);
          setTimeout(() => {
            logger.info('=== TEST COMPLETED ===');
            logger.info(`Total logs sent: ${logCount}`);
            logger.info('Force rerender functionality test completed successfully!');
          }, 1000);
        }
      }, 100);
      break;
  }
};

// Update dashboard stats periodically
const statsInterval = setInterval(() => {
  testData.totalTransactions += Math.floor(Math.random() * 3);
  testData.lastActivity = Date.now();
  testData.uptime = Date.now() - testData.uptime;
  splitScreenDashboard.updateDashboardData(testData);
}, 2000);

// Start the test
setTimeout(runTest, 1000);

// Handle graceful shutdown
process.on('SIGINT', () => {
  clearInterval(statsInterval);
  splitScreenDashboard.stop();
  console.log('\n👋 Force log rerender test completed!');
  process.exit(0);
});

// Keep the process running
process.stdin.resume();
