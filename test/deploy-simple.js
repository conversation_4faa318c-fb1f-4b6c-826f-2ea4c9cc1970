const { ethers } = require('ethers');
const fs = require('fs');

async function deploySimple() {
    console.log('🚀 Deploying fixed contract to Hardhat fork...');
    
    // Connect directly to the fork
    const provider = new ethers.JsonRpcProvider('http://127.0.0.1:8547');
    const wallet = new ethers.Wallet('0xac0974bec39a17e36ba4a6b4d238ff944bacb478cbed5efcae784d7bf4f2ff80', provider);
    
    console.log(`📋 Deployment Configuration:`);
    console.log(`   Provider: http://127.0.0.1:8547`);
    console.log(`   Deployer: ${wallet.address}`);
    console.log(`   Balance: ${ethers.formatEther(await provider.getBalance(wallet.address))} ETH`);
    
    // Contract parameters
    const aavePool = '******************************************';
    const balancerVault = '******************************************';
    
    console.log(`📋 Contract Parameters:`);
    console.log(`   Aave Pool: ${aavePool}`);
    console.log(`   Balancer Vault: ${balancerVault}`);
    
    try {
        // Load contract artifact
        const contractArtifact = JSON.parse(
            fs.readFileSync('./artifacts/contracts/HybridFlashloanArbitrageFixed.sol/HybridFlashloanArbitrageFixed.json', 'utf8')
        );
        
        // Create contract factory
        const contractFactory = new ethers.ContractFactory(
            contractArtifact.abi,
            contractArtifact.bytecode,
            wallet
        );
        
        console.log('⏳ Deploying contract...');
        const contract = await contractFactory.deploy(aavePool, balancerVault);
        
        // Wait for deployment
        await contract.waitForDeployment();
        const contractAddress = await contract.getAddress();
        
        console.log(`✅ Contract deployed successfully!`);
        console.log(`📍 Contract address: ${contractAddress}`);
        
        // Verify contract state
        console.log('\n🔍 Verifying contract state...');
        
        const owner = await contract.owner();
        const chainId = await contract.CHAIN_ID();
        const v2Router = await contract.UNISWAP_V2_ROUTER();
        const v3Router = await contract.UNISWAP_V3_ROUTER();
        const aavePoolAddr = await contract.AAVE_POOL();
        const balancerVaultAddr = await contract.BALANCER_VAULT();
        
        console.log(`   Owner: ${owner}`);
        console.log(`   Chain ID: ${chainId}`);
        console.log(`   V2 Router: ${v2Router}`);
        console.log(`   V3 Router: ${v3Router}`);
        console.log(`   Aave Pool: ${aavePoolAddr}`);
        console.log(`   Balancer Vault: ${balancerVaultAddr}`);
        
        // Test router support
        const v2Supported = await contract.supportedRouters(v2Router);
        const v3Supported = await contract.supportedRouters(v3Router);
        
        console.log(`   V2 Router supported: ${v2Supported ? '✅' : '❌'}`);
        console.log(`   V3 Router supported: ${v3Supported ? '✅' : '❌'}`);
        
        // Test the failing transaction
        console.log('\n🧪 Testing the previously failing transaction...');
        
        const wethAddress = '******************************************';
        const daiAddress = '******************************************';
        
        const testParams = ethers.AbiCoder.defaultAbiCoder().encode(
            ['address[]', 'address[]', 'address', 'address', 'uint24[]', 'uint256', 'uint8', 'uint256', 'uint256'],
            [
                [wethAddress, daiAddress],               // buyPath: WETH → DAI
                [daiAddress, wethAddress],               // sellPath: DAI → WETH
                v3Router,                                // buyDex (V3)
                v2Router,                                // sellDex (V2)
                [3000],                                  // v3Fees (0.3%)
                ethers.parseEther('0.00001'),            // minProfit
                0,                                       // provider (AAVE)
                1000,                                    // slippageToleranceBps (10%)
                ethers.parseUnits('200', 'gwei')         // maxGasCostWei
            ]
        );
        
        try {
            await contract.executeOptimalFlashloan.staticCall(
                wethAddress,
                ethers.parseEther('2.0'),
                testParams
            );
            
            console.log(`   ✅ Transaction validation passed!`);
            
        } catch (error) {
            const errorMsg = error.message;
            console.log(`   Result: ${errorMsg.split('(')[0]}`);
            
            if (errorMsg.includes('require(false)')) {
                console.log(`   ❌ Still getting require(false) - more investigation needed`);
            } else if (errorMsg.includes('Arbitrage resulted in loss')) {
                console.log(`   ✅ SUCCESS! Fixed the require(false) error!`);
                console.log(`   💡 Now getting proper arbitrage validation instead of generic require(false)`);
            } else if (errorMsg.includes('Invalid V3 fees')) {
                console.log(`   ❌ V3 fees validation still failing`);
            } else {
                console.log(`   💡 Different error - progress made!`);
            }
        }
        
        // Test profitability check
        console.log('\n🧪 Testing profitability check function...');
        
        try {
            const [profitable, expectedProfit, gasEstimate] = await contract.checkProfitability(
                wethAddress,
                ethers.parseEther('2.0'),
                testParams
            );
            
            console.log(`   ✅ Profitability check working:`);
            console.log(`      Profitable: ${profitable}`);
            console.log(`      Expected profit: ${ethers.formatEther(expectedProfit)} ETH`);
            console.log(`      Gas estimate: ${ethers.formatUnits(gasEstimate, 'gwei')} gwei`);
            
        } catch (error) {
            console.log(`   Profitability check result: ${error.message.split('(')[0]}`);
        }
        
        console.log('\n🎯 Contract ready for testing!');
        console.log(`📝 Contract Address: ${contractAddress}`);
        
        return contractAddress;
        
    } catch (error) {
        console.log(`❌ Deployment failed: ${error.message}`);
        throw error;
    }
}

deploySimple().catch(console.error);
