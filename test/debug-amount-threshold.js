const { ethers } = require('ethers');

async function debugAmountThreshold() {
    console.log('🔍 Finding the amount threshold for enum conversion error...');
    
    // Connect to Hardhat fork
    const provider = new ethers.JsonRpcProvider('http://127.0.0.1:8547');
    const wallet = new ethers.Wallet('0xac0974bec39a17e36ba4a6b4d238ff944bacb478cbed5efcae784d7bf4f2ff80', provider);
    
    // Deploy fresh contract
    const aavePoolAddressesProvider = '******************************************';
    const balancerVault = '******************************************';
    
    const fs = require('fs');
    const contractArtifact = JSON.parse(fs.readFileSync('./artifacts/contracts/HybridFlashloanArbitrage.sol/HybridFlashloanArbitrage.json', 'utf8'));
    
    const contractFactory = new ethers.ContractFactory(
        contractArtifact.abi,
        contractArtifact.bytecode,
        wallet
    );
    
    const contract = await contractFactory.deploy(aavePoolAddressesProvider, balancerVault);
    await contract.waitForDeployment();
    const contractAddress = await contract.getAddress();
    
    console.log(`✅ Contract deployed at: ${contractAddress}`);
    
    const v2Router = await contract.UNISWAP_V2_ROUTER();
    const v3Router = await contract.UNISWAP_V3_ROUTER();
    
    const wethAddress = '******************************************';
    const usdcAddress = '******************************************';
    
    console.log('\n🧪 Testing different flashloan amounts to find threshold...');
    
    // Test amounts from very small to large
    const testAmounts = [
        { amount: ethers.parseUnits('1', 'wei'), label: '1 wei' },
        { amount: ethers.parseUnits('1000', 'wei'), label: '1000 wei' },
        { amount: ethers.parseUnits('1', 'gwei'), label: '1 gwei' },
        { amount: ethers.parseUnits('1000', 'gwei'), label: '1000 gwei' },
        { amount: ethers.parseEther('0.000001'), label: '0.000001 ETH' },
        { amount: ethers.parseEther('0.00001'), label: '0.00001 ETH' },
        { amount: ethers.parseEther('0.0001'), label: '0.0001 ETH' },
        { amount: ethers.parseEther('0.001'), label: '0.001 ETH' },
        { amount: ethers.parseEther('0.01'), label: '0.01 ETH' },
        { amount: ethers.parseEther('0.02'), label: '0.02 ETH (bot amount)' },
        { amount: ethers.parseEther('0.1'), label: '0.1 ETH' },
        { amount: ethers.parseEther('1'), label: '1 ETH' }
    ];
    
    let thresholdFound = false;
    let workingAmount = null;
    let failingAmount = null;
    
    for (const test of testAmounts) {
        console.log(`\n   Testing ${test.label}...`);
        
        const arbitrageParams = ethers.AbiCoder.defaultAbiCoder().encode(
            ['address', 'address', 'address', 'address', 'uint24', 'uint256', 'uint8'],
            [
                wethAddress,                     // tokenA
                usdcAddress,                     // tokenB
                v2Router,                        // buyDex
                v3Router,                        // sellDex
                3000,                            // v3Fee
                ethers.parseEther('0.0001'),     // minProfit
                0                                // FlashloanProvider.AAVE
            ]
        );
        
        try {
            await contract.executeOptimalFlashloan.staticCall(
                wethAddress,
                test.amount,
                arbitrageParams
            );
            
            console.log(`      ✅ SUCCESS - No enum conversion error`);
            workingAmount = test;
            
        } catch (error) {
            const errorMsg = error.message.split('(')[0];
            console.log(`      ❌ ${errorMsg}`);
            
            if (error.data && error.data.startsWith('0x4e487b71')) {
                const panicCode = parseInt(error.data.slice(10, 74), 16);
                if (panicCode === 17) {
                    console.log(`         🚨 ENUM_CONVERSION_ERROR at ${test.label}`);
                    if (workingAmount && !thresholdFound) {
                        console.log(`         📊 THRESHOLD FOUND: Between ${workingAmount.label} and ${test.label}`);
                        thresholdFound = true;
                        failingAmount = test;
                    }
                }
            } else if (errorMsg.includes('INSUFFICIENT_OUTPUT_AMOUNT')) {
                console.log(`         ✅ Trade execution error (not enum error) - struct encoding works`);
                workingAmount = test;
            }
        }
    }
    
    if (thresholdFound) {
        console.log(`\n🎯 THRESHOLD ANALYSIS:`);
        console.log(`   Last working amount: ${workingAmount.label}`);
        console.log(`   First failing amount: ${failingAmount.label}`);
        
        // Binary search to find exact threshold
        await binarySearchThreshold(contract, wethAddress, usdcAddress, v2Router, v3Router, workingAmount.amount, failingAmount.amount);
    }
    
    // Test if the issue is specific to the amount or something else
    console.log('\n🔬 Testing if issue is amount-specific or contract logic...');
    
    // Test with working amount but different parameters
    if (workingAmount) {
        console.log(`\n   Testing ${workingAmount.label} with different providers...`);
        
        // Test with Balancer provider
        const balancerParams = ethers.AbiCoder.defaultAbiCoder().encode(
            ['address', 'address', 'address', 'address', 'uint24', 'uint256', 'uint8'],
            [
                wethAddress,
                usdcAddress,
                v2Router,
                v3Router,
                3000,
                ethers.parseEther('0.0001'),
                1 // FlashloanProvider.BALANCER
            ]
        );
        
        try {
            await contract.executeOptimalFlashloan.staticCall(
                wethAddress,
                workingAmount.amount,
                balancerParams
            );
            console.log(`      ✅ BALANCER provider works with ${workingAmount.label}`);
        } catch (error) {
            console.log(`      ❌ BALANCER provider fails: ${error.message.split('(')[0]}`);
            if (error.data && error.data.startsWith('0x4e487b71')) {
                const panicCode = parseInt(error.data.slice(10, 74), 16);
                if (panicCode === 17) {
                    console.log(`         🚨 ENUM_CONVERSION_ERROR even with working amount!`);
                }
            }
        }
    }
    
    console.log('\n🏁 Amount threshold analysis completed!');
}

async function binarySearchThreshold(contract, wethAddress, usdcAddress, v2Router, v3Router, workingAmount, failingAmount) {
    console.log('\n🔍 Binary search for exact threshold...');
    
    let low = workingAmount;
    let high = failingAmount;
    let iterations = 0;
    const maxIterations = 20;
    
    while (iterations < maxIterations && high - low > 1n) {
        iterations++;
        const mid = (low + high) / 2n;
        
        console.log(`   Iteration ${iterations}: Testing ${ethers.formatEther(mid)} ETH`);
        
        const arbitrageParams = ethers.AbiCoder.defaultAbiCoder().encode(
            ['address', 'address', 'address', 'address', 'uint24', 'uint256', 'uint8'],
            [wethAddress, usdcAddress, v2Router, v3Router, 3000, ethers.parseEther('0.0001'), 0]
        );
        
        try {
            await contract.executeOptimalFlashloan.staticCall(wethAddress, mid, arbitrageParams);
            console.log(`      ✅ Works`);
            low = mid;
        } catch (error) {
            if (error.data && error.data.startsWith('0x4e487b71')) {
                const panicCode = parseInt(error.data.slice(10, 74), 16);
                if (panicCode === 17) {
                    console.log(`      ❌ ENUM_CONVERSION_ERROR`);
                    high = mid;
                } else {
                    console.log(`      ❌ Other panic: ${panicCode}`);
                    low = mid;
                }
            } else {
                console.log(`      ❌ Trade error (not enum)`);
                low = mid;
            }
        }
    }
    
    console.log(`\n📊 EXACT THRESHOLD:`);
    console.log(`   Last working: ${ethers.formatEther(low)} ETH`);
    console.log(`   First failing: ${ethers.formatEther(high)} ETH`);
    console.log(`   Difference: ${ethers.formatEther(high - low)} ETH`);
}

debugAmountThreshold().catch(console.error);
