const { ethers } = require('ethers');

async function debugExecutionFlow() {
    console.log('🔍 Debugging contract execution flow to find overflow location...');
    
    // Connect to Hardhat fork
    const provider = new ethers.JsonRpcProvider('http://127.0.0.1:8547');
    const wallet = new ethers.Wallet('0xac0974bec39a17e36ba4a6b4d238ff944bacb478cbed5efcae784d7bf4f2ff80', provider);
    
    // Create a modified contract with debug functions
    console.log('\n🚀 Creating debug version of contract...');
    
    const debugContractCode = `
        // SPDX-License-Identifier: MIT
        pragma solidity ^0.8.19;
        
        import "@openzeppelin/contracts/access/Ownable.sol";
        import "@openzeppelin/contracts/token/ERC20/IERC20.sol";
        
        contract DebugFlashloanContract is Ownable {
            enum FlashloanProvider { AAVE, BALANCER }
            
            struct ArbitrageParams {
                address tokenA;
                address tokenB;
                address buyDex;
                address sellDex;
                uint24 v3Fee;
                uint256 minProfit;
                FlashloanProvider provider;
            }
            
            event DebugStep(string step, uint256 value);
            
            constructor() Ownable(msg.sender) {}
            
            function testStep1_Decode(bytes calldata params) external returns (ArbitrageParams memory) {
                emit DebugStep("decode_start", 0);
                ArbitrageParams memory arbParams = abi.decode(params, (ArbitrageParams));
                emit DebugStep("decode_success", uint256(arbParams.provider));
                return arbParams;
            }
            
            function testStep2_Validation(
                address asset,
                uint256 amount,
                bytes calldata params
            ) external returns (bool) {
                emit DebugStep("validation_start", amount);
                
                ArbitrageParams memory arbParams = abi.decode(params, (ArbitrageParams));
                emit DebugStep("decode_in_validation", uint256(arbParams.provider));
                
                require(asset == arbParams.tokenA, "Asset mismatch");
                emit DebugStep("asset_check_passed", 0);
                
                require(amount > 0, "Invalid amount");
                emit DebugStep("amount_check_passed", amount);
                
                return true;
            }
            
            function testStep3_ProviderSelection(
                uint256 amount,
                bytes calldata params
            ) external returns (FlashloanProvider) {
                emit DebugStep("provider_selection_start", amount);
                
                ArbitrageParams memory arbParams = abi.decode(params, (ArbitrageParams));
                emit DebugStep("provider_from_params", uint256(arbParams.provider));
                
                // Test the provider enum conversion explicitly
                FlashloanProvider provider = arbParams.provider;
                emit DebugStep("provider_assigned", uint256(provider));
                
                if (provider == FlashloanProvider.BALANCER) {
                    emit DebugStep("provider_is_balancer", 1);
                } else {
                    emit DebugStep("provider_is_aave", 0);
                }
                
                return provider;
            }
            
            function testStep4_TokenApproval(
                address token,
                address spender,
                uint256 amount
            ) external returns (bool) {
                emit DebugStep("approval_start", amount);
                
                // Test if the issue is in token approval
                try {
                    IERC20(token).approve(spender, amount);
                    emit DebugStep("approval_success", amount);
                    return true;
                } catch {
                    emit DebugStep("approval_failed", amount);
                    return false;
                }
            }
            
            function testStep5_BalanceCheck(
                address token,
                uint256 expectedAmount
            ) external view returns (uint256) {
                uint256 balance = IERC20(token).balanceOf(address(this));
                return balance;
            }
        }
    `;
    
    // Write the debug contract
    const fs = require('fs');
    fs.writeFileSync('./contracts/DebugFlashloanContract.sol', debugContractCode);
    
    // Compile
    const { execSync } = require('child_process');
    try {
        execSync('npx hardhat compile', { stdio: 'inherit' });
    } catch (error) {
        console.log('Compilation error, but continuing...');
    }
    
    // Deploy debug contract
    const debugArtifact = JSON.parse(fs.readFileSync('./artifacts/contracts/DebugFlashloanContract.sol/DebugFlashloanContract.json', 'utf8'));
    
    const debugFactory = new ethers.ContractFactory(
        debugArtifact.abi,
        debugArtifact.bytecode,
        wallet
    );
    
    const debugContract = await debugFactory.deploy();
    await debugContract.waitForDeployment();
    const debugAddress = await debugContract.getAddress();
    
    console.log(`✅ Debug contract deployed at: ${debugAddress}`);
    
    // Test parameters
    const wethAddress = '******************************************';
    const usdcAddress = '******************************************';
    const v2Router = '******************************************';
    const v3Router = '******************************************';
    
    const testParams = ethers.AbiCoder.defaultAbiCoder().encode(
        ['address', 'address', 'address', 'address', 'uint24', 'uint256', 'uint8'],
        [wethAddress, usdcAddress, v2Router, v3Router, 3000, ethers.parseEther('0.0001'), 0]
    );
    
    const workingAmount = ethers.parseUnits('1000', 'wei');
    const failingAmount = ethers.parseUnits('1', 'gwei');
    
    console.log('\n🧪 Testing each step with working amount (1000 wei)...');
    
    // Step 1: Decode
    try {
        const result1 = await debugContract.testStep1_Decode(testParams);
        console.log(`   ✅ Step 1 (Decode): SUCCESS - Provider: ${result1.provider}`);
    } catch (error) {
        console.log(`   ❌ Step 1 (Decode): ${error.message.split('(')[0]}`);
    }
    
    // Step 2: Validation
    try {
        await debugContract.testStep2_Validation(wethAddress, workingAmount, testParams);
        console.log(`   ✅ Step 2 (Validation): SUCCESS`);
    } catch (error) {
        console.log(`   ❌ Step 2 (Validation): ${error.message.split('(')[0]}`);
    }
    
    // Step 3: Provider Selection
    try {
        const provider = await debugContract.testStep3_ProviderSelection(workingAmount, testParams);
        console.log(`   ✅ Step 3 (Provider Selection): SUCCESS - Provider: ${provider}`);
    } catch (error) {
        console.log(`   ❌ Step 3 (Provider Selection): ${error.message.split('(')[0]}`);
    }
    
    console.log('\n🧪 Testing each step with failing amount (1 gwei)...');
    
    // Step 1: Decode
    try {
        const result1 = await debugContract.testStep1_Decode(testParams);
        console.log(`   ✅ Step 1 (Decode): SUCCESS - Provider: ${result1.provider}`);
    } catch (error) {
        console.log(`   ❌ Step 1 (Decode): ${error.message.split('(')[0]}`);
    }
    
    // Step 2: Validation
    try {
        await debugContract.testStep2_Validation(wethAddress, failingAmount, testParams);
        console.log(`   ✅ Step 2 (Validation): SUCCESS`);
    } catch (error) {
        console.log(`   ❌ Step 2 (Validation): ${error.message.split('(')[0]}`);
    }
    
    // Step 3: Provider Selection
    try {
        const provider = await debugContract.testStep3_ProviderSelection(failingAmount, testParams);
        console.log(`   ✅ Step 3 (Provider Selection): SUCCESS - Provider: ${provider}`);
    } catch (error) {
        console.log(`   ❌ Step 3 (Provider Selection): ${error.message.split('(')[0]}`);
        
        if (error.data && error.data.startsWith('0x4e487b71')) {
            const panicCode = parseInt(error.data.slice(10, 74), 16);
            if (panicCode === 17) {
                console.log(`      🚨 ENUM_CONVERSION_ERROR found in Step 3!`);
            }
        }
    }
    
    // Test if the issue is related to the specific amount value
    console.log('\n🔬 Testing if the issue is amount-related...');
    
    // Test with the exact threshold amounts
    const thresholdAmounts = [
        ethers.parseUnits('224029361', 'wei'), // Last working
        ethers.parseUnits('224030315', 'wei')  // First failing
    ];
    
    for (let i = 0; i < thresholdAmounts.length; i++) {
        const amount = thresholdAmounts[i];
        const label = i === 0 ? 'Last working' : 'First failing';
        
        console.log(`\n   Testing ${label} (${ethers.formatEther(amount)} ETH):`);
        
        try {
            const provider = await debugContract.testStep3_ProviderSelection(amount, testParams);
            console.log(`      ✅ Provider Selection: SUCCESS - Provider: ${provider}`);
        } catch (error) {
            console.log(`      ❌ Provider Selection: ${error.message.split('(')[0]}`);
            
            if (error.data && error.data.startsWith('0x4e487b71')) {
                const panicCode = parseInt(error.data.slice(10, 74), 16);
                if (panicCode === 17) {
                    console.log(`         🚨 ENUM_CONVERSION_ERROR at threshold!`);
                }
            }
        }
    }
    
    console.log('\n🏁 Execution flow debugging completed!');
    
    // Clean up
    try {
        fs.unlinkSync('./contracts/DebugFlashloanContract.sol');
    } catch (e) {}
}

debugExecutionFlow().catch(console.error);
