const { ethers } = require('ethers');

async function debugEnumValue() {
    console.log('🔍 Debugging the enum value in parameters...');
    
    // Recreate the exact same parameters from the test
    const wethAddress = '******************************************';
    const usdcAddress = '******************************************';
    const v3Router = '******************************************';
    const v2Router = '******************************************';
    
    console.log('📋 Parameter Analysis:');
    console.log(`   WETH: ${wethAddress}`);
    console.log(`   USDC: ${usdcAddress}`);
    console.log(`   V3 Router: ${v3Router}`);
    console.log(`   V2 Router: ${v2Router}`);
    
    // Test different enum values
    const enumTests = [
        { value: 0, name: 'AAVE (0)' },
        { value: 1, name: 'BALANCER (1)' },
        { value: 2, name: 'INVALID (2)' },
        { value: 255, name: 'INVALID (255)' }
    ];
    
    for (const enumTest of enumTests) {
        console.log(`\n🧪 Testing enum value: ${enumTest.name}`);
        
        const testParams = ethers.AbiCoder.defaultAbiCoder().encode(
            ['address', 'address', 'address', 'address', 'uint24', 'uint256', 'uint8'],
            [wethAddress, usdcAddress, v3Router, v2Router, 3000, ethers.parseEther('0.0001'), enumTest.value]
        );
        
        console.log(`   Encoded params: ${testParams}`);
        
        // Try to decode the parameters
        try {
            const decoded = ethers.AbiCoder.defaultAbiCoder().decode(
                ['address', 'address', 'address', 'address', 'uint24', 'uint256', 'uint8'],
                testParams
            );
            
            console.log(`   ✅ Decoding successful:`);
            console.log(`      tokenA: ${decoded[0]}`);
            console.log(`      tokenB: ${decoded[1]}`);
            console.log(`      buyDex: ${decoded[2]}`);
            console.log(`      sellDex: ${decoded[3]}`);
            console.log(`      v3Fee: ${decoded[4]}`);
            console.log(`      minProfit: ${ethers.formatEther(decoded[5])} ETH`);
            console.log(`      provider: ${decoded[6]} (raw value)`);
            
            // Test enum conversion
            if (decoded[6] === 0n) {
                console.log(`      ✅ Enum: AAVE (valid)`);
            } else if (decoded[6] === 1n) {
                console.log(`      ✅ Enum: BALANCER (valid)`);
            } else {
                console.log(`      ❌ Enum: INVALID (${decoded[6]}) - this will cause panic!`);
            }
            
        } catch (error) {
            console.log(`   ❌ Decoding failed: ${error.message}`);
        }
    }
    
    // Check if there's an issue with the specific router addresses
    console.log('\n🔍 Checking router addresses...');
    
    // Test with different router combinations
    const routerTests = [
        { name: 'V3 → V2 (original)', buy: v3Router, sell: v2Router },
        { name: 'V2 → V3', buy: v2Router, sell: v3Router },
        { name: 'V2 → V2', buy: v2Router, sell: v2Router },
        { name: 'V3 → V3', buy: v3Router, sell: v3Router }
    ];
    
    for (const routerTest of routerTests) {
        console.log(`\n   Testing ${routerTest.name}:`);
        
        const testParams = ethers.AbiCoder.defaultAbiCoder().encode(
            ['address', 'address', 'address', 'address', 'uint24', 'uint256', 'uint8'],
            [wethAddress, usdcAddress, routerTest.buy, routerTest.sell, 3000, ethers.parseEther('0.0001'), 0]
        );
        
        try {
            const decoded = ethers.AbiCoder.defaultAbiCoder().decode(
                ['address', 'address', 'address', 'address', 'uint24', 'uint256', 'uint8'],
                testParams
            );
            
            console.log(`      ✅ ${routerTest.name}: Decoding successful, provider: ${decoded[6]}`);
            
        } catch (error) {
            console.log(`      ❌ ${routerTest.name}: ${error.message}`);
        }
    }
    
    // Test if the issue is with the specific values
    console.log('\n🔍 Testing with different parameter values...');
    
    const valueTests = [
        { name: 'Zero minProfit', minProfit: 0 },
        { name: 'Large minProfit', minProfit: ethers.parseEther('1000') },
        { name: 'Zero v3Fee', v3Fee: 0 },
        { name: 'Large v3Fee', v3Fee: 1000000 }
    ];
    
    for (const valueTest of valueTests) {
        console.log(`\n   Testing ${valueTest.name}:`);
        
        const testParams = ethers.AbiCoder.defaultAbiCoder().encode(
            ['address', 'address', 'address', 'address', 'uint24', 'uint256', 'uint8'],
            [
                wethAddress, 
                usdcAddress, 
                v3Router, 
                v2Router, 
                valueTest.v3Fee || 3000, 
                valueTest.minProfit !== undefined ? valueTest.minProfit : ethers.parseEther('0.0001'), 
                0
            ]
        );
        
        try {
            const decoded = ethers.AbiCoder.defaultAbiCoder().decode(
                ['address', 'address', 'address', 'address', 'uint24', 'uint256', 'uint8'],
                testParams
            );
            
            console.log(`      ✅ ${valueTest.name}: Success, provider: ${decoded[6]}`);
            
        } catch (error) {
            console.log(`      ❌ ${valueTest.name}: ${error.message}`);
        }
    }
    
    console.log('\n🏁 Enum value debugging completed!');
}

debugEnumValue().catch(console.error);
