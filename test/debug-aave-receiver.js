const { ethers } = require('ethers');

async function debugAaveReceiver() {
    console.log('🔍 Debugging Aave flashloan receiver implementation...');
    
    // Connect to Hardhat fork
    const provider = new ethers.JsonRpcProvider('http://127.0.0.1:8547');
    const wallet = new ethers.Wallet('0xac0974bec39a17e36ba4a6b4d238ff944bacb478cbed5efcae784d7bf4f2ff80', provider);
    
    // Create a minimal Aave flashloan receiver for testing
    const minimalReceiverCode = `
        // SPDX-License-Identifier: MIT
        pragma solidity ^0.8.19;
        
        import "@aave/core-v3/contracts/flashloan/base/FlashLoanSimpleReceiverBase.sol";
        import "@aave/core-v3/contracts/interfaces/IPoolAddressesProvider.sol";
        import "@openzeppelin/contracts/access/Ownable.sol";
        import "@openzeppelin/contracts/token/ERC20/IERC20.sol";
        
        contract MinimalAaveReceiver is FlashLoanSimpleReceiverBase, Ownable {
            
            constructor(IPoolAddressesProvider _addressProvider) 
                FlashLoanSimpleReceiverBase(_addressProvider)
                Ownable(msg.sender)
            {}
            
            function executeOperation(
                address asset,
                uint256 amount,
                uint256 premium,
                address initiator,
                bytes calldata params
            ) external override returns (bool) {
                // Minimal implementation - just approve repayment
                uint256 amountToRepay = amount + premium;
                IERC20(asset).approve(address(POOL), amountToRepay);
                return true;
            }
            
            function testFlashloan(address asset, uint256 amount) external onlyOwner {
                POOL.flashLoanSimple(
                    address(this),
                    asset,
                    amount,
                    "",
                    0
                );
            }
        }
    `;
    
    // Write and compile the minimal receiver
    const fs = require('fs');
    fs.writeFileSync('./contracts/MinimalAaveReceiver.sol', minimalReceiverCode);
    
    const { execSync } = require('child_process');
    try {
        execSync('npx hardhat compile', { stdio: 'inherit' });
    } catch (error) {
        console.log('Compilation error, but continuing...');
    }
    
    // Deploy the minimal receiver
    console.log('\n🚀 Deploying minimal Aave receiver...');
    
    const aavePoolAddressesProvider = '******************************************';
    
    const minimalArtifact = JSON.parse(fs.readFileSync('./artifacts/contracts/MinimalAaveReceiver.sol/MinimalAaveReceiver.json', 'utf8'));
    
    const minimalFactory = new ethers.ContractFactory(
        minimalArtifact.abi,
        minimalArtifact.bytecode,
        wallet
    );
    
    const minimalContract = await minimalFactory.deploy(aavePoolAddressesProvider);
    await minimalContract.waitForDeployment();
    const minimalAddress = await minimalContract.getAddress();
    
    console.log(`✅ Minimal receiver deployed at: ${minimalAddress}`);
    
    // Test the minimal receiver
    console.log('\n🧪 Testing minimal Aave receiver...');
    
    const wethAddress = '******************************************';
    
    try {
        await minimalContract.testFlashloan.staticCall(wethAddress, ethers.parseEther('1'));
        console.log(`   ✅ Minimal receiver works!`);
    } catch (error) {
        console.log(`   ❌ Minimal receiver failed: ${error.message.split('(')[0]}`);
        
        if (error.message.includes('Invalid initiator')) {
            console.log(`      💡 Still getting "Invalid initiator" - this is an Aave configuration issue`);
        } else if (error.message.includes('INSUFFICIENT_BALANCE')) {
            console.log(`      ✅ Different error - the receiver interface works!`);
        }
    }
    
    // Now test our original contract with a simpler approach
    console.log('\n🧪 Testing original contract with direct Aave pool call...');
    
    // Deploy our original contract
    const balancerVault = '******************************************';
    
    const originalArtifact = JSON.parse(fs.readFileSync('./artifacts/contracts/HybridFlashloanArbitrage.sol/HybridFlashloanArbitrage.json', 'utf8'));
    
    const originalFactory = new ethers.ContractFactory(
        originalArtifact.abi,
        originalArtifact.bytecode,
        wallet
    );
    
    const originalContract = await originalFactory.deploy(aavePoolAddressesProvider, balancerVault);
    await originalContract.waitForDeployment();
    const originalAddress = await originalContract.getAddress();
    
    console.log(`✅ Original contract deployed at: ${originalAddress}`);
    
    // Test direct Aave pool call with our contract
    const aavePool = new ethers.Contract(
        '******************************************',
        [
            'function flashLoanSimple(address receiverAddress, address asset, uint256 amount, bytes calldata params, uint16 referralCode) external'
        ],
        wallet
    );
    
    try {
        await aavePool.flashLoanSimple.staticCall(
            originalAddress,
            wethAddress,
            ethers.parseEther('1'),
            '0x',
            0
        );
        
        console.log(`   ✅ Direct Aave call with original contract works!`);
        
    } catch (error) {
        console.log(`   ❌ Direct Aave call failed: ${error.message.split('(')[0]}`);
        
        if (error.message.includes('Invalid initiator')) {
            console.log(`      💡 The issue is that the wallet calling flashLoanSimple must be the initiator`);
            console.log(`      💡 But our contract is calling it internally, so the initiator should be the contract`);
        }
    }
    
    // Test the actual issue - check if the problem is in our _executeAaveFlashloan function
    console.log('\n🧪 Testing our contract\'s _executeAaveFlashloan function...');
    
    // Let's check what happens when we call our contract's function that internally calls Aave
    const testParams = ethers.AbiCoder.defaultAbiCoder().encode(
        ['address', 'address', 'address', 'address', 'uint24', 'uint256', 'uint8'],
        [wethAddress, wethAddress, originalAddress, originalAddress, 3000, ethers.parseEther('0.0001'), 0]
    );
    
    try {
        await originalContract.executeOptimalFlashloan.staticCall(
            wethAddress,
            ethers.parseEther('1'),
            testParams
        );
        
        console.log(`   ✅ Our contract's Aave flashloan works!`);
        
    } catch (error) {
        console.log(`   ❌ Our contract's Aave flashloan failed: ${error.message.split('(')[0]}`);
        
        // Let's check if the issue is in the try-catch block
        if (error.message.includes('Aave flashloan failed with unknown error')) {
            console.log(`      💡 The try-catch is catching an error from Aave`);
            console.log(`      💡 Let's remove the try-catch to see the actual error`);
        }
    }
    
    console.log('\n🏁 Aave receiver debugging completed!');
    
    // Clean up
    try {
        fs.unlinkSync('./contracts/MinimalAaveReceiver.sol');
    } catch (e) {}
}

debugAaveReceiver().catch(console.error);
