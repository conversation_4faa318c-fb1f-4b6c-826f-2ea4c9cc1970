const { ethers } = require('ethers');

async function debugNoExternalCalls() {
    console.log('🔍 Testing contract without external flashloan calls...');
    
    // Connect to Hardhat fork
    const provider = new ethers.JsonRpcProvider('http://127.0.0.1:8547');
    const wallet = new ethers.Wallet('0xac0974bec39a17e36ba4a6b4d238ff944bacb478cbed5efcae784d7bf4f2ff80', provider);
    
    // Create a test contract that simulates the logic without external calls
    const testContractCode = `
        // SPDX-License-Identifier: MIT
        pragma solidity ^0.8.19;
        
        import "@openzeppelin/contracts/access/Ownable.sol";
        
        contract NoExternalCallsTest is Ownable {
            enum FlashloanProvider { AAVE, BALANCER }
            
            struct ArbitrageParams {
                address tokenA;
                address tokenB;
                address buyDex;
                address sellDex;
                uint24 v3Fee;
                uint256 minProfit;
                FlashloanProvider provider;
            }
            
            constructor() Ownable(msg.sender) {}
            
            // Simulate executeOptimalFlashloan without external calls
            function simulateExecuteOptimalFlashloan(
                address asset,
                uint256 amount,
                bytes calldata params
            ) external onlyOwner returns (bool) {
                // Step 1: Decode parameters (same as original)
                ArbitrageParams memory arbParams = abi.decode(params, (ArbitrageParams));
                
                // Step 2: Validation (same as original)
                require(asset == arbParams.tokenA, "Asset mismatch");
                require(amount > 0, "Invalid amount");
                
                // Step 3: Provider selection (same as original)
                if (arbParams.provider == FlashloanProvider.BALANCER) {
                    return simulateBalancerFlashloan(asset, amount, params);
                } else {
                    return simulateAaveFlashloan(asset, amount, params);
                }
            }
            
            // Simulate Balancer flashloan without external call
            function simulateBalancerFlashloan(
                address asset,
                uint256 amount,
                bytes memory params
            ) internal returns (bool) {
                // Simulate what would happen in receiveFlashLoan callback
                ArbitrageParams memory arbParams = abi.decode(params, (ArbitrageParams));
                
                // Simulate arbitrage execution (without actual DEX calls)
                uint256 profit = simulateArbitrage(asset, amount, arbParams);
                
                return true;
            }
            
            // Simulate Aave flashloan without external call
            function simulateAaveFlashloan(
                address asset,
                uint256 amount,
                bytes memory params
            ) internal returns (bool) {
                // Simulate what would happen in executeOperation callback
                ArbitrageParams memory arbParams = abi.decode(params, (ArbitrageParams));
                
                // Simulate arbitrage execution (without actual DEX calls)
                uint256 profit = simulateArbitrage(asset, amount, arbParams);
                
                // Simulate Aave premium calculation
                uint256 premium = amount * 9 / 10000; // 0.09%
                uint256 amountToRepay = amount + premium;
                
                require(profit > premium, "Arbitrage not profitable after Aave fees");
                
                return true;
            }
            
            // Simulate arbitrage without DEX calls
            function simulateArbitrage(
                address asset,
                uint256 amount,
                ArbitrageParams memory params
            ) internal pure returns (uint256 profit) {
                // Just return a simulated profit without actual DEX interactions
                profit = amount / 100; // Simulate 1% profit
                return profit;
            }
            
            // Test just the decode operation
            function testDecodeOnly(bytes calldata params) external pure returns (ArbitrageParams memory) {
                return abi.decode(params, (ArbitrageParams));
            }
            
            // Test decode with amount dependency
            function testDecodeWithAmount(
                uint256 amount,
                bytes calldata params
            ) external pure returns (ArbitrageParams memory, uint256) {
                ArbitrageParams memory arbParams = abi.decode(params, (ArbitrageParams));
                
                // Test if there's any amount-dependent logic that could overflow
                uint256 testCalculation = amount * 9 / 10000; // Aave premium calculation
                
                return (arbParams, testCalculation);
            }
        }
    `;
    
    // Write and compile the test contract
    const fs = require('fs');
    fs.writeFileSync('./contracts/NoExternalCallsTest.sol', testContractCode);
    
    const { execSync } = require('child_process');
    try {
        execSync('npx hardhat compile', { stdio: 'inherit' });
    } catch (error) {
        console.log('Compilation error, but continuing...');
    }
    
    // Deploy the test contract
    const testArtifact = JSON.parse(fs.readFileSync('./artifacts/contracts/NoExternalCallsTest.sol/NoExternalCallsTest.json', 'utf8'));
    
    const testFactory = new ethers.ContractFactory(
        testArtifact.abi,
        testArtifact.bytecode,
        wallet
    );
    
    const testContract = await testFactory.deploy();
    await testContract.waitForDeployment();
    const testAddress = await testContract.getAddress();
    
    console.log(`✅ Test contract deployed at: ${testAddress}`);
    
    // Test parameters
    const wethAddress = '******************************************';
    const usdcAddress = '******************************************';
    const v2Router = '******************************************';
    const v3Router = '******************************************';
    
    const testParams = ethers.AbiCoder.defaultAbiCoder().encode(
        ['address', 'address', 'address', 'address', 'uint24', 'uint256', 'uint8'],
        [wethAddress, usdcAddress, v2Router, v3Router, 3000, ethers.parseEther('0.0001'), 0]
    );
    
    const workingAmount = ethers.parseUnits('1000', 'wei');
    const failingAmount = ethers.parseUnits('1', 'gwei');
    
    console.log('\n🧪 Testing without external calls...');
    
    // Test 1: Decode only
    console.log('\n   Test 1: Decode only');
    try {
        const result = await testContract.testDecodeOnly(testParams);
        console.log(`      ✅ Decode only: SUCCESS - Provider: ${result.provider}`);
    } catch (error) {
        console.log(`      ❌ Decode only: ${error.message.split('(')[0]}`);
    }
    
    // Test 2: Decode with working amount
    console.log('\n   Test 2: Decode with working amount');
    try {
        const result = await testContract.testDecodeWithAmount(workingAmount, testParams);
        console.log(`      ✅ Decode with working amount: SUCCESS`);
        console.log(`         Provider: ${result[0].provider}, Calculation: ${result[1]}`);
    } catch (error) {
        console.log(`      ❌ Decode with working amount: ${error.message.split('(')[0]}`);
    }
    
    // Test 3: Decode with failing amount
    console.log('\n   Test 3: Decode with failing amount');
    try {
        const result = await testContract.testDecodeWithAmount(failingAmount, testParams);
        console.log(`      ✅ Decode with failing amount: SUCCESS`);
        console.log(`         Provider: ${result[0].provider}, Calculation: ${result[1]}`);
    } catch (error) {
        console.log(`      ❌ Decode with failing amount: ${error.message.split('(')[0]}`);
        
        if (error.data && error.data.startsWith('0x4e487b71')) {
            const panicCode = parseInt(error.data.slice(10, 74), 16);
            if (panicCode === 17) {
                console.log(`         🚨 ENUM_CONVERSION_ERROR even without external calls!`);
            }
        }
    }
    
    // Test 4: Full simulation with working amount
    console.log('\n   Test 4: Full simulation with working amount');
    try {
        await testContract.simulateExecuteOptimalFlashloan(wethAddress, workingAmount, testParams);
        console.log(`      ✅ Full simulation with working amount: SUCCESS`);
    } catch (error) {
        console.log(`      ❌ Full simulation with working amount: ${error.message.split('(')[0]}`);
    }
    
    // Test 5: Full simulation with failing amount
    console.log('\n   Test 5: Full simulation with failing amount');
    try {
        await testContract.simulateExecuteOptimalFlashloan(wethAddress, failingAmount, testParams);
        console.log(`      ✅ Full simulation with failing amount: SUCCESS`);
    } catch (error) {
        console.log(`      ❌ Full simulation with failing amount: ${error.message.split('(')[0]}`);
        
        if (error.data && error.data.startsWith('0x4e487b71')) {
            const panicCode = parseInt(error.data.slice(10, 74), 16);
            if (panicCode === 17) {
                console.log(`         🚨 ENUM_CONVERSION_ERROR in simulation!`);
            }
        }
    }
    
    console.log('\n🏁 No external calls test completed!');
    
    // Clean up
    try {
        fs.unlinkSync('./contracts/NoExternalCallsTest.sol');
    } catch (e) {}
}

debugNoExternalCalls().catch(console.error);
