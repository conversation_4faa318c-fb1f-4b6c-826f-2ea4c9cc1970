const { ethers } = require('ethers');

async function testDAIIssue() {
    console.log('🔍 Investigating DAI-specific issue...');
    
    const provider = new ethers.JsonRpcProvider('http://127.0.0.1:8547');
    const wallet = new ethers.Wallet('0xac0974bec39a17e36ba4a6b4d238ff944bacb478cbed5efcae784d7bf4f2ff80', provider);
    
    // Deploy fresh contract
    const aavePoolAddressesProvider = '******************************************';
    const balancerVault = '******************************************';
    
    const fs = require('fs');
    const contractArtifact = JSON.parse(fs.readFileSync('./artifacts/contracts/HybridFlashloanArbitrage.sol/HybridFlashloanArbitrage.json', 'utf8'));
    
    const contractFactory = new ethers.ContractFactory(
        contractArtifact.abi,
        contractArtifact.bytecode,
        wallet
    );
    
    const contract = await contractFactory.deploy(
        aavePoolAddressesProvider,
        balancerVault
    );
    
    await contract.waitForDeployment();
    const contractAddress = await contract.getAddress();
    
    console.log(`✅ Contract deployed at: ${contractAddress}`);
    
    // Get router addresses
    const v2Router = await contract.UNISWAP_V2_ROUTER();
    const v3Router = await contract.UNISWAP_V3_ROUTER();
    
    // Token addresses
    const wethAddress = '******************************************';
    const usdcAddress = '******************************************';
    const daiAddress = '******************************************';
    
    console.log('\n📋 Token Information:');
    console.log(`   WETH: ${wethAddress}`);
    console.log(`   USDC: ${usdcAddress}`);
    console.log(`   DAI:  ${daiAddress}`);
    
    // Check if DAI contract exists and is valid
    console.log('\n🔍 Checking DAI contract...');
    
    try {
        const daiCode = await provider.getCode(daiAddress);
        console.log(`   DAI contract code size: ${daiCode.length} bytes`);
        
        if (daiCode === '0x') {
            console.log('   ❌ DAI contract not found!');
        } else {
            console.log('   ✅ DAI contract exists');
            
            // Try to call DAI contract
            const daiContract = new ethers.Contract(daiAddress, [
                'function symbol() view returns (string)',
                'function decimals() view returns (uint8)',
                'function totalSupply() view returns (uint256)'
            ], provider);
            
            try {
                const symbol = await daiContract.symbol();
                const decimals = await daiContract.decimals();
                const totalSupply = await daiContract.totalSupply();
                
                console.log(`   Symbol: ${symbol}`);
                console.log(`   Decimals: ${decimals}`);
                console.log(`   Total Supply: ${ethers.formatUnits(totalSupply, decimals)} ${symbol}`);
                
            } catch (daiError) {
                console.log(`   ❌ Error calling DAI contract: ${daiError.message}`);
            }
        }
        
    } catch (error) {
        console.log(`   ❌ Error checking DAI: ${error.message}`);
    }
    
    // Test with different DAI-like addresses
    console.log('\n🧪 Testing with different token addresses...');
    
    const testTokens = [
        { address: daiAddress, name: 'Real DAI' },
        { address: usdcAddress, name: 'USDC (as tokenB)' },
        { address: '******************************************', name: 'Address 0x1' },
        { address: '******************************************', name: 'Address 0x2' },
        { address: ethers.ZeroAddress, name: 'Zero Address' }
    ];
    
    for (const testToken of testTokens) {
        console.log(`\n   Testing WETH → ${testToken.name} (${testToken.address}):`);
        
        const arbitrageParams = ethers.AbiCoder.defaultAbiCoder().encode(
            ['address', 'address', 'address', 'address', 'uint24', 'uint256', 'uint8'],
            [
                wethAddress,        // tokenA
                testToken.address,  // tokenB
                v2Router,           // buyDex
                v3Router,           // sellDex
                3000,               // v3Fee
                ethers.parseEther('0.0001'), // minProfit
                0                   // FlashloanProvider.AAVE
            ]
        );
        
        try {
            await contract.executeOptimalFlashloan.staticCall(
                wethAddress,
                ethers.parseUnits('1', 'wei'),
                arbitrageParams
            );
            
            console.log(`      ✅ SUCCESS`);
            
        } catch (error) {
            const errorMsg = error.message.split('(')[0];
            console.log(`      ❌ ${errorMsg}`);
            
            if (errorMsg.includes('INSUFFICIENT_OUTPUT_AMOUNT')) {
                console.log(`         ✅ Struct encoding works - issue is with trade execution`);
            } else if (errorMsg.includes('OVERFLOW(17)')) {
                console.log(`         ⚠️  Enum conversion error persists`);
            }
        }
    }
    
    // Test if the issue is with the specific DAI address value
    console.log('\n🔬 Testing if issue is with DAI address value...');
    
    // Convert DAI address to number to see if there's something special about it
    const daiAddressNum = BigInt(daiAddress);
    console.log(`   DAI address as BigInt: ${daiAddressNum}`);
    console.log(`   DAI address hex: ${daiAddress}`);
    
    // Test with addresses that are similar to DAI but slightly different
    const daiVariations = [
        '******************************************', // DAI - 1
        '******************************************', // DAI + 1
        '******************************************'  // DAI with last byte as 00
    ];
    
    for (let i = 0; i < daiVariations.length; i++) {
        const variation = daiVariations[i];
        console.log(`\n   Testing DAI variation ${i + 1}: ${variation}`);
        
        const arbitrageParams = ethers.AbiCoder.defaultAbiCoder().encode(
            ['address', 'address', 'address', 'address', 'uint24', 'uint256', 'uint8'],
            [
                wethAddress,  // tokenA
                variation,    // tokenB (DAI variation)
                v2Router,     // buyDex
                v3Router,     // sellDex
                3000,         // v3Fee
                ethers.parseEther('0.0001'), // minProfit
                0             // FlashloanProvider.AAVE
            ]
        );
        
        try {
            await contract.executeOptimalFlashloan.staticCall(
                wethAddress,
                ethers.parseUnits('1', 'wei'),
                arbitrageParams
            );
            
            console.log(`      ✅ SUCCESS with variation ${i + 1}`);
            
        } catch (error) {
            const errorMsg = error.message.split('(')[0];
            console.log(`      ❌ ${errorMsg}`);
        }
    }
    
    console.log('\n🏁 DAI investigation completed!');
}

testDAIIssue().catch(console.error);
