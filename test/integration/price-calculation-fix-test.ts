import { expect } from 'chai';
import { ethers } from 'ethers';
import { config } from '../../src/config';
import { FlashloanStrategy } from '../../src/strategies/flashloan';
import { PoolManager } from '../../src/dex/pools';
import { COMMON_TOKENS } from '../../src/config';
import { logger } from '../../src/utils/logger';

describe('🔧 Price Calculation Fix Verification', function() {
  this.timeout(300000); // 5 minutes

  let provider: ethers.JsonRpcProvider;
  let flashloanStrategy: FlashloanStrategy;
  let poolManager: PoolManager;

  before(async function() {
    provider = new ethers.JsonRpcProvider(config.rpcUrl);
    flashloanStrategy = new FlashloanStrategy(provider);
    poolManager = new PoolManager();

    logger.info('🔧 Price calculation fix test setup completed');
  });

  describe('🎯 V3 Price Calculation Fix', function() {
    it('should calculate realistic V3 prices for WETH/USDC pairs', async function() {
      logger.info('🎯 Testing V3 price calculation fix...');
      
      const weth = COMMON_TOKENS.find((t: any) => t.symbol === 'WETH');
      const usdc = COMMON_TOKENS.find((t: any) => t.symbol === 'USDC');
      
      if (!weth || !usdc) {
        logger.error('WETH or USDC token not found');
        return;
      }
      
      // Test different fee tiers
      const feeTiers = [500, 3000, 10000];
      const prices: { fee: number; price: number | null }[] = [];
      
      for (const fee of feeTiers) {
        logger.info(`\n--- Testing V3 ${fee/10000}% fee tier ---`);
        
        const pool = await poolManager.getPool(weth.address, usdc.address, 'uniswap-v3', fee);
        
        if (pool && pool.tick !== undefined) {
          // Use the private method through reflection to test the actual calculation
          const price = await testV3PriceCalculation(flashloanStrategy, pool, weth, usdc);
          
          prices.push({ fee, price });
          
          if (price !== null) {
            logger.info(`✅ V3 ${fee/10000}% price: ${price.toFixed(6)} USDC per WETH`);
            
            // Validate price is in realistic range
            // ETH price should be between $1000-$10000
            if (price > 100 && price < 20000) {
              logger.info(`✅ Price is in realistic range ($${price.toFixed(2)})`);
            } else {
              logger.warn(`⚠️  Price outside expected range: $${price.toFixed(2)}`);
            }
          } else {
            logger.error(`❌ Failed to calculate price for ${fee/10000}% fee tier`);
          }
        } else {
          logger.warn(`❌ Pool not found for ${fee/10000}% fee tier`);
          prices.push({ fee, price: null });
        }
      }
      
      // Analyze results
      const validPrices = prices.filter(p => p.price !== null);
      logger.info(`\n📊 Price Calculation Summary:`);
      logger.info(`Valid prices calculated: ${validPrices.length}/${prices.length}`);
      
      if (validPrices.length > 0) {
        const avgPrice = validPrices.reduce((sum, p) => sum + p.price!, 0) / validPrices.length;
        logger.info(`Average price: $${avgPrice.toFixed(2)}`);
        
        // Check price consistency across fee tiers
        const maxPrice = Math.max(...validPrices.map(p => p.price!));
        const minPrice = Math.min(...validPrices.map(p => p.price!));
        const priceSpread = ((maxPrice - minPrice) / minPrice) * 100;
        
        logger.info(`Price spread across fee tiers: ${priceSpread.toFixed(4)}%`);
        
        if (priceSpread < 5) {
          logger.info('✅ Price consistency across fee tiers looks good');
        } else {
          logger.warn('⚠️  Large price spread across fee tiers - investigate');
        }
      }
      
      expect(validPrices.length).to.be.greaterThan(0);
    });

    it('should compare V2 vs V3 prices and detect arbitrage opportunities', async function() {
      logger.info('💰 Testing V2 vs V3 price comparison...');
      
      const weth = COMMON_TOKENS.find((t: any) => t.symbol === 'WETH');
      const usdc = COMMON_TOKENS.find((t: any) => t.symbol === 'USDC');
      
      if (!weth || !usdc) {
        logger.error('WETH or USDC token not found');
        return;
      }
      
      // Get V2 pool
      const v2Pool = await poolManager.getPool(weth.address, usdc.address, 'uniswap-v2');
      
      // Get V3 pool (0.3% fee tier)
      const v3Pool = await poolManager.getPool(weth.address, usdc.address, 'uniswap-v3', 3000);
      
      if (!v2Pool || !v3Pool) {
        logger.error('V2 or V3 pool not found');
        return;
      }
      
      // Calculate prices using the actual strategy method
      const v2Price = await testV2PriceCalculation(flashloanStrategy, v2Pool, weth, usdc);
      const v3Price = await testV3PriceCalculation(flashloanStrategy, v3Pool, weth, usdc);
      
      logger.info(`\n📊 Price Comparison:`);
      logger.info(`V2 price: ${v2Price?.toFixed(6) || 'null'} USDC per WETH`);
      logger.info(`V3 price: ${v3Price?.toFixed(6) || 'null'} USDC per WETH`);
      
      if (v2Price && v3Price) {
        const priceDifference = Math.abs(v2Price - v3Price);
        const priceSpread = (priceDifference / Math.min(v2Price, v3Price)) * 100;
        
        logger.info(`Price difference: ${priceDifference.toFixed(6)} USDC`);
        logger.info(`Price spread: ${priceSpread.toFixed(4)}%`);
        
        if (priceSpread > 0.1) {
          logger.info(`🎯 Arbitrage opportunity detected: ${priceSpread.toFixed(4)}% spread`);
          
          if (v2Price > v3Price) {
            logger.info(`Strategy: Buy WETH on V3 (${v3Price.toFixed(2)}), sell on V2 (${v2Price.toFixed(2)})`);
          } else {
            logger.info(`Strategy: Buy WETH on V2 (${v2Price.toFixed(2)}), sell on V3 (${v3Price.toFixed(2)})`);
          }
        } else {
          logger.info(`📉 No significant arbitrage opportunity (${priceSpread.toFixed(4)}% spread)`);
        }
        
        // Validate both prices are realistic
        expect(v2Price).to.be.greaterThan(100);
        expect(v2Price).to.be.lessThan(20000);
        expect(v3Price).to.be.greaterThan(100);
        expect(v3Price).to.be.lessThan(20000);
      }
      
      expect(v2Price).to.not.be.null;
      expect(v3Price).to.not.be.null;
    });
  });

  describe('🔍 Price Normalization Fix', function() {
    it('should properly normalize prices for different token orderings', async function() {
      logger.info('🔍 Testing price normalization...');
      
      const weth = COMMON_TOKENS.find((t: any) => t.symbol === 'WETH');
      const usdc = COMMON_TOKENS.find((t: any) => t.symbol === 'USDC');
      
      if (!weth || !usdc) {
        logger.error('WETH or USDC token not found');
        return;
      }
      
      // Test both token orderings
      const pool = await poolManager.getPool(weth.address, usdc.address, 'uniswap-v3', 3000);
      
      if (!pool) {
        logger.error('Pool not found');
        return;
      }
      
      // Test price calculation with different token orderings
      const priceWethUsdc = await testV3PriceCalculation(flashloanStrategy, pool, weth, usdc);
      const priceUsdcWeth = await testV3PriceCalculation(flashloanStrategy, pool, usdc, weth);
      
      logger.info(`Price (WETH → USDC): ${priceWethUsdc?.toFixed(6) || 'null'}`);
      logger.info(`Price (USDC → WETH): ${priceUsdcWeth?.toFixed(6) || 'null'}`);
      
      if (priceWethUsdc && priceUsdcWeth) {
        // These should be reciprocals of each other
        const expectedReciprocal = 1 / priceWethUsdc;
        const reciprocalDifference = Math.abs(priceUsdcWeth - expectedReciprocal);
        const reciprocalError = (reciprocalDifference / expectedReciprocal) * 100;
        
        logger.info(`Expected reciprocal: ${expectedReciprocal.toFixed(6)}`);
        logger.info(`Reciprocal error: ${reciprocalError.toFixed(4)}%`);
        
        if (reciprocalError < 1) {
          logger.info('✅ Price normalization working correctly');
        } else {
          logger.warn('⚠️  Price normalization may have issues');
        }
        
        expect(reciprocalError).to.be.lessThan(1);
      }
      
      expect(priceWethUsdc).to.not.be.null;
      expect(priceUsdcWeth).to.not.be.null;
    });
  });
});

// Helper functions to test private methods
async function testV2PriceCalculation(strategy: any, pool: any, tokenA: any, tokenB: any): Promise<number | null> {
  try {
    // Access private method through reflection
    return await strategy.calculatePoolPrice(pool, tokenA, tokenB);
  } catch (error) {
    logger.error('Error testing V2 price calculation:', error);
    return null;
  }
}

async function testV3PriceCalculation(strategy: any, pool: any, tokenA: any, tokenB: any): Promise<number | null> {
  try {
    // Access private method through reflection
    return await strategy.calculatePoolPrice(pool, tokenA, tokenB);
  } catch (error) {
    logger.error('Error testing V3 price calculation:', error);
    return null;
  }
}
