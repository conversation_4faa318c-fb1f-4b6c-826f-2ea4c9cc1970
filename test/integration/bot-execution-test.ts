import { expect } from 'chai';
import { ethers } from 'ethers';
import { config } from '../../src/config';
import { Bot } from '../../src/bot';
import { logger } from '../../src/utils/logger';

describe('🤖 Bot Execution Integration Test', function() {
  this.timeout(300000); // 5 minutes

  let bot: Bot;

  before(async function() {
    logger.info('🤖 Initializing Bot for execution testing...');
    
    // Initialize the main bot
    bot = new Bot();
    
    logger.info('✅ Bot initialized successfully');
  });

  describe('🔄 Bot Scanning Integration', function() {
    it('should execute the same scanning logic as the running bot', async function() {
      logger.info('🔄 Testing bot scanning integration...');
      
      // This test will execute the exact same logic as bot.ts
      // to ensure our diagnostic tests match real bot behavior
      
      logger.info('Configuration being used by bot:');
      logger.info(`  Chain ID: ${config.chainId}`);
      logger.info(`  Simulation mode: ${config.simulationMode}`);
      logger.info(`  Min profit: ${ethers.formatEther(config.minProfitWei)} ETH`);
      logger.info(`  Flashloan attacks enabled: ${config.enableFlashloanAttacks}`);
      logger.info(`  Arbitrage enabled: ${config.enableArbitrage}`);
      
      // Test the bot's scanning method directly
      logger.info('🔍 Running bot scan...');
      
      // We'll need to access the bot's internal scanning methods
      // This ensures we're testing the exact same code path as the running bot
      
      // For now, let's just verify the bot can be initialized and configured correctly
      expect(bot).to.not.be.undefined;
      expect(config.enableFlashloanAttacks).to.be.true;
      
      logger.info('✅ Bot execution test completed');
    });

    it('should test bot with temporarily lowered thresholds', async function() {
      logger.info('🎯 Testing bot with lowered thresholds...');
      
      // Save original configuration
      const originalMinProfit = config.minProfitWei;
      const originalMinSpread = config.minArbitrageSpread;
      
      // Set very low thresholds for testing
      (config as any).minProfitWei = ethers.parseEther('0.001'); // 0.001 ETH
      (config as any).minArbitrageSpread = 0.001; // 0.1%
      
      logger.info('Testing with lowered thresholds:');
      logger.info(`  Min profit: ${ethers.formatEther(config.minProfitWei)} ETH`);
      logger.info(`  Min spread: ${config.minArbitrageSpread * 100}%`);
      
      // Test bot behavior with lowered thresholds
      // This will help identify if the issue is just threshold-related
      
      logger.info('🔍 Bot should find more opportunities with lower thresholds...');
      
      // Restore original configuration
      (config as any).minProfitWei = originalMinProfit;
      (config as any).minArbitrageSpread = originalMinSpread;
      
      expect(true).to.be.true; // Test passes, we're gathering diagnostic info
    });
  });

  describe('📊 Bot Performance Analysis', function() {
    it('should analyze bot performance characteristics', async function() {
      logger.info('📊 Analyzing bot performance...');
      
      // Test bot initialization time
      const initStart = Date.now();
      const testBot = new Bot();
      const initTime = Date.now() - initStart;
      
      logger.info(`Bot initialization time: ${initTime}ms`);
      
      if (initTime > 10000) {
        logger.warn('⚠️  Bot initialization is slow (>10s) - this may affect opportunity detection');
      }
      
      // Test configuration validation
      logger.info('Validating bot configuration...');
      
      const configValidation = {
        hasPrivateKey: !!config.privateKey,
        hasRpcUrl: !!config.rpcUrl,
        hasFlashbotsKey: !!config.flashbotsSignerKey,
        hasAaveContract: !!config.aaveFlashloanContract,
        hasBalancerContract: !!config.balancerFlashloanContract,
        enabledStrategies: {
          flashloan: config.enableFlashloanAttacks,
          arbitrage: config.enableArbitrage,
          sandwich: config.enableSandwichAttacks,
          frontrunning: config.enableFrontRunning
        }
      };
      
      logger.info('Configuration validation:', configValidation);
      
      // Check for common configuration issues
      if (!configValidation.hasPrivateKey) {
        logger.error('🚨 CRITICAL: No private key configured');
      }
      
      if (!configValidation.hasRpcUrl) {
        logger.error('🚨 CRITICAL: No RPC URL configured');
      }
      
      if (!configValidation.enabledStrategies.flashloan && !configValidation.enabledStrategies.arbitrage) {
        logger.error('🚨 CRITICAL: No strategies enabled');
      }
      
      expect(configValidation.hasPrivateKey).to.be.true;
      expect(configValidation.hasRpcUrl).to.be.true;
    });
  });

  describe('🔧 Configuration Recommendations', function() {
    it('should provide specific configuration recommendations', async function() {
      logger.info('🔧 Generating configuration recommendations...');
      
      const recommendations: string[] = [];
      
      // Analyze current configuration and provide recommendations
      const currentMinProfitUsd = Number(ethers.formatEther(config.minProfitWei)) * 3350; // Assume $3350 ETH
      
      if (currentMinProfitUsd > 100) {
        recommendations.push(`Lower MIN_PROFIT_WEI: Current $${currentMinProfitUsd.toFixed(2)} is too high. Try ${ethers.parseEther('0.003').toString()} (~$10)`);
      }
      
      if (config.minArbitrageSpread > 0.01) {
        recommendations.push(`Lower MIN_ARBITRAGE_SPREAD: Current ${config.minArbitrageSpread * 100}% is restrictive. Try 0.005 (0.5%)`);
      }
      
      if (config.flashloanTokens.length < 3) {
        recommendations.push(`Add more FLASHLOAN_TOKENS: Current ${config.flashloanTokens.length} tokens. Add WBTC, LINK, UNI`);
      }
      
      if (config.targetTokens.length < 4) {
        recommendations.push(`Add more TARGET_TOKENS: Current ${config.targetTokens.length} tokens. Add more major tokens`);
      }
      
      if (!config.enableCrossDexArbitrage) {
        recommendations.push(`Enable ENABLE_CROSS_DEX_ARBITRAGE=true for more opportunities`);
      }
      
      logger.info('\n=== CONFIGURATION RECOMMENDATIONS ===');
      if (recommendations.length > 0) {
        recommendations.forEach((rec, i) => {
          logger.info(`${i + 1}. ${rec}`);
        });
      } else {
        logger.info('✅ Configuration looks good');
      }
      
      // Generate optimal .env settings
      logger.info('\n=== SUGGESTED .env CHANGES ===');
      logger.info('# Lower thresholds for more opportunities');
      logger.info(`MIN_PROFIT_WEI=${ethers.parseEther('0.003').toString()}  # ~$10 minimum`);
      logger.info('MIN_ARBITRAGE_SPREAD=0.005  # 0.5% minimum spread');
      logger.info('ENABLE_CROSS_DEX_ARBITRAGE=true');
      logger.info('FLASHLOAN_TOKENS=WETH,USDC,USDT,DAI,WBTC');
      logger.info('TARGET_TOKENS=WETH,USDC,USDT,DAI,WBTC,LINK,UNI');
      
      expect(recommendations).to.be.an('array');
    });
  });
});
