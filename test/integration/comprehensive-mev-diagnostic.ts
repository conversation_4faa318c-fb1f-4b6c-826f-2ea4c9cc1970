import { expect } from 'chai';
import { ethers } from 'ethers';
import { config } from '../../src/config';
import { DynamicFlashloanStrategy } from '../../src/strategies/dynamic-flashloan';
import { FlashloanStrategy } from '../../src/strategies/flashloan';
import { BalancerFlashloanStrategy } from '../../src/strategies/balancer-flashloan';
import { UniswapV3FlashSwapStrategy } from '../../src/strategies/uniswap-v3-flash';
import { FlashbotsBundleManager } from '../../src/flashbots/bundle-provider';
import { FlashbotsExecutor } from '../../src/execution/flashbots-executor';
import { AdvancedGasEstimator } from '../../src/gas/advanced-estimator';
import { GasOptimizer } from '../../src/gas/optimizer';
import { PoolManager } from '../../src/dex/pools';
import { COMMON_TOKENS } from '../../src/config';
import { logger } from '../../src/utils/logger';

describe('🔍 MEV Bot Diagnostic Tests - Find Why No Opportunities', function() {
  this.timeout(300000); // 5 minutes

  let provider: ethers.JsonRpcProvider;
  let wallet: ethers.Wallet;
  let flashbotsManager: FlashbotsBundleManager;
  let flashbotsExecutor: FlashbotsExecutor;
  let dynamicFlashloanStrategy: DynamicFlashloanStrategy;
  let poolManager: PoolManager;
  let aaveStrategy: FlashloanStrategy;
  let balancerStrategy: BalancerFlashloanStrategy;
  let uniswapV3Strategy: UniswapV3FlashSwapStrategy;

  before(async function() {
    // Initialize provider and wallet
    provider = new ethers.JsonRpcProvider(config.rpcUrl);
    wallet = new ethers.Wallet(config.privateKey, provider);
    
    // Initialize pool manager
    poolManager = new PoolManager();
    
    // Initialize individual strategies for detailed testing
    aaveStrategy = new FlashloanStrategy(provider);
    balancerStrategy = new BalancerFlashloanStrategy(provider);
    uniswapV3Strategy = new UniswapV3FlashSwapStrategy(provider, wallet);
    
    // Initialize Flashbots components
    flashbotsManager = new FlashbotsBundleManager(provider, wallet);
    const gasEstimator = new AdvancedGasEstimator(provider);
    const gasOptimizer = new GasOptimizer();
    flashbotsExecutor = new FlashbotsExecutor(provider, wallet, flashbotsManager, gasEstimator, gasOptimizer);
    
    // Initialize dynamic flashloan strategy
    dynamicFlashloanStrategy = new DynamicFlashloanStrategy(
      provider,
      wallet,
      flashbotsManager,
      flashbotsExecutor
    );

    logger.info('🧪 Comprehensive diagnostic test setup completed');
    logger.info(`Network: ${config.chainId === 1 ? 'Mainnet' : 'Testnet'}`);
    logger.info(`Simulation mode: ${config.simulationMode}`);
    logger.info(`Min profit threshold: ${ethers.formatEther(config.minProfitWei)} ETH`);
  });

  describe('🔧 Configuration Analysis', function() {
    it('should diagnose configuration issues that prevent opportunity detection', async function() {
      logger.info('🔧 DIAGNOSTIC: Configuration Analysis');
      
      // Check critical configuration values
      const diagnostics = {
        chainId: config.chainId,
        simulationMode: config.simulationMode,
        minProfitWei: config.minProfitWei.toString(),
        minProfitEth: ethers.formatEther(config.minProfitWei),
        enableFlashloanAttacks: config.enableFlashloanAttacks,
        enableArbitrage: config.enableArbitrage,
        enableCrossDexArbitrage: config.enableCrossDexArbitrage,
        flashloanTokens: config.flashloanTokens,
        aaveContract: config.aaveFlashloanContract,
        balancerContract: config.balancerFlashloanContract,
        minArbitrageSpread: config.minArbitrageSpread
      };
      
      logger.info('📊 Configuration Diagnostics:', diagnostics);
      
      // Check if profit threshold is too high
      const currentEthPrice = 3350; // Approximate ETH price
      const minProfitUsd = Number(ethers.formatEther(config.minProfitWei)) * currentEthPrice;
      logger.info(`💰 Min profit in USD: $${minProfitUsd.toFixed(2)}`);
      
      if (minProfitUsd > 50) {
        logger.warn('⚠️  POTENTIAL ISSUE: Min profit threshold might be too high for current market');
        logger.warn(`   Consider lowering MIN_PROFIT_WEI in .env file`);
      }
      
      if (config.minArbitrageSpread > 0.02) {
        logger.warn('⚠️  POTENTIAL ISSUE: Min arbitrage spread might be too high');
        logger.warn(`   Current: ${config.minArbitrageSpread * 100}%, consider lowering to 0.5-1%`);
      }
      
      expect(config.enableFlashloanAttacks).to.be.true;
      expect(config.chainId).to.equal(1);
    });

    it('should validate contract addresses and network connectivity', async function() {
      logger.info('🌐 DIAGNOSTIC: Network and Contract Validation');
      
      // Test network connectivity
      const blockNumber = await provider.getBlockNumber();
      logger.info(`✅ Connected to network, current block: ${blockNumber}`);
      
      // Validate contract addresses
      const contracts = {
        aave: config.aaveFlashloanContract,
        balancer: config.balancerFlashloanContract,
        hybrid: config.hybridFlashloanContract
      };
      
      for (const [name, address] of Object.entries(contracts)) {
        if (address && ethers.isAddress(address)) {
          const code = await provider.getCode(address);
          const hasCode = code !== '0x';
          logger.info(`${name} contract (${address}): ${hasCode ? '✅ Valid' : '❌ No code'}`);
          
          if (!hasCode) {
            logger.error(`🚨 ISSUE: ${name} contract has no code at ${address}`);
          }
        } else {
          logger.error(`🚨 ISSUE: Invalid ${name} contract address: ${address}`);
        }
      }
      
      expect(blockNumber).to.be.greaterThan(0);
    });
  });

  describe('🎯 Individual Strategy Testing', function() {
    it('should test each strategy individually to isolate issues', async function() {
      logger.info('🎯 DIAGNOSTIC: Individual Strategy Testing');
      
      // Test each strategy individually to isolate issues
      logger.info('\n--- Testing Aave Flashloan Strategy ---');
      const aaveOpportunities = await aaveStrategy.scanForFlashloanOpportunities();
      logger.info(`Aave opportunities found: ${aaveOpportunities.length}`);
      
      if (aaveOpportunities.length > 0) {
        const best = aaveOpportunities[0];
        logger.info(`Best Aave opportunity: ${ethers.formatEther(best.expectedProfit)} ETH profit, ${best.confidence}% confidence`);
      }
      
      logger.info('\n--- Testing Balancer Flashloan Strategy ---');
      const balancerOpportunities = await balancerStrategy.scanForBalancerFlashloanOpportunities();
      logger.info(`Balancer opportunities found: ${balancerOpportunities.length}`);
      
      if (balancerOpportunities.length > 0) {
        const best = balancerOpportunities[0];
        logger.info(`Best Balancer opportunity: ${ethers.formatEther(best.expectedProfit)} ETH profit, ${best.confidence}% confidence`);
      }
      
      logger.info('\n--- Testing Uniswap V3 Flash Swap Strategy ---');
      const uniswapV3Opportunities = await uniswapV3Strategy.scanForOpportunities();
      logger.info(`Uniswap V3 opportunities found: ${uniswapV3Opportunities.length}`);
      
      if (uniswapV3Opportunities.length > 0) {
        const best = uniswapV3Opportunities[0];
        logger.info(`Best Uniswap V3 opportunity: ${best.profitability} ETH profit`);
      }
      
      const totalIndividual = aaveOpportunities.length + balancerOpportunities.length + uniswapV3Opportunities.length;
      logger.info(`\n📊 Total individual strategy opportunities: ${totalIndividual}`);
      
      // Now test dynamic strategy
      logger.info('\n--- Testing Dynamic Flashloan Strategy ---');
      const dynamicOpportunities = await dynamicFlashloanStrategy.scanForOpportunities();
      logger.info(`Dynamic strategy opportunities found: ${dynamicOpportunities.length}`);
      
      if (dynamicOpportunities.length > 0) {
        const best = dynamicOpportunities[0];
        logger.info(`Best dynamic opportunity: ${best.strategy} - ${ethers.formatEther(best.netProfit)} ETH net profit`);
      }
      
      if (totalIndividual > 0 && dynamicOpportunities.length === 0) {
        logger.error('🚨 CRITICAL ISSUE: Individual strategies find opportunities but dynamic strategy does not!');
        logger.error('   This suggests an issue in the dynamic strategy aggregation logic');
      } else if (totalIndividual === 0) {
        logger.warn('⚠️  No opportunities found in any individual strategy');
        logger.warn('   This suggests market conditions or configuration issues');
      }
      
      expect(aaveOpportunities).to.be.an('array');
      expect(balancerOpportunities).to.be.an('array');
      expect(uniswapV3Opportunities).to.be.an('array');
      expect(dynamicOpportunities).to.be.an('array');
    });
  });

  describe('🏊 Pool and Liquidity Validation', function() {
    it('should validate pool existence and liquidity for configured token pairs', async function() {
      logger.info('🏊 DIAGNOSTIC: Pool Validation');
      
      const testPairs = [
        { tokenA: 'WETH', tokenB: 'USDC', expectedLiquidity: true },
        { tokenA: 'WETH', tokenB: 'USDT', expectedLiquidity: true },
        { tokenA: 'WBTC', tokenB: 'WETH', expectedLiquidity: true },
        { tokenA: 'DAI', tokenB: 'USDC', expectedLiquidity: true }
      ];
      
      let totalPoolsFound = 0;
      let totalPoolsMissing = 0;
      
      for (const pair of testPairs) {
        logger.info(`\n--- Testing ${pair.tokenA}/${pair.tokenB} ---`);
        
        const tokenA = COMMON_TOKENS.find((t: any) => t.symbol === pair.tokenA);
        const tokenB = COMMON_TOKENS.find((t: any) => t.symbol === pair.tokenB);
        
        if (!tokenA || !tokenB) {
          logger.error(`❌ Token not found: ${pair.tokenA} or ${pair.tokenB}`);
          totalPoolsMissing++;
          continue;
        }
        
        // Test Uniswap V2 pool
        const v2Pool = await poolManager.getPool(tokenA.address, tokenB.address, 'uniswap-v2');
        if (v2Pool) {
          logger.info(`✅ Uniswap V2 pool exists: ${v2Pool.address}`);
          logger.info(`   Reserves: ${v2Pool.reserves ? 'Available' : 'Missing'}`);
          totalPoolsFound++;
        } else {
          logger.warn(`❌ Uniswap V2 pool missing`);
          totalPoolsMissing++;
        }
        
        // Test Uniswap V3 pools (different fee tiers)
        const feeTiers = [500, 3000, 10000];
        for (const fee of feeTiers) {
          const v3Pool = await poolManager.getPool(tokenA.address, tokenB.address, 'uniswap-v3', fee);
          if (v3Pool) {
            logger.info(`✅ Uniswap V3 pool (${fee/10000}%): ${v3Pool.address}`);
            logger.info(`   Tick: ${v3Pool.tick?.toString() || 'Missing'}`);
            totalPoolsFound++;
          } else {
            logger.warn(`❌ Uniswap V3 pool (${fee/10000}%) missing`);
            totalPoolsMissing++;
          }
        }
      }
      
      logger.info(`\n📊 Pool Summary: ${totalPoolsFound} found, ${totalPoolsMissing} missing`);
      
      if (totalPoolsFound === 0) {
        logger.error('🚨 CRITICAL ISSUE: No pools found for any configured token pairs!');
        logger.error('   This explains why no opportunities are detected');
      } else if (totalPoolsMissing > totalPoolsFound) {
        logger.warn('⚠️  Many pools are missing, this reduces opportunity detection');
      }
      
      expect(totalPoolsFound).to.be.greaterThan(0);
    });
  });

  describe('💱 Price Analysis', function() {
    it('should test price calculation and arbitrage detection', async function() {
      logger.info('💱 DIAGNOSTIC: Price Calculation Testing');
      
      const weth = COMMON_TOKENS.find((t: any) => t.symbol === 'WETH');
      const usdc = COMMON_TOKENS.find((t: any) => t.symbol === 'USDC');
      
      if (!weth || !usdc) {
        logger.error('WETH or USDC token not found');
        return;
      }
      
      // Get pools and calculate prices
      const v2Pool = await poolManager.getPool(weth.address, usdc.address, 'uniswap-v2');
      const v3Pool3000 = await poolManager.getPool(weth.address, usdc.address, 'uniswap-v3', 3000);
      const v3Pool500 = await poolManager.getPool(weth.address, usdc.address, 'uniswap-v3', 500);
      
      logger.info('\n--- WETH/USDC Price Analysis ---');
      
      const prices: number[] = [];
      
      if (v2Pool && v2Pool.reserves) {
        const v2Price = calculateV2Price(v2Pool, weth, usdc);
        if (v2Price) {
          logger.info(`Uniswap V2 price: ${v2Price.toFixed(2)} USDC per WETH`);
          prices.push(v2Price);
        }
      }

      if (v3Pool3000 && v3Pool3000.tick !== undefined) {
        const v3Price3000 = calculateV3Price(v3Pool3000, weth, usdc);
        if (v3Price3000) {
          logger.info(`Uniswap V3 (0.3%) price: ${v3Price3000.toFixed(2)} USDC per WETH`);
          prices.push(v3Price3000);
        }
      }

      if (v3Pool500 && v3Pool500.tick !== undefined) {
        const v3Price500 = calculateV3Price(v3Pool500, weth, usdc);
        if (v3Price500) {
          logger.info(`Uniswap V3 (0.05%) price: ${v3Price500.toFixed(2)} USDC per WETH`);
          prices.push(v3Price500);
        }
      }
      
      // Calculate price differences
      if (prices.length >= 2) {
        const maxPrice = Math.max(...prices);
        const minPrice = Math.min(...prices);
        const priceDifference = ((maxPrice - minPrice) / minPrice) * 100;
        
        logger.info(`\n📊 Price Analysis:`);
        logger.info(`   Max price: ${maxPrice.toFixed(2)} USDC`);
        logger.info(`   Min price: ${minPrice.toFixed(2)} USDC`);
        logger.info(`   Price difference: ${priceDifference.toFixed(4)}%`);
        logger.info(`   Min required spread: ${config.minArbitrageSpread * 100}%`);
        
        if (priceDifference < config.minArbitrageSpread * 100) {
          logger.warn('⚠️  Price difference is below minimum arbitrage spread threshold');
          logger.warn(`   Consider lowering MIN_ARBITRAGE_SPREAD in .env file`);
        } else {
          logger.info('✅ Price difference exceeds minimum threshold - arbitrage should be possible');
        }
      } else {
        logger.error('🚨 ISSUE: Not enough price data to calculate arbitrage opportunities');
      }
      
      expect(prices.length).to.be.greaterThan(0);
    });
  });

});

// Helper functions for price calculation
function calculateV2Price(pool: any, tokenA: any, tokenB: any): number | null {
  if (!pool.reserves || pool.reserves.length !== 2) return null;

  const [reserve0, reserve1] = pool.reserves;
  const token0IsA = pool.token0.address.toLowerCase() === tokenA.address.toLowerCase();

  if (token0IsA) {
    return Number(reserve1) / Number(reserve0) * Math.pow(10, tokenA.decimals - tokenB.decimals);
  } else {
    return Number(reserve0) / Number(reserve1) * Math.pow(10, tokenA.decimals - tokenB.decimals);
  }
}

function calculateV3Price(pool: any, tokenA: any, tokenB: any): number | null {
  if (pool.tick === undefined || pool.tick === null) return null;

  const tick = Number(pool.tick);
  const price = Math.pow(1.0001, tick);

  const token0IsA = pool.token0.address.toLowerCase() === tokenA.address.toLowerCase();
  const decimalsAdjustment = Math.pow(10, tokenB.decimals - tokenA.decimals);

  return token0IsA ? price * decimalsAdjustment : (1 / price) * decimalsAdjustment;
}
