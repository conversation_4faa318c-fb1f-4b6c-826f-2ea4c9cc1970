import { expect } from 'chai';
import { ethers } from 'ethers';
import { config } from '../../src/config';
import { DynamicFlashloanStrategy } from '../../src/strategies/dynamic-flashloan';
import { FlashbotsBundleManager } from '../../src/flashbots/bundle-provider';
import { FlashbotsExecutor } from '../../src/execution/flashbots-executor';
import { AdvancedGasEstimator } from '../../src/gas/advanced-estimator';
import { GasOptimizer } from '../../src/gas/optimizer';
import { logger } from '../../src/utils/logger';

describe('🚀 Real-Life MEV Opportunity Tests', function() {
  this.timeout(600000); // 10 minutes for thorough testing

  let provider: ethers.JsonRpcProvider;
  let wallet: ethers.Wallet;
  let flashbotsManager: FlashbotsBundleManager;
  let flashbotsExecutor: FlashbotsExecutor;
  let dynamicFlashloanStrategy: DynamicFlashloanStrategy;

  before(async function() {
    // Initialize provider and wallet
    provider = new ethers.JsonRpcProvider(config.rpcUrl);
    wallet = new ethers.Wallet(config.privateKey, provider);
    
    // Initialize Flashbots components
    flashbotsManager = new FlashbotsBundleManager(provider, wallet);
    const gasEstimator = new AdvancedGasEstimator(provider);
    const gasOptimizer = new GasOptimizer();
    flashbotsExecutor = new FlashbotsExecutor(provider, wallet, flashbotsManager, gasEstimator, gasOptimizer);
    
    // Initialize dynamic flashloan strategy
    dynamicFlashloanStrategy = new DynamicFlashloanStrategy(
      provider,
      wallet,
      flashbotsManager,
      flashbotsExecutor,
      gasOptimizer
    );

    logger.info('🚀 Real-life opportunity test setup completed');
  });

  describe('🎯 Opportunity Detection with Adjusted Thresholds', function() {
    it('should test opportunity detection with progressively lower thresholds', async function() {
      logger.info('🎯 Testing with progressively lower profit thresholds...');
      
      const originalMinProfit = config.minProfitWei;
      const originalMinSpread = config.minArbitrageSpread;
      
      // Test with different threshold levels
      const testThresholds = [
        { profit: '0.01', spread: 0.01, description: 'High threshold ($33.50)' },
        { profit: '0.005', spread: 0.005, description: 'Medium threshold ($16.75)' },
        { profit: '0.001', spread: 0.001, description: 'Low threshold ($3.35)' },
        { profit: '0.0001', spread: 0.0001, description: 'Very low threshold ($0.34)' }
      ];
      
      for (const threshold of testThresholds) {
        logger.info(`\n--- Testing ${threshold.description} ---`);
        
        // Set test thresholds
        (config as any).minProfitWei = ethers.parseEther(threshold.profit);
        (config as any).minArbitrageSpread = threshold.spread;
        
        logger.info(`Min profit: ${threshold.profit} ETH`);
        logger.info(`Min spread: ${threshold.spread * 100}%`);
        
        const opportunities = await dynamicFlashloanStrategy.scanForOpportunities();
        logger.info(`Opportunities found: ${opportunities.length}`);
        
        if (opportunities.length > 0) {
          const best = opportunities[0];
          logger.info(`✅ FOUND OPPORTUNITY!`);
          logger.info(`  Strategy: ${best.strategy}`);
          logger.info(`  Expected profit: ${ethers.formatEther(best.expectedProfit)} ETH`);
          logger.info(`  Net profit: ${ethers.formatEther(best.netProfit)} ETH`);
          logger.info(`  Confidence: ${best.confidence}%`);
          logger.info(`  Risk score: ${best.riskScore}/100`);
          
          // If we found opportunities, test execution in simulation mode
          if (config.simulationMode) {
            logger.info('🧪 Testing execution in simulation mode...');
            const success = await dynamicFlashloanStrategy.executeBestOpportunity(opportunities);
            logger.info(`Execution result: ${success ? '✅ Success' : '❌ Failed'}`);
          }
          
          break; // Found opportunities, no need to test lower thresholds
        } else {
          logger.warn(`❌ No opportunities found with ${threshold.description}`);
        }
      }
      
      // Restore original values
      (config as any).minProfitWei = originalMinProfit;
      (config as any).minArbitrageSpread = originalMinSpread;
      
      expect(true).to.be.true; // Test always passes, we're just gathering data
    });

    it('should test with different confidence thresholds', async function() {
      logger.info('🎯 Testing with different confidence thresholds...');
      
      // Test with lower confidence requirements
      const confidenceThresholds = [70, 60, 50, 40, 30];
      
      for (const confidenceThreshold of confidenceThresholds) {
        logger.info(`\n--- Testing with ${confidenceThreshold}% confidence threshold ---`);
        
        // We'll need to modify the strategy to accept different confidence thresholds
        // For now, let's just scan and see what we get
        const opportunities = await dynamicFlashloanStrategy.scanForOpportunities();
        
        if (opportunities.length > 0) {
          const validOpportunities = opportunities.filter(op => op.confidence >= confidenceThreshold);
          logger.info(`Opportunities with ${confidenceThreshold}% confidence: ${validOpportunities.length}`);
          
          if (validOpportunities.length > 0) {
            const best = validOpportunities[0];
            logger.info(`Best opportunity:`);
            logger.info(`  Strategy: ${best.strategy}`);
            logger.info(`  Confidence: ${best.confidence}%`);
            logger.info(`  Expected profit: ${ethers.formatEther(best.expectedProfit)} ETH`);
          }
        }
      }
      
      expect(true).to.be.true;
    });
  });

  describe('📊 Market Condition Analysis', function() {
    it('should analyze current market conditions and provide recommendations', async function() {
      logger.info('📊 Analyzing current market conditions...');
      
      const marketConditions = await dynamicFlashloanStrategy.getMarketConditions();
      
      logger.info('\n=== MARKET ANALYSIS REPORT ===');
      logger.info(`Total opportunities: ${marketConditions.totalOpportunities}`);
      logger.info(`Best profit: ${marketConditions.bestProfit} ETH`);
      logger.info(`Best strategy: ${marketConditions.bestStrategy}`);
      logger.info(`Average confidence: ${marketConditions.averageConfidence.toFixed(2)}%`);
      logger.info(`Flashbots available: ${marketConditions.flashbotsAvailable}`);
      
      // Provide recommendations based on analysis
      if (marketConditions.totalOpportunities === 0) {
        logger.warn('\n🔍 RECOMMENDATIONS FOR ZERO OPPORTUNITIES:');
        logger.warn('1. Lower MIN_PROFIT_WEI in .env (currently too high)');
        logger.warn('2. Lower MIN_ARBITRAGE_SPREAD in .env (currently too restrictive)');
        logger.warn('3. Check if pools exist for configured token pairs');
        logger.warn('4. Verify network connectivity and RPC provider');
        logger.warn('5. Consider adding more token pairs to scan');
      } else if (marketConditions.totalOpportunities < 5) {
        logger.info('\n💡 RECOMMENDATIONS FOR LOW OPPORTUNITIES:');
        logger.info('1. Consider slightly lowering profit thresholds');
        logger.info('2. Add more token pairs to increase opportunity surface');
        logger.info('3. Monitor during higher volatility periods');
      } else {
        logger.info('\n✅ GOOD MARKET CONDITIONS:');
        logger.info('Sufficient opportunities detected for profitable trading');
      }
      
      expect(marketConditions).to.have.property('totalOpportunities');
    });

    it('should test opportunity detection during different time periods', async function() {
      logger.info('⏰ Testing opportunity detection over time...');
      
      const testDuration = 60000; // 1 minute
      const scanInterval = 10000; // 10 seconds
      const scans = testDuration / scanInterval;
      
      logger.info(`Running ${scans} scans over ${testDuration/1000} seconds...`);
      
      const results: any[] = [];
      
      for (let i = 0; i < scans; i++) {
        const startTime = Date.now();
        const opportunities = await dynamicFlashloanStrategy.scanForOpportunities();
        const scanTime = Date.now() - startTime;
        
        const result = {
          scan: i + 1,
          timestamp: new Date().toISOString(),
          opportunities: opportunities.length,
          scanTimeMs: scanTime,
          bestProfit: opportunities.length > 0 ? ethers.formatEther(opportunities[0].expectedProfit) : '0'
        };
        
        results.push(result);
        logger.info(`Scan ${i + 1}/${scans}: ${opportunities.length} opportunities, ${scanTime}ms`);
        
        if (i < scans - 1) {
          await new Promise(resolve => setTimeout(resolve, scanInterval));
        }
      }
      
      // Analyze results
      const totalOpportunities = results.reduce((sum, r) => sum + r.opportunities, 0);
      const avgScanTime = results.reduce((sum, r) => sum + r.scanTimeMs, 0) / results.length;
      const maxOpportunities = Math.max(...results.map(r => r.opportunities));
      
      logger.info('\n=== TIME-BASED ANALYSIS ===');
      logger.info(`Total opportunities across all scans: ${totalOpportunities}`);
      logger.info(`Average opportunities per scan: ${(totalOpportunities / scans).toFixed(2)}`);
      logger.info(`Maximum opportunities in single scan: ${maxOpportunities}`);
      logger.info(`Average scan time: ${avgScanTime.toFixed(0)}ms`);
      
      if (totalOpportunities === 0) {
        logger.error('🚨 CRITICAL: No opportunities found in any scan over 1 minute');
        logger.error('   This confirms the configuration or market condition issue');
      } else if (totalOpportunities < scans) {
        logger.warn('⚠️  Opportunities are rare - consider adjusting thresholds');
      } else {
        logger.info('✅ Regular opportunities detected - system is working');
      }
      
      expect(results).to.have.length(scans);
    });
  });

  describe('🧪 Simulation Mode Testing', function() {
    it('should test execution in simulation mode if opportunities are found', async function() {
      logger.info('🧪 Testing execution in simulation mode...');
      
      if (!config.simulationMode) {
        logger.warn('⚠️  Simulation mode is disabled - skipping execution test');
        return;
      }
      
      // Lower thresholds temporarily for testing
      const originalMinProfit = config.minProfitWei;
      (config as any).minProfitWei = ethers.parseEther('0.001'); // Very low for testing
      
      const opportunities = await dynamicFlashloanStrategy.scanForOpportunities();
      
      if (opportunities.length > 0) {
        logger.info(`Found ${opportunities.length} opportunities for simulation testing`);
        
        const best = opportunities[0];
        logger.info(`Testing execution of best opportunity:`);
        logger.info(`  Strategy: ${best.strategy}`);
        logger.info(`  Expected profit: ${ethers.formatEther(best.expectedProfit)} ETH`);
        logger.info(`  Confidence: ${best.confidence}%`);
        
        const executionStart = Date.now();
        const success = await dynamicFlashloanStrategy.executeBestOpportunity(opportunities);
        const executionTime = Date.now() - executionStart;
        
        logger.info(`Execution completed in ${executionTime}ms: ${success ? '✅ Success' : '❌ Failed'}`);
        
        if (!success) {
          logger.error('🚨 Execution failed in simulation mode - investigate execution logic');
        }
        
        expect(success).to.be.a('boolean');
      } else {
        logger.warn('❌ No opportunities found for simulation testing');
      }
      
      // Restore original threshold
      (config as any).minProfitWei = originalMinProfit;
    });
  });

  describe('🔧 Configuration Optimization', function() {
    it('should provide optimal configuration recommendations', async function() {
      logger.info('🔧 Generating optimal configuration recommendations...');
      
      // Test different configurations to find optimal settings
      const testConfigs = [
        { minProfit: '0.001', minSpread: 0.001, description: 'Aggressive (high frequency, low profit)' },
        { minProfit: '0.005', minSpread: 0.005, description: 'Balanced (medium frequency, medium profit)' },
        { minProfit: '0.01', minSpread: 0.01, description: 'Conservative (low frequency, high profit)' }
      ];
      
      const results: any[] = [];
      
      for (const testConfig of testConfigs) {
        logger.info(`\n--- Testing ${testConfig.description} ---`);
        
        // Set test configuration
        const originalMinProfit = config.minProfitWei;
        const originalMinSpread = config.minArbitrageSpread;
        
        (config as any).minProfitWei = ethers.parseEther(testConfig.minProfit);
        (config as any).minArbitrageSpread = testConfig.minSpread;
        
        const startTime = Date.now();
        const opportunities = await dynamicFlashloanStrategy.scanForOpportunities();
        const scanTime = Date.now() - startTime;
        
        const result = {
          config: testConfig.description,
          minProfit: testConfig.minProfit,
          minSpread: testConfig.minSpread,
          opportunities: opportunities.length,
          scanTimeMs: scanTime,
          bestProfit: opportunities.length > 0 ? ethers.formatEther(opportunities[0].expectedProfit) : '0',
          avgConfidence: opportunities.length > 0 ? 
            opportunities.reduce((sum, op) => sum + op.confidence, 0) / opportunities.length : 0
        };
        
        results.push(result);
        logger.info(`Result: ${opportunities.length} opportunities, ${scanTime}ms scan time`);
        
        // Restore original configuration
        (config as any).minProfitWei = originalMinProfit;
        (config as any).minArbitrageSpread = originalMinSpread;
      }
      
      // Find optimal configuration
      const bestConfig = results.reduce((best, current) => 
        current.opportunities > best.opportunities ? current : best
      );
      
      logger.info('\n=== OPTIMAL CONFIGURATION RECOMMENDATION ===');
      logger.info(`Best configuration: ${bestConfig.config}`);
      logger.info(`Recommended MIN_PROFIT_WEI: ${ethers.parseEther(bestConfig.minProfit).toString()}`);
      logger.info(`Recommended MIN_ARBITRAGE_SPREAD: ${bestConfig.minSpread}`);
      logger.info(`Expected opportunities: ${bestConfig.opportunities}`);
      logger.info(`Average confidence: ${bestConfig.avgConfidence.toFixed(2)}%`);
      
      if (bestConfig.opportunities === 0) {
        logger.error('🚨 CRITICAL: Even with aggressive settings, no opportunities found');
        logger.error('   This indicates a fundamental issue with:');
        logger.error('   - Pool data availability');
        logger.error('   - Price calculation logic');
        logger.error('   - Network connectivity');
        logger.error('   - Token configuration');
      }
      
      expect(results).to.have.length(testConfigs.length);
    });
  });
});
