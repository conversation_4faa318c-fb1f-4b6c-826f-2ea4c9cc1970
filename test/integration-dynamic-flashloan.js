const { ethers } = require('ethers');
const { config } = require('../src/config');
const { DynamicFlashloanStrategy } = require('../src/strategies/dynamic-flashloan');
const { FlashbotsBundleManager } = require('../src/flashbots/bundle-provider');
const { FlashbotsExecutor } = require('../src/execution/flashbots-executor');
const { AdvancedGasEstimator } = require('../src/gas/advanced-gas-estimator');
const { GasOptimizer } = require('../src/gas/gas-optimizer');
const { logger } = require('../src/utils/logger');

/**
 * Integration test for Dynamic Flashloan Strategy
 * Tests the complete flow from opportunity detection to execution
 */
async function main() {
  console.log("🧪 Dynamic Flashloan Strategy - Integration Test");
  console.log("=" .repeat(60));

  const startTime = Date.now();
  let testsPassed = 0;
  let testsTotal = 0;

  try {
    // Initialize provider and wallet
    console.log("🔧 Initializing test environment...");
    const provider = new ethers.JsonRpcProvider(config.rpcUrl);
    const wallet = new ethers.Wallet(config.privateKey, provider);
    
    console.log("   Provider:", config.rpcUrl);
    console.log("   Wallet:", wallet.address);
    console.log("   Network:", await provider.getNetwork());

    // Initialize components
    const flashbotsManager = new FlashbotsBundleManager(provider, wallet);
    const advancedGasEstimator = new AdvancedGasEstimator(provider);
    const gasOptimizer = new GasOptimizer();
    const flashbotsExecutor = new FlashbotsExecutor(
      provider,
      wallet,
      flashbotsManager,
      advancedGasEstimator,
      gasOptimizer
    );

    // Initialize dynamic strategy
    const dynamicStrategy = new DynamicFlashloanStrategy(
      provider,
      wallet,
      flashbotsManager,
      flashbotsExecutor
    );

    console.log("✅ Test environment initialized");

    // Test 1: Strategy Initialization
    testsTotal++;
    console.log(`\n🧪 Test ${testsTotal}: Strategy Initialization`);
    try {
      const report = dynamicStrategy.getStrategyReport();
      console.log("   Strategy report:", Object.keys(report));
      
      const bestStrategy = dynamicStrategy.getBestStrategy();
      console.log("   Best strategy:", bestStrategy);
      
      testsPassed++;
      console.log("   ✅ PASSED");
    } catch (error) {
      console.log("   ❌ FAILED:", error.message);
    }

    // Test 2: Market Conditions Analysis
    testsTotal++;
    console.log(`\n🧪 Test ${testsTotal}: Market Conditions Analysis`);
    try {
      const marketConditions = await dynamicStrategy.getMarketConditions();
      console.log("   Market conditions:", marketConditions);
      
      console.log(`   Total opportunities: ${marketConditions.totalOpportunities}`);
      console.log(`   Best profit: ${marketConditions.bestProfit} ETH`);
      console.log(`   Best strategy: ${marketConditions.bestStrategy}`);
      console.log(`   Average confidence: ${marketConditions.averageConfidence.toFixed(1)}%`);
      console.log(`   Flashbots available: ${marketConditions.flashbotsAvailable}`);
      
      testsPassed++;
      console.log("   ✅ PASSED");
    } catch (error) {
      console.log("   ❌ FAILED:", error.message);
    }

    // Test 3: Opportunity Scanning
    testsTotal++;
    console.log(`\n🧪 Test ${testsTotal}: Opportunity Scanning`);
    try {
      console.log("   Scanning for opportunities...");
      const opportunities = await dynamicStrategy.scanForOpportunities();
      
      console.log(`   Found ${opportunities.length} opportunities`);
      
      if (opportunities.length > 0) {
        const best = opportunities[0];
        console.log("   Best opportunity:");
        console.log(`     Strategy: ${best.strategy}`);
        console.log(`     Expected profit: ${ethers.formatEther(best.expectedProfit)} ETH`);
        console.log(`     Net profit: ${ethers.formatEther(best.netProfit)} ETH`);
        console.log(`     Confidence: ${best.confidence}%`);
        console.log(`     Risk score: ${best.riskScore}/100`);
        console.log(`     Execution complexity: ${best.executionComplexity}/4`);
      }
      
      testsPassed++;
      console.log("   ✅ PASSED");
    } catch (error) {
      console.log("   ❌ FAILED:", error.message);
    }

    // Test 4: Profitability Check
    testsTotal++;
    console.log(`\n🧪 Test ${testsTotal}: Profitability Check`);
    try {
      const isProfitable = await dynamicStrategy.isAnyStrategyProfitable();
      console.log(`   Any strategy profitable: ${isProfitable}`);
      
      testsPassed++;
      console.log("   ✅ PASSED");
    } catch (error) {
      console.log("   ❌ FAILED:", error.message);
    }

    // Test 5: Flashbots Integration
    testsTotal++;
    console.log(`\n🧪 Test ${testsTotal}: Flashbots Integration`);
    try {
      const flashbotsAvailable = flashbotsManager.isAvailable();
      console.log(`   Flashbots available: ${flashbotsAvailable}`);
      
      if (flashbotsAvailable) {
        console.log("   Flashbots relay URL:", flashbotsManager.getRelayUrl());
      }
      
      testsPassed++;
      console.log("   ✅ PASSED");
    } catch (error) {
      console.log("   ❌ FAILED:", error.message);
    }

    // Test 6: Gas Estimation
    testsTotal++;
    console.log(`\n🧪 Test ${testsTotal}: Gas Estimation`);
    try {
      const gasData = await advancedGasEstimator.getOptimalGasPrice();
      console.log("   Gas estimation:");
      console.log(`     Fast: ${ethers.formatUnits(gasData.fast, 'gwei')} gwei`);
      console.log(`     Standard: ${ethers.formatUnits(gasData.standard, 'gwei')} gwei`);
      console.log(`     Safe: ${ethers.formatUnits(gasData.safe, 'gwei')} gwei`);
      
      testsPassed++;
      console.log("   ✅ PASSED");
    } catch (error) {
      console.log("   ❌ FAILED:", error.message);
    }

    // Test 7: Strategy Performance Comparison
    testsTotal++;
    console.log(`\n🧪 Test ${testsTotal}: Strategy Performance Comparison`);
    try {
      const strategies = ['aave', 'balancer', 'uniswap-v3'];
      const report = dynamicStrategy.getStrategyReport();
      
      console.log("   Strategy performance:");
      strategies.forEach(strategy => {
        const stats = report[strategy];
        if (stats) {
          console.log(`     ${strategy.padEnd(12)}: ${stats.successRate.toFixed(1)}% success, ${stats.profitability.toFixed(4)} ETH total`);
        }
      });
      
      testsPassed++;
      console.log("   ✅ PASSED");
    } catch (error) {
      console.log("   ❌ FAILED:", error.message);
    }

    // Test 8: Execution Simulation (if opportunities exist)
    testsTotal++;
    console.log(`\n🧪 Test ${testsTotal}: Execution Simulation`);
    try {
      const isProfitable = await dynamicStrategy.isAnyStrategyProfitable();
      
      if (isProfitable && config.simulationMode) {
        console.log("   Simulating execution (simulation mode)...");
        
        // In simulation mode, we would execute without real transactions
        console.log("   ✅ Execution simulation would proceed");
        console.log("   💡 Set SIMULATION_MODE=false for real execution");
      } else if (isProfitable) {
        console.log("   ⚠️  Profitable opportunities found but simulation mode disabled");
        console.log("   💡 Set SIMULATION_MODE=true for safe testing");
      } else {
        console.log("   📉 No profitable opportunities for execution simulation");
      }
      
      testsPassed++;
      console.log("   ✅ PASSED");
    } catch (error) {
      console.log("   ❌ FAILED:", error.message);
    }

    // Test Results Summary
    const endTime = Date.now();
    const duration = (endTime - startTime) / 1000;

    console.log("\n" + "=" .repeat(60));
    console.log("🧪 INTEGRATION TEST RESULTS");
    console.log("=" .repeat(60));
    console.log(`📊 Tests passed: ${testsPassed}/${testsTotal}`);
    console.log(`⏱️  Duration: ${duration.toFixed(2)} seconds`);
    console.log(`✅ Success rate: ${((testsPassed / testsTotal) * 100).toFixed(1)}%`);

    if (testsPassed === testsTotal) {
      console.log("\n🎉 ALL TESTS PASSED!");
      console.log("✅ Dynamic Flashloan Strategy is ready for deployment");
      
      console.log("\n📝 Deployment checklist:");
      console.log("   ✅ Strategy initialization works");
      console.log("   ✅ Market analysis functional");
      console.log("   ✅ Opportunity scanning works");
      console.log("   ✅ Profitability checks work");
      console.log("   ✅ Flashbots integration ready");
      console.log("   ✅ Gas estimation functional");
      console.log("   ✅ Performance tracking works");
      console.log("   ✅ Execution simulation ready");
      
      console.log("\n🚀 Ready to deploy:");
      console.log("   npm run deploy:dynamic:sepolia  # Test deployment");
      console.log("   npm run deploy:dynamic:mainnet  # Production deployment");
      
    } else {
      console.log("\n❌ SOME TESTS FAILED");
      console.log("🔧 Please fix the issues before deployment");
      process.exit(1);
    }

  } catch (error) {
    console.error("\n💥 Integration test failed:", error);
    process.exit(1);
  }
}

// Execute integration test
main().catch(error => {
  console.error("💥 Integration test script failed:", error);
  process.exit(1);
});
