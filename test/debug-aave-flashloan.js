const { ethers } = require('ethers');

async function debugAaveFlashloan() {
    console.log('🔍 Debugging Aave flashloan failure on Hardhat fork...');
    
    // Connect to Hardhat fork
    const provider = new ethers.JsonRpcProvider('http://127.0.0.1:8547');
    const wallet = new ethers.Wallet('0xac0974bec39a17e36ba4a6b4d238ff944bacb478cbed5efcae784d7bf4f2ff80', provider);
    
    console.log(`📋 Test Configuration:`);
    console.log(`   Provider: Hardhat fork (mainnet)`);
    console.log(`   Wallet: ${wallet.address}`);
    console.log(`   Balance: ${ethers.formatEther(await provider.getBalance(wallet.address))} ETH`);
    
    // Deploy the contract
    console.log('\n🚀 Deploying HybridFlashloanArbitrage contract...');
    
    const aavePoolAddressesProvider = '******************************************';
    const balancerVault = '******************************************';
    
    const fs = require('fs');
    const contractArtifact = JSON.parse(fs.readFileSync('./artifacts/contracts/HybridFlashloanArbitrage.sol/HybridFlashloanArbitrage.json', 'utf8'));
    
    const contractFactory = new ethers.ContractFactory(
        contractArtifact.abi,
        contractArtifact.bytecode,
        wallet
    );
    
    const contract = await contractFactory.deploy(aavePoolAddressesProvider, balancerVault);
    await contract.waitForDeployment();
    const contractAddress = await contract.getAddress();
    
    console.log(`✅ Contract deployed at: ${contractAddress}`);
    
    // Test the exact same transaction that's failing
    console.log('\n🧪 Testing the exact failing transaction...');
    
    const wethAddress = '******************************************';
    const usdcAddress = '******************************************';
    const v3Router = '******************************************';
    const v2Router = '******************************************';
    const flashloanAmount = ethers.parseEther('20'); // 20 ETH from the failing transaction
    
    const testParams = ethers.AbiCoder.defaultAbiCoder().encode(
        ['address', 'address', 'address', 'address', 'uint24', 'uint256', 'uint8'],
        [wethAddress, usdcAddress, v3Router, v2Router, 3000, ethers.parseEther('0.0001'), 0]
    );
    
    console.log(`   Testing 20 ETH flashloan (exact failing amount):`);
    
    try {
        await contract.executeOptimalFlashloan.staticCall(
            wethAddress,
            flashloanAmount,
            testParams
        );
        
        console.log(`      ✅ 20 ETH flashloan: SUCCESS`);
        
    } catch (error) {
        console.log(`      ❌ 20 ETH flashloan: ${error.message.split('(')[0]}`);
        
        // Check if it's our custom error message
        if (error.message.includes('Aave flashloan failed')) {
            console.log(`         🎯 Our error handling is working - now let's find the root cause`);
            
            // Try to get more details about the Aave error
            console.log(`         Error details: ${error.message}`);
            
            if (error.data) {
                console.log(`         Error data: ${error.data}`);
            }
        }
    }
    
    // Test with smaller amounts to see if it's an amount issue
    console.log('\n🧪 Testing with different amounts...');
    
    const testAmounts = [
        { amount: ethers.parseEther('0.1'), label: '0.1 ETH' },
        { amount: ethers.parseEther('1'), label: '1 ETH' },
        { amount: ethers.parseEther('5'), label: '5 ETH' },
        { amount: ethers.parseEther('10'), label: '10 ETH' }
    ];
    
    for (const test of testAmounts) {
        console.log(`\n   Testing ${test.label}:`);
        
        try {
            await contract.executeOptimalFlashloan.staticCall(
                wethAddress,
                test.amount,
                testParams
            );
            
            console.log(`      ✅ ${test.label}: SUCCESS`);
            
        } catch (error) {
            const errorMsg = error.message.split('(')[0];
            console.log(`      ❌ ${test.label}: ${errorMsg}`);
            
            if (errorMsg.includes('INSUFFICIENT_OUTPUT_AMOUNT')) {
                console.log(`         ✅ Trade execution error - flashloan itself works!`);
            } else if (errorMsg.includes('Aave flashloan failed')) {
                console.log(`         ❌ Aave flashloan still failing`);
            }
        }
    }
    
    // Check Aave pool liquidity
    console.log('\n🏦 Checking Aave pool liquidity...');
    
    try {
        const aavePool = new ethers.Contract(
            '******************************************', // Aave V3 Pool
            [
                'function getReserveData(address asset) view returns (tuple(uint256 configuration, uint128 liquidityIndex, uint128 currentLiquidityRate, uint128 variableBorrowIndex, uint128 currentVariableBorrowRate, uint128 currentStableBorrowRate, uint40 lastUpdateTimestamp, uint16 id, address aTokenAddress, address stableDebtTokenAddress, address variableDebtTokenAddress, address interestRateStrategyAddress, uint128 accruedToTreasury, uint128 unbacked, uint128 isolationModeTotalDebt))',
                'function getConfiguration(address asset) view returns (uint256)'
            ],
            provider
        );
        
        const reserveData = await aavePool.getReserveData(wethAddress);
        console.log(`   WETH aToken: ${reserveData.aTokenAddress}`);
        
        // Check aToken balance (available liquidity)
        const aToken = new ethers.Contract(
            reserveData.aTokenAddress,
            ['function totalSupply() view returns (uint256)'],
            provider
        );
        
        const totalSupply = await aToken.totalSupply();
        console.log(`   Available WETH liquidity: ${ethers.formatEther(totalSupply)} ETH`);
        
        if (totalSupply < ethers.parseEther('20')) {
            console.log(`   ❌ Insufficient liquidity for 20 ETH flashloan!`);
        } else {
            console.log(`   ✅ Sufficient liquidity for 20 ETH flashloan`);
        }
        
    } catch (error) {
        console.log(`   ❌ Error checking Aave liquidity: ${error.message}`);
    }
    
    // Test direct Aave flashloan call
    console.log('\n🧪 Testing direct Aave flashloan call...');
    
    try {
        const aavePool = new ethers.Contract(
            '******************************************',
            [
                'function flashLoanSimple(address receiverAddress, address asset, uint256 amount, bytes calldata params, uint16 referralCode) external'
            ],
            provider
        );
        
        // Try a direct flashloan call to see what error we get
        await aavePool.flashLoanSimple.staticCall(
            contractAddress,
            wethAddress,
            ethers.parseEther('20'),
            '0x',
            0
        );
        
        console.log(`   ✅ Direct Aave flashloan call would succeed`);
        
    } catch (error) {
        console.log(`   ❌ Direct Aave flashloan call failed: ${error.message.split('(')[0]}`);
        
        if (error.message.includes('FLASHLOAN_PREMIUM_INVALID')) {
            console.log(`      💡 Premium calculation issue`);
        } else if (error.message.includes('COLLATERAL_BALANCE_IS_ZERO')) {
            console.log(`      💡 No collateral in pool`);
        } else if (error.message.includes('RESERVE_LIQUIDITY_NOT_ZERO')) {
            console.log(`      💡 Reserve liquidity issue`);
        }
    }
    
    console.log('\n🏁 Aave flashloan debugging completed!');
}

debugAaveFlashloan().catch(console.error);
