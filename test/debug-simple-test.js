const { ethers } = require('ethers');

async function debugSimpleTest() {
    console.log('🔍 Simple test to isolate the enum conversion error...');
    
    // Connect to Hardhat fork
    const provider = new ethers.JsonRpcProvider('http://127.0.0.1:8547');
    const wallet = new ethers.Wallet('0xac0974bec39a17e36ba4a6b4d238ff944bacb478cbed5efcae784d7bf4f2ff80', provider);
    
    // Deploy the original contract
    const aavePoolAddressesProvider = '******************************************';
    const balancerVault = '******************************************';
    
    const fs = require('fs');
    const contractArtifact = JSON.parse(fs.readFileSync('./artifacts/contracts/HybridFlashloanArbitrage.sol/HybridFlashloanArbitrage.json', 'utf8'));
    
    const contractFactory = new ethers.ContractFactory(
        contractArtifact.abi,
        contractArtifact.bytecode,
        wallet
    );
    
    const contract = await contractFactory.deploy(aavePoolAddressesProvider, balancerVault);
    await contract.waitForDeployment();
    const contractAddress = await contract.getAddress();
    
    console.log(`✅ Contract deployed at: ${contractAddress}`);
    
    // Test parameters
    const wethAddress = '******************************************';
    const usdcAddress = '******************************************';
    const v2Router = await contract.UNISWAP_V2_ROUTER();
    const v3Router = await contract.UNISWAP_V3_ROUTER();
    
    console.log(`   V2 Router: ${v2Router}`);
    console.log(`   V3 Router: ${v3Router}`);
    
    // Key insight: Let's test if the issue is with the specific router addresses
    console.log('\n🧪 Testing with different router combinations...');
    
    const routerTests = [
        { name: 'V2 → V2', buy: v2Router, sell: v2Router },
        { name: 'V2 → V3', buy: v2Router, sell: v3Router },
        { name: 'V3 → V2', buy: v3Router, sell: v2Router },
        { name: 'V3 → V3', buy: v3Router, sell: v3Router },
        { name: 'Same address', buy: v2Router, sell: v2Router },
        { name: 'Zero addresses', buy: ethers.ZeroAddress, sell: ethers.ZeroAddress }
    ];
    
    const workingAmount = ethers.parseUnits('1000', 'wei');
    const failingAmount = ethers.parseUnits('1', 'gwei');
    
    for (const routerTest of routerTests) {
        console.log(`\n   Testing ${routerTest.name}:`);
        
        const testParams = ethers.AbiCoder.defaultAbiCoder().encode(
            ['address', 'address', 'address', 'address', 'uint24', 'uint256', 'uint8'],
            [wethAddress, usdcAddress, routerTest.buy, routerTest.sell, 3000, ethers.parseEther('0.0001'), 0]
        );
        
        // Test with working amount
        try {
            await contract.executeOptimalFlashloan.staticCall(wethAddress, workingAmount, testParams);
            console.log(`      ✅ Working amount (1000 wei): SUCCESS`);
        } catch (error) {
            const errorMsg = error.message.split('(')[0];
            console.log(`      ❌ Working amount: ${errorMsg}`);
        }
        
        // Test with failing amount
        try {
            await contract.executeOptimalFlashloan.staticCall(wethAddress, failingAmount, testParams);
            console.log(`      ✅ Failing amount (1 gwei): SUCCESS`);
        } catch (error) {
            const errorMsg = error.message.split('(')[0];
            console.log(`      ❌ Failing amount: ${errorMsg}`);
            
            if (error.data && error.data.startsWith('0x4e487b71')) {
                const panicCode = parseInt(error.data.slice(10, 74), 16);
                if (panicCode === 17) {
                    console.log(`         🚨 ENUM_CONVERSION_ERROR`);
                }
            }
        }
    }
    
    // Test if the issue is with the v3Fee parameter
    console.log('\n🧪 Testing with different v3Fee values...');
    
    const feeTests = [0, 500, 3000, 10000, 100000];
    
    for (const fee of feeTests) {
        console.log(`\n   Testing v3Fee: ${fee}`);
        
        const testParams = ethers.AbiCoder.defaultAbiCoder().encode(
            ['address', 'address', 'address', 'address', 'uint24', 'uint256', 'uint8'],
            [wethAddress, usdcAddress, v2Router, v3Router, fee, ethers.parseEther('0.0001'), 0]
        );
        
        try {
            await contract.executeOptimalFlashloan.staticCall(wethAddress, failingAmount, testParams);
            console.log(`      ✅ v3Fee ${fee}: SUCCESS`);
        } catch (error) {
            const errorMsg = error.message.split('(')[0];
            console.log(`      ❌ v3Fee ${fee}: ${errorMsg}`);
            
            if (error.data && error.data.startsWith('0x4e487b71')) {
                const panicCode = parseInt(error.data.slice(10, 74), 16);
                if (panicCode === 17) {
                    console.log(`         🚨 ENUM_CONVERSION_ERROR`);
                }
            }
        }
    }
    
    // Test if the issue is with the minProfit parameter
    console.log('\n🧪 Testing with different minProfit values...');
    
    const profitTests = [
        ethers.parseEther('0'),
        ethers.parseEther('0.0001'),
        ethers.parseEther('0.001'),
        ethers.parseEther('1'),
        ethers.parseEther('1000')
    ];
    
    for (const profit of profitTests) {
        console.log(`\n   Testing minProfit: ${ethers.formatEther(profit)} ETH`);
        
        const testParams = ethers.AbiCoder.defaultAbiCoder().encode(
            ['address', 'address', 'address', 'address', 'uint24', 'uint256', 'uint8'],
            [wethAddress, usdcAddress, v2Router, v3Router, 3000, profit, 0]
        );
        
        try {
            await contract.executeOptimalFlashloan.staticCall(wethAddress, failingAmount, testParams);
            console.log(`      ✅ minProfit ${ethers.formatEther(profit)}: SUCCESS`);
        } catch (error) {
            const errorMsg = error.message.split('(')[0];
            console.log(`      ❌ minProfit ${ethers.formatEther(profit)}: ${errorMsg}`);
            
            if (error.data && error.data.startsWith('0x4e487b71')) {
                const panicCode = parseInt(error.data.slice(10, 74), 16);
                if (panicCode === 17) {
                    console.log(`         🚨 ENUM_CONVERSION_ERROR`);
                }
            }
        }
    }
    
    // Test a theory: maybe the issue is with gas estimation
    console.log('\n🧪 Testing with different gas limits...');
    
    const gasLimits = [100000, 500000, 1000000, 5000000, 10000000];
    
    const standardParams = ethers.AbiCoder.defaultAbiCoder().encode(
        ['address', 'address', 'address', 'address', 'uint24', 'uint256', 'uint8'],
        [wethAddress, usdcAddress, v2Router, v3Router, 3000, ethers.parseEther('0.0001'), 0]
    );
    
    for (const gasLimit of gasLimits) {
        try {
            await contract.executeOptimalFlashloan.staticCall(
                wethAddress, 
                failingAmount, 
                standardParams,
                { gasLimit }
            );
            console.log(`   ✅ Gas limit ${gasLimit}: SUCCESS`);
        } catch (error) {
            const errorMsg = error.message.split('(')[0];
            console.log(`   ❌ Gas limit ${gasLimit}: ${errorMsg}`);
        }
    }
    
    console.log('\n🏁 Simple test completed!');
}

debugSimpleTest().catch(console.error);
