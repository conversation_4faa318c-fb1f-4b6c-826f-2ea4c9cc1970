const { ethers } = require('ethers');

async function debugExecutionLine() {
    console.log('🔍 Debugging exact execution line that fails...');
    
    // Connect to mainnet
    const provider = new ethers.JsonRpcProvider('http://************:8545');
    const wallet = new ethers.Wallet('0xac0974bec39a17e36ba4a6b4d238ff944bacb478cbed5efcae784d7bf4f2ff80', provider);
    
    // Current mainnet contract address
    const contractAddress = '******************************************';
    
    console.log(`📋 Configuration:`);
    console.log(`   Contract: ${contractAddress}`);
    
    // Test the new safe V3 quoter function directly
    console.log('\n🧪 Testing _getV3QuoteSafe function directly...');
    
    const wethAddress = '******************************************';
    const daiAddress = '******************************************';
    
    // Test the V3 quoter directly with a low-level call
    const quoterAddress = '******************************************';
    
    try {
        console.log('   Testing V3 quoter directly...');
        
        // Encode the quoter call
        const quoterInterface = new ethers.Interface([
            'function quoteExactInputSingle(address tokenIn, address tokenOut, uint24 fee, uint256 amountIn, uint160 sqrtPriceLimitX96) external returns (uint256 amountOut)'
        ]);
        
        const quoterData = quoterInterface.encodeFunctionData('quoteExactInputSingle', [
            wethAddress,
            daiAddress,
            3000,
            ethers.parseEther('1.0'),
            0
        ]);
        
        // Try static call to quoter
        const result = await provider.call({
            to: quoterAddress,
            data: quoterData
        });
        
        if (result && result !== '0x') {
            const amountOut = ethers.AbiCoder.defaultAbiCoder().decode(['uint256'], result)[0];
            console.log(`   ✅ V3 quoter works: ${ethers.formatUnits(amountOut, 18)} DAI for 1 WETH`);
        } else {
            console.log(`   ❌ V3 quoter returned empty result`);
        }
        
    } catch (error) {
        console.log(`   ❌ V3 quoter failed: ${error.message.split('(')[0]}`);
        console.log(`   💡 This might be the source of the execution revert`);
    }
    
    // Test if the issue is in the checkProfitability function specifically
    console.log('\n🧪 Testing contract functions step by step...');
    
    // Create a minimal contract interface to test individual functions
    const contract = new ethers.Contract(contractAddress, [
        'function owner() external view returns (address)',
        'function paused() external view returns (bool)',
        'function CHAIN_ID() external view returns (uint256)',
        'function UNISWAP_V2_ROUTER() external view returns (address)',
        'function UNISWAP_V3_ROUTER() external view returns (address)',
        'function supportedRouters(address) external view returns (bool)'
    ], provider);
    
    try {
        console.log('   Testing basic contract state...');
        
        const owner = await contract.owner();
        const paused = await contract.paused();
        const chainId = await contract.CHAIN_ID();
        
        console.log(`   ✅ Basic state: owner=${owner.slice(0,6)}..., paused=${paused}, chainId=${chainId}`);
        
        const v2Router = await contract.UNISWAP_V2_ROUTER();
        const v3Router = await contract.UNISWAP_V3_ROUTER();
        
        console.log(`   ✅ Router addresses: V2=${v2Router.slice(0,6)}..., V3=${v3Router.slice(0,6)}...`);
        
        const v2Supported = await contract.supportedRouters(v2Router);
        const v3Supported = await contract.supportedRouters(v3Router);
        
        console.log(`   ✅ Router support: V2=${v2Supported}, V3=${v3Supported}`);
        
    } catch (error) {
        console.log(`   ❌ Basic contract calls failed: ${error.message}`);
        console.log(`   💡 Contract might not be deployed correctly`);
        return;
    }
    
    // Test the checkProfitability function with minimal parameters
    console.log('\n🧪 Testing checkProfitability with absolute minimal parameters...');
    
    const minimalContract = new ethers.Contract(contractAddress, [
        'function checkProfitability(address asset, uint256 amount, bytes calldata params) external view returns (bool, uint256, uint256)'
    ], provider);
    
    // Create the absolute minimal parameters
    const minimalParams = ethers.AbiCoder.defaultAbiCoder().encode(
        ['address[]', 'address[]', 'address', 'address', 'uint24[]', 'uint256', 'uint8', 'uint256', 'uint256'],
        [
            [wethAddress, daiAddress],               // buyPath
            [daiAddress, wethAddress],               // sellPath
            await contract.UNISWAP_V2_ROUTER(),      // buyDex (V2)
            await contract.UNISWAP_V2_ROUTER(),      // sellDex (V2)
            [],                                      // v3Fees (empty for V2)
            ethers.parseEther('0.001'),              // minProfit
            0,                                       // provider (AAVE)
            100,                                     // slippageToleranceBps (1%)
            ethers.parseUnits('50', 'gwei')          // maxGasCostWei
        ]
    );
    
    try {
        console.log('   Testing with minimal V2-only parameters...');
        
        const [profitable, expectedProfit, gasEstimate] = await minimalContract.checkProfitability(
            wethAddress,
            ethers.parseUnits('100', 'finney'), // 0.1 ETH
            minimalParams
        );
        
        console.log(`   ✅ checkProfitability SUCCESS with minimal params!`);
        console.log(`      Profitable: ${profitable}`);
        console.log(`      Expected Profit: ${ethers.formatEther(expectedProfit)} ETH`);
        console.log(`      Gas Estimate: ${ethers.formatUnits(gasEstimate, 'gwei')} gwei`);
        
        console.log(`\n💡 BREAKTHROUGH: Minimal parameters work!`);
        console.log(`   The issue is likely in the V3-specific logic or parameter encoding.`);
        
    } catch (error) {
        console.log(`   ❌ Even minimal parameters failed: ${error.message.split('(')[0]}`);
        
        // If even minimal parameters fail, the issue is fundamental
        console.log(`\n💡 FUNDAMENTAL ISSUE IDENTIFIED:`);
        console.log(`   The contract has a basic execution problem.`);
        console.log(`   This could be:`);
        console.log(`   1. Contract not properly initialized`);
        console.log(`   2. Missing dependencies or interfaces`);
        console.log(`   3. Gas limit issues in view functions`);
        console.log(`   4. State corruption in the contract`);
        
        // Try to get more specific error information
        if (error.data) {
            console.log(`   Raw error data: ${error.data}`);
        }
    }
    
    console.log('\n🎯 NEXT STEPS:');
    console.log('   Based on this analysis, we can determine if the issue is:');
    console.log('   1. V3-specific (if minimal V2 params work)');
    console.log('   2. Fundamental contract issue (if even minimal params fail)');
    console.log('   3. Parameter encoding problem');
    console.log('   4. External dependency issue');
}

debugExecutionLine().catch(console.error);
