const { ethers } = require('ethers');

async function systematicDebug() {
    console.log('🔍 SYSTEMATIC DEBUGGING PLAN');
    console.log('═══════════════════════════════════════════════════════════════');
    
    // Connect to mainnet
    const provider = new ethers.JsonRpcProvider('http://************:8545');
    const wallet = new ethers.Wallet('0xac0974bec39a17e36ba4a6b4d238ff944bacb478cbed5efcae784d7bf4f2ff80', provider);
    
    // Current mainnet contract address
    const contractAddress = '******************************************';
    
    console.log(`📋 Configuration:`);
    console.log(`   Contract: ${contractAddress}`);
    console.log(`   Network: Mainnet`);
    
    // STEP 1: LOG EXACT PARAMETERS BEING SENT
    console.log('\n🔍 STEP 1: LOGGING EXACT PARAMETERS');
    console.log('─'.repeat(50));
    
    // Get router addresses from contract
    const contract = new ethers.Contract(contractAddress, [
        'function UNISWAP_V2_ROUTER() external view returns (address)',
        'function UNISWAP_V3_ROUTER() external view returns (address)',
        'function checkProfitability(address asset, uint256 amount, bytes calldata params) external view returns (bool, uint256, uint256)',
        'function executeOptimalFlashloan(address asset, uint256 amount, bytes calldata params) external'
    ], wallet);
    
    const v2Router = await contract.UNISWAP_V2_ROUTER();
    const v3Router = await contract.UNISWAP_V3_ROUTER();
    
    // Token addresses
    const wethAddress = '******************************************';
    const daiAddress = '******************************************';
    
    // Create the exact parameters that the bot would send
    const botParameters = {
        asset: wethAddress,
        amount: ethers.parseEther('2.0'),
        buyPath: [wethAddress, daiAddress],
        sellPath: [daiAddress, wethAddress],
        buyDex: v3Router,
        sellDex: v2Router,
        v3Fees: [3000],
        minProfit: ethers.parseEther('0.00001'),
        provider: 0, // AAVE
        slippageToleranceBps: 1000, // 10%
        maxGasCostWei: ethers.parseUnits('200', 'gwei')
    };
    
    console.log('📊 EXACT PARAMETERS BEING SENT:');
    console.log(`   Asset: ${botParameters.asset}`);
    console.log(`   Amount: ${ethers.formatEther(botParameters.amount)} ETH`);
    console.log(`   Buy Path: [${botParameters.buyPath.map(addr => addr.slice(0,6) + '...' + addr.slice(-4)).join(' → ')}]`);
    console.log(`   Sell Path: [${botParameters.sellPath.map(addr => addr.slice(0,6) + '...' + addr.slice(-4)).join(' → ')}]`);
    console.log(`   Buy DEX: ${botParameters.buyDex} (${botParameters.buyDex === v3Router ? 'V3' : 'V2'})`);
    console.log(`   Sell DEX: ${botParameters.sellDex} (${botParameters.sellDex === v2Router ? 'V2' : 'V3'})`);
    console.log(`   V3 Fees: [${botParameters.v3Fees.map(fee => Number(fee)/10000 + '%').join(', ')}]`);
    console.log(`   Min Profit: ${ethers.formatEther(botParameters.minProfit)} ETH`);
    console.log(`   Provider: ${botParameters.provider} (${botParameters.provider === 0 ? 'AAVE' : 'BALANCER'})`);
    console.log(`   Slippage: ${botParameters.slippageToleranceBps} bps (${botParameters.slippageToleranceBps/100}%)`);
    console.log(`   Max Gas: ${ethers.formatUnits(botParameters.maxGasCostWei, 'gwei')} gwei`);
    
    // Encode parameters exactly as the bot would
    const encodedParams = ethers.AbiCoder.defaultAbiCoder().encode(
        ['address[]', 'address[]', 'address', 'address', 'uint24[]', 'uint256', 'uint8', 'uint256', 'uint256'],
        [
            botParameters.buyPath,
            botParameters.sellPath,
            botParameters.buyDex,
            botParameters.sellDex,
            botParameters.v3Fees,
            botParameters.minProfit,
            botParameters.provider,
            botParameters.slippageToleranceBps,
            botParameters.maxGasCostWei
        ]
    );
    
    console.log(`   Encoded Params Length: ${encodedParams.length} bytes`);
    console.log(`   Encoded Params: ${encodedParams.substring(0, 100)}...`);
    
    // STEP 2: MANUAL VALIDATION CHECK
    console.log('\n🔍 STEP 2: MANUAL VALIDATION CHECK');
    console.log('─'.repeat(50));
    
    console.log('📋 Checking each validation requirement:');
    
    // Check 1: Asset matches first buy token
    const assetMatch = botParameters.asset.toLowerCase() === botParameters.buyPath[0].toLowerCase();
    console.log(`   ✓ Asset matches buy path[0]: ${assetMatch ? '✅ PASS' : '❌ FAIL'}`);
    
    // Check 2: Buy path length
    const buyPathValid = botParameters.buyPath.length >= 2 && botParameters.buyPath.length <= 5;
    console.log(`   ✓ Buy path length (2-5): ${buyPathValid ? '✅ PASS' : '❌ FAIL'} (${botParameters.buyPath.length})`);
    
    // Check 3: Sell path length
    const sellPathValid = botParameters.sellPath.length >= 2 && botParameters.sellPath.length <= 5;
    console.log(`   ✓ Sell path length (2-5): ${sellPathValid ? '✅ PASS' : '❌ FAIL'} (${botParameters.sellPath.length})`);
    
    // Check 4: Complete arbitrage loop
    const firstBuyToken = botParameters.buyPath[0];
    const lastSellToken = botParameters.sellPath[botParameters.sellPath.length - 1];
    const formsLoop = firstBuyToken.toLowerCase() === lastSellToken.toLowerCase();
    console.log(`   ✓ Forms complete loop: ${formsLoop ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`     First buy: ${firstBuyToken}`);
    console.log(`     Last sell: ${lastSellToken}`);
    
    // Check 5: No zero addresses
    let hasZeroAddress = false;
    for (const addr of [...botParameters.buyPath, ...botParameters.sellPath, botParameters.buyDex, botParameters.sellDex]) {
        if (addr === ethers.ZeroAddress) {
            hasZeroAddress = true;
            break;
        }
    }
    console.log(`   ✓ No zero addresses: ${!hasZeroAddress ? '✅ PASS' : '❌ FAIL'}`);
    
    // Check 6: V3 fees validation
    const maxPossibleFees = (botParameters.buyPath.length - 1) + (botParameters.sellPath.length - 1);
    const v3FeesValid = botParameters.v3Fees.length <= maxPossibleFees && 
                       (botParameters.v3Fees.length === 0 || botParameters.v3Fees.length >= 1);
    console.log(`   ✓ V3 fees valid: ${v3FeesValid ? '✅ PASS' : '❌ FAIL'} (${botParameters.v3Fees.length} fees, max ${maxPossibleFees})`);
    
    // Check 7: Parameter ranges
    const minProfitValid = botParameters.minProfit > 0n;
    const slippageValid = botParameters.slippageToleranceBps <= 5000;
    const gasCostValid = botParameters.maxGasCostWei > 0n;
    
    console.log(`   ✓ Min profit > 0: ${minProfitValid ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`   ✓ Slippage <= 50%: ${slippageValid ? '✅ PASS' : '❌ FAIL'} (${botParameters.slippageToleranceBps/100}%)`);
    console.log(`   ✓ Gas cost > 0: ${gasCostValid ? '✅ PASS' : '❌ FAIL'}`);
    
    // Check 8: DEX router support (requires contract call)
    try {
        const buyDexSupported = await contract.supportedRouters(botParameters.buyDex);
        const sellDexSupported = await contract.supportedRouters(botParameters.sellDex);
        
        console.log(`   ✓ Buy DEX supported: ${buyDexSupported ? '✅ PASS' : '❌ FAIL'}`);
        console.log(`   ✓ Sell DEX supported: ${sellDexSupported ? '✅ PASS' : '❌ FAIL'}`);
        
    } catch (error) {
        console.log(`   ❌ Error checking DEX support: ${error.message}`);
    }
    
    // STEP 3: USE checkProfitability FUNCTION
    console.log('\n🔍 STEP 3: USING checkProfitability FUNCTION');
    console.log('─'.repeat(50));
    
    try {
        console.log('📊 Testing checkProfitability (view function)...');
        
        const [profitable, expectedProfit, gasEstimate] = await contract.checkProfitability(
            botParameters.asset,
            botParameters.amount,
            encodedParams
        );
        
        console.log(`   ✅ checkProfitability SUCCESS:`);
        console.log(`      Profitable: ${profitable}`);
        console.log(`      Expected Profit: ${ethers.formatEther(expectedProfit)} ETH`);
        console.log(`      Gas Estimate: ${ethers.formatUnits(gasEstimate, 'gwei')} gwei`);
        
        if (!profitable) {
            console.log(`   💡 Not profitable, but validation passed - this is expected behavior`);
        }
        
    } catch (error) {
        console.log(`   ❌ checkProfitability FAILED:`);
        console.log(`      Error: ${error.message.split('(')[0]}`);
        
        // Analyze the specific error
        if (error.message.includes('execution reverted') && !error.message.includes('reason')) {
            console.log(`      💡 Generic execution revert - likely in validation logic`);
        } else if (error.message.includes('Buy path too short')) {
            console.log(`      💡 FOUND ISSUE: Buy path validation failing`);
        } else if (error.message.includes('Sell path too short')) {
            console.log(`      💡 FOUND ISSUE: Sell path validation failing`);
        } else if (error.message.includes('complete loop')) {
            console.log(`      💡 FOUND ISSUE: Arbitrage loop validation failing`);
        } else if (error.message.includes('Invalid V3 fees')) {
            console.log(`      💡 FOUND ISSUE: V3 fees validation failing`);
        } else if (error.message.includes('Unsupported')) {
            console.log(`      💡 FOUND ISSUE: DEX router not supported`);
        } else if (error.message.includes('Slippage tolerance too high')) {
            console.log(`      💡 FOUND ISSUE: Slippage validation failing`);
        }
        
        // Try to decode error data for more details
        if (error.data) {
            console.log(`      Raw error data: ${error.data}`);
        }
    }
    
    // STEP 4: COMPARE WITH WORKING PARAMETERS
    console.log('\n🔍 STEP 4: TESTING WITH SIMPLIFIED PARAMETERS');
    console.log('─'.repeat(50));
    
    // Test with minimal parameters that should definitely work
    const simpleParams = ethers.AbiCoder.defaultAbiCoder().encode(
        ['address[]', 'address[]', 'address', 'address', 'uint24[]', 'uint256', 'uint8', 'uint256', 'uint256'],
        [
            [wethAddress, daiAddress],               // Simple 2-hop path
            [daiAddress, wethAddress],               // Simple 2-hop path
            v2Router,                                // Both V2 (no V3 fees needed)
            v2Router,                                // Both V2
            [],                                      // No V3 fees
            ethers.parseEther('0.001'),              // Higher min profit
            0,                                       // AAVE
            100,                                     // 1% slippage
            ethers.parseUnits('50', 'gwei')          // Lower gas cost
        ]
    );
    
    try {
        console.log('📊 Testing with simplified parameters (both V2, no V3 fees)...');
        
        const [profitable, expectedProfit, gasEstimate] = await contract.checkProfitability(
            wethAddress,
            ethers.parseEther('0.1'), // Smaller amount
            simpleParams
        );
        
        console.log(`   ✅ Simplified test SUCCESS:`);
        console.log(`      Profitable: ${profitable}`);
        console.log(`      Expected Profit: ${ethers.formatEther(expectedProfit)} ETH`);
        console.log(`      Gas Estimate: ${ethers.formatUnits(gasEstimate, 'gwei')} gwei`);
        
    } catch (error) {
        console.log(`   ❌ Even simplified test FAILED:`);
        console.log(`      Error: ${error.message.split('(')[0]}`);
        console.log(`      💡 This indicates a fundamental issue in the contract`);
    }
    
    console.log('\n🎯 DEBUGGING SUMMARY:');
    console.log('═'.repeat(60));
    console.log('   This systematic approach should identify the exact validation');
    console.log('   requirement that is failing and provide a clear path to fix it.');
}

systematicDebug().catch(console.error);
