const { ethers } = require('ethers');

async function testContractState() {
    console.log('🔍 Testing contract state and supported routers...');
    
    // Connect to mainnet
    const provider = new ethers.JsonRpcProvider('http://************:8545');
    
    // Contract address
    const contractAddress = '******************************************';
    
    // Contract interface
    const contractInterface = [
        'function UNISWAP_V2_ROUTER() external view returns (address)',
        'function UNISWAP_V3_ROUTER() external view returns (address)',
        'function UNISWAP_V3_QUOTER() external view returns (address)',
        'function supportedRouters(address) external view returns (bool)',
        'function CHAIN_ID() external view returns (uint256)'
    ];
    
    const contract = new ethers.Contract(contractAddress, contractInterface, provider);
    
    try {
        console.log('📋 Contract State Analysis:');
        
        // Get chain ID
        const chainId = await contract.CHAIN_ID();
        console.log(`   Chain ID: ${chainId}`);
        
        // Get router addresses
        const v2Router = await contract.UNISWAP_V2_ROUTER();
        const v3Router = await contract.UNISWAP_V3_ROUTER();
        const v3Quoter = await contract.UNISWAP_V3_QUOTER();
        
        console.log(`   V2 Router: ${v2Router}`);
        console.log(`   V3 Router: ${v3Router}`);
        console.log(`   V3 Quoter: ${v3Quoter}`);
        
        // Check if routers are supported
        const v2Supported = await contract.supportedRouters(v2Router);
        const v3Supported = await contract.supportedRouters(v3Router);
        
        console.log('\n🔍 Supported Router Status:');
        console.log(`   V2 Router supported: ${v2Supported ? '✅ YES' : '❌ NO'}`);
        console.log(`   V3 Router supported: ${v3Supported ? '✅ YES' : '❌ NO'}`);
        
        // Check the specific addresses from the failing transaction
        const failingBuyDex = '0xE592427A0AEce92De3Edee1F18E0157C05861564';
        const failingSellDex = '0x7a250d5630B4cF539739dF2C5dAcb4c659F2488D';
        
        const buyDexSupported = await contract.supportedRouters(failingBuyDex);
        const sellDexSupported = await contract.supportedRouters(failingSellDex);
        
        console.log('\n🔍 Failing Transaction DEX Status:');
        console.log(`   Buy DEX (${failingBuyDex}): ${buyDexSupported ? '✅ SUPPORTED' : '❌ NOT SUPPORTED'}`);
        console.log(`   Sell DEX (${failingSellDex}): ${sellDexSupported ? '✅ SUPPORTED' : '❌ NOT SUPPORTED'}`);
        
        // Check SushiSwap support
        const sushiRouter = '0xd9e1cE17f2641f24aE83637ab66a2cca9C378B9F';
        const sushiSupported = await contract.supportedRouters(sushiRouter);
        console.log(`   SushiSwap (${sushiRouter}): ${sushiSupported ? '✅ SUPPORTED' : '❌ NOT SUPPORTED'}`);
        
        // Address comparison
        console.log('\n🔍 Address Comparison:');
        console.log(`   Contract V2 == Failing Sell: ${v2Router.toLowerCase() === failingSellDex.toLowerCase()}`);
        console.log(`   Contract V3 == Failing Buy: ${v3Router.toLowerCase() === failingBuyDex.toLowerCase()}`);
        
        if (!buyDexSupported || !sellDexSupported) {
            console.log('\n💡 ISSUE IDENTIFIED:');
            console.log('   ❌ One or both DEX routers are not in the supportedRouters mapping!');
            console.log('   💡 This is causing the require(false) error in the contract validation.');
            console.log('\n🔧 SOLUTION:');
            console.log('   1. The contract initialization might have failed');
            console.log('   2. The addresses might have case sensitivity issues');
            console.log('   3. The _initializeSupportedRouters() function might not have been called');
        } else {
            console.log('\n✅ All DEX routers are properly supported!');
            console.log('   💡 The issue might be elsewhere in the validation logic.');
        }
        
    } catch (error) {
        console.log(`❌ Error testing contract state: ${error.message}`);
    }
}

testContractState().catch(console.error);
