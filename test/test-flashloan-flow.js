const { ethers } = require('ethers');

// Load configuration
require('dotenv').config();

async function testFlashloanFlow() {
  console.log('🧪 Testing Flashloan Attack Flow');
  console.log('=' .repeat(50));

  try {
    const provider = new ethers.JsonRpcProvider(process.env.RPC_URL);
    const wallet = new ethers.Wallet(process.env.PRIVATE_KEY, provider);

    // 1. Test Pool Data Retrieval
    console.log('\n1. 🏊 Testing Pool Data Retrieval');
    
    const tokens = {
      WETH: '******************************************',
      USDC: '******************************************',
      USDT: '******************************************',
      DAI: '******************************************'
    };

    // Test Uniswap V2 WETH/USDC pool
    const uniV2Factory = '******************************************';
    const factoryContract = new ethers.Contract(
      uniV2Factory,
      ['function getPair(address tokenA, address tokenB) external view returns (address pair)'],
      provider
    );

    const wethUsdcV2Pair = await factoryContract.getPair(tokens.WETH, tokens.USDC);
    console.log(`   Uniswap V2 WETH/USDC pair: ${wethUsdcV2Pair}`);

    if (wethUsdcV2Pair !== ethers.ZeroAddress) {
      const pairContract = new ethers.Contract(
        wethUsdcV2Pair,
        [
          'function getReserves() external view returns (uint112 reserve0, uint112 reserve1, uint32 blockTimestampLast)',
          'function token0() external view returns (address)',
          'function token1() external view returns (address)'
        ],
        provider
      );

      const reserves = await pairContract.getReserves();
      const token0 = await pairContract.token0();
      const token1 = await pairContract.token1();
      
      console.log(`   ✅ V2 Pool found with reserves:`);
      console.log(`      Token0 (${token0}): ${ethers.formatUnits(reserves.reserve0, 18)}`);
      console.log(`      Token1 (${token1}): ${ethers.formatUnits(reserves.reserve1, 6)}`);
    } else {
      console.log('   ❌ V2 WETH/USDC pair not found');
    }

    // Test Uniswap V3 WETH/USDC pool
    const uniV3Factory = '******************************************';
    const v3FactoryContract = new ethers.Contract(
      uniV3Factory,
      ['function getPool(address tokenA, address tokenB, uint24 fee) external view returns (address pool)'],
      provider
    );

    const wethUsdcV3Pool = await v3FactoryContract.getPool(tokens.WETH, tokens.USDC, 3000); // 0.3% fee
    console.log(`   Uniswap V3 WETH/USDC pool (0.3%): ${wethUsdcV3Pool}`);

    if (wethUsdcV3Pool !== ethers.ZeroAddress) {
      const v3PoolContract = new ethers.Contract(
        wethUsdcV3Pool,
        [
          'function slot0() external view returns (uint160 sqrtPriceX96, int24 tick, uint16 observationIndex, uint16 observationCardinality, uint16 observationCardinalityNext, uint8 feeProtocol, bool unlocked)',
          'function liquidity() external view returns (uint128)'
        ],
        provider
      );

      const slot0 = await v3PoolContract.slot0();
      const liquidity = await v3PoolContract.liquidity();
      
      console.log(`   ✅ V3 Pool found:`);
      console.log(`      Current tick: ${slot0.tick}`);
      console.log(`      Liquidity: ${liquidity.toString()}`);
      console.log(`      SqrtPriceX96: ${slot0.sqrtPriceX96.toString()}`);
    } else {
      console.log('   ❌ V3 WETH/USDC pool not found');
    }

    // 2. Test Price Calculations
    console.log('\n2. 💰 Testing Price Calculations');
    
    if (wethUsdcV2Pair !== ethers.ZeroAddress && wethUsdcV3Pool !== ethers.ZeroAddress) {
      // Calculate V2 price
      const pairContract = new ethers.Contract(
        wethUsdcV2Pair,
        ['function getReserves() external view returns (uint112 reserve0, uint112 reserve1, uint32 blockTimestampLast)'],
        provider
      );
      const reserves = await pairContract.getReserves();
      
      // Assuming token0 is USDC and token1 is WETH (need to verify)
      const v2Price = Number(reserves.reserve0) / Number(reserves.reserve1) * (10**12); // Adjust for decimals
      console.log(`   V2 Price (USDC per WETH): ${v2Price.toFixed(2)}`);

      // Calculate V3 price from tick
      const v3PoolContract = new ethers.Contract(
        wethUsdcV3Pool,
        ['function slot0() external view returns (uint160 sqrtPriceX96, int24 tick, uint16 observationIndex, uint16 observationCardinality, uint16 observationCardinalityNext, uint8 feeProtocol, bool unlocked)'],
        provider
      );
      const slot0 = await v3PoolContract.slot0();
      
      // Convert tick to price (simplified)
      const v3Price = Math.pow(1.0001, Number(slot0.tick));
      console.log(`   V3 Price (from tick): ${v3Price.toFixed(2)}`);
      
      const priceDiff = Math.abs(v2Price - v3Price);
      const priceSpread = (priceDiff / Math.min(v2Price, v3Price)) * 100;
      console.log(`   Price spread: ${priceSpread.toFixed(4)}%`);
      
      if (priceSpread > 0.5) {
        console.log(`   🎯 Potential arbitrage opportunity detected!`);
      } else {
        console.log(`   📉 No significant price difference found`);
      }
    }

    // 3. Test Contract Interface
    console.log('\n3. 📜 Testing Contract Interface');
    
    const contractAddress = process.env.HYBRID_FLASHLOAN_CONTRACT;
    const contractInterface = new ethers.Interface([
      'function executeOptimalFlashloan(address asset, uint256 amount, bytes calldata params) external',
      'function owner() external view returns (address)',
      'function CHAIN_ID() external view returns (uint256)'
    ]);

    const contract = new ethers.Contract(contractAddress, contractInterface, provider);
    
    try {
      const owner = await contract.owner();
      const chainId = await contract.CHAIN_ID();
      
      console.log(`   ✅ Contract interface working`);
      console.log(`      Owner: ${owner}`);
      console.log(`      Chain ID: ${chainId}`);
      
      if (owner.toLowerCase() === wallet.address.toLowerCase()) {
        console.log(`   ✅ Wallet is contract owner`);
      } else {
        console.log(`   ❌ Wallet is NOT contract owner`);
      }
    } catch (error) {
      console.log(`   ❌ Contract interface error: ${error.message}`);
    }

    // 4. Test Gas Estimation
    console.log('\n4. ⛽ Testing Gas Estimation');
    
    try {
      const feeData = await provider.getFeeData();
      console.log(`   Current gas price: ${ethers.formatUnits(feeData.gasPrice || 0n, 'gwei')} gwei`);
      console.log(`   Max fee per gas: ${ethers.formatUnits(feeData.maxFeePerGas || 0n, 'gwei')} gwei`);
      console.log(`   Priority fee: ${ethers.formatUnits(feeData.maxPriorityFeePerGas || 0n, 'gwei')} gwei`);
      
      // Estimate gas for a simple flashloan call (dry run)
      const testAmount = ethers.parseEther('1'); // 1 WETH
      const testParams = ethers.AbiCoder.defaultAbiCoder().encode(
        ['uint8', 'address', 'address', 'address', 'address', 'uint24', 'uint256'],
        [0, tokens.WETH, tokens.USDC, ethers.ZeroAddress, ethers.ZeroAddress, 3000, ethers.parseEther('0.001')]
      );
      
      try {
        const gasEstimate = await contract.executeOptimalFlashloan.estimateGas(
          tokens.WETH,
          testAmount,
          testParams
        );
        console.log(`   ✅ Gas estimate for flashloan: ${gasEstimate.toString()}`);
        
        const gasCost = gasEstimate * (feeData.gasPrice || 0n);
        console.log(`   Estimated gas cost: ${ethers.formatEther(gasCost)} ETH`);
      } catch (error) {
        console.log(`   ⚠️  Gas estimation failed (expected in dry run): ${error.message.slice(0, 100)}...`);
      }
    } catch (error) {
      console.log(`   ❌ Gas data retrieval failed: ${error.message}`);
    }

    // 5. Test Token Balances
    console.log('\n5. 🪙 Testing Token Balances');
    
    const erc20Abi = [
      'function balanceOf(address owner) external view returns (uint256)',
      'function decimals() external view returns (uint8)',
      'function symbol() external view returns (string)'
    ];

    for (const [symbol, address] of Object.entries(tokens)) {
      try {
        const tokenContract = new ethers.Contract(address, erc20Abi, provider);
        const balance = await tokenContract.balanceOf(wallet.address);
        const decimals = await tokenContract.decimals();
        const tokenSymbol = await tokenContract.symbol();
        
        console.log(`   ${tokenSymbol}: ${ethers.formatUnits(balance, decimals)}`);
      } catch (error) {
        console.log(`   ${symbol}: Error reading balance`);
      }
    }

    console.log('\n' + '='.repeat(50));
    console.log('✅ Flow test completed successfully!');
    console.log('\n🚀 Ready to run the MEV bot with:');
    console.log('   npm run dev');

  } catch (error) {
    console.error('❌ Flow test failed:', error.message);
    console.error(error.stack);
  }
}

// Run test
testFlashloanFlow().catch(console.error);
