const { ethers } = require('ethers');

async function debugEnumComparison() {
    console.log('🔍 Debugging enum comparison in contract...');
    
    // Connect to Hardhat fork
    const provider = new ethers.JsonRpcProvider('http://127.0.0.1:8547');
    const wallet = new ethers.Wallet('0xac0974bec39a17e36ba4a6b4d238ff944bacb478cbed5efcae784d7bf4f2ff80', provider);
    
    // Create a minimal test contract to isolate the enum comparison issue
    const testContractCode = `
        // SPDX-License-Identifier: MIT
        pragma solidity ^0.8.19;
        
        import "@openzeppelin/contracts/access/Ownable.sol";
        
        contract EnumComparisonTest is Ownable {
            enum FlashloanProvider { AAVE, BALANCER }
            
            struct ArbitrageParams {
                address tokenA;
                address tokenB;
                address buyDex;
                address sellDex;
                uint24 v3Fee;
                uint256 minProfit;
                FlashloanProvider provider;
            }
            
            constructor() Ownable(msg.sender) {}
            
            function testEnumComparison(bytes calldata params) external view returns (string memory) {
                // Step 1: Decode parameters
                ArbitrageParams memory arbParams = abi.decode(params, (ArbitrageParams));
                
                // Step 2: Test the enum comparison that's failing
                if (arbParams.provider == FlashloanProvider.BALANCER) {
                    return "BALANCER";
                } else if (arbParams.provider == FlashloanProvider.AAVE) {
                    return "AAVE";
                } else {
                    return "UNKNOWN";
                }
            }
            
            function testEnumValue(bytes calldata params) external pure returns (uint8) {
                ArbitrageParams memory arbParams = abi.decode(params, (ArbitrageParams));
                return uint8(arbParams.provider);
            }
            
            function testDirectEnum(uint8 providerValue) external pure returns (string memory) {
                FlashloanProvider provider = FlashloanProvider(providerValue);
                
                if (provider == FlashloanProvider.BALANCER) {
                    return "BALANCER";
                } else if (provider == FlashloanProvider.AAVE) {
                    return "AAVE";
                } else {
                    return "UNKNOWN";
                }
            }
        }
    `;
    
    // Write and compile the test contract
    const fs = require('fs');
    fs.writeFileSync('./contracts/EnumComparisonTest.sol', testContractCode);
    
    const { execSync } = require('child_process');
    try {
        execSync('npx hardhat compile', { stdio: 'inherit' });
    } catch (error) {
        console.log('Compilation error, but continuing...');
    }
    
    // Deploy the test contract
    console.log('\n🚀 Deploying enum comparison test contract...');
    
    const testArtifact = JSON.parse(fs.readFileSync('./artifacts/contracts/EnumComparisonTest.sol/EnumComparisonTest.json', 'utf8'));
    
    const testFactory = new ethers.ContractFactory(
        testArtifact.abi,
        testArtifact.bytecode,
        wallet
    );
    
    const testContract = await testFactory.deploy();
    await testContract.waitForDeployment();
    const testAddress = await testContract.getAddress();
    
    console.log(`✅ Test contract deployed at: ${testAddress}`);
    
    // Test with the exact same parameters that are failing
    console.log('\n🧪 Testing enum comparison with failing parameters...');
    
    const wethAddress = '******************************************';
    const usdcAddress = '******************************************';
    const v3Router = '******************************************';
    const v2Router = '******************************************';
    
    const testParams = ethers.AbiCoder.defaultAbiCoder().encode(
        ['address', 'address', 'address', 'address', 'uint24', 'uint256', 'uint8'],
        [wethAddress, usdcAddress, v3Router, v2Router, 3000, ethers.parseEther('0.0001'), 0]
    );
    
    // Test 1: Enum value extraction
    console.log('\n   Test 1: Extract enum value');
    try {
        const enumValue = await testContract.testEnumValue(testParams);
        console.log(`      ✅ Enum value: ${enumValue} (${enumValue === 0n ? 'AAVE' : 'BALANCER'})`);
    } catch (error) {
        console.log(`      ❌ Enum value extraction failed: ${error.message.split('(')[0]}`);
        
        if (error.data && error.data.startsWith('0x4e487b71')) {
            const panicCode = parseInt(error.data.slice(10, 74), 16);
            if (panicCode === 17) {
                console.log(`         🚨 ENUM_CONVERSION_ERROR in value extraction!`);
            }
        }
    }
    
    // Test 2: Enum comparison
    console.log('\n   Test 2: Enum comparison');
    try {
        const result = await testContract.testEnumComparison(testParams);
        console.log(`      ✅ Enum comparison result: ${result}`);
    } catch (error) {
        console.log(`      ❌ Enum comparison failed: ${error.message.split('(')[0]}`);
        
        if (error.data && error.data.startsWith('0x4e487b71')) {
            const panicCode = parseInt(error.data.slice(10, 74), 16);
            if (panicCode === 17) {
                console.log(`         🚨 ENUM_CONVERSION_ERROR in comparison!`);
            }
        }
    }
    
    // Test 3: Direct enum conversion
    console.log('\n   Test 3: Direct enum conversion');
    const enumTests = [0, 1, 2, 255];
    
    for (const enumValue of enumTests) {
        try {
            const result = await testContract.testDirectEnum(enumValue);
            console.log(`      ✅ Enum ${enumValue}: ${result}`);
        } catch (error) {
            console.log(`      ❌ Enum ${enumValue}: ${error.message.split('(')[0]}`);
            
            if (error.data && error.data.startsWith('0x4e487b71')) {
                const panicCode = parseInt(error.data.slice(10, 74), 16);
                if (panicCode === 17) {
                    console.log(`         🚨 ENUM_CONVERSION_ERROR for value ${enumValue}`);
                }
            }
        }
    }
    
    // Test 4: Test with Balancer provider
    console.log('\n   Test 4: Test with Balancer provider (enum value 1)');
    
    const balancerParams = ethers.AbiCoder.defaultAbiCoder().encode(
        ['address', 'address', 'address', 'address', 'uint24', 'uint256', 'uint8'],
        [wethAddress, usdcAddress, v3Router, v2Router, 3000, ethers.parseEther('0.0001'), 1]
    );
    
    try {
        const result = await testContract.testEnumComparison(balancerParams);
        console.log(`      ✅ Balancer enum comparison: ${result}`);
    } catch (error) {
        console.log(`      ❌ Balancer enum comparison failed: ${error.message.split('(')[0]}`);
        
        if (error.data && error.data.startsWith('0x4e487b71')) {
            const panicCode = parseInt(error.data.slice(10, 74), 16);
            if (panicCode === 17) {
                console.log(`         🚨 ENUM_CONVERSION_ERROR with Balancer!`);
            }
        }
    }
    
    console.log('\n🏁 Enum comparison debugging completed!');
    
    // Clean up
    try {
        fs.unlinkSync('./contracts/EnumComparisonTest.sol');
    } catch (e) {}
}

debugEnumComparison().catch(console.error);
