const { webDashboard } = require('../dist/server/webDashboard');
const { ethers } = require('ethers');

console.log('🧪 Testing Web Dashboard...\n');

// Set environment variable to enable web dashboard
process.env.WEB_DASHBOARD = 'true';

// Initialize dashboard with test data
const testData = {
  currentBlock: 18500000,
  networkName: 'Sepolia',
  isRunning: true,
  uptime: Date.now(),
  lastActivity: Date.now(),
  flashloanEnabled: true,
  mevShareEnabled: false,
  arbitrageEnabled: true,
  totalTransactions: 1250,
  relevantTransactions: 89,
  opportunitiesFound: 23,
  opportunitiesExecuted: 8,
  totalProfit: ethers.parseEther('0.0456'),
  avgGasPrice: ethers.parseUnits('25', 'gwei'),
  ethBalance: ethers.parseEther('1.2345'),
  lastBalanceUpdate: Date.now(),
  configuration: {
    tokenPairs: ['USDC/WETH', 'DAI/USDC', 'WETH/USDT'],
    dexes: ['Uniswap V3', 'Curve', 'Sushiswap'],
    minProfitThreshold: '0.01',
    maxGasPrice: '50'
  },
  successfulTransactions: [
    {
      timestamp: Date.now() - 300000,
      type: 'flashloan',
      profit: ethers.parseEther('0.0123'),
      gasUsed: ethers.parseEther('0.002'),
      txHash: '******************************************',
      confidence: 95,
      details: 'USDC/DAI arbitrage via Curve + Uniswap V3'
    },
    {
      timestamp: Date.now() - 180000,
      type: 'arbitrage',
      profit: ethers.parseEther('0.0087'),
      gasUsed: ethers.parseEther('0.0015'),
      txHash: '******************************************',
      confidence: 88,
      details: 'WETH/USDT price difference'
    },
    {
      timestamp: Date.now() - 60000,
      type: 'sandwich',
      profit: ethers.parseEther('0.0234'),
      gasUsed: ethers.parseEther('0.003'),
      txHash: '******************************************',
      confidence: 92,
      details: 'Large USDC swap detected'
    }
  ],
  errors: 2,
  lastError: 'Gas price too high for profitable execution'
};

// Start the web dashboard
webDashboard.updateDashboardData(testData);
webDashboard.start();

console.log('🌐 Web dashboard started!');
console.log(`📊 Dashboard URL: http://192.168.0.16:${webDashboard.getPort()}`);
console.log('🔄 Simulating live activity with real-time WebSocket updates...\n');

// Simulate live activity
let transactionCount = 0;
let opportunityCount = 0;

const simulateActivity = () => {
  // Simulate new transactions
  if (Math.random() > 0.3) {
    testData.totalTransactions += Math.floor(Math.random() * 5) + 1;
    
    if (Math.random() > 0.7) {
      testData.relevantTransactions += 1;
      
      // Add log entry
      webDashboard.addLog('info', `Relevant transaction detected: ${testData.relevantTransactions}`, {
        txHash: `0x${Math.random().toString(16).substring(2, 18)}...`,
        gasPrice: Math.floor(Math.random() * 50) + 20
      });
    }
  }
  
  // Simulate MEV opportunities
  if (Math.random() > 0.8) {
    testData.opportunitiesFound += 1;
    opportunityCount++;
    
    const opportunityTypes = ['flashloan', 'arbitrage', 'sandwich', 'frontrun'];
    const type = opportunityTypes[Math.floor(Math.random() * opportunityTypes.length)];
    const profit = (Math.random() * 0.05 + 0.001).toFixed(6);
    
    webDashboard.addLog('warn', `MEV opportunity found: ${type}`, {
      estimatedProfit: `${profit} ETH`,
      confidence: Math.floor(Math.random() * 30) + 70
    });
    
    // Sometimes execute the opportunity
    if (Math.random() > 0.6) {
      testData.opportunitiesExecuted += 1;
      const actualProfit = ethers.parseEther(profit);
      testData.totalProfit += actualProfit;
      
      // Add to successful transactions
      testData.successfulTransactions.push({
        timestamp: Date.now(),
        type: type,
        profit: actualProfit,
        gasUsed: ethers.parseEther((Math.random() * 0.005 + 0.001).toFixed(6)),
        txHash: `0x${Math.random().toString(16).substring(2, 18)}...`,
        confidence: Math.floor(Math.random() * 30) + 70,
        details: `Simulated ${type} execution`
      });
      
      // Keep only last 10 transactions
      if (testData.successfulTransactions.length > 10) {
        testData.successfulTransactions = testData.successfulTransactions.slice(-10);
      }
      
      webDashboard.addLog('info', `✅ MEV opportunity executed successfully!`, {
        type: type,
        profit: `${profit} ETH`,
        txHash: testData.successfulTransactions[testData.successfulTransactions.length - 1].txHash
      });
    }
  }
  
  // Simulate gas price changes
  if (Math.random() > 0.5) {
    const newGasPrice = ethers.parseUnits((Math.random() * 100 + 10).toFixed(2), 'gwei');
    testData.avgGasPrice = newGasPrice;
  }
  
  // Update current block
  if (Math.random() > 0.7) {
    testData.currentBlock += 1;
    testData.lastActivity = Date.now();
  }
  
  // Simulate errors occasionally
  if (Math.random() > 0.95) {
    testData.errors += 1;
    const errorMessages = [
      'Transaction reverted due to slippage',
      'Gas price too high for profitable execution',
      'Insufficient liquidity for flashloan',
      'MEV opportunity expired before execution'
    ];
    testData.lastError = errorMessages[Math.floor(Math.random() * errorMessages.length)];
    
    webDashboard.addLog('error', testData.lastError);
  }
  
  // Update dashboard data
  webDashboard.updateDashboardData(testData);
  
  // Reset counters periodically
  if (transactionCount++ > 20) {
    webDashboard.addLog('debug', 'Periodic status update', {
      totalTx: testData.totalTransactions,
      relevantTx: testData.relevantTransactions,
      opportunities: testData.opportunitiesFound
    });
    
    transactionCount = 0;
  }
  
  if (opportunityCount > 5) {
    opportunityCount = 0;
  }
};

// Start simulation
const activityInterval = setInterval(simulateActivity, 2000);

// Add some initial logs
setTimeout(() => {
  webDashboard.addLog('info', 'Web dashboard initialized successfully');
  webDashboard.addLog('info', 'MEV bot strategies loaded', {
    flashloan: testData.flashloanEnabled,
    arbitrage: testData.arbitrageEnabled,
    mevShare: testData.mevShareEnabled
  });
  webDashboard.addLog('debug', 'Starting mempool monitoring...');
}, 1000);

// Handle graceful shutdown
process.on('SIGINT', () => {
  clearInterval(activityInterval);
  webDashboard.stop();
  console.log('\n🛑 Web dashboard test stopped');
  process.exit(0);
});

console.log('Press Ctrl+C to stop the test');
console.log('Open your browser and navigate to the dashboard URL above');
