const { ethers } = require('ethers');

async function testFixedOriginal() {
    console.log('🔍 Testing fixed original contract with proper error handling...');
    
    // Connect to Hardhat fork
    const provider = new ethers.JsonRpcProvider('http://127.0.0.1:8547');
    const wallet = new ethers.Wallet('0xac0974bec39a17e36ba4a6b4d238ff944bacb478cbed5efcae784d7bf4f2ff80', provider);
    
    console.log(`📋 Test Configuration:`);
    console.log(`   Provider: Hardhat fork (mainnet)`);
    console.log(`   Wallet: ${wallet.address}`);
    console.log(`   Balance: ${ethers.formatEther(await provider.getBalance(wallet.address))} ETH`);
    
    // Deploy the fixed original contract
    console.log('\n🚀 Deploying fixed original contract...');
    
    const aavePoolAddressesProvider = '******************************************';
    const balancerVault = '******************************************';
    
    const fs = require('fs');
    const contractArtifact = JSON.parse(fs.readFileSync('./artifacts/contracts/HybridFlashloanArbitrage.sol/HybridFlashloanArbitrage.json', 'utf8'));
    
    const contractFactory = new ethers.ContractFactory(
        contractArtifact.abi,
        contractArtifact.bytecode,
        wallet
    );
    
    const contract = await contractFactory.deploy(aavePoolAddressesProvider, balancerVault);
    await contract.waitForDeployment();
    const contractAddress = await contract.getAddress();
    
    console.log(`✅ Fixed contract deployed at: ${contractAddress}`);
    
    // Verify contract state
    const owner = await contract.owner();
    const chainId = await contract.CHAIN_ID();
    const v2Router = await contract.UNISWAP_V2_ROUTER();
    const v3Router = await contract.UNISWAP_V3_ROUTER();
    
    console.log(`   Owner: ${owner}`);
    console.log(`   Chain ID: ${chainId}`);
    console.log(`   V2 Router: ${v2Router}`);
    console.log(`   V3 Router: ${v3Router}`);
    
    // Test parameters
    const wethAddress = '******************************************';
    const usdcAddress = '******************************************';
    
    const testParams = ethers.AbiCoder.defaultAbiCoder().encode(
        ['address', 'address', 'address', 'address', 'uint24', 'uint256', 'uint8'],
        [wethAddress, usdcAddress, v2Router, v3Router, 3000, ethers.parseEther('0.0001'), 0]
    );
    
    console.log('\n🧪 Testing with problematic amounts (should now give clear errors)...');
    
    const testAmounts = [
        { amount: ethers.parseUnits('1000', 'wei'), label: '1000 wei (too small)' },
        { amount: ethers.parseUnits('1', 'gwei'), label: '1 gwei (too small)' },
        { amount: ethers.parseEther('0.001'), label: '0.001 ETH (minimum)' },
        { amount: ethers.parseEther('0.01'), label: '0.01 ETH (reasonable)' },
        { amount: ethers.parseEther('0.02'), label: '0.02 ETH (bot amount)' }
    ];
    
    for (const test of testAmounts) {
        console.log(`\n   Testing ${test.label}:`);
        
        try {
            await contract.executeOptimalFlashloan.staticCall(
                wethAddress,
                test.amount,
                testParams
            );
            
            console.log(`      ✅ SUCCESS - No enum conversion error!`);
            
        } catch (error) {
            const errorMsg = error.message.split('(')[0];
            console.log(`      ❌ ${errorMsg}`);
            
            // Check for specific error types
            if (error.data && error.data.startsWith('0x4e487b71')) {
                const panicCode = parseInt(error.data.slice(10, 74), 16);
                if (panicCode === 17) {
                    console.log(`         🚨 ENUM_CONVERSION_ERROR still present!`);
                } else {
                    console.log(`         📊 Different panic code: ${panicCode}`);
                }
            } else if (errorMsg.includes('Amount too small')) {
                console.log(`         ✅ Expected minimum amount error - fix working!`);
            } else if (errorMsg.includes('flashloan failed')) {
                console.log(`         ✅ Clear flashloan error - much better than enum error!`);
            } else if (errorMsg.includes('INSUFFICIENT_OUTPUT_AMOUNT')) {
                console.log(`         ✅ Trade execution error - contract logic working!`);
            } else {
                console.log(`         🔍 Other error type: ${errorMsg}`);
            }
        }
    }
    
    console.log('\n🧪 Testing with different providers...');
    
    const providerTests = [
        { provider: 0, name: 'AAVE' },
        { provider: 1, name: 'BALANCER' }
    ];
    
    for (const providerTest of providerTests) {
        console.log(`\n   Testing ${providerTest.name} provider with 0.01 ETH:`);
        
        const providerParams = ethers.AbiCoder.defaultAbiCoder().encode(
            ['address', 'address', 'address', 'address', 'uint24', 'uint256', 'uint8'],
            [wethAddress, usdcAddress, v2Router, v3Router, 3000, ethers.parseEther('0.0001'), providerTest.provider]
        );
        
        try {
            await contract.executeOptimalFlashloan.staticCall(
                wethAddress,
                ethers.parseEther('0.01'),
                providerParams
            );
            
            console.log(`      ✅ ${providerTest.name}: SUCCESS`);
            
        } catch (error) {
            const errorMsg = error.message.split('(')[0];
            console.log(`      ❌ ${providerTest.name}: ${errorMsg}`);
            
            if (error.data && error.data.startsWith('0x4e487b71')) {
                const panicCode = parseInt(error.data.slice(10, 74), 16);
                if (panicCode === 17) {
                    console.log(`         🚨 ENUM_CONVERSION_ERROR persists`);
                }
            } else if (errorMsg.includes('flashloan failed')) {
                console.log(`         ✅ Clear flashloan error message`);
            }
        }
    }
    
    console.log('\n🧪 Testing edge cases...');
    
    // Test with exactly the minimum amount
    console.log('\n   Testing exactly 0.001 ETH (minimum):');
    try {
        await contract.executeOptimalFlashloan.staticCall(
            wethAddress,
            ethers.parseEther('0.001'),
            testParams
        );
        console.log(`      ✅ Minimum amount works`);
    } catch (error) {
        const errorMsg = error.message.split('(')[0];
        console.log(`      ❌ Minimum amount: ${errorMsg}`);
        
        if (!errorMsg.includes('Amount too small')) {
            console.log(`         ✅ Passed minimum amount check - error is from flashloan execution`);
        }
    }
    
    // Test with amount just below minimum
    console.log('\n   Testing 0.0009 ETH (below minimum):');
    try {
        await contract.executeOptimalFlashloan.staticCall(
            wethAddress,
            ethers.parseEther('0.0009'),
            testParams
        );
        console.log(`      ❌ Below minimum should have failed`);
    } catch (error) {
        const errorMsg = error.message.split('(')[0];
        console.log(`      ✅ Below minimum correctly rejected: ${errorMsg}`);
        
        if (errorMsg.includes('Amount too small')) {
            console.log(`         ✅ Minimum amount validation working perfectly!`);
        }
    }
    
    console.log('\n🏁 Fixed original contract testing completed!');
    
    console.log('\n📊 SUMMARY:');
    console.log('   The fixed contract should now give clear, meaningful error messages');
    console.log('   instead of cryptic enum conversion errors. This makes debugging much easier!');
}

testFixedOriginal().catch(console.error);
