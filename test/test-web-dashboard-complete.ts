#!/usr/bin/env ts-node

import { bundleAnalyzer } from '../src/utils/bundle-analyzer';
import { statusDashboard } from '../src/utils/statusDashboard';
import { webDashboard } from '../src/server/webDashboard';
import { logger } from '../src/utils/logger';
import { ethers } from 'ethers';

/**
 * Complete Web Dashboard Test with Bundle Statistics
 * 
 * This script tests the complete integration of bundle statistics
 * in the web dashboard by simulating a running MEV bot.
 */

async function main() {
    try {
        logger.system('🧪 COMPLETE WEB DASHBOARD BUNDLE STATS TEST');
        logger.system('============================================');

        // Set environment variable for web dashboard
        process.env.WEB_DASHBOARD = 'true';
        process.env.WEB_DASHBOARD_PORT = '3001';

        // Add test bundle data
        logger.system('📝 Adding test bundle data...');
        
        const testBundleData = [
            { included: true, priorityFee: '45', baseFee: '25' },
            { included: false, priorityFee: '30', baseFee: '28', error: 'Block passed without inclusion' },
            { included: true, priorityFee: '50', baseFee: '22' },
            { included: true, priorityFee: '55', baseFee: '26' },
            { included: false, priorityFee: '35', baseFee: '30', error: 'Block passed without inclusion' },
            { included: true, priorityFee: '60', baseFee: '24' },
            { included: false, priorityFee: '40', baseFee: '32', error: 'Account nonce too high' },
            { included: true, priorityFee: '65', baseFee: '23' },
            { included: true, priorityFee: '70', baseFee: '27' },
            { included: false, priorityFee: '45', baseFee: '35', error: 'Block passed without inclusion' }
        ];

        testBundleData.forEach((data, index) => {
            const targetBlock = 3000000 + index;
            const priorityFee = ethers.parseUnits(data.priorityFee, 'gwei');
            const baseFee = ethers.parseUnits(data.baseFee, 'gwei');
            
            bundleAnalyzer.recordBundleSubmission(
                targetBlock,
                data.included,
                priorityFee,
                baseFee,
                data.error
            );
        });

        logger.system('✅ Test bundle data added');

        // Initialize status dashboard with test data
        logger.system('🔧 Initializing status dashboard...');
        
        // Update status dashboard with some test stats
        statusDashboard.updateBlockInfo(19500000, BigInt(21000), ethers.parseUnits('25', 'gwei'));
        statusDashboard.updateEthBalance(ethers.parseEther('2.5'));
        statusDashboard.updateStrategyStatus(true, true, true);

        logger.system('✅ Status dashboard initialized');

        // Start web dashboard
        logger.system('🌐 Starting web dashboard...');
        
        // Start the status dashboard (which will start web dashboard)
        statusDashboard.start();

        // Wait a moment for initialization
        await new Promise(resolve => setTimeout(resolve, 2000));

        // Trigger an update to ensure bundle stats are included
        statusDashboard.triggerUpdate();

        logger.system('✅ Web dashboard started');
        logger.system('');

        // Get current bundle statistics
        const bundleStats = bundleAnalyzer.getInclusionStats();
        logger.system('📊 CURRENT BUNDLE STATISTICS');
        logger.system('============================');
        logger.system(`Total Submissions: ${bundleStats.totalSubmissions}`);
        logger.system(`Successful Inclusions: ${bundleStats.successfulInclusions}`);
        logger.system(`Inclusion Rate: ${bundleStats.inclusionRate.toFixed(1)}%`);
        logger.system(`Average Priority Fee: ${bundleStats.averagePriorityFee} gwei`);
        logger.system('');

        // Test API endpoint
        logger.system('🔍 TESTING API ENDPOINT');
        logger.system('=======================');
        
        try {
            const axios = require('axios');
            const response = await axios.get('http://localhost:3001/api/dashboard');
            const data = response.data;
            
            logger.system('API Response Bundle Data:');
            logger.system(`  bundleSubmissions: ${data.bundleSubmissions}`);
            logger.system(`  bundleInclusions: ${data.bundleInclusions}`);
            logger.system(`  bundleInclusionRate: ${data.bundleInclusionRate}%`);
            logger.system(`  avgPriorityFee: ${data.avgPriorityFee} gwei`);
            
            // Verify data
            if (data.bundleSubmissions === bundleStats.totalSubmissions &&
                data.bundleInclusions === bundleStats.successfulInclusions &&
                Math.abs(data.bundleInclusionRate - bundleStats.inclusionRate) < 0.1) {
                logger.system('✅ API endpoint returns correct bundle statistics');
            } else {
                logger.system('❌ API endpoint data mismatch');
            }
            
        } catch (error) {
            logger.system(`❌ API test failed: ${(error as Error).message}`);
        }
        
        logger.system('');
        logger.system('🎉 WEB DASHBOARD TEST COMPLETED');
        logger.system('===============================');
        logger.system('');
        logger.system('🌐 DASHBOARD ACCESS:');
        logger.system(`   URL: http://localhost:3001`);
        logger.system(`   API: http://localhost:3001/api/dashboard`);
        logger.system('');
        logger.system('📊 EXPECTED BUNDLE SECTION:');
        logger.system('   Bundle Submissions: 10');
        logger.system('   Successful Inclusions: 6');
        logger.system('   Inclusion Rate: 60.0%');
        logger.system('   Avg Priority Fee: 52.0 gwei');
        logger.system('');
        logger.system('🔧 NEXT STEPS:');
        logger.system('1. Open http://localhost:3001 in your browser');
        logger.system('2. Look for the "Bundle Inclusion" section');
        logger.system('3. Verify the statistics match the expected values above');
        logger.system('4. The dashboard will continue running until you stop this script');
        logger.system('');
        logger.system('Press Ctrl+C to stop the dashboard...');

        // Keep the dashboard running
        process.on('SIGINT', () => {
            logger.system('');
            logger.system('🛑 Stopping web dashboard...');
            webDashboard.stop();
            statusDashboard.stop();
            process.exit(0);
        });

        // Keep the process alive
        await new Promise(() => {});

    } catch (error) {
        logger.error('Complete web dashboard test failed:', error);
        process.exit(1);
    }
}

// Run the test
if (require.main === module) {
    main().catch(error => {
        logger.error('Script failed:', error);
        process.exit(1);
    });
}

export { main as testWebDashboardComplete };
