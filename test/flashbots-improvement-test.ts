import { ethers } from 'ethers';
import { FlashbotsBundleManager } from '../src/flashbots/bundle-provider';
import { config } from '../src/config';
import { logger } from '../src/utils/logger';

/**
 * Test the improved Flashbots bundle submission with competitive gas pricing
 */
async function testFlashbotsImprovements() {
  logger.info('🚀 Testing Flashbots Bundle Inclusion Improvements');
  logger.info('='.repeat(60));

  // Initialize components
  const provider = new ethers.JsonRpcProvider(config.rpcUrl);
  const wallet = new ethers.Wallet(config.privateKey, provider);
  const flashbotsManager = new FlashbotsBundleManager(provider, wallet);

  await flashbotsManager.initialize();

  if (!flashbotsManager.isAvailable()) {
    logger.warn('⚠️  Flashbots not available (likely not on mainnet)');
    logger.info('   Testing gas pricing calculations only...');
  }

  // Test 1: Network Congestion Analysis
  logger.info('\n📊 Network Congestion Analysis:');
  const congestion = await flashbotsManager.getNetworkCongestion();
  logger.info(`   Congestion Level: ${congestion.congestionLevel}`);
  logger.info(`   Gas Usage Ratio: ${(congestion.gasUsageRatio * 100).toFixed(1)}%`);
  logger.info(`   Recommended Priority Fee: ${ethers.formatUnits(congestion.recommendedPriorityFee, 'gwei')} gwei`);

  // Test 2: Priority Fee Calculations
  logger.info('\n💰 Priority Fee Calculations:');
  const currentBlock = await provider.getBlockNumber();
  const targetBlock = currentBlock + 1;

  const standardPriorityFee = await flashbotsManager.calculateBundlePriorityFee(targetBlock, false);
  const highPriorityFee = await flashbotsManager.calculateBundlePriorityFee(targetBlock, true);

  logger.info(`   Standard Priority Fee: ${ethers.formatUnits(standardPriorityFee, 'gwei')} gwei`);
  logger.info(`   High Priority Fee: ${ethers.formatUnits(highPriorityFee, 'gwei')} gwei`);

  // Test 3: Gas Pricing Comparison
  logger.info('\n📈 Gas Pricing Comparison:');
  
  // Old conservative approach (10% of base fee)
  const block = await provider.getBlock(currentBlock);
  const baseFee = block?.baseFeePerGas || ethers.parseUnits('20', 'gwei');
  const oldPriorityFee = baseFee / BigInt(10);
  const minOldFee = ethers.parseUnits('1', 'gwei');
  const finalOldFee = oldPriorityFee > minOldFee ? oldPriorityFee : minOldFee;

  logger.info(`   OLD Approach: ${ethers.formatUnits(finalOldFee, 'gwei')} gwei`);
  logger.info(`   NEW Standard: ${ethers.formatUnits(standardPriorityFee, 'gwei')} gwei`);
  logger.info(`   NEW High Priority: ${ethers.formatUnits(highPriorityFee, 'gwei')} gwei`);

  const improvementStandard = ((Number(standardPriorityFee) / Number(finalOldFee)) - 1) * 100;
  const improvementHigh = ((Number(highPriorityFee) / Number(finalOldFee)) - 1) * 100;

  logger.info(`   Improvement (Standard): +${improvementStandard.toFixed(1)}%`);
  logger.info(`   Improvement (High): +${improvementHigh.toFixed(1)}%`);

  // Test 4: Configuration Improvements
  logger.info('\n⚙️  Configuration Improvements:');
  logger.info(`   Max Gas Price: ${config.maxGasPriceGwei} gwei (was 100 gwei)`);
  logger.info(`   Max Priority Fee: ${config.maxPriorityFeeGwei} gwei (was 10 gwei)`);
  logger.info(`   Improvement: ${((config.maxPriorityFeeGwei / 10) - 1) * 100}% higher limits`);

  // Test 5: Competitive Analysis
  logger.info('\n🏆 Competitive Analysis:');
  
  // Simulate typical MEV bot competition
  const competitorFees = [
    ethers.parseUnits('3', 'gwei'),   // Conservative bot
    ethers.parseUnits('7', 'gwei'),   // Moderate bot
    ethers.parseUnits('15', 'gwei'),  // Aggressive bot
    ethers.parseUnits('25', 'gwei'),  // Very aggressive bot
  ];

  logger.info('   Competitor Priority Fees:');
  competitorFees.forEach((fee, i) => {
    logger.info(`     Bot ${i + 1}: ${ethers.formatUnits(fee, 'gwei')} gwei`);
  });

  const ourStandardFee = Number(ethers.formatUnits(standardPriorityFee, 'gwei'));
  const ourHighFee = Number(ethers.formatUnits(highPriorityFee, 'gwei'));

  const beatenByStandard = competitorFees.filter(fee => 
    Number(ethers.formatUnits(fee, 'gwei')) < ourStandardFee
  ).length;

  const beatenByHigh = competitorFees.filter(fee => 
    Number(ethers.formatUnits(fee, 'gwei')) < ourHighFee
  ).length;

  logger.info(`   Our Standard Fee beats ${beatenByStandard}/${competitorFees.length} competitors`);
  logger.info(`   Our High Priority Fee beats ${beatenByHigh}/${competitorFees.length} competitors`);

  // Test 6: Recommendations
  logger.info('\n💡 Recommendations:');
  
  if (congestion.congestionLevel === 'high' || congestion.congestionLevel === 'extreme') {
    logger.info('   🔥 High congestion detected:');
    logger.info('     - Use multi-block submission strategy');
    logger.info('     - Enable high priority mode');
    logger.info('     - Consider increasing maxPriorityFeeGwei further');
  } else {
    logger.info('   ✅ Normal congestion:');
    logger.info('     - Standard priority fees should be sufficient');
    logger.info('     - Single block submission is fine');
  }

  if (ourHighFee < 20) {
    logger.info('   ⚠️  Consider increasing priority fees during peak MEV times');
  } else {
    logger.info('   ✅ Priority fees are competitive for MEV');
  }

  logger.info('\n🎯 Key Improvements Made:');
  logger.info('   1. ✅ Increased priority fee calculation (25-50% of base fee vs 10%)');
  logger.info('   2. ✅ Added high priority mode for competitive situations');
  logger.info('   3. ✅ Implemented multi-block submission strategy');
  logger.info('   4. ✅ Added network congestion awareness');
  logger.info('   5. ✅ Increased default gas limits for competitiveness');
  logger.info('   6. ✅ Added automatic gas pricing optimization');

  logger.info('\n✅ Flashbots improvement test completed!');
  logger.info('   These changes should significantly improve bundle inclusion rates.');
}

// Run the test
if (require.main === module) {
  testFlashbotsImprovements().catch(console.error);
}

export { testFlashbotsImprovements };
