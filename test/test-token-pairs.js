const { ethers } = require('ethers');

async function testTokenPairs() {
    console.log('🧪 Testing different token pairs and providers...');
    
    const provider = new ethers.JsonRpcProvider('http://127.0.0.1:8547');
    const wallet = new ethers.Wallet('0xac0974bec39a17e36ba4a6b4d238ff944bacb478cbed5efcae784d7bf4f2ff80', provider);
    
    // Deploy fresh contract
    const aavePoolAddressesProvider = '******************************************';
    const balancerVault = '******************************************';
    
    const fs = require('fs');
    const contractArtifact = JSON.parse(fs.readFileSync('./artifacts/contracts/HybridFlashloanArbitrage.sol/HybridFlashloanArbitrage.json', 'utf8'));
    
    const contractFactory = new ethers.ContractFactory(
        contractArtifact.abi,
        contractArtifact.bytecode,
        wallet
    );
    
    const contract = await contractFactory.deploy(
        aavePoolAddressesProvider,
        balancerVault
    );
    
    await contract.waitForDeployment();
    const contractAddress = await contract.getAddress();
    
    console.log(`✅ Contract deployed at: ${contractAddress}`);
    
    // Get router addresses from contract
    const v2Router = await contract.UNISWAP_V2_ROUTER();
    const v3Router = await contract.UNISWAP_V3_ROUTER();
    
    console.log(`   V2 Router: ${v2Router}`);
    console.log(`   V3 Router: ${v3Router}`);
    
    // Token addresses
    const tokens = {
        WETH: '******************************************',
        USDC: '******************************************',
        DAI: '******************************************',
        USDT: '******************************************'
    };
    
    // Test cases
    const testCases = [
        { tokenA: 'WETH', tokenB: 'USDC', provider: 0, name: 'WETH → USDC (AAVE)' },
        { tokenA: 'WETH', tokenB: 'USDC', provider: 1, name: 'WETH → USDC (BALANCER)' },
        { tokenA: 'WETH', tokenB: 'DAI', provider: 0, name: 'WETH → DAI (AAVE)' },
        { tokenA: 'WETH', tokenB: 'DAI', provider: 1, name: 'WETH → DAI (BALANCER)' },
        { tokenA: 'WETH', tokenB: 'USDT', provider: 0, name: 'WETH → USDT (AAVE)' },
        { tokenA: 'USDC', tokenB: 'DAI', provider: 0, name: 'USDC → DAI (AAVE)' },
        { tokenA: 'USDC', tokenB: 'USDT', provider: 0, name: 'USDC → USDT (AAVE)' }
    ];
    
    console.log('\n🔬 Testing token pairs and providers...');
    
    for (const testCase of testCases) {
        console.log(`\n   ${testCase.name}:`);
        
        const arbitrageParams = ethers.AbiCoder.defaultAbiCoder().encode(
            ['address', 'address', 'address', 'address', 'uint24', 'uint256', 'uint8'],
            [
                tokens[testCase.tokenA],  // tokenA
                tokens[testCase.tokenB],  // tokenB
                v2Router,                 // buyDex
                v3Router,                 // sellDex
                3000,                     // v3Fee
                ethers.parseEther('0.0001'), // minProfit
                testCase.provider         // FlashloanProvider
            ]
        );
        
        try {
            await contract.executeOptimalFlashloan.staticCall(
                tokens[testCase.tokenA],
                ethers.parseUnits('1', 'wei'),
                arbitrageParams
            );
            
            console.log(`      ✅ SUCCESS`);
            
        } catch (error) {
            const errorMsg = error.message.split('(')[0];
            console.log(`      ❌ ${errorMsg}`);
            
            if (errorMsg.includes('INSUFFICIENT_OUTPUT_AMOUNT')) {
                console.log(`         ✅ This is expected - struct encoding works!`);
            } else if (errorMsg.includes('OVERFLOW(17)')) {
                console.log(`         ⚠️  Enum conversion error - there's still an issue`);
            }
        }
    }
    
    // Test with different router combinations
    console.log('\n🔧 Testing different router combinations...');
    
    const routerCombos = [
        { buy: v2Router, sell: v2Router, name: 'V2 → V2' },
        { buy: v2Router, sell: v3Router, name: 'V2 → V3' },
        { buy: v3Router, sell: v2Router, name: 'V3 → V2' },
        { buy: v3Router, sell: v3Router, name: 'V3 → V3' }
    ];
    
    for (const combo of routerCombos) {
        console.log(`\n   ${combo.name} (WETH → USDC, AAVE):`);
        
        const arbitrageParams = ethers.AbiCoder.defaultAbiCoder().encode(
            ['address', 'address', 'address', 'address', 'uint24', 'uint256', 'uint8'],
            [
                tokens.WETH,    // tokenA
                tokens.USDC,    // tokenB
                combo.buy,      // buyDex
                combo.sell,     // sellDex
                3000,           // v3Fee
                ethers.parseEther('0.0001'), // minProfit
                0               // FlashloanProvider.AAVE
            ]
        );
        
        try {
            await contract.executeOptimalFlashloan.staticCall(
                tokens.WETH,
                ethers.parseUnits('1', 'wei'),
                arbitrageParams
            );
            
            console.log(`      ✅ SUCCESS`);
            
        } catch (error) {
            const errorMsg = error.message.split('(')[0];
            console.log(`      ❌ ${errorMsg}`);
            
            if (errorMsg.includes('INSUFFICIENT_OUTPUT_AMOUNT')) {
                console.log(`         ✅ Router combination works!`);
            }
        }
    }
    
    console.log('\n🏁 Token pair testing completed!');
}

testTokenPairs().catch(console.error);
