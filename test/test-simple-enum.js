const { ethers } = require('ethers');

async function testSimpleEnum() {
    console.log('🔍 Testing simple enum contract to isolate the issue...');
    
    // Connect to Hardhat fork
    const provider = new ethers.JsonRpcProvider('http://127.0.0.1:8547');
    const wallet = new ethers.Wallet('0xac0974bec39a17e36ba4a6b4d238ff944bacb478cbed5efcae784d7bf4f2ff80', provider);
    
    console.log(`📋 Test Configuration:`);
    console.log(`   Provider: Hardhat fork (mainnet)`);
    console.log(`   Wallet: ${wallet.address}`);
    
    // Deploy the simple test contract
    console.log('\n🚀 Deploying simple enum test contract...');
    
    const fs = require('fs');
    const contractArtifact = JSON.parse(fs.readFileSync('./artifacts/contracts/SimpleEnumTest.sol/SimpleEnumTest.json', 'utf8'));
    
    const contractFactory = new ethers.ContractFactory(
        contractArtifact.abi,
        contractArtifact.bytecode,
        wallet
    );
    
    const contract = await contractFactory.deploy();
    await contract.waitForDeployment();
    const contractAddress = await contract.getAddress();
    
    console.log(`✅ Simple test contract deployed at: ${contractAddress}`);
    
    // Test parameters
    const wethAddress = '******************************************';
    const usdcAddress = '******************************************';
    const v2Router = '******************************************';
    const v3Router = '******************************************';
    
    const testParams = ethers.AbiCoder.defaultAbiCoder().encode(
        ['address', 'address', 'address', 'address', 'uint24', 'uint256', 'uint8'],
        [wethAddress, usdcAddress, v2Router, v3Router, 3000, ethers.parseEther('0.0001'), 0]
    );
    
    console.log('\n🧪 Testing basic decode functionality...');
    
    // Test 1: Basic decode
    try {
        const decoded = await contract.testDecodeOnly(testParams);
        console.log(`   ✅ Basic decode: SUCCESS - Provider: ${decoded.provider}`);
    } catch (error) {
        console.log(`   ❌ Basic decode: ${error.message.split('(')[0]}`);
    }
    
    // Test 2: Enum conversion with valid values
    console.log('\n🧪 Testing enum conversion...');
    
    const enumTests = [0, 1, 2, 255];
    for (const enumValue of enumTests) {
        try {
            const result = await contract.testEnumConversion(enumValue);
            console.log(`   ✅ Enum ${enumValue}: SUCCESS - Result: ${result}`);
        } catch (error) {
            console.log(`   ❌ Enum ${enumValue}: ${error.message.split('(')[0]}`);
            
            if (error.data && error.data.startsWith('0x4e487b71')) {
                const panicCode = parseInt(error.data.slice(10, 74), 16);
                if (panicCode === 17) {
                    console.log(`      🚨 ENUM_CONVERSION_ERROR as expected for invalid value`);
                }
            }
        }
    }
    
    // Test 3: Decode with amount dependency
    console.log('\n🧪 Testing decode with amount dependency...');
    
    const testAmounts = [
        { amount: ethers.parseUnits('1000', 'wei'), label: '1000 wei (working)' },
        { amount: ethers.parseUnits('224030315', 'wei'), label: '224030315 wei (threshold)' },
        { amount: ethers.parseUnits('1', 'gwei'), label: '1 gwei (failing)' }
    ];
    
    for (const test of testAmounts) {
        try {
            const result = await contract.testDecodeWithAmount(test.amount, testParams);
            console.log(`   ✅ ${test.label}: SUCCESS - Provider: ${result[0].provider}, TestValue: ${result[1]}`);
        } catch (error) {
            console.log(`   ❌ ${test.label}: ${error.message.split('(')[0]}`);
            
            if (error.data && error.data.startsWith('0x4e487b71')) {
                const panicCode = parseInt(error.data.slice(10, 74), 16);
                if (panicCode === 17) {
                    console.log(`      🚨 ENUM_CONVERSION_ERROR - Issue confirmed!`);
                }
            }
        }
    }
    
    // Test 4: Full execution test
    console.log('\n🧪 Testing full execution logic...');
    
    for (const test of testAmounts) {
        try {
            const tx = await contract.testExecuteOptimalFlashloan(wethAddress, test.amount, testParams);
            console.log(`   ✅ ${test.label}: SUCCESS`);
            
            // Check for events
            const receipt = await tx.wait();
            const events = receipt.logs.filter(log => {
                try {
                    return contract.interface.parseLog(log);
                } catch {
                    return false;
                }
            });
            
            if (events.length > 0) {
                const parsedEvent = contract.interface.parseLog(events[0]);
                console.log(`      Event: ${parsedEvent.args.message}, Amount: ${parsedEvent.args.amount}, Provider: ${parsedEvent.args.provider}`);
            }
            
        } catch (error) {
            console.log(`   ❌ ${test.label}: ${error.message.split('(')[0]}`);
            
            if (error.data && error.data.startsWith('0x4e487b71')) {
                const panicCode = parseInt(error.data.slice(10, 74), 16);
                if (panicCode === 17) {
                    console.log(`      🚨 ENUM_CONVERSION_ERROR in full execution!`);
                }
            }
        }
    }
    
    // Test 5: Alternative struct layouts
    console.log('\n🧪 Testing alternative struct layouts...');
    
    try {
        const result = await contract.testAlternativeStruct(testParams);
        console.log(`   ✅ Alternative struct test: ${result ? 'SUCCESS' : 'FAILED'}`);
    } catch (error) {
        console.log(`   ❌ Alternative struct test: ${error.message.split('(')[0]}`);
    }
    
    // Test 6: Test with different provider values in params
    console.log('\n🧪 Testing with different provider values...');
    
    const providerTests = [
        { provider: 0, name: 'AAVE (0)' },
        { provider: 1, name: 'BALANCER (1)' },
        { provider: 2, name: 'Invalid (2)' }
    ];
    
    for (const providerTest of providerTests) {
        const providerParams = ethers.AbiCoder.defaultAbiCoder().encode(
            ['address', 'address', 'address', 'address', 'uint24', 'uint256', 'uint8'],
            [wethAddress, usdcAddress, v2Router, v3Router, 3000, ethers.parseEther('0.0001'), providerTest.provider]
        );
        
        try {
            await contract.testExecuteOptimalFlashloan(
                wethAddress, 
                ethers.parseUnits('1', 'gwei'), 
                providerParams
            );
            console.log(`   ✅ ${providerTest.name}: SUCCESS`);
        } catch (error) {
            console.log(`   ❌ ${providerTest.name}: ${error.message.split('(')[0]}`);
            
            if (error.data && error.data.startsWith('0x4e487b71')) {
                const panicCode = parseInt(error.data.slice(10, 74), 16);
                if (panicCode === 17) {
                    console.log(`      🚨 ENUM_CONVERSION_ERROR`);
                }
            }
        }
    }
    
    console.log('\n🏁 Simple enum testing completed!');
    
    console.log('\n📊 ANALYSIS:');
    console.log('   If enum conversion errors persist in this simple contract,');
    console.log('   it confirms the issue is fundamental and not related to');
    console.log('   external contract interactions or complex logic.');
}

testSimpleEnum().catch(console.error);
