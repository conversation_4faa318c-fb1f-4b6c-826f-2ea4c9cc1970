#!/usr/bin/env node

/**
 * Test script to verify WebSocket event-driven architecture is working
 * This script monitors WebSocket events and measures response times
 */

const { ethers } = require('ethers');
const { config } = require('../dist/config');

class WebSocketEventTester {
  constructor() {
    this.provider = new ethers.JsonRpcProvider(config.rpcUrl);
    this.wsProvider = null;
    this.eventCounts = {
      pendingTransactions: 0,
      newBlocks: 0,
      errors: 0
    };
    this.startTime = Date.now();
    this.lastBlockTime = 0;
    this.lastTxTime = 0;
  }

  async initialize() {
    try {
      // Setup WebSocket provider
      if (config.mempoolWebsocketUrl) {
        this.wsProvider = new ethers.WebSocketProvider(config.mempoolWebsocketUrl);
        console.system('✅ WebSocket provider initialized');
        console.system(`🔗 Connected to: ${config.mempoolWebsocketUrl}`);
      } else {
        throw new Error('No WebSocket URL configured');
      }

      // Test basic connectivity
      const blockNumber = await this.provider.getBlockNumber();
      console.log(`📊 Current block: ${blockNumber}`);
      
      return true;
    } catch (error) {
      console.error('❌ Failed to initialize WebSocket provider:', error.message);
      return false;
    }
  }

  startMonitoring() {
    console.log('\n🚀 Starting WebSocket Event Monitoring...');
    console.log('📝 This will test the event-driven architecture');
    console.log('⏹️  Press Ctrl+C to stop\n');

    // Monitor pending transactions
    if (this.wsProvider) {
      this.wsProvider.on('pending', (txHash) => {
        this.eventCounts.pendingTransactions++;
        this.lastTxTime = Date.now();
        
        // Log every 100th transaction to avoid spam
        if (this.eventCounts.pendingTransactions % 100 === 0) {
          console.log(`📨 Pending transactions: ${this.eventCounts.pendingTransactions}`);
        }
      });

      this.wsProvider.on('block', (blockNumber) => {
        this.eventCounts.newBlocks++;
        this.lastBlockTime = Date.now();
        
        console.log(`🧱 New block: ${blockNumber} (${this.eventCounts.newBlocks} total)`);
      });

      this.wsProvider.on('error', (error) => {
        this.eventCounts.errors++;
        console.error(`❌ WebSocket error: ${error.message}`);
      });

      console.log('✅ WebSocket event listeners attached');
    }

    // Start status reporting
    this.startStatusReporting();
  }

  startStatusReporting() {
    setInterval(() => {
      this.printStatus();
    }, 10000); // Every 10 seconds

    // Initial status
    setTimeout(() => this.printStatus(), 2000);
  }

  printStatus() {
    const now = Date.now();
    const uptime = Math.floor((now - this.startTime) / 1000);
    const timeSinceLastBlock = this.lastBlockTime ? Math.floor((now - this.lastBlockTime) / 1000) : 'N/A';
    const timeSinceLastTx = this.lastTxTime ? Math.floor((now - this.lastTxTime) / 1000) : 'N/A';

    console.log('\n📊 EVENT-DRIVEN MONITORING STATUS');
    console.log('═'.repeat(50));
    console.log(`⏱️  Uptime: ${uptime}s`);
    console.log(`🧱 Blocks received: ${this.eventCounts.newBlocks}`);
    console.log(`📨 Pending TXs: ${this.eventCounts.pendingTransactions}`);
    console.log(`❌ Errors: ${this.eventCounts.errors}`);
    console.log(`🕐 Last block: ${timeSinceLastBlock}s ago`);
    console.log(`🕐 Last TX: ${timeSinceLastTx}s ago`);
    
    // Calculate rates
    if (uptime > 0) {
      const blockRate = (this.eventCounts.newBlocks / uptime * 60).toFixed(1);
      const txRate = (this.eventCounts.pendingTransactions / uptime * 60).toFixed(0);
      console.log(`📈 Block rate: ${blockRate}/min`);
      console.log(`📈 TX rate: ${txRate}/min`);
    }

    // Health check
    if (this.eventCounts.newBlocks === 0 && uptime > 30) {
      console.log('⚠️  WARNING: No blocks received in 30+ seconds');
    }
    
    if (this.eventCounts.pendingTransactions === 0 && uptime > 10) {
      console.log('⚠️  WARNING: No pending transactions received');
    }

    if (this.eventCounts.errors > 0) {
      console.log(`⚠️  WARNING: ${this.eventCounts.errors} WebSocket errors detected`);
    }

    console.log('═'.repeat(50));
  }

  async testEventLatency() {
    console.log('\n🔬 Testing Event Latency...');
    
    try {
      const startTime = Date.now();
      const blockNumber = await this.provider.getBlockNumber();
      const rpcLatency = Date.now() - startTime;
      
      console.log(`📊 Current block: ${blockNumber}`);
      console.log(`⚡ RPC latency: ${rpcLatency}ms`);
      
      // Wait for next block via WebSocket
      return new Promise((resolve) => {
        const wsStartTime = Date.now();
        
        const blockHandler = (wsBlockNumber) => {
          if (wsBlockNumber > blockNumber) {
            const wsLatency = Date.now() - wsStartTime;
            console.log(`⚡ WebSocket block latency: ${wsLatency}ms`);
            console.log(`🎯 Event-driven advantage: ${wsLatency < 1000 ? 'EXCELLENT' : wsLatency < 3000 ? 'GOOD' : 'NEEDS_IMPROVEMENT'}`);
            
            this.wsProvider.off('block', blockHandler);
            resolve();
          }
        };
        
        this.wsProvider.on('block', blockHandler);
        
        // Timeout after 30 seconds
        setTimeout(() => {
          this.wsProvider.off('block', blockHandler);
          console.log('⏰ Block latency test timed out');
          resolve();
        }, 30000);
      });
      
    } catch (error) {
      console.error('❌ Latency test failed:', error.message);
    }
  }

  async cleanup() {
    console.log('\n🧹 Cleaning up...');
    
    if (this.wsProvider) {
      this.wsProvider.removeAllListeners();
      await this.wsProvider.destroy();
      console.log('✅ WebSocket provider cleaned up');
    }
  }
}

// Main execution
async function main() {
  const tester = new WebSocketEventTester();
  
  // Handle graceful shutdown
  process.on('SIGINT', async () => {
    console.log('\n\n🛑 Shutting down...');
    await tester.cleanup();
    process.exit(0);
  });

  // Initialize and start testing
  const initialized = await tester.initialize();
  if (!initialized) {
    console.error('❌ Failed to initialize WebSocket tester');
    process.exit(1);
  }

  // Test latency first
  await tester.testEventLatency();
  
  // Start continuous monitoring
  tester.startMonitoring();
  
  console.log('\n✅ WebSocket event testing started');
  console.log('📊 Monitor the output to verify event-driven architecture is working');
  console.log('🎯 Look for immediate block and transaction events');
}

// Run the test
main().catch(error => {
  console.error('❌ Test failed:', error);
  process.exit(1);
});
