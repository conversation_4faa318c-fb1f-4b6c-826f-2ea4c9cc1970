import { expect } from 'chai';
import { ethers } from 'hardhat';
import { FlashbotsExecutor } from '../src/execution/flashbots-executor';
import { FlashbotsBundleManager } from '../src/flashbots/bundle-provider';
import { AdvancedGasEstimator } from '../src/gas/advanced-estimator';
import { GasOptimizer } from '../src/gas/optimizer';
import { DynamicFlashloanStrategy } from '../src/strategies/dynamic-flashloan';
import { config } from '../src/config';

describe('Advanced MEV Optimization Features', function() {
    let flashbotsExecutor: FlashbotsExecutor;
    let flashbotsManager: FlashbotsBundleManager;
    let gasEstimator: AdvancedGasEstimator;
    let gasOptimizer: GasOptimizer;
    let dynamicStrategy: DynamicFlashloanStrategy;
    let provider: ethers.JsonRpcProvider;
    let wallet: ethers.Wallet;

    before(async function() {
        // Setup test environment
        [wallet] = await ethers.getSigners();
        provider = ethers.provider as ethers.JsonRpcProvider;
        
        // Initialize components
        flashbotsManager = new FlashbotsBundleManager(provider, wallet);
        gasEstimator = new AdvancedGasEstimator(provider);
        gasOptimizer = new GasOptimizer();
        flashbotsExecutor = new FlashbotsExecutor(
            provider,
            wallet,
            flashbotsManager,
            gasEstimator,
            gasOptimizer
        );
        
        // Initialize strategy
        dynamicStrategy = new DynamicFlashloanStrategy(
            provider,
            wallet,
            flashbotsExecutor
        );
    });

    describe('Configuration Parameters', function() {
        it('should have all advanced MEV parameters configured', function() {
            expect(config.bundleSubmissionStrategy).to.be.oneOf(['conservative', 'balanced', 'aggressive']);
            expect(config.enableBundleMultiplexing).to.be.a('boolean');
            expect(config.bundleRetryCount).to.be.a('number');
            expect(config.bundleTimeoutMs).to.be.a('number');
            expect(config.dynamicGasPricing).to.be.a('boolean');
            expect(config.gasEscalationFactor).to.be.a('number');
            expect(config.competitiveGasBuffer).to.be.a('number');
            expect(config.profitMarginMultiplier).to.be.a('number');
            expect(config.enableProfitMaximization).to.be.a('boolean');
            expect(config.minProfitMarginPercent).to.be.a('number');
            expect(config.earlySubmissionOffsetMs).to.be.a('number');
            expect(config.blockTimingBufferMs).to.be.a('number');
            expect(config.enablePreemptiveSubmission).to.be.a('boolean');
        });

        it('should have reasonable default values', function() {
            expect(config.bundleRetryCount).to.be.at.least(1);
            expect(config.bundleTimeoutMs).to.be.at.least(5000);
            expect(config.gasEscalationFactor).to.be.at.least(1.0);
            expect(config.competitiveGasBuffer).to.be.at.least(1.0);
            expect(config.profitMarginMultiplier).to.be.at.least(1.0);
            expect(config.minProfitMarginPercent).to.be.at.least(0);
            expect(config.blockTimingBufferMs).to.be.at.least(0);
        });
    });

    describe('Dynamic Gas Pricing', function() {
        it('should apply dynamic gas pricing when enabled', async function() {
            // Test with dynamic pricing enabled
            const originalSetting = config.dynamicGasPricing;
            (config as any).dynamicGasPricing = true;

            try {
                const gasPrice1 = await gasEstimator.getOptimalGasPrice('fast', 0);
                const gasPrice2 = await gasEstimator.getOptimalGasPrice('fast', 1);
                const gasPrice3 = await gasEstimator.getOptimalGasPrice('fast', 2);

                // Gas prices should escalate with retry count
                expect(Number(gasPrice2.gasPrice)).to.be.greaterThan(Number(gasPrice1.gasPrice));
                expect(Number(gasPrice3.gasPrice)).to.be.greaterThan(Number(gasPrice2.gasPrice));

                console.log(`   Gas escalation test:`);
                console.log(`   Retry 0: ${ethers.formatUnits(gasPrice1.gasPrice, 'gwei')} gwei`);
                console.log(`   Retry 1: ${ethers.formatUnits(gasPrice2.gasPrice, 'gwei')} gwei`);
                console.log(`   Retry 2: ${ethers.formatUnits(gasPrice3.gasPrice, 'gwei')} gwei`);
            } finally {
                (config as any).dynamicGasPricing = originalSetting;
            }
        });

        it('should apply urgency multipliers correctly', async function() {
            const slowGas = await gasEstimator.getOptimalGasPrice('slow');
            const standardGas = await gasEstimator.getOptimalGasPrice('standard');
            const fastGas = await gasEstimator.getOptimalGasPrice('fast');
            const instantGas = await gasEstimator.getOptimalGasPrice('instant');

            // Higher urgency should result in higher gas prices
            expect(Number(standardGas.gasPrice)).to.be.at.least(Number(slowGas.gasPrice));
            expect(Number(fastGas.gasPrice)).to.be.at.least(Number(standardGas.gasPrice));
            expect(Number(instantGas.gasPrice)).to.be.at.least(Number(fastGas.gasPrice));

            console.log(`   Urgency multiplier test:`);
            console.log(`   Slow: ${ethers.formatUnits(slowGas.gasPrice, 'gwei')} gwei`);
            console.log(`   Standard: ${ethers.formatUnits(standardGas.gasPrice, 'gwei')} gwei`);
            console.log(`   Fast: ${ethers.formatUnits(fastGas.gasPrice, 'gwei')} gwei`);
            console.log(`   Instant: ${ethers.formatUnits(instantGas.gasPrice, 'gwei')} gwei`);
        });
    });

    describe('Advanced Bundle Priority Fee Calculation', function() {
        it('should calculate different fees for different strategies', async function() {
            const currentBlock = await provider.getBlockNumber();
            const targetBlock = currentBlock + 1;

            // Test different strategies
            const originalStrategy = config.bundleSubmissionStrategy;
            
            try {
                (config as any).bundleSubmissionStrategy = 'conservative';
                const conservativeFee = await flashbotsManager.calculateAdvancedBundlePriorityFee(targetBlock, false, 0);

                (config as any).bundleSubmissionStrategy = 'balanced';
                const balancedFee = await flashbotsManager.calculateAdvancedBundlePriorityFee(targetBlock, false, 0);

                (config as any).bundleSubmissionStrategy = 'aggressive';
                const aggressiveFee = await flashbotsManager.calculateAdvancedBundlePriorityFee(targetBlock, false, 0);

                // Aggressive should be highest, conservative lowest
                expect(Number(aggressiveFee)).to.be.greaterThan(Number(balancedFee));
                expect(Number(balancedFee)).to.be.greaterThan(Number(conservativeFee));

                console.log(`   Strategy fee comparison:`);
                console.log(`   Conservative: ${ethers.formatUnits(conservativeFee, 'gwei')} gwei`);
                console.log(`   Balanced: ${ethers.formatUnits(balancedFee, 'gwei')} gwei`);
                console.log(`   Aggressive: ${ethers.formatUnits(aggressiveFee, 'gwei')} gwei`);
            } finally {
                (config as any).bundleSubmissionStrategy = originalStrategy;
            }
        });

        it('should apply retry escalation correctly', async function() {
            const currentBlock = await provider.getBlockNumber();
            const targetBlock = currentBlock + 1;

            const fee0 = await flashbotsManager.calculateAdvancedBundlePriorityFee(targetBlock, false, 0);
            const fee1 = await flashbotsManager.calculateAdvancedBundlePriorityFee(targetBlock, false, 1);
            const fee2 = await flashbotsManager.calculateAdvancedBundlePriorityFee(targetBlock, false, 2);

            // Fees should escalate with retry count
            expect(Number(fee1)).to.be.greaterThan(Number(fee0));
            expect(Number(fee2)).to.be.greaterThan(Number(fee1));

            console.log(`   Retry escalation test:`);
            console.log(`   Attempt 1: ${ethers.formatUnits(fee0, 'gwei')} gwei`);
            console.log(`   Attempt 2: ${ethers.formatUnits(fee1, 'gwei')} gwei`);
            console.log(`   Attempt 3: ${ethers.formatUnits(fee2, 'gwei')} gwei`);
        });

        it('should apply competitive gas buffer', async function() {
            const currentBlock = await provider.getBlockNumber();
            const targetBlock = currentBlock + 1;

            const originalBuffer = config.competitiveGasBuffer;
            
            try {
                (config as any).competitiveGasBuffer = 1.0;
                const fee1x = await flashbotsManager.calculateAdvancedBundlePriorityFee(targetBlock, false, 0);

                (config as any).competitiveGasBuffer = 2.0;
                const fee2x = await flashbotsManager.calculateAdvancedBundlePriorityFee(targetBlock, false, 0);

                // 2x buffer should result in higher fees
                expect(Number(fee2x)).to.be.greaterThan(Number(fee1x));

                console.log(`   Competitive buffer test:`);
                console.log(`   1x buffer: ${ethers.formatUnits(fee1x, 'gwei')} gwei`);
                console.log(`   2x buffer: ${ethers.formatUnits(fee2x, 'gwei')} gwei`);
            } finally {
                (config as any).competitiveGasBuffer = originalBuffer;
            }
        });
    });

    describe('Block Timing Buffer', function() {
        it('should calculate optimal target block based on timing', async function() {
            // Access the private method through reflection for testing
            const calculateOptimalTargetBlock = (flashbotsExecutor as any).calculateOptimalTargetBlock.bind(flashbotsExecutor);
            
            const targetBlock = await calculateOptimalTargetBlock();
            const currentBlock = await provider.getBlockNumber();

            // Target block should be at least current + 1
            expect(targetBlock).to.be.greaterThan(currentBlock);
            expect(targetBlock).to.be.at.most(currentBlock + 3); // Reasonable upper bound

            console.log(`   Block timing test:`);
            console.log(`   Current block: ${currentBlock}`);
            console.log(`   Target block: ${targetBlock}`);
            console.log(`   Blocks ahead: ${targetBlock - currentBlock}`);
        });

        it('should consider timing buffer in calculations', async function() {
            const originalBuffer = config.blockTimingBufferMs;
            
            try {
                // Test with different buffer sizes
                (config as any).blockTimingBufferMs = 500;
                const targetBlock1 = await (flashbotsExecutor as any).calculateOptimalTargetBlock();

                (config as any).blockTimingBufferMs = 2000;
                const targetBlock2 = await (flashbotsExecutor as any).calculateOptimalTargetBlock();

                // Both should be valid target blocks
                const currentBlock = await provider.getBlockNumber();
                expect(targetBlock1).to.be.greaterThan(currentBlock);
                expect(targetBlock2).to.be.greaterThan(currentBlock);

                console.log(`   Timing buffer comparison:`);
                console.log(`   500ms buffer → block ${targetBlock1}`);
                console.log(`   2000ms buffer → block ${targetBlock2}`);
            } finally {
                (config as any).blockTimingBufferMs = originalBuffer;
            }
        });
    });

    describe('Profit Optimization', function() {
        it('should validate profit requirements correctly', async function() {
            // Create test route
            const testRoute = {
                flashloanToken: { symbol: 'WETH', address: '0x...', decimals: 18 },
                flashloanAmount: ethers.parseEther('1'),
                expectedProfit: ethers.parseEther('0.1'), // 0.1 ETH profit
                confidence: 85,
                buyDex: 'UNISWAP_V2',
                sellDex: 'UNISWAP_V3',
                buyPrice: ethers.parseEther('2000'),
                sellPrice: ethers.parseEther('2100'),
                gasEstimate: BigInt(300000)
            };

            // Test profit validation
            const isValid = (dynamicStrategy as any).validateAdvancedProfitRequirements(testRoute);
            
            // Should be valid with 0.1 ETH profit
            expect(isValid).to.be.true;

            console.log(`   Profit validation test:`);
            console.log(`   Profit: ${ethers.formatEther(testRoute.expectedProfit)} ETH`);
            console.log(`   Flashloan: ${ethers.formatEther(testRoute.flashloanAmount)} ETH`);
            console.log(`   Margin: ${(Number(testRoute.expectedProfit) / Number(testRoute.flashloanAmount) * 100).toFixed(2)}%`);
            console.log(`   Valid: ${isValid}`);
        });

        it('should optimize routes for maximum profit', async function() {
            const testRoute = {
                flashloanToken: { symbol: 'WETH', address: '0x...', decimals: 18 },
                flashloanAmount: ethers.parseEther('1'),
                expectedProfit: ethers.parseEther('0.05'),
                confidence: 75,
                buyDex: 'UNISWAP_V2',
                sellDex: 'UNISWAP_V3',
                buyPrice: ethers.parseEther('2000'),
                sellPrice: ethers.parseEther('2050'),
                gasEstimate: BigInt(300000)
            };

            const originalProfit = testRoute.expectedProfit;
            const optimizedRoute = await (dynamicStrategy as any).optimizeRouteForProfit(testRoute);

            // Optimized route should have higher profit requirement
            expect(Number(optimizedRoute.expectedProfit)).to.be.greaterThan(Number(originalProfit));

            console.log(`   Profit optimization test:`);
            console.log(`   Original profit: ${ethers.formatEther(originalProfit)} ETH`);
            console.log(`   Optimized profit: ${ethers.formatEther(optimizedRoute.expectedProfit)} ETH`);
            console.log(`   Multiplier applied: ${config.profitMarginMultiplier}x`);
        });
    });

    describe('Integration Tests', function() {
        it('should combine all optimization features correctly', async function() {
            // Test that all features work together
            const originalSettings = {
                bundleSubmissionStrategy: config.bundleSubmissionStrategy,
                dynamicGasPricing: config.dynamicGasPricing,
                enableProfitMaximization: config.enableProfitMaximization,
                enablePreemptiveSubmission: config.enablePreemptiveSubmission
            };

            try {
                // Enable all optimizations
                (config as any).bundleSubmissionStrategy = 'aggressive';
                (config as any).dynamicGasPricing = true;
                (config as any).enableProfitMaximization = true;
                (config as any).enablePreemptiveSubmission = true;

                // Test gas pricing with all optimizations
                const gasPrice = await gasEstimator.getOptimalGasPrice('instant', 1);
                expect(gasPrice.gasPrice).to.be.greaterThan(0);

                // Test target block calculation
                const targetBlock = await (flashbotsExecutor as any).calculateOptimalTargetBlock();
                const currentBlock = await provider.getBlockNumber();
                expect(targetBlock).to.be.greaterThan(currentBlock);

                console.log(`   Integration test results:`);
                console.log(`   Gas price: ${ethers.formatUnits(gasPrice.gasPrice, 'gwei')} gwei`);
                console.log(`   Target block: ${targetBlock} (current: ${currentBlock})`);
                console.log(`   All optimizations: ENABLED`);

            } finally {
                // Restore original settings
                Object.assign(config, originalSettings);
            }
        });

        it('should handle edge cases gracefully', async function() {
            // Test with extreme values
            const originalSettings = {
                gasEscalationFactor: config.gasEscalationFactor,
                competitiveGasBuffer: config.competitiveGasBuffer,
                blockTimingBufferMs: config.blockTimingBufferMs
            };

            try {
                // Test with extreme escalation
                (config as any).gasEscalationFactor = 3.0;
                (config as any).competitiveGasBuffer = 5.0;
                (config as any).blockTimingBufferMs = 10000;

                const gasPrice = await gasEstimator.getOptimalGasPrice('instant', 2);
                const targetBlock = await (flashbotsExecutor as any).calculateOptimalTargetBlock();

                // Should still produce valid results
                expect(gasPrice.gasPrice).to.be.greaterThan(0);
                expect(targetBlock).to.be.greaterThan(await provider.getBlockNumber());

                console.log(`   Edge case test:`);
                console.log(`   Extreme gas price: ${ethers.formatUnits(gasPrice.gasPrice, 'gwei')} gwei`);
                console.log(`   Extreme timing target: ${targetBlock}`);

            } finally {
                Object.assign(config, originalSettings);
            }
        });
    });
});
