const { ethers } = require('ethers');

async function debugRouterAddresses() {
    console.log('🔍 Debugging DEX router address mismatch...');
    
    // Connect to mainnet to check the current deployed contract
    const provider = new ethers.JsonRpcProvider('http://************:8545');
    
    // Current mainnet contract address
    const contractAddress = '******************************************';
    
    console.log(`📋 Analysis Configuration:`);
    console.log(`   Contract: ${contractAddress}`);
    console.log(`   Network: Mainnet (Chain ID 1)`);
    
    // Contract interface
    const contractInterface = [
        'function UNISWAP_V2_ROUTER() external view returns (address)',
        'function UNISWAP_V3_ROUTER() external view returns (address)',
        'function UNISWAP_V3_QUOTER() external view returns (address)',
        'function supportedRouters(address) external view returns (bool)',
        'function CHAIN_ID() external view returns (uint256)'
    ];
    
    const contract = new ethers.Contract(contractAddress, contractInterface, provider);
    
    try {
        console.log('\n📊 Contract Router Addresses:');
        
        // Get addresses stored in contract
        const chainId = await contract.CHAIN_ID();
        const contractV2Router = await contract.UNISWAP_V2_ROUTER();
        const contractV3Router = await contract.UNISWAP_V3_ROUTER();
        const contractV3Quoter = await contract.UNISWAP_V3_QUOTER();
        
        console.log(`   Chain ID: ${chainId}`);
        console.log(`   V2 Router: ${contractV2Router}`);
        console.log(`   V3 Router: ${contractV3Router}`);
        console.log(`   V3 Quoter: ${contractV3Quoter}`);
        
        // Check if these addresses are supported
        const v2Supported = await contract.supportedRouters(contractV2Router);
        const v3Supported = await contract.supportedRouters(contractV3Router);
        
        console.log(`   V2 Router supported: ${v2Supported ? '✅' : '❌'}`);
        console.log(`   V3 Router supported: ${v3Supported ? '✅' : '❌'}`);
        
        console.log('\n📊 Known Mainnet Router Addresses:');
        
        // Known correct mainnet addresses
        const knownRouters = {
            'Uniswap V2': '0x7a250d5630B4cF539739dF2C5dAcb4c659F2488D',
            'Uniswap V3': '0xE592427A0AEce92De3Edee1F18E0157C05861564',
            'Uniswap V3 Quoter': '0xb27308f9F90D607463bb33eA1BeBb41C27CE5AB6',
            'SushiSwap': '0xd9e1cE17f2641f24aE83637ab66a2cca9C378B9F'
        };
        
        for (const [name, address] of Object.entries(knownRouters)) {
            const isSupported = await contract.supportedRouters(address);
            const matchesContract = address.toLowerCase() === contractV2Router.toLowerCase() || 
                                  address.toLowerCase() === contractV3Router.toLowerCase();
            
            console.log(`   ${name}: ${address}`);
            console.log(`     Supported: ${isSupported ? '✅' : '❌'}`);
            console.log(`     Matches contract: ${matchesContract ? '✅' : '❌'}`);
        }
        
        console.log('\n🔍 Address Comparison Analysis:');
        
        // Compare with the addresses from the failing transaction
        const failingBuyDex = '0xE592427A0AEce92De3Edee1F18E0157C05861564';  // From error log
        const failingSellDex = '0x7a250d5630B4cF539739dF2C5dAcb4c659F2488D'; // From error log
        
        console.log(`   Failing Buy DEX:  ${failingBuyDex}`);
        console.log(`   Contract V3:      ${contractV3Router}`);
        console.log(`   Exact match: ${failingBuyDex.toLowerCase() === contractV3Router.toLowerCase() ? '✅' : '❌'}`);
        
        console.log(`   Failing Sell DEX: ${failingSellDex}`);
        console.log(`   Contract V2:      ${contractV2Router}`);
        console.log(`   Exact match: ${failingSellDex.toLowerCase() === contractV2Router.toLowerCase() ? '✅' : '❌'}`);
        
        // Check if the failing addresses are supported
        const failingBuySupported = await contract.supportedRouters(failingBuyDex);
        const failingSellSupported = await contract.supportedRouters(failingSellDex);
        
        console.log(`   Failing Buy DEX supported: ${failingBuySupported ? '✅' : '❌'}`);
        console.log(`   Failing Sell DEX supported: ${failingSellSupported ? '✅' : '❌'}`);
        
        console.log('\n🔍 Bot Configuration Check:');
        
        // Check what the bot configuration is sending
        // Let's look at the bot's router address configuration
        console.log('   Checking bot configuration...');
        
        // Read the bot's configuration
        const fs = require('fs');
        const path = require('path');
        
        try {
            // Check if there's a router configuration in the bot
            const configPath = path.join(__dirname, 'src', 'config', 'index.ts');
            if (fs.existsSync(configPath)) {
                const configContent = fs.readFileSync(configPath, 'utf8');
                console.log('   📄 Found bot configuration file');
                
                // Look for router addresses in config
                const v2Match = configContent.match(/0x7a250d5630B4cF539739dF2C5dAcb4c659F2488D/i);
                const v3Match = configContent.match(/0xE592427A0AEce92De3Edee1F18E0157C05861564/i);
                
                console.log(`   V2 Router in config: ${v2Match ? '✅' : '❌'}`);
                console.log(`   V3 Router in config: ${v3Match ? '✅' : '❌'}`);
            }
        } catch (error) {
            console.log('   ❌ Could not read bot configuration');
        }
        
        console.log('\n💡 DIAGNOSIS:');
        
        if (!failingBuySupported || !failingSellSupported) {
            console.log('   ❌ ISSUE FOUND: Router addresses not supported in contract!');
            console.log('   🔧 SOLUTION: The contract initialization failed to add these routers');
            console.log('   💡 This explains the require(false) error in validation');
            
            if (!failingBuySupported) {
                console.log(`   ❌ Buy DEX not supported: ${failingBuyDex}`);
            }
            if (!failingSellSupported) {
                console.log(`   ❌ Sell DEX not supported: ${failingSellDex}`);
            }
            
            console.log('\n🔧 FIXES NEEDED:');
            console.log('   1. Check contract initialization in constructor');
            console.log('   2. Verify _initializeSupportedRouters() is called correctly');
            console.log('   3. Ensure router addresses match exactly');
            console.log('   4. Redeploy contract with fixed initialization');
            
        } else {
            console.log('   ✅ All router addresses are properly supported');
            console.log('   💡 The issue might be elsewhere in the validation logic');
        }
        
    } catch (error) {
        console.log(`❌ Error analyzing router addresses: ${error.message}`);
    }
}

debugRouterAddresses().catch(console.error);
