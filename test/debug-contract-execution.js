const { ethers } = require('ethers');

async function debugContractExecution() {
    console.log('🔍 Deep debugging contract execution on Hardhat fork...');
    
    // Connect to Hardhat fork
    const provider = new ethers.JsonRpcProvider('http://127.0.0.1:8547');
    
    // Use Hardhat's first account (has 10000 ETH)
    const wallet = new ethers.Wallet('0xac0974bec39a17e36ba4a6b4d238ff944bacb478cbed5efcae784d7bf4f2ff80', provider);
    
    console.log(`📋 Test Configuration:`);
    console.log(`   Provider: Hardhat fork (mainnet)`);
    console.log(`   Wallet: ${wallet.address}`);
    console.log(`   Balance: ${ethers.formatEther(await provider.getBalance(wallet.address))} ETH`);
    
    // Deploy fresh contract on Hardhat fork
    console.log('\n🚀 Deploying fresh HybridFlashloanArbitrage contract...');
    
    const aavePoolAddressesProvider = '******************************************';
    const balancerVault = '******************************************';
    
    const fs = require('fs');
    const contractArtifact = JSON.parse(fs.readFileSync('./artifacts/contracts/HybridFlashloanArbitrage.sol/HybridFlashloanArbitrage.json', 'utf8'));
    
    const contractFactory = new ethers.ContractFactory(
        contractArtifact.abi,
        contractArtifact.bytecode,
        wallet
    );
    
    const contract = await contractFactory.deploy(
        aavePoolAddressesProvider,
        balancerVault
    );
    
    await contract.waitForDeployment();
    const contractAddress = await contract.getAddress();
    
    console.log(`✅ Contract deployed at: ${contractAddress}`);
    
    // Verify contract state
    console.log('\n📋 Contract State Verification:');
    const owner = await contract.owner();
    const chainId = await contract.CHAIN_ID();
    const v2Router = await contract.UNISWAP_V2_ROUTER();
    const v3Router = await contract.UNISWAP_V3_ROUTER();
    const balancerVaultAddr = await contract.BALANCER_VAULT();
    
    console.log(`   Owner: ${owner}`);
    console.log(`   Chain ID: ${chainId}`);
    console.log(`   V2 Router: ${v2Router}`);
    console.log(`   V3 Router: ${v3Router}`);
    console.log(`   Balancer Vault: ${balancerVaultAddr}`);
    
    // Token addresses
    const wethAddress = '******************************************';
    const usdcAddress = '******************************************';
    const daiAddress = '******************************************';
    
    console.log('\n🧪 Testing with exact parameters from failed bot execution...');
    
    // Test the exact same parameters that the bot was trying to use
    const flashloanAmount = ethers.parseEther('0.02'); // From bot logs
    
    // Test 1: WETH → USDC (working pair from previous tests)
    console.log('\n   Test 1: WETH → USDC (known working pair)');
    await testFlashloanExecution(contract, wethAddress, usdcAddress, flashloanAmount, v2Router, v3Router, 0, '1');
    
    // Test 2: WETH → DAI (problematic pair)
    console.log('\n   Test 2: WETH → DAI (problematic pair)');
    await testFlashloanExecution(contract, wethAddress, daiAddress, flashloanAmount, v2Router, v3Router, 0, '2');
    
    // Test 3: Different amounts
    console.log('\n   Test 3: Very small amount (1 wei)');
    await testFlashloanExecution(contract, wethAddress, usdcAddress, ethers.parseUnits('1', 'wei'), v2Router, v3Router, 0, '3');
    
    // Test 4: Different provider (Balancer)
    console.log('\n   Test 4: WETH → USDC with Balancer provider');
    await testFlashloanExecution(contract, wethAddress, usdcAddress, flashloanAmount, v2Router, v3Router, 1, '4');
    
    // Test 5: Check if pools actually exist and have liquidity
    console.log('\n🔍 Verifying DEX pool existence and liquidity...');
    await verifyPoolLiquidity(provider, wethAddress, usdcAddress, daiAddress);
    
    // Test 6: Try to simulate the exact bot transaction
    console.log('\n🤖 Simulating exact bot transaction...');
    await simulateBotTransaction(contract, provider, wallet);
    
    console.log('\n🏁 Deep debugging completed!');
}

async function testFlashloanExecution(contract, tokenA, tokenB, amount, v2Router, v3Router, provider, testName) {
    try {
        // Encode arbitrage parameters
        const arbitrageParams = ethers.AbiCoder.defaultAbiCoder().encode(
            ['address', 'address', 'address', 'address', 'uint24', 'uint256', 'uint8'],
            [
                tokenA,                          // tokenA
                tokenB,                          // tokenB
                v2Router,                        // buyDex
                v3Router,                        // sellDex
                3000,                            // v3Fee
                ethers.parseEther('0.0001'),     // minProfit
                provider                         // FlashloanProvider (0=AAVE, 1=BALANCER)
            ]
        );
        
        // Try static call first
        await contract.executeOptimalFlashloan.staticCall(
            tokenA,
            amount,
            arbitrageParams
        );
        
        console.log(`      ✅ Test ${testName}: SUCCESS`);
        return true;
        
    } catch (error) {
        console.log(`      ❌ Test ${testName}: ${error.message.split('(')[0]}`);
        
        // Decode error details
        if (error.data) {
            console.log(`         Error data: ${error.data}`);
            
            // Check for panic codes
            if (error.data.startsWith('0x4e487b71')) {
                const panicCode = parseInt(error.data.slice(10, 74), 16);
                console.log(`         Panic code: ${panicCode}`);
                
                const panicMeanings = {
                    1: 'ASSERT_FAILED',
                    17: 'ENUM_CONVERSION_ERROR',
                    18: 'DIVISION_BY_ZERO',
                    33: 'ARRAY_ACCESS_OUT_OF_BOUNDS',
                    34: 'BYTE_ARRAY_ACCESS_OUT_OF_BOUNDS'
                };
                
                if (panicMeanings[panicCode]) {
                    console.log(`         Meaning: ${panicMeanings[panicCode]}`);
                }
            }
            
            // Check for revert with reason
            if (error.data.startsWith('0x08c379a0')) {
                try {
                    const reason = ethers.AbiCoder.defaultAbiCoder().decode(['string'], '0x' + error.data.slice(10))[0];
                    console.log(`         Revert reason: "${reason}"`);
                } catch (decodeError) {
                    console.log(`         Could not decode revert reason`);
                }
            }
        }
        
        return false;
    }
}

async function verifyPoolLiquidity(provider, wethAddress, usdcAddress, daiAddress) {
    // Check Uniswap V2 pools
    const uniV2FactoryAddress = '******************************************';
    const uniV2Factory = new ethers.Contract(uniV2FactoryAddress, [
        'function getPair(address tokenA, address tokenB) view returns (address pair)'
    ], provider);
    
    try {
        const wethUsdcPair = await uniV2Factory.getPair(wethAddress, usdcAddress);
        const wethDaiPair = await uniV2Factory.getPair(wethAddress, daiAddress);
        
        console.log(`   WETH/USDC V2 pair: ${wethUsdcPair}`);
        console.log(`   WETH/DAI V2 pair: ${wethDaiPair}`);
        
        if (wethUsdcPair !== ethers.ZeroAddress) {
            const pairContract = new ethers.Contract(wethUsdcPair, [
                'function getReserves() view returns (uint112 reserve0, uint112 reserve1, uint32 blockTimestampLast)',
                'function token0() view returns (address)',
                'function token1() view returns (address)'
            ], provider);
            
            const reserves = await pairContract.getReserves();
            const token0 = await pairContract.token0();
            const token1 = await pairContract.token1();
            
            console.log(`   WETH/USDC reserves: ${ethers.formatEther(reserves[0])} / ${ethers.formatUnits(reserves[1], 6)}`);
            console.log(`   Token0: ${token0}, Token1: ${token1}`);
        }
        
        if (wethDaiPair !== ethers.ZeroAddress) {
            const pairContract = new ethers.Contract(wethDaiPair, [
                'function getReserves() view returns (uint112 reserve0, uint112 reserve1, uint32 blockTimestampLast)'
            ], provider);
            
            const reserves = await pairContract.getReserves();
            console.log(`   WETH/DAI reserves: ${ethers.formatEther(reserves[0])} / ${ethers.formatEther(reserves[1])}`);
        }
        
    } catch (error) {
        console.log(`   ❌ Error checking pools: ${error.message}`);
    }
}

async function simulateBotTransaction(contract, provider, wallet) {
    // Simulate the exact transaction the bot was trying to execute
    // Based on the bot logs: WETH → USDC → WETH, 0.02 WETH, Aave provider
    
    const wethAddress = '******************************************';
    const usdcAddress = '******************************************';
    const flashloanAmount = ethers.parseEther('0.02');
    
    const v2Router = await contract.UNISWAP_V2_ROUTER();
    const v3Router = await contract.UNISWAP_V3_ROUTER();
    
    console.log(`   Simulating: ${ethers.formatEther(flashloanAmount)} WETH flashloan`);
    console.log(`   Route: WETH → USDC → WETH`);
    console.log(`   Provider: Aave (0)`);
    
    const arbitrageParams = ethers.AbiCoder.defaultAbiCoder().encode(
        ['address', 'address', 'address', 'address', 'uint24', 'uint256', 'uint8'],
        [
            wethAddress,                     // tokenA
            usdcAddress,                     // tokenB
            v2Router,                        // buyDex
            v3Router,                        // sellDex
            3000,                            // v3Fee
            ethers.parseEther('0.0001'),     // minProfit
            0                                // FlashloanProvider.AAVE
        ]
    );
    
    try {
        // Try with different gas limits
        const gasLimits = [500000, 1000000, 2000000, 5000000];
        
        for (const gasLimit of gasLimits) {
            try {
                console.log(`   Testing with gas limit: ${gasLimit}`);
                
                const result = await contract.executeOptimalFlashloan.staticCall(
                    wethAddress,
                    flashloanAmount,
                    arbitrageParams,
                    { gasLimit }
                );
                
                console.log(`   ✅ SUCCESS with gas limit ${gasLimit}`);
                console.log(`   Result: ${result}`);
                return;
                
            } catch (gasError) {
                console.log(`   ❌ Failed with gas limit ${gasLimit}: ${gasError.message.split('(')[0]}`);
            }
        }
        
    } catch (error) {
        console.log(`   ❌ Bot simulation failed: ${error.message}`);
        
        if (error.data) {
            console.log(`   Error data: ${error.data}`);
        }
    }
}

// Run the debugging
debugContractExecution().catch(console.error);
