// Set environment for Hardhat testing
process.env.CHAIN_ID = '31337';
process.env.RPC_URL = 'http://************:8545';

const { ethers } = require('ethers');
const { config } = require('./dist/config');
const { BalancerFlashloanStrategy } = require('./dist/strategies/balancer-flashloan');

console.log('🧪 Testing Balancer Flashloan Contract Integration');
console.log('=' .repeat(60));

async function testBalancerFlashloan() {
  try {
    // Connect to Hardhat network
    const provider = new ethers.JsonRpcProvider('http://localhost:8545');

    // Get actual chain ID from the network
    const network = await provider.getNetwork();
    const actualChainId = Number(network.chainId);

    console.log('📋 Configuration:');
    console.log(`Balancer Vault: ${config.balancerVaultAddress}`);
    console.log(`Balancer Contract: ${config.balancerFlashloanContract}`);
    console.log(`Config Chain ID: ${config.chainId}`);
    console.log(`Actual Chain ID: ${actualChainId}`);
    
    // Initialize strategy
    console.log('\n🔵 Initializing Balancer Flashloan Strategy...');
    const strategy = new BalancerFlashloanStrategy(provider);
    
    // Test contract connection
    console.log('\n🔍 Testing contract connection...');
    
    // Check if contract is deployed
    const contractCode = await provider.getCode(config.balancerFlashloanContract);
    if (contractCode === '0x') {
      console.log('❌ Contract not found at address:', config.balancerFlashloanContract);
      return false;
    }
    
    console.log(`✅ Contract deployed (${contractCode.length} bytes)`);
    
    // Test scanning for opportunities
    console.log('\n🔍 Scanning for Balancer flashloan opportunities...');
    const opportunities = await strategy.scanForBalancerFlashloanOpportunities();
    
    console.log(`📊 Found ${opportunities.length} opportunities`);
    
    if (opportunities.length > 0) {
      const bestOpportunity = opportunities[0];
      console.log('\n🎯 Best Opportunity:');
      console.log(`Token: ${bestOpportunity.flashloanToken.symbol}`);
      console.log(`Amount: ${ethers.formatUnits(bestOpportunity.flashloanAmount, bestOpportunity.flashloanToken.decimals)}`);
      console.log(`Expected Profit: ${ethers.formatEther(bestOpportunity.expectedProfit)} ETH`);
      console.log(`Confidence: ${bestOpportunity.confidence}%`);
      
      // Test execution in simulation mode
      console.log('\n🎭 Testing execution in simulation mode...');
      const success = await strategy.executeBalancerFlashloan(bestOpportunity);
      
      if (success) {
        console.log('✅ Simulation successful!');
      } else {
        console.log('❌ Simulation failed');
      }
    } else {
      console.log('ℹ️  No opportunities found (this is normal in test environment)');
    }
    
    console.log('\n✅ Balancer flashloan integration test completed!');
    return true;
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('Stack:', error.stack);
    return false;
  }
}

// Run the test
testBalancerFlashloan()
  .then((success) => {
    if (success) {
      console.log('\n🎉 All tests passed!');
      process.exit(0);
    } else {
      console.log('\n💥 Tests failed!');
      process.exit(1);
    }
  })
  .catch((error) => {
    console.error('💥 Unexpected error:', error);
    process.exit(1);
  });
