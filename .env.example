# Application Configuration
NODE_ENV=development
PORT=3000
LOG_LEVEL=info

# Dashboard Configuration
WEB_DASHBOARD=true
SPLIT_SCREEN_DASHBOARD=false
DASHBOARD_UPDATE_INTERVAL=5000

# Blockchain Configuration
CHAIN_ID=1
PRIVATE_KEY=your_private_key_here
RPC_URLS=https://eth-mainnet.alchemyapi.io/v2/your-api-key,https://mainnet.infura.io/v3/your-project-id

# Flashbots Configuration
FLASHBOTS_RELAY_URL=https://relay.flashbots.net

# Gas Configuration
MAX_GAS_PRICE=100
MAX_PRIORITY_FEE_PER_GAS=2
GAS_LIMIT_MULTIPLIER=1.2
CONFIRMATION_BLOCKS=1
BLOCK_TIME_MS=12000

# Arbitrage Configuration
ENABLE_FLASHLOAN_ATTACKS=true
ENABLE_ARBITRAGE=true
ENABLE_MEV_SHARE=false
FLASHLOAN_CONTRACT_ADDRESS=******************************************

# Trading Configuration
MIN_PROFIT_THRESHOLD=0.001
MAX_SLIPPAGE_BPS=100
MAX_TRADE_AMOUNT=10
MIN_TRADE_AMOUNT=0.1
MAX_CONCURRENT_TRADES=3
TRADE_TIMEOUT_MS=30000

# Risk Management
MAX_CONSECUTIVE_FAILURES=5
DAILY_LOSS_LIMIT=1
ENABLE_RISK_MANAGEMENT=true

# Token Configuration
MONITORED_TOKENS=WETH,USDC,DAI,USDT
SUPPORTED_DEXES=UNISWAP_V2,UNISWAP_V3,SUSHISWAP,CURVE,BALANCER
