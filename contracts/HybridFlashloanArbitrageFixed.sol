// SPDX-License-Identifier: MIT
pragma solidity ^0.8.19;

import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import "@openzeppelin/contracts/utils/ReentrancyGuard.sol";
import "@openzeppelin/contracts/utils/Pausable.sol";

// Minimal interfaces to avoid dependency conflicts
interface IAavePool {
    function flashLoanSimple(
        address receiverAddress,
        address asset,
        uint256 amount,
        bytes calldata params,
        uint16 referralCode
    ) external;
}

interface IBalancerVault {
    function flashLoan(
        address recipient,
        IERC20[] memory tokens,
        uint256[] memory amounts,
        bytes memory userData
    ) external;
}

interface IUniswapV2Router {
    function swapExactTokensForTokens(
        uint amountIn,
        uint amountOutMin,
        address[] calldata path,
        address to,
        uint deadline
    ) external returns (uint[] memory amounts);

    function getAmountsOut(uint amountIn, address[] calldata path)
    external view returns (uint[] memory amounts);
}

interface IUniswapV3Router {
    struct ExactInputSingleParams {
        address tokenIn;
        address tokenOut;
        uint24 fee;
        address recipient;
        uint256 deadline;
        uint256 amountIn;
        uint256 amountOutMinimum;
        uint160 sqrtPriceLimitX96;
    }

    struct ExactInputParams {
        bytes path;
        address recipient;
        uint256 deadline;
        uint256 amountIn;
        uint256 amountOutMinimum;
    }

    function exactInputSingle(ExactInputSingleParams calldata params)
    external
    payable
    returns (uint256 amountOut);

    function exactInput(ExactInputParams calldata params)
    external
    payable
    returns (uint256 amountOut);
}

interface IUniswapV3Quoter {
    function quoteExactInputSingle(
        address tokenIn,
        address tokenOut,
        uint24 fee,
        uint256 amountIn,
        uint160 sqrtPriceLimitX96
    ) external returns (uint256 amountOut);
}

interface ICurvePool {
    function exchange(
        int128 i,
        int128 j,
        uint256 dx,
        uint256 min_dy
    ) external returns (uint256);

    function get_dy(
        int128 i,
        int128 j,
        uint256 dx
    ) external view returns (uint256);

    function coins(uint256 i) external view returns (address);
}

interface IBalancerV2Vault {
    struct SingleSwap {
        bytes32 poolId;
        SwapKind kind;
        address assetIn;
        address assetOut;
        uint256 amount;
        bytes userData;
    }

    struct FundManagement {
        address sender;
        bool fromInternalBalance;
        address payable recipient;
        bool toInternalBalance;
    }

    enum SwapKind { GIVEN_IN, GIVEN_OUT }

    function swap(
        SingleSwap memory singleSwap,
        FundManagement memory funds,
        uint256 limit,
        uint256 deadline
    ) external returns (uint256);

    function querySwap(
        SingleSwap memory singleSwap,
        FundManagement memory funds
    ) external returns (uint256);

    function getPoolTokens(bytes32 poolId) external view returns (
        address[] memory tokens,
        uint256[] memory balances,
        uint256 lastChangeBlock
    );

    function getPool(bytes32 poolId) external view returns (
        address poolAddress,
        uint8 specialization
    );
}

interface IBalancerV2Pool {
    function getPoolId() external view returns (bytes32);
    function getSwapFeePercentage() external view returns (uint256);
    function getVault() external view returns (address);
}

// Interface for querying Balancer pools
interface IBalancerV2PoolRegistry {
    function getPoolsWithTokens(address[] memory tokens) external view returns (bytes32[] memory);
    function isPoolRegistered(bytes32 poolId) external view returns (bool);
}

interface IUniswapV3QuoterV2 {
    function quoteExactInputSingle(
        address tokenIn,
        address tokenOut,
        uint24 fee,
        uint256 amountIn,
        uint160 sqrtPriceLimitX96
    ) external view returns (uint256 amountOut, uint160 sqrtPriceX96After, uint32 initializedTicksCrossed, uint256 gasEstimate);
}

/**
 * @title HybridFlashloanArbitrageFixed
 * @dev Enhanced version with security features: reentrancy protection, pausable mechanism, and strict validation
 */
contract HybridFlashloanArbitrageFixed is Ownable, ReentrancyGuard, Pausable {

    // Contract state
    IAavePool public immutable AAVE_POOL;
    IBalancerVault public immutable BALANCER_VAULT;
    address public immutable UNISWAP_V2_ROUTER;
    address public immutable UNISWAP_V3_ROUTER;
    address public immutable UNISWAP_V3_QUOTER;
    uint256 public immutable CHAIN_ID;

    // Supported DEX routers mapping with type classification
    mapping(address => DEX_TYPE) public supportedRouterTypes;

    // Balancer V2 pool management
    mapping(bytes32 => bool) public balancerPoolExists;
    mapping(address => mapping(address => bytes32)) public balancerPoolIds; // tokenA => tokenB => poolId
    mapping(bytes32 => uint256) public balancerPoolFees; // poolId => fee percentage (in basis points)

    // Events
    event ArbitrageExecuted(
        address indexed asset,
        uint256 amount,
        uint256 profit,
        address[] buyPath,
        address[] sellPath
    );

    enum FlashloanProvider { AAVE, BALANCER }

    enum DEX_TYPE {
        UNSUPPORTED,
        V2,
        V3,
        CURVE,
        BALANCER_V2
    }

    struct ArbitrageParams {
        address[] buyPath;            // Multi-hop path for buy trade (e.g., [WETH, DAI, USDC])
        address[] sellPath;           // Multi-hop path for sell trade (e.g., [USDC, DAI, WETH])
        address buyDex;               // DEX router for buy trade
        address sellDex;              // DEX router for sell trade
        uint24[] v3Fees;              // V3 fees for each hop (if using V3)
        uint256 minProfit;            // Minimum profit required
        FlashloanProvider provider;   // Flashloan provider (Aave/Balancer)
        uint256 slippageToleranceBps; // Slippage tolerance in basis points
        uint256 maxGasCostWei;        // Maximum gas cost in wei
    }

    // Constants
    uint256 private constant BASIS_POINTS_DENOMINATOR = 10000; // 100% in basis points
    uint256 private constant MAX_SLIPPAGE_BPS = 5000; // 50% maximum slippage
    uint256 private constant AAVE_FLASHLOAN_FEE_BPS = 9; // 0.09% Aave flashloan fee
    uint256 private constant ESTIMATED_LOSS_PER_HOP_BPS = 4; // 0.04% estimated loss per hop

    /*
     * Error Codes (for gas optimization):
     * E1: Invalid token in buy path
     * E2: Invalid token in sell path
     * E3: Too many V3 fees provided
     * E4: At least one V3 fee required when fees provided
     * E5: Arbitrage path must form a complete loop
     * E6: Unsupported buy DEX
     * E7: Unsupported sell DEX
     * E8: Unsupported router type
     * E9: Tokens not found in Curve pool
     * E10: Balancer pool not found
     * E11: Balancer V2 only supports direct swaps
     * E12: Invalid amount
     * E13: Pool not registered
     * E14: Tokens not in pool
     * E15: Insufficient liquidity
     * E16: Minimum output too low
     * E17: Slippage exceeded
     * E18: Invalid output amount
     * E19: Balancer swap failed
     * E20: Token not found in pool
     * E21: Balancer querySwap failed
     */

    event FlashloanExecuted(
        FlashloanProvider indexed provider,
        address indexed asset,
        uint256 amount,
        uint256 premium,
        uint256 profit
    );

    constructor(
        address _aavePool,
        address _balancerVault
    ) Ownable(msg.sender) {
        AAVE_POOL = IAavePool(_aavePool);
        BALANCER_VAULT = IBalancerVault(_balancerVault);
        CHAIN_ID = block.chainid;

        // Set router addresses based on chain
        (address v2Router, address v3Router, address v3Quoter) = _getRouterAddresses();
        UNISWAP_V2_ROUTER = v2Router;
        UNISWAP_V3_ROUTER = v3Router;
        UNISWAP_V3_QUOTER = v3Quoter;

        // Initialize supported routers
        _initializeSupportedRouters();

        // Initialize Balancer V2 pools
        _initializeBalancerPools();
    }

    /**
     * @dev Initialize supported DEX routers
     */
    function _initializeSupportedRouters() internal {
        // Cache chain ID to reduce SLOAD operations
        uint256 chainId = CHAIN_ID;

        // Uniswap routers (always available)
        supportedRouterTypes[UNISWAP_V2_ROUTER] = DEX_TYPE.V2;
        supportedRouterTypes[UNISWAP_V3_ROUTER] = DEX_TYPE.V3;

        // Add other DEX routers based on chain
        if (chainId == 1) {
            // Mainnet - add V2 forks, Curve, and Balancer V2
            supportedRouterTypes[******************************************] = DEX_TYPE.V2; // SushiSwap

            // Curve pools (major stablecoin and ETH pools)
            supportedRouterTypes[******************************************] = DEX_TYPE.CURVE; // 3Pool (DAI/USDC/USDT)
            supportedRouterTypes[******************************************] = DEX_TYPE.CURVE; // Tricrypto2 (USDT/WBTC/WETH)
            supportedRouterTypes[******************************************] = DEX_TYPE.CURVE; // GUSD Metapool
            supportedRouterTypes[******************************************] = DEX_TYPE.CURVE; // STECRV (stETH/ETH)

            // Balancer V2 Vault
            supportedRouterTypes[******************************************] = DEX_TYPE.BALANCER_V2; // Balancer V2 Vault
        } else if (chainId == 56) {
            // BSC - PancakeSwap and other BSC DEXs
            supportedRouterTypes[******************************************] = DEX_TYPE.V2; // PancakeSwap V2
        } else if (chainId == 31337) {
            // Hardhat - use mainnet addresses when forking
            supportedRouterTypes[******************************************] = DEX_TYPE.V2; // SushiSwap
        }
    }

    /**
     * @dev Initialize Balancer V2 pools with real mainnet pool IDs
     */
    function _initializeBalancerPools() internal {
        // Cache chain ID to reduce SLOAD operations
        uint256 chainId = CHAIN_ID;

        if (chainId == 1) {
            // Mainnet Balancer V2 pools - Real pool IDs from Balancer V2

            // WETH/USDC 80/20 Weighted Pool (High liquidity)
            bytes32 wethUsdcPoolId = 0x96646936b91d6b9d7d0c47c496afbf3d6ec7b6f8000200000000000000000019;
            balancerPoolExists[wethUsdcPoolId] = true;
            balancerPoolIds[******************************************][******************************************] = wethUsdcPoolId;
            balancerPoolIds[******************************************][******************************************] = wethUsdcPoolId;
            balancerPoolFees[wethUsdcPoolId] = 100; // 1% swap fee

            // WETH/DAI 80/20 Weighted Pool
            bytes32 wethDaiPoolId = 0x0b09dea16768f0799065c475be02919503cb2a3500020000000000000000001a;
            balancerPoolExists[wethDaiPoolId] = true;
            balancerPoolIds[******************************************][******************************************] = wethDaiPoolId;
            balancerPoolIds[******************************************][******************************************] = wethDaiPoolId;
            balancerPoolFees[wethDaiPoolId] = 100; // 1% swap fee

            // USDC/DAI Stable Pool (Low slippage for stablecoins)
            bytes32 usdcDaiPoolId = 0x06df3b2bbb68adc8b0e302443692037ed9f91b42000000000000000000000063;
            balancerPoolExists[usdcDaiPoolId] = true;
            balancerPoolIds[******************************************][******************************************] = usdcDaiPoolId;
            balancerPoolIds[******************************************][******************************************] = usdcDaiPoolId;
            balancerPoolFees[usdcDaiPoolId] = 10; // 0.1% swap fee for stable pool

        } else if (chainId == 31337) {
            // Hardhat - use mainnet pool IDs when forking
            bytes32 wethUsdcPoolId = 0x96646936b91d6b9d7d0c47c496afbf3d6ec7b6f8000200000000000000000019;
            balancerPoolExists[wethUsdcPoolId] = true;
            balancerPoolIds[******************************************][******************************************] = wethUsdcPoolId;
            balancerPoolIds[******************************************][******************************************] = wethUsdcPoolId;
            balancerPoolFees[wethUsdcPoolId] = 100;
        }
    }

    /**
     * @dev Get router addresses based on chain ID
     */
    function _getRouterAddresses() internal view returns (address v2Router, address v3Router, address v3Quoter) {
        uint256 chainId = CHAIN_ID;

        if (chainId == 1) {
            // Mainnet
            return (
                ******************************************, // Uniswap V2
                ******************************************,  // Uniswap V3
                ******************************************   // Uniswap V3 Quoter
            );
        } else if (chainId == 11155111) {
            // Sepolia
            return (
                ******************************************, // Uniswap V2
                ******************************************, // Uniswap V3
                ******************************************  // Uniswap V3 Quoter
            );
        } else if (chainId == 31337) {
            // Hardhat - use mainnet addresses when forking mainnet
            return (
                ******************************************, // Uniswap V2
                ******************************************,  // Uniswap V3
                ******************************************   // Uniswap V3 Quoter
            );
        } else {
            revert("Unsupported chain");
        }
    }

    /**
     * @dev Execute optimal flashloan with security protections
     */
    function executeOptimalFlashloan(
        address asset,
        uint256 amount,
        bytes calldata params
    ) external onlyOwner nonReentrant whenNotPaused {
        // Strict input validation
        require(asset != address(0), "Invalid asset address");
        require(amount > 0, "Invalid amount");
        require(amount >= 1e15, "Amount too small for flashloan"); // 0.001 ETH minimum
        require(params.length > 0, "Empty parameters");

        ArbitrageParams memory arbParams = abi.decode(params, (ArbitrageParams));

        // Validate arbitrage parameters
        _validateArbitrageParams(arbParams);

        require(asset == arbParams.buyPath[0], "Asset must match first token in buy path");

        if (arbParams.provider == FlashloanProvider.BALANCER) {
            _executeBalancerFlashloan(asset, amount, params);
        } else {
            _executeAaveFlashloan(asset, amount, params);
        }
    }

    /**
     * @dev Execute Balancer flashloan
     */
    function _executeBalancerFlashloan(
        address asset,
        uint256 amount,
        bytes memory params
    ) internal {
        IERC20[] memory tokens = new IERC20[](1);
        tokens[0] = IERC20(asset);

        uint256[] memory amounts = new uint256[](1);
        amounts[0] = amount;

        BALANCER_VAULT.flashLoan(
            address(this),
            tokens,
            amounts,
            params
        );
    }

    /**
     * @dev Execute Aave flashloan
     */
    function _executeAaveFlashloan(
        address asset,
        uint256 amount,
        bytes memory params
    ) internal {
        AAVE_POOL.flashLoanSimple(
            address(this),
            asset,
            amount,
            params,
            0 // referralCode
        );
    }

    /**
     * @dev Balancer flashloan callback with reentrancy protection
     */
    function receiveFlashLoan(
        IERC20[] memory tokens,
        uint256[] memory amounts,
        uint256[] memory, // feeAmounts (always 0 for Balancer)
        bytes memory userData
    ) external nonReentrant whenNotPaused {
        require(msg.sender == address(BALANCER_VAULT), "Caller must be Balancer Vault");
        require(tokens.length == 1, "Invalid tokens array");
        require(amounts.length == 1, "Invalid amounts array");
        require(amounts[0] > 0, "Invalid flashloan amount");

        ArbitrageParams memory arbParams = abi.decode(userData, (ArbitrageParams));
        _validateArbitrageParams(arbParams);

        // Execute arbitrage
        uint256 profit = _executeArbitrage(address(tokens[0]), amounts[0], arbParams);

        // Balancer has 0% fees, so just repay the original amount
        // Balancer automatically pulls the tokens, no need to transfer

        emit FlashloanExecuted(
            FlashloanProvider.BALANCER,
            address(tokens[0]),
            amounts[0],
            0, // No premium for Balancer
            profit
        );
    }

    /**
     * @dev Aave flashloan callback with reentrancy protection
     */
    function executeOperation(
        address asset,
        uint256 amount,
        uint256 premium,
        address initiator,
        bytes calldata params
    ) external nonReentrant whenNotPaused returns (bool) {
        require(msg.sender == address(AAVE_POOL), "Caller must be Aave pool");
        require(initiator == address(this), "Invalid initiator");
        require(asset != address(0), "Invalid asset address");
        require(amount > 0, "Invalid flashloan amount");

        ArbitrageParams memory arbParams = abi.decode(params, (ArbitrageParams));
        _validateArbitrageParams(arbParams);

        // Execute arbitrage
        uint256 profit = _executeArbitrage(asset, amount, arbParams);

        // Repay flashloan with premium
        uint256 amountToRepay = amount + premium;
        IERC20(asset).approve(address(AAVE_POOL), amountToRepay);

        // Validate profit after Aave fees and gas costs
        require(profit > premium + arbParams.maxGasCostWei, "Arbitrage not profitable after Aave fees and gas costs");

        emit FlashloanExecuted(
            FlashloanProvider.AAVE,
            asset,
            amount,
            premium,
            profit - premium
        );

        return true;
    }

    /**
     * @dev Execute multi-hop arbitrage with gas cost consideration
     */
    function _executeArbitrage(
        address asset,
        uint256 amount,
        ArbitrageParams memory params
    ) internal returns (uint256 profit) {
        // Step 1: Execute buy trade (multi-hop)
        uint256 intermediateAmount = _executeMultiHopTrade(
            amount,
            params.buyPath,
            params.buyDex,
            params.v3Fees,
            params.slippageToleranceBps
        );
        require(intermediateAmount > 0, "Buy execution failed");

        // Step 2: Execute sell trade (multi-hop)
        uint256 finalAmount = _executeMultiHopTrade(
            intermediateAmount,
            params.sellPath,
            params.sellDex,
            params.v3Fees,
            params.slippageToleranceBps
        );
        require(finalAmount > 0, "Sell execution failed");

        // Step 3: Calculate gross profit
        require(finalAmount >= amount, "Arbitrage resulted in loss");
        profit = finalAmount - amount;

        // Step 4: Validate profit after gas costs
        require(profit >= params.minProfit + params.maxGasCostWei, "Profit insufficient after gas costs");

        // Emit event
        emit ArbitrageExecuted(asset, amount, profit, params.buyPath, params.sellPath);

        return profit;
    }

    /**
     * @dev Execute multi-hop trade on any supported DEX
     */
    function _executeMultiHopTrade(
        uint256 amountIn,
        address[] memory path,
        address router,
        uint24[] memory v3Fees,
        uint256 slippageToleranceBps
    ) internal returns (uint256 amountOut) {
        require(path.length >= 2, "Invalid path length");
        require(supportedRouterTypes[router] != DEX_TYPE.UNSUPPORTED, "E8"); // Unsupported router

        // Approve router to spend input token
        IERC20(path[0]).approve(router, amountIn);

        // Use DEX type system for cleaner routing logic
        DEX_TYPE dexType = supportedRouterTypes[router];

        if (dexType == DEX_TYPE.V2) {
            // Execute V2-style multi-hop trade (works for Uniswap V2, SushiSwap, PancakeSwap, etc.)
            amountOut = _executeV2MultiHop(amountIn, path, router, slippageToleranceBps);
        } else if (dexType == DEX_TYPE.V3) {
            // Execute V3 multi-hop trade
            amountOut = _executeV3MultiHop(amountIn, path, router, v3Fees, slippageToleranceBps);
        } else if (dexType == DEX_TYPE.CURVE) {
            // Execute Curve trade
            amountOut = _executeCurveTrade(amountIn, path, router, slippageToleranceBps);
        } else if (dexType == DEX_TYPE.BALANCER_V2) {
            // Execute Balancer V2 trade
            amountOut = _executeBalancerV2Trade(amountIn, path, router, slippageToleranceBps);
        } else {
            revert("E8"); // Unsupported router type
        }

        return amountOut;
    }







    /**
     * @dev Execute V2-style multi-hop trade
     */
    function _executeV2MultiHop(
        uint256 amountIn,
        address[] memory path,
        address router,
        uint256 slippageToleranceBps
    ) internal returns (uint256 amountOut) {
        // Get expected amount out
        uint256 expectedAmountOut;
        try IUniswapV2Router(router).getAmountsOut(amountIn, path) returns (uint[] memory amounts) {
            expectedAmountOut = amounts[amounts.length - 1];
        } catch {
            expectedAmountOut = amountIn; // Fallback
        }

        // Calculate minimum amount out with dynamic slippage
        uint256 minAmountOut = (expectedAmountOut * (BASIS_POINTS_DENOMINATOR - slippageToleranceBps)) / BASIS_POINTS_DENOMINATOR;

        // Execute multi-hop trade
        uint[] memory amounts = IUniswapV2Router(router).swapExactTokensForTokens(
            amountIn,
            minAmountOut,
            path,
            address(this),
            block.timestamp + 300
        );

        return amounts[amounts.length - 1];
    }

    /**
     * @dev Execute V3 multi-hop trade
     */
    function _executeV3MultiHop(
        uint256 amountIn,
        address[] memory path,
        address router,
        uint24[] memory fees,
        uint256 slippageToleranceBps
    ) internal returns (uint256 amountOut) {
        if (path.length == 2) {
            // Single hop
            uint24 fee = fees.length > 0 ? fees[0] : 3000;

            // Try to get expected amount out from V3 quoter (safe approach)
            uint256 expectedAmountOut = _getV3QuoteSafe(path[0], path[1], fee, amountIn);

            // Calculate minimum amount out with dynamic slippage
            uint256 minAmountOut = (expectedAmountOut * (BASIS_POINTS_DENOMINATOR - slippageToleranceBps)) / BASIS_POINTS_DENOMINATOR;

            IUniswapV3Router.ExactInputSingleParams memory params = IUniswapV3Router.ExactInputSingleParams({
                tokenIn: path[0],
                tokenOut: path[1],
                fee: fee,
                recipient: address(this),
                deadline: block.timestamp + 300,
                amountIn: amountIn,
                amountOutMinimum: minAmountOut,
                sqrtPriceLimitX96: 0
            });

            return IUniswapV3Router(router).exactInputSingle(params);
        } else {
            // Multi-hop: encode path for V3
            bytes memory encodedPath = _encodeV3Path(path, fees);

            // Get expected amount (simplified)
            uint256 expectedAmountOut = amountIn; // Simplified for now
            uint256 minAmountOut = (expectedAmountOut * (BASIS_POINTS_DENOMINATOR - slippageToleranceBps)) / BASIS_POINTS_DENOMINATOR;

            // Execute multi-hop trade using exactInput
            IUniswapV3Router.ExactInputParams memory params = IUniswapV3Router.ExactInputParams({
                path: encodedPath,
                recipient: address(this),
                deadline: block.timestamp + 300,
                amountIn: amountIn,
                amountOutMinimum: minAmountOut
            });

            return IUniswapV3Router(router).exactInput(params);
        }
    }

    /**
     * @dev Encode path for Uniswap V3 multi-hop
     */
    function _encodeV3Path(address[] memory path, uint24[] memory fees) internal pure returns (bytes memory) {
        bytes memory encoded = abi.encodePacked(path[0]);

        for (uint i = 1; i < path.length; i++) {
            uint24 fee = fees.length > i - 1 ? fees[i - 1] : 3000; // Default 0.3%
            encoded = abi.encodePacked(encoded, fee, path[i]);
        }

        return encoded;
    }

    /**
     * @dev Execute trade on Uniswap V3 with dynamic slippage protection
     */
    function _executeV3TradeWithSlippage(
        address tokenIn,
        address tokenOut,
        uint256 amountIn,
        uint24 fee,
        address router,
        uint256 slippageToleranceBps
    ) internal returns (uint256 amountOut) {
        // Get expected amount out from Uniswap V3 Quoter
        uint256 expectedAmountOut;
        try IUniswapV3Quoter(UNISWAP_V3_QUOTER).quoteExactInputSingle(
            tokenIn,
            tokenOut,
            fee,
            amountIn,
            0
        ) returns (uint256 quotedAmount) {
            expectedAmountOut = quotedAmount;
        } catch {
            // Fallback to minimal protection if quote fails
            expectedAmountOut = amountIn;
        }

        // Calculate minimum amount out with dynamic slippage
        uint256 minAmountOut = (expectedAmountOut * (BASIS_POINTS_DENOMINATOR - slippageToleranceBps)) / BASIS_POINTS_DENOMINATOR;

        IUniswapV3Router.ExactInputSingleParams memory params = IUniswapV3Router.ExactInputSingleParams({
            tokenIn: tokenIn,
            tokenOut: tokenOut,
            fee: fee,
            recipient: address(this),
            deadline: block.timestamp + 300, // 5 minutes deadline
            amountIn: amountIn,
            amountOutMinimum: minAmountOut, // Dynamic slippage protection
            sqrtPriceLimitX96: 0
        });

        amountOut = IUniswapV3Router(router).exactInputSingle(params);
        return amountOut;
    }

    /**
     * @dev Execute Curve trade
     */
    function _executeCurveTrade(
        uint256 amountIn,
        address[] memory path,
        address pool,
        uint256 slippageToleranceBps
    ) internal returns (uint256) {
        require(path.length == 2, "Curve only supports direct swaps");

        // Get token indices in the Curve pool
        (int128 i, int128 j) = _getCurveTokenIndices(pool, path[0], path[1]);
        require(i >= 0 && j >= 0, "E9"); // Tokens not found in Curve pool

        // Get expected amount out
        uint256 expectedAmountOut = ICurvePool(pool).get_dy(i, j, amountIn);

        // Calculate minimum amount out with slippage protection
        uint256 minAmountOut = (expectedAmountOut * (BASIS_POINTS_DENOMINATOR - slippageToleranceBps)) / BASIS_POINTS_DENOMINATOR;

        // Execute the trade
        return ICurvePool(pool).exchange(i, j, amountIn, minAmountOut);
    }

    /**
     * @dev Get token indices in Curve pool
     */
    function _getCurveTokenIndices(
        address pool,
        address tokenA,
        address tokenB
    ) internal view returns (int128 i, int128 j) {
        i = -1;
        j = -1;

        // Check up to 8 tokens (most Curve pools have 2-4 tokens)
        for (uint256 idx = 0; idx < 8; idx++) {
            try ICurvePool(pool).coins(idx) returns (address token) {
                if (token == tokenA) {
                    i = int128(int256(idx));
                } else if (token == tokenB) {
                    j = int128(int256(idx));
                }

                // Break early if both tokens found
                if (i >= 0 && j >= 0) {
                    break;
                }
            } catch {
                // End of tokens array
                break;
            }
        }
    }

    /**
     * @dev Execute Balancer V2 trade (Production-ready implementation)
     */
    function _executeBalancerV2Trade(
        uint256 amountIn,
        address[] memory path,
        address vault,
        uint256 slippageToleranceBps
    ) internal returns (uint256) {
        require(path.length == 2, "E11"); // Balancer V2 only supports direct swaps
        require(amountIn > 0, "E12"); // Invalid amount

        // Get pool ID for the token pair
        bytes32 poolId = _getBalancerPoolId(path[0], path[1]);
        require(poolId != bytes32(0), "E10"); // Pool not found

        // Verify pool exists and is valid (only if using on-chain registry)
        require(balancerPoolExists[poolId], "E13"); // Pool not registered

        // Validate tokens are in the pool
        (address[] memory poolTokens, uint256[] memory balances,) = IBalancerV2Vault(vault).getPoolTokens(poolId);
        require(_validateTokensInPool(path[0], path[1], poolTokens), "E14"); // Tokens not in pool

        // Check pool has sufficient liquidity
        uint256 tokenInIndex = _getTokenIndex(path[0], poolTokens);
        uint256 tokenOutIndex = _getTokenIndex(path[1], poolTokens);
        require(balances[tokenOutIndex] > 0, "E15"); // Insufficient liquidity

        // Create single swap struct with optimized parameters
        IBalancerV2Vault.SingleSwap memory singleSwap = IBalancerV2Vault.SingleSwap({
            poolId: poolId,
            kind: IBalancerV2Vault.SwapKind.GIVEN_IN,
            assetIn: path[0],
            assetOut: path[1],
            amount: amountIn,
            userData: "" // Empty for standard swaps
        });

        // Create fund management struct for this contract
        IBalancerV2Vault.FundManagement memory funds = IBalancerV2Vault.FundManagement({
            sender: address(this),
            fromInternalBalance: false,
            recipient: payable(address(this)),
            toInternalBalance: false
        });

        // Get accurate expected amount out using Balancer's querySwap
        uint256 expectedAmountOut;
        try IBalancerV2Vault(vault).querySwap(singleSwap, funds) returns (uint256 queryResult) {
            expectedAmountOut = queryResult;
        } catch {
            // Fallback to conservative estimation if querySwap fails
            expectedAmountOut = _calculateBalancerFallbackOutput(
                amountIn,
                balances[tokenInIndex],
                balances[tokenOutIndex],
                poolId
            );
        }

        // Apply slippage protection to the accurate expected output
        uint256 minAmountOut = (expectedAmountOut * (BASIS_POINTS_DENOMINATOR - slippageToleranceBps)) / BASIS_POINTS_DENOMINATOR;

        // Ensure minimum output is reasonable
        require(minAmountOut > 0, "E16"); // Minimum output too low

        // Execute the swap with deadline protection
        uint256 deadline = block.timestamp + 300; // 5 minute deadline

        try IBalancerV2Vault(vault).swap(singleSwap, funds, minAmountOut, deadline) returns (uint256 amountOut) {
            // Verify the swap was successful
            require(amountOut >= minAmountOut, "E17"); // Slippage exceeded
            require(amountOut > 0, "E18"); // Invalid output amount

            return amountOut;
        } catch {
            revert("E19"); // Balancer swap failed
        }
    }

    /**
     * @dev Get Balancer pool ID for token pair (Production-ready implementation)
     */
    function _getBalancerPoolId(address tokenA, address tokenB) internal view returns (bytes32) {
        // Use the pre-initialized mapping for O(1) lookup
        bytes32 poolId = balancerPoolIds[tokenA][tokenB];

        // If direct lookup fails, try reverse order
        if (poolId == bytes32(0)) {
            poolId = balancerPoolIds[tokenB][tokenA];
        }

        // Verify the pool exists and is registered
        if (poolId != bytes32(0) && balancerPoolExists[poolId]) {
            return poolId;
        }

        return bytes32(0); // Pool not found
    }

    /**
     * @dev Execute Balancer V2 trade with direct pool ID (Gas-optimized version)
     * This version accepts pool ID directly from off-chain to save gas
     */
    function _executeBalancerV2TradeOptimized(
        uint256 amountIn,
        address tokenIn,
        address tokenOut,
        bytes32 poolId,
        address vault,
        uint256 slippageToleranceBps
    ) internal returns (uint256) {
        require(amountIn > 0, "E12"); // Invalid amount
        require(poolId != bytes32(0), "E10"); // Pool not found

        // Create single swap struct with provided pool ID
        IBalancerV2Vault.SingleSwap memory singleSwap = IBalancerV2Vault.SingleSwap({
            poolId: poolId,
            kind: IBalancerV2Vault.SwapKind.GIVEN_IN,
            assetIn: tokenIn,
            assetOut: tokenOut,
            amount: amountIn,
            userData: ""
        });

        // Create fund management struct
        IBalancerV2Vault.FundManagement memory funds = IBalancerV2Vault.FundManagement({
            sender: address(this),
            fromInternalBalance: false,
            recipient: payable(address(this)),
            toInternalBalance: false
        });

        // Get accurate expected amount out using querySwap
        uint256 expectedAmountOut;
        try IBalancerV2Vault(vault).querySwap(singleSwap, funds) returns (uint256 queryResult) {
            expectedAmountOut = queryResult;
        } catch {
            revert("E21"); // Balancer querySwap failed
        }

        // Apply slippage protection
        uint256 minAmountOut = (expectedAmountOut * (BASIS_POINTS_DENOMINATOR - slippageToleranceBps)) / BASIS_POINTS_DENOMINATOR;
        require(minAmountOut > 0, "E16"); // Minimum output too low

        // Execute the swap with deadline protection
        uint256 deadline = block.timestamp + 300; // 5 minute deadline

        try IBalancerV2Vault(vault).swap(singleSwap, funds, minAmountOut, deadline) returns (uint256 amountOut) {
            require(amountOut >= minAmountOut, "E17"); // Slippage exceeded
            require(amountOut > 0, "E18"); // Invalid output amount
            return amountOut;
        } catch {
            revert("E19"); // Balancer swap failed
        }
    }

    /**
     * @dev Fallback calculation for Balancer V2 swap (only used when querySwap fails)
     * WARNING: This is a simplified estimation and should only be used as fallback
     */
    function _calculateBalancerFallbackOutput(
        uint256 amountIn,
        uint256 balanceIn,
        uint256 balanceOut,
        bytes32 poolId
    ) internal view returns (uint256) {
        // Get pool fee
        uint256 feePercentage = balancerPoolFees[poolId];

        // Apply fee to input amount
        uint256 amountInAfterFee = amountIn * (BASIS_POINTS_DENOMINATOR - feePercentage) / BASIS_POINTS_DENOMINATOR;

        // FALLBACK ONLY: Simplified constant product formula
        // This is NOT accurate for Balancer pools but serves as emergency fallback
        // Real Balancer pools use complex weighted formulas or StableSwap invariants
        uint256 numerator = amountInAfterFee * balanceOut;
        uint256 denominator = balanceIn + amountInAfterFee;

        // Apply additional safety margin for fallback calculation
        uint256 result = numerator / denominator;
        return (result * 9500) / BASIS_POINTS_DENOMINATOR; // 5% safety margin
    }

    /**
     * @dev Validate that tokens exist in the Balancer pool
     */
    function _validateTokensInPool(
        address tokenA,
        address tokenB,
        address[] memory poolTokens
    ) internal pure returns (bool) {
        bool foundA = false;
        bool foundB = false;

        uint256 poolTokensLength = poolTokens.length;
        for (uint256 i = 0; i < poolTokensLength;) {
            if (poolTokens[i] == tokenA) {
                foundA = true;
            } else if (poolTokens[i] == tokenB) {
                foundB = true;
            }

            // Early exit if both tokens found
            if (foundA && foundB) {
                return true;
            }

            unchecked { ++i; }
        }

        return false;
    }

    /**
     * @dev Get token index in pool tokens array
     */
    function _getTokenIndex(address token, address[] memory poolTokens) internal pure returns (uint256) {
        uint256 poolTokensLength = poolTokens.length;
        for (uint256 i = 0; i < poolTokensLength;) {
            if (poolTokens[i] == token) {
                return i;
            }
            unchecked { ++i; }
        }
        revert("E20"); // Token not found in pool
    }

    /**
     * @dev Add a new Balancer pool (only owner)
     */
    function addBalancerPool(
        address tokenA,
        address tokenB,
        bytes32 poolId,
        uint256 feePercentage
    ) external onlyOwner {
        require(tokenA != address(0) && tokenB != address(0), "Invalid tokens");
        require(poolId != bytes32(0), "Invalid pool ID");
        require(feePercentage <= 1000, "Fee too high"); // Max 10%

        // Register the pool
        balancerPoolExists[poolId] = true;
        balancerPoolIds[tokenA][tokenB] = poolId;
        balancerPoolIds[tokenB][tokenA] = poolId;
        balancerPoolFees[poolId] = feePercentage;

        emit BalancerPoolAdded(tokenA, tokenB, poolId, feePercentage);
    }

    /**
     * @dev Remove a Balancer pool (only owner)
     */
    function removeBalancerPool(
        address tokenA,
        address tokenB,
        bytes32 poolId
    ) external onlyOwner {
        require(balancerPoolExists[poolId], "Pool does not exist");

        // Remove the pool
        balancerPoolExists[poolId] = false;
        delete balancerPoolIds[tokenA][tokenB];
        delete balancerPoolIds[tokenB][tokenA];
        delete balancerPoolFees[poolId];

        emit BalancerPoolRemoved(tokenA, tokenB, poolId);
    }

    /**
     * @dev Get Balancer pool info
     */
    function getBalancerPoolInfo(address tokenA, address tokenB) external view returns (
        bytes32 poolId,
        bool exists,
        uint256 feePercentage
    ) {
        poolId = _getBalancerPoolId(tokenA, tokenB);
        exists = balancerPoolExists[poolId];
        feePercentage = balancerPoolFees[poolId];
    }

    // Events for Balancer pool management
    event BalancerPoolAdded(address indexed tokenA, address indexed tokenB, bytes32 indexed poolId, uint256 feePercentage);
    event BalancerPoolRemoved(address indexed tokenA, address indexed tokenB, bytes32 indexed poolId);

    /**
     * @dev Check if a router is supported (backward compatibility)
     */
    function supportedRouters(address router) external view returns (bool) {
        return supportedRouterTypes[router] != DEX_TYPE.UNSUPPORTED;
    }

    /**
     * @dev Validate arbitrage parameters for security
     */
    function _validateArbitrageParams(ArbitrageParams memory params) internal view {
        // Validate paths
        require(params.buyPath.length >= 2, "Buy path too short");
        require(params.sellPath.length >= 2, "Sell path too short");
        require(params.buyPath.length <= 5, "Buy path too long"); // Max 5 hops
        require(params.sellPath.length <= 5, "Sell path too long"); // Max 5 hops

        // Validate path addresses (optimized loops with cached lengths)
        uint256 buyPathLength = params.buyPath.length;
        for (uint256 i = 0; i < buyPathLength; ) {
            require(params.buyPath[i] != address(0), "E1"); // Short error code
            unchecked { ++i; }
        }

        uint256 sellPathLength = params.sellPath.length;
        for (uint256 i = 0; i < sellPathLength; ) {
            require(params.sellPath[i] != address(0), "E2"); // Short error code
            unchecked { ++i; }
        }

        // Validate DEX addresses and types
        require(params.buyDex != address(0), "Invalid buyDex address");
        require(params.sellDex != address(0), "Invalid sellDex address");
        require(supportedRouterTypes[params.buyDex] != DEX_TYPE.UNSUPPORTED, "E6"); // Unsupported buy DEX
        require(supportedRouterTypes[params.sellDex] != DEX_TYPE.UNSUPPORTED, "E7"); // Unsupported sell DEX

        // Validate other parameters
        require(params.minProfit > 0, "Invalid minimum profit");
        require(params.slippageToleranceBps <= MAX_SLIPPAGE_BPS, "Slippage tolerance too high");
        require(params.maxGasCostWei > 0, "Invalid gas cost limit");

        // Validate V3 fees if provided - allow flexible fee arrays for mixed DEX scenarios
        uint256 v3FeesLength = params.v3Fees.length;
        if (v3FeesLength > 0) {
            // V3 fees are only needed for V3 paths, allow reasonable fee array sizes
            // Use cached lengths to avoid repeated SLOAD operations
            uint256 maxPossibleFees = (buyPathLength - 1) + (sellPathLength - 1);
            require(v3FeesLength <= maxPossibleFees, "E3"); // Short error code
            require(v3FeesLength >= 1, "E4"); // Short error code
        }

        // Validate arbitrage loop (last sell token should match first buy token)
        // Use cached length to avoid SLOAD
        require(
            params.sellPath[sellPathLength - 1] == params.buyPath[0],
            "E5" // Short error code
        );
    }

    /**
     * @dev Emergency pause function
     */
    function pause() external onlyOwner {
        _pause();
    }

    /**
     * @dev Unpause function
     */
    function unpause() external onlyOwner {
        _unpause();
    }

    /**
     * @dev On-chain profitability check (view function)
     * @param asset The flashloan asset
     * @param amount The flashloan amount
     * @param params The arbitrage parameters
     * @return profitable Whether the arbitrage is profitable
     * @return expectedProfit The expected profit in wei
     * @return gasEstimate The estimated gas cost
     */
    function checkProfitability(
        address asset,
        uint256 amount,
        bytes calldata params
    ) external returns (
        bool profitable,
        uint256 expectedProfit,
        uint256 gasEstimate
    ) {
        // Decode parameters
        ArbitrageParams memory arbParams = abi.decode(params, (ArbitrageParams));

        // Validate basic parameters
        if (asset != arbParams.buyPath[0] ||
        arbParams.buyPath.length < 2 ||
        arbParams.sellPath.length < 2 ||
            amount == 0) {
            return (false, 0, 0);
        }

        try this._simulateArbitrage(asset, amount, arbParams) returns (uint256 profit) {
            // Calculate total costs
            gasEstimate = arbParams.maxGasCostWei;
            uint256 flashloanFee = _calculateFlashloanFee(amount, arbParams.provider);
            uint256 totalCosts = gasEstimate + flashloanFee + arbParams.minProfit;

            // Check if profitable after all costs
            profitable = profit > totalCosts;
            expectedProfit = profitable ? profit - totalCosts : 0;

        } catch {
            // Simulation failed
            return (false, 0, 0);
        }
    }

    /**
     * @dev Internal function to simulate arbitrage (for view calls)
     */
    function _simulateArbitrage(
        address /* asset */,
        uint256 amount,
        ArbitrageParams memory params
    ) external returns (uint256 profit) {
        // This function should only be called by checkProfitability
        require(msg.sender == address(this), "Internal function only");

        // Simulate buy trade
        uint256 intermediateAmount = _simulateMultiHopTrade(
            amount,
            params.buyPath,
            params.buyDex,
            params.v3Fees
        );

        if (intermediateAmount == 0) {
            return 0;
        }

        // Simulate sell trade
        uint256 finalAmount = _simulateMultiHopTrade(
            intermediateAmount,
            params.sellPath,
            params.sellDex,
            params.v3Fees
        );

        if (finalAmount <= amount) {
            return 0;
        }

        return finalAmount - amount;
    }

    /**
     * @dev Simulate multi-hop trade without execution (simplified)
     */
    function _simulateMultiHopTrade(
        uint256 amountIn,
        address[] memory path,
        address router,
        uint24[] memory /* v3Fees */
    ) internal returns (uint256 amountOut) {
        if (path.length < 2) {
            return 0;
        }

        // Use DEX type system for cleaner logic
        DEX_TYPE dexType = supportedRouterTypes[router];

        if (dexType == DEX_TYPE.V2) {
            // Simulate V2-style trade (works for all V2 forks)
            try IUniswapV2Router(router).getAmountsOut(amountIn, path) returns (uint[] memory amounts) {
                return amounts[amounts.length - 1];
            } catch {
                return 0;
            }
        } else if (dexType == DEX_TYPE.CURVE) {
            // Simulate Curve trade
            if (path.length == 2) {
                (int128 i, int128 j) = _getCurveTokenIndices(router, path[0], path[1]);
                if (i >= 0 && j >= 0) {
                    try ICurvePool(router).get_dy(i, j, amountIn) returns (uint256 dy) {
                        return dy;
                    } catch {
                        return 0;
                    }
                }
            }
            return 0;
        } else if (dexType == DEX_TYPE.BALANCER_V2) {
            // Balancer V2 simulation - use accurate querySwap
            if (path.length == 2) {
                bytes32 poolId = _getBalancerPoolId(path[0], path[1]);
                if (poolId != bytes32(0) && balancerPoolExists[poolId]) {
                    // Create SingleSwap struct for simulation
                    IBalancerV2Vault.SingleSwap memory singleSwap = IBalancerV2Vault.SingleSwap({
                        poolId: poolId,
                        kind: IBalancerV2Vault.SwapKind.GIVEN_IN,
                        assetIn: path[0],
                        assetOut: path[1],
                        amount: amountIn,
                        userData: ""
                    });

                    // Create fund management struct for simulation
                    IBalancerV2Vault.FundManagement memory funds = IBalancerV2Vault.FundManagement({
                        sender: address(this),
                        fromInternalBalance: false,
                        recipient: payable(address(this)),
                        toInternalBalance: false
                    });

                    // Use Balancer's accurate querySwap for simulation
                    try IBalancerV2Vault(router).querySwap(singleSwap, funds) returns (uint256 amountOut) {
                        return amountOut;
                    } catch {
                        // Fallback to conservative estimate if querySwap fails
                        uint256 feePercentage = balancerPoolFees[poolId];
                        uint256 afterFee = (amountIn * (BASIS_POINTS_DENOMINATOR - feePercentage)) / BASIS_POINTS_DENOMINATOR;
                        return (afterFee * 9500) / BASIS_POINTS_DENOMINATOR; // 5% safety margin
                    }
                }
            }
            return 0;
        } else {
            // For V3 and other routers, use simplified estimation
            // This is a conservative estimate - actual implementation would use proper quoters
            uint256 estimatedOutput = amountIn;

            // Apply estimated slippage per hop (0.3% fee + 0.1% slippage per hop)
            // Cache path length and use unchecked arithmetic for gas optimization
            uint256 pathLength = path.length;
            for (uint256 i = 0; i < pathLength - 1; ) {
                estimatedOutput = (estimatedOutput * (BASIS_POINTS_DENOMINATOR - ESTIMATED_LOSS_PER_HOP_BPS)) / BASIS_POINTS_DENOMINATOR;
                unchecked { ++i; }
            }

            return estimatedOutput;
        }
    }

    /**
     * @dev Calculate flashloan fee based on provider
     */
    function _calculateFlashloanFee(uint256 amount, FlashloanProvider provider) internal pure returns (uint256) {
        if (provider == FlashloanProvider.AAVE) {
            return (amount * AAVE_FLASHLOAN_FEE_BPS) / BASIS_POINTS_DENOMINATOR;
        } else {
            return 0; // Balancer has no fees
        }
    }

    /**
     * @dev Safely get V3 quote without causing reverts in static calls
     */
    function _getV3QuoteSafe(
        address tokenIn,
        address tokenOut,
        uint24 fee,
        uint256 amountIn
    ) internal view returns (uint256) {
        // Cache state variable to reduce SLOAD
        address quoterAddress = UNISWAP_V3_QUOTER;

        // Use a low-level call to avoid reverts in static call context
        bytes memory data = abi.encodeWithSelector(
            IUniswapV3Quoter.quoteExactInputSingle.selector,
            tokenIn,
            tokenOut,
            fee,
            amountIn,
            0
        );

        (bool success, bytes memory result) = quoterAddress.staticcall(data);

        if (success && result.length >= 32) {
            // Decode the result
            uint256 quotedAmount = abi.decode(result, (uint256));

            // Sanity check: quoted amount should be reasonable (avoid manipulation)
            if (quotedAmount > 0 && quotedAmount <= amountIn * 2) {
                return quotedAmount;
            }
        }

        // Fallback: use conservative estimate (90% of input for safety)
        return (amountIn * 9000) / BASIS_POINTS_DENOMINATOR;
    }

    /**
     * @dev Withdraw profits with security checks
     */
    function withdrawProfits(address token, uint256 amount) external onlyOwner whenNotPaused {
        if (token == address(0)) {
            payable(owner()).transfer(amount);
        } else {
            IERC20(token).transfer(owner(), amount);
        }
    }

    /**
     * @dev Emergency function to withdraw all tokens (works even when paused)
     */
    function emergencyWithdraw(address token) external onlyOwner {
        if (token == address(0)) {
            payable(owner()).transfer(address(this).balance);
        } else {
            IERC20 tokenContract = IERC20(token);
            tokenContract.transfer(owner(), tokenContract.balanceOf(address(this)));
        }
    }

    // Allow contract to receive ETH
    receive() external payable {}
}
