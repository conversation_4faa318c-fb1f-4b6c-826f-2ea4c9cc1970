// SPDX-License-Identifier: MIT
pragma solidity ^0.8.19;

// Official Balancer V2 interfaces
import "@balancer-labs/v2-interfaces/contracts/vault/IVault.sol";
import "@balancer-labs/v2-interfaces/contracts/vault/IFlashLoanRecipient.sol";
import "@balancer-labs/v2-interfaces/contracts/solidity-utils/openzeppelin/IERC20.sol";
import "@openzeppelin/contracts/access/Ownable.sol";

interface IUniswapV2Router {
    function swapExactTokensForTokens(
        uint amountIn,
        uint amountOutMin,
        address[] calldata path,
        address to,
        uint deadline
    ) external returns (uint[] memory amounts);
    
    function getAmountsOut(uint amountIn, address[] calldata path)
        external view returns (uint[] memory amounts);
}

interface IUniswapV3Router {
    struct ExactInputSingleParams {
        address tokenIn;
        address tokenOut;
        uint24 fee;
        address recipient;
        uint256 deadline;
        uint256 amountIn;
        uint256 amountOutMinimum;
        uint160 sqrtPriceLimitX96;
    }

    function exactInputSingle(ExactInputSingleParams calldata params)
        external payable returns (uint256 amountOut);
}

/**
 * @title BalancerFlashloanArbitrage
 * @dev Official Balancer V2 flashloan implementation for MEV arbitrage
 * Based on: https://docs-v2.balancer.fi/reference/contracts/flash-loans.html
 */
contract BalancerFlashloanArbitrage is IFlashLoanRecipient, Ownable {
    
    // Balancer V2 Vault - same address on all networks
    IVault private constant VAULT = IVault(0xBA12222222228d8Ba445958a75a0704d566BF2C8);
    
    // Network-specific router addresses
    address public immutable UNISWAP_V2_ROUTER;
    address public immutable UNISWAP_V3_ROUTER;
    uint256 public immutable CHAIN_ID;
    
    struct ArbitrageParams {
        address tokenA;
        address tokenB;
        address buyDex;
        address sellDex;
        uint24 v3Fee;
        uint256 minProfit;
    }
    
    event FlashloanExecuted(
        address indexed asset,
        uint256 amount,
        uint256 profit
    );
    
    event ArbitrageCompleted(
        address indexed tokenA,
        address indexed tokenB,
        uint256 profit
    );

    constructor() Ownable(msg.sender) {
        CHAIN_ID = block.chainid;
        
        // Set router addresses based on chain ID
        (UNISWAP_V2_ROUTER, UNISWAP_V3_ROUTER) = _getRouterAddresses(block.chainid);
    }

    function _getRouterAddresses(uint256 chainId) private pure returns (address, address) {
        if (chainId == 1) {
            // Mainnet addresses
            return (
                0x7a250d5630B4cF539739dF2C5dAcb4c659F2488D, // Uniswap V2
                0xE592427A0AEce92De3Edee1F18E0157C05861564   // Uniswap V3
            );
        } else if (chainId == 11155111) {
            // Sepolia addresses
            return (
                0x86dcd3293C53Cf8EFd7303B57beb2a3F671dDE98, // Uniswap V2
                0x3bFA4769FB09eefC5a80d6E87c3B9C650f7Ae48E  // Uniswap V3
            );
        } else if (chainId == 31337) {
            // Hardhat local network - use mainnet addresses since it forks mainnet
            return (
                0x7a250d5630B4cF539739dF2C5dAcb4c659F2488D, // Uniswap V2
                0xE592427A0AEce92De3Edee1F18E0157C05861564   // Uniswap V3
            );
        } else {
            revert("Unsupported network");
        }
    }

    /**
     * @dev Execute flashloan arbitrage using Balancer V2 (0% fees!)
     * @param tokens Array of token addresses to flashloan
     * @param amounts Array of amounts to flashloan
     * @param userData Encoded arbitrage parameters
     */
    function executeFlashloanArbitrage(
        IERC20[] memory tokens,
        uint256[] memory amounts,
        bytes memory userData
    ) external onlyOwner {
        // Validate parameters
        require(tokens.length == amounts.length, "Array length mismatch");
        require(tokens.length > 0, "No tokens specified");
        
        ArbitrageParams memory arbParams = abi.decode(userData, (ArbitrageParams));
        require(address(tokens[0]) == arbParams.tokenA, "Token mismatch");
        require(amounts[0] > 0, "Invalid amount");
        
        // Execute Balancer flashloan - 0% fees!
        VAULT.flashLoan(this, tokens, amounts, userData);
    }

    /**
     * @dev Balancer flashloan callback - called by Vault
     * @param tokens Array of flashloaned tokens
     * @param amounts Array of flashloaned amounts  
     * @param feeAmounts Array of fees (always 0 for Balancer!)
     * @param userData Encoded arbitrage parameters
     */
    function receiveFlashLoan(
        IERC20[] memory tokens,
        uint256[] memory amounts,
        uint256[] memory feeAmounts, // Always 0 for Balancer!
        bytes memory userData
    ) external override {
        // Ensure this is called by the Balancer Vault
        require(msg.sender == address(VAULT), "Caller must be Balancer Vault");
        
        // Decode arbitrage parameters
        ArbitrageParams memory arbParams = abi.decode(userData, (ArbitrageParams));
        
        // Execute arbitrage with the flashloaned tokens
        uint256 profit = _executeArbitrage(address(tokens[0]), amounts[0], arbParams);
        
        // Calculate amount to repay (no fees for Balancer!)
        uint256 amountToRepay = amounts[0]; // No premium!
        
        // Ensure we have enough to repay
        require(
            tokens[0].balanceOf(address(this)) >= amountToRepay,
            "Insufficient balance to repay flashloan"
        );
        
        // Ensure we made profit
        require(profit > 0, "Arbitrage not profitable");
        
        // Note: Balancer Vault automatically pulls the repayment
        // No need to transfer tokens back manually
        
        emit FlashloanExecuted(
            address(tokens[0]),
            amounts[0],
            profit
        );
    }

    /**
     * @dev Execute arbitrage between two DEXs
     */
    function _executeArbitrage(
        address asset,
        uint256 amount,
        ArbitrageParams memory params
    ) internal returns (uint256 profit) {
        uint256 initialBalance = IERC20(asset).balanceOf(address(this));

        // Approve tokens for DEX routers before swapping
        IERC20(asset).approve(params.buyDex, amount);

        // Step 1: Buy tokenB on cheaper DEX
        uint256 tokenBAmount = _executeBuy(asset, amount, params);
        require(tokenBAmount > 0, "Buy swap failed");

        // Approve tokenB for sell DEX
        IERC20(params.tokenB).approve(params.sellDex, tokenBAmount);

        // Step 2: Sell tokenB on more expensive DEX
        uint256 finalAmount = _executeSell(params.tokenB, tokenBAmount, params);
        require(finalAmount > 0, "Sell swap failed");

        uint256 finalBalance = IERC20(asset).balanceOf(address(this));

        // Prevent underflow - check if we made profit
        if (finalBalance > initialBalance) {
            profit = finalBalance - initialBalance;
        } else {
            profit = 0; // No profit or loss
            revert("Arbitrage resulted in loss");
        }

        // Ensure minimum profit requirement
        require(profit >= params.minProfit, "Profit below minimum threshold");

        emit ArbitrageCompleted(params.tokenA, params.tokenB, profit);

        return profit;
    }

    /**
     * @dev Execute buy operation on DEX
     */
    function _executeBuy(
        address tokenIn,
        uint256 amountIn,
        ArbitrageParams memory params
    ) internal returns (uint256 amountOut) {
        require(amountIn > 0, "Invalid buy amount");
        require(IERC20(tokenIn).balanceOf(address(this)) >= amountIn, "Insufficient balance for buy");

        if (params.buyDex == UNISWAP_V2_ROUTER) {
            // Execute V2 swap
            address[] memory path = new address[](2);
            path[0] = tokenIn;
            path[1] = params.tokenB;

            // Calculate minimum output with 3% slippage protection
            uint256 minAmountOut = (amountIn * 97) / 100; // 3% slippage tolerance

            uint[] memory amounts = IUniswapV2Router(params.buyDex)
                .swapExactTokensForTokens(
                    amountIn,
                    minAmountOut, // Proper slippage protection
                    path,
                    address(this),
                    block.timestamp + 300
                );

            amountOut = amounts[1];
        } else {
            // Execute V3 swap with proper slippage protection
            uint256 minAmountOut = (amountIn * 95) / 100; // 5% slippage tolerance for V3

            IUniswapV3Router.ExactInputSingleParams memory swapParams =
                IUniswapV3Router.ExactInputSingleParams({
                    tokenIn: tokenIn,
                    tokenOut: params.tokenB,
                    fee: params.v3Fee,
                    recipient: address(this),
                    deadline: block.timestamp + 300,
                    amountIn: amountIn,
                    amountOutMinimum: minAmountOut, // Proper slippage protection
                    sqrtPriceLimitX96: 0
                });

            amountOut = IUniswapV3Router(params.buyDex).exactInputSingle(swapParams);
        }

        require(amountOut > 0, "Buy swap returned zero tokens");
        return amountOut;
    }

    /**
     * @dev Execute sell operation on DEX
     */
    function _executeSell(
        address tokenIn,
        uint256 amountIn,
        ArbitrageParams memory params
    ) internal returns (uint256 amountOut) {
        require(amountIn > 0, "Invalid sell amount");
        require(IERC20(tokenIn).balanceOf(address(this)) >= amountIn, "Insufficient balance for sell");

        if (params.sellDex == UNISWAP_V2_ROUTER) {
            // Execute V2 swap
            address[] memory path = new address[](2);
            path[0] = tokenIn;
            path[1] = params.tokenA;

            // Calculate minimum output with 3% slippage protection
            uint256 minAmountOut = (amountIn * 97) / 100; // 3% slippage tolerance

            uint[] memory amounts = IUniswapV2Router(params.sellDex)
                .swapExactTokensForTokens(
                    amountIn,
                    minAmountOut, // Proper slippage protection
                    path,
                    address(this),
                    block.timestamp + 300
                );

            amountOut = amounts[1];
        } else {
            // Execute V3 swap with proper slippage protection
            uint256 minAmountOut = (amountIn * 95) / 100; // 5% slippage tolerance for V3

            IUniswapV3Router.ExactInputSingleParams memory swapParams =
                IUniswapV3Router.ExactInputSingleParams({
                    tokenIn: tokenIn,
                    tokenOut: params.tokenA,
                    fee: params.v3Fee,
                    recipient: address(this),
                    deadline: block.timestamp + 300,
                    amountIn: amountIn,
                    amountOutMinimum: minAmountOut, // Proper slippage protection
                    sqrtPriceLimitX96: 0
                });

            amountOut = IUniswapV3Router(params.sellDex).exactInputSingle(swapParams);
        }

        require(amountOut > 0, "Sell swap returned zero tokens");
        return amountOut;
    }

    /**
     * @dev Withdraw profits to owner
     */
    function withdrawProfits(IERC20 token) external onlyOwner {
        uint256 balance = token.balanceOf(address(this));
        require(balance > 0, "No profits to withdraw");
        token.transfer(owner(), balance);
    }

    /**
     * @dev Emergency function to withdraw any stuck tokens
     */
    function emergencyWithdraw(IERC20 token, uint256 amount) external onlyOwner {
        token.transfer(owner(), amount);
    }

    /**
     * @dev Get the Balancer Vault address
     */
    function getVault() external pure returns (address) {
        return address(VAULT);
    }
}
