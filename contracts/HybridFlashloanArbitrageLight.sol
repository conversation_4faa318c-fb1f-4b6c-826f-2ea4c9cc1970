// SPDX-License-Identifier: MIT
pragma solidity ^0.8.19;

import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import "@openzeppelin/contracts/utils/ReentrancyGuard.sol";
import "@openzeppelin/contracts/utils/Pausable.sol";

// --- Minimale Interfaces (keine Änderungen hier) ---
interface IAavePool {
    function flashLoanSimple(address, address, uint256, bytes calldata, uint16) external;
}
interface IBalancerVault {
    function flashLoan(address, IERC20[] memory, uint256[] memory, bytes memory) external;
}
interface IUniswapV2Router {
    function swapExactTokensForTokens(uint, uint, address[] calldata, address, uint) external returns (uint[] memory);
    function getAmountsOut(uint, address[] calldata) external view returns (uint[] memory);
}
interface IUniswapV3Router {
    struct ExactInputSingleParams { address tokenIn; address tokenOut; uint24 fee; address recipient; uint256 deadline; uint256 amountIn; uint256 amountOutMinimum; uint160 sqrtPriceLimitX96; }
    function exactInputSingle(ExactInputSingleParams calldata) external payable returns (uint256);
}
interface IUniswapV3Quoter {
    function quoteExactInputSingle(address, address, uint24, uint256, uint160) external view returns (uint256);
}
interface ICurvePool {
    function exchange(int128, int128, uint256, uint256) external returns (uint256);
    function get_dy(int128, int128, uint256) external view returns (uint256);
    function coins(uint256) external view returns (address);
}
interface IBalancerV2Vault {
    struct SingleSwap { bytes32 poolId; SwapKind kind; address assetIn; address assetOut; uint256 amount; bytes userData; }
    struct FundManagement { address sender; bool fromInternalBalance; address payable recipient; bool toInternalBalance; }
    enum SwapKind { GIVEN_IN, GIVEN_OUT }
    function swap(SingleSwap memory, FundManagement memory, uint256, uint256) external returns (uint256);
    function querySwap(SingleSwap memory, FundManagement memory) external view returns (uint256);
}

/**
 * @title HybridFlashloanArbitrageLite
 * @dev Gas-optimierte Version. Speichert kein Wissen über Pools/DEXs on-chain.
 *      Alle Handelsdetails werden vom vertrauenswürdigen Off-Chain-Bot bereitgestellt.
 */
contract HybridFlashloanArbitrageLite is Ownable, ReentrancyGuard, Pausable {

    // --- State-Variablen (stark reduziert) ---
    IAavePool public immutable AAVE_POOL;
    IBalancerVault public immutable BALANCER_VAULT;
    // Die Router-Adressen werden weiterhin gespeichert, da sie für die Validierung des Flash-Loan-Callbacks wichtig sein können.
    IUniswapV3Quoter public immutable UNISWAP_V3_QUOTER;

    // --- Enums & Structs (neu strukturiert) ---
    enum FlashloanProvider { AAVE, BALANCER }
    enum DEX_TYPE { V2, V3, CURVE, BALANCER_V2 }

    struct TradeStep {
        address dex;                  // Adresse des Routers oder Pools
        DEX_TYPE dexType;             // Typ der DEX zur korrekten Funktionsauswahl
        address tokenIn;
        address tokenOut;
        uint256 slippageToleranceBps; // Slippage pro Schritt
        // Optionale, typspezifische Daten
        uint24 v3Fee;
        bytes32 balancerPoolId;
    }

    struct ArbitrageParams {
        TradeStep[] tradeSteps;       // Eine einzige, sequentielle Liste von Trades
        uint256 minProfit;
        FlashloanProvider provider;
        uint256 maxGasCostWei;
    }

    // --- Konstanten ---
    uint256 private constant BASIS_POINTS = 10000;
    uint256 private constant AAVE_FEE_BPS = 9;

    // --- Events ---
    event ArbitrageExecuted(
        FlashloanProvider indexed provider,
        address indexed asset,
        uint256 amount,
        uint256 netProfit
    );

    // --- Constructor (extrem günstig) ---
    constructor(address _aavePool, address _balancerVault, address _uniswapV3Quoter) Ownable(msg.sender) {
        AAVE_POOL = IAavePool(_aavePool);
        BALANCER_VAULT = IBalancerVault(_balancerVault);
        UNISWAP_V3_QUOTER = IUniswapV3Quoter(_uniswapV3Quoter);
    }

    // --- Flashloan-Logik ---
    function executeOptimalFlashloan(address asset, uint256 amount, bytes calldata params) external onlyOwner nonReentrant whenNotPaused {
        require(asset != address(0), "E01");
        require(amount > 0, "E02");
        require(params.length > 0, "E03");

        ArbitrageParams memory arbParams = abi.decode(params, (ArbitrageParams));

        require(arbParams.tradeSteps.length > 0, "E04");
        require(asset == arbParams.tradeSteps[0].tokenIn, "E05");

        if (arbParams.provider == FlashloanProvider.AAVE) {
            IAavePool(AAVE_POOL).flashLoanSimple(address(this), asset, amount, params, 0);
        } else {
            IERC20[] memory tokens = new IERC20[](1);
            tokens[0] = IERC20(asset);
            uint256[] memory amounts = new uint256[](1);
            amounts[0] = amount;
            IBalancerVault(BALANCER_VAULT).flashLoan(address(this), tokens, amounts, params);
        }
    }

    // Aave Flashloan Callback
    function executeOperation(address asset, uint256 amount, uint256 premium, address, bytes calldata params) external nonReentrant returns (bool) {
        require(msg.sender == address(AAVE_POOL), "C01");
        _performArbitrage(amount, premium, abi.decode(params, (ArbitrageParams)));
        IERC20(asset).approve(address(AAVE_POOL), amount + premium);
        return true;
    }

    // Balancer Flashloan Callback
    function receiveFlashLoan(IERC20[] memory, uint256[] memory amounts, uint256[] memory, bytes memory userData) external nonReentrant {
        require(msg.sender == address(BALANCER_VAULT), "C02");
        _performArbitrage(amounts[0], 0, abi.decode(userData, (ArbitrageParams)));
    }

    // --- Kernlogik ---
    function _performArbitrage(uint256 amount, uint256 premium, ArbitrageParams memory params) internal {
        uint256 currentAmount = amount;
        uint256 tradeStepsLength = params.tradeSteps.length;

        for (uint i = 0; i < tradeStepsLength; ) {
            currentAmount = _executeSingleTrade(currentAmount, params.tradeSteps[i]);
            unchecked { ++i; }
        }

        require(currentAmount > amount, "L01"); // Loss
        uint256 profit = currentAmount - amount;
        require(profit >= params.minProfit + params.maxGasCostWei + premium, "P01"); // Profit too low

        emit ArbitrageExecuted(params.provider, params.tradeSteps[0].tokenIn, amount, profit - premium);
    }

    function _executeSingleTrade(uint256 amountIn, TradeStep memory step) internal returns (uint256) {
        IERC20(step.tokenIn).approve(step.dex, amountIn);
        DEX_TYPE dexType = step.dexType;

        if (dexType == DEX_TYPE.V2) {
            address[] memory path = new address[](2);
            path[0] = step.tokenIn;
            path[1] = step.tokenOut;
            uint256 expectedOut;
            try IUniswapV2Router(step.dex).getAmountsOut(amountIn, path) returns (uint[] memory amounts) { expectedOut = amounts[1]; } catch { revert("Q01"); }
            uint256 minOut = (expectedOut * (BASIS_POINTS - step.slippageToleranceBps)) / BASIS_POINTS;
            uint[] memory amountsOut = IUniswapV2Router(step.dex).swapExactTokensForTokens(amountIn, minOut, path, address(this), block.timestamp);
            return amountsOut[1];
        }
        if (dexType == DEX_TYPE.V3) {
            uint256 expectedOut;
            try IUniswapV3Quoter(UNISWAP_V3_QUOTER).quoteExactInputSingle(step.tokenIn, step.tokenOut, step.v3Fee, amountIn, 0) returns (uint256 out) { expectedOut = out; } catch { revert("Q02"); }
            uint256 minOut = (expectedOut * (BASIS_POINTS - step.slippageToleranceBps)) / BASIS_POINTS;
            IUniswapV3Router.ExactInputSingleParams memory p = IUniswapV3Router.ExactInputSingleParams(step.tokenIn, step.tokenOut, step.v3Fee, address(this), block.timestamp, amountIn, minOut, 0);
            return IUniswapV3Router(step.dex).exactInputSingle(p);
        }
        if (dexType == DEX_TYPE.CURVE) {
            (int128 i, int128 j) = _getCurveTokenIndices(step.dex, step.tokenIn, step.tokenOut);
            require(i >= 0 && j >= 0, "T01");
            uint256 expectedOut;
            try ICurvePool(step.dex).get_dy(i, j, amountIn) returns (uint256 out) { expectedOut = out; } catch { revert("Q03"); }
            uint256 minOut = (expectedOut * (BASIS_POINTS - step.slippageToleranceBps)) / BASIS_POINTS;
            return ICurvePool(step.dex).exchange(i, j, amountIn, minOut);
        }
        if (dexType == DEX_TYPE.BALANCER_V2) {
            IBalancerV2Vault.SingleSwap memory swap = IBalancerV2Vault.SingleSwap(step.balancerPoolId, IBalancerV2Vault.SwapKind.GIVEN_IN, step.tokenIn, step.tokenOut, amountIn, "");
            IBalancerV2Vault.FundManagement memory funds = IBalancerV2Vault.FundManagement(address(this), false, payable(address(this)), false);
            uint256 expectedOut;
            try IBalancerV2Vault(step.dex).querySwap(swap, funds) returns (uint256 out) { expectedOut = out; } catch { revert("Q04"); }
            uint256 minOut = (expectedOut * (BASIS_POINTS - step.slippageToleranceBps)) / BASIS_POINTS;
            return IBalancerV2Vault(step.dex).swap(swap, funds, minOut, block.timestamp);
        }
        revert("T02");
    }

    // --- Hilfsfunktionen ---
    function _getCurveTokenIndices(address pool, address tokenA, address tokenB) internal view returns (int128 i, int128 j) {
        i = -1; j = -1;
        for (uint k = 0; k < 8; ) {
            try ICurvePool(pool).coins(k) returns (address token) {
                if (token == address(0)) break;
                if (token == tokenA) i = int128(int256(k));
                if (token == tokenB) j = int128(int256(k));
                if (i >= 0 && j >= 0) break;
            } catch { break; }
            unchecked { ++k; }
        }
    }

    // --- Owner-Funktionen ---
    function pause() external onlyOwner { _pause(); }
    function unpause() external onlyOwner { _unpause(); }
    function withdraw(address token) external onlyOwner {
        if (token == address(0)) {
            payable(owner()).transfer(address(this).balance);
        } else {
            IERC20(token).transfer(owner(), IERC20(token).balanceOf(address(this)));
        }
    }
    receive() external payable {}
}
