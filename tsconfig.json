{"compilerOptions": {"module": "commonjs", "declaration": true, "removeComments": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "target": "ES2020", "sourceMap": true, "outDir": "./dist", "baseUrl": "./", "incremental": true, "skipLibCheck": true, "strictNullChecks": false, "noImplicitAny": false, "strictBindCallApply": false, "forceConsistentCasingInFileNames": false, "noFallthroughCasesInSwitch": false, "paths": {"@/*": ["src/*"], "@config/*": ["src/config/*"], "@blockchain/*": ["src/blockchain/*"], "@contracts/*": ["src/contracts/*"], "@arbitrage/*": ["src/arbitrage/*"], "@bundles/*": ["src/bundles/*"], "@monitoring/*": ["src/monitoring/*"], "@graphql/*": ["src/graphql/*"], "@shared/*": ["src/shared/*"]}}, "include": ["src/**/*"], "exclude": ["old/**/*", "node_modules", "dist"]}