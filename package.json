{"name": "bo1", "version": "1.0.0", "description": "", "main": "dist/index.js", "scripts": {"// === BUILD & DEVELOPMENT ===": "", "build": "tsc", "clean": "rm -rf dist", "start": "node dist/index.js", "dev": "ts-node src/index.ts", "dev:watch": "WEB_DASHBOARD=true nodemon --exec ts-node src/index.ts", "dev:watch:web": "WEB_DASHBOARD=true nodemon --exec ts-node src/index.ts", "dev:simulate": "SIMULATION_MODE=true CHAIN_ID=1 SPLIT_SCREEN_DASHBOARD=true ts-node src/index.ts", "dev:multi-dex": "ENABLE_MULTI_DEX_ARBITRAGE=true SPLIT_SCREEN_DASHBOARD=true ts-node src/index.ts", "dev:hardhat": "cp .env.hardhat .env && npm run dev", "dev:local": "cp .env.local .env && npm run dev", "// === PRODUCTION DEPLOYMENT ===": "", "check:mainnet": "npx hardhat run scripts/check-deployment-readiness.js --network local-mainnet", "monitor:gas": "npx hardhat run scripts/monitor-gas-prices.js --network local-mainnet", "deploy:cost-optimized": "npx hardhat run scripts/deploy-hybrid-cost-optimized.js --network local-mainnet", "deploy:mainnet": "npx hardhat run scripts/deploy-hybrid-flashloan.js --network local-mainnet", "deploy:uniswap-v3-flash": "npx hardhat run scripts/deploy-uniswap-v3-flash-swap.js --network local-mainnet", "deploy:mainnet-production": "./scripts/deploy-mainnet-production.sh", "start:mainnet": "cp .env.mainnet-production .env && npm run dev", "// === FLASHLOAN ATTACKS ===": "", "flashloan-attack": "ts-node src/flashloan-attack-app.ts", "flashloan-attack:build": "tsc && node dist/flashloan-attack-app.js", "deploy:light-contract": "node scripts/deploy-light-contract.js", "// === HARDHAT TESTING ===": "", "compile": "npx hardhat compile", "hardhat:node": "npx hardhat node", "hardhat:fork:mainnet": "./scripts/start-hardhat-mainnet.sh", "hardhat:fork:sepolia": "./scripts/start-hardhat-sepolia.sh", "hardhat:fork:local": "./scripts/start-hardhat-local-fork.sh", "hardhat:setup": "npx hardhat run scripts/setup-hardhat-fork.js --network localhost", "hardhat:deploy": "npx hardhat run scripts/deploy-hybrid-flashloan.js --network localhost", "hardhat:fund": "npx hardhat run scripts/fund-hardhat-accounts.js --network localhost", "quickstart:hardhat": "./scripts/quick-start-hardhat.sh", "// === TESTING & VALIDATION ===": "", "test": "echo \"No tests yet\" && exit 0", "test:websocket": "node test/test-websocket-events.js", "test:web-dashboard": "npm run build && node test/test-web-dashboard.js", "test:mempool-filters": "npm run build && node test/test-mempool-filters.js", "test:hardhat": "npx hardhat run test/test-hardhat-integration.js --network localhost", "test:flashloan": "npx hardhat run test/test-flashloan-attack.js --network localhost", "test:dynamic-flashloan": "npx hardhat test test/dynamic-flashloan.test.js", "build:dynamic": "node scripts/build-and-test.js", "deploy:dynamic:sepolia": "npx hardhat run scripts/deploy-dynamic-flashloan.js --network sepolia", "deploy:dynamic:mainnet": "npx hardhat run scripts/deploy-dynamic-flashloan.js --network mainnet", "test:atomic": "npx hardhat run test/test-atomic-flashloan.js --network localhost", "test:uniswap-v3-config": "node test/test-uniswap-v3-config.js", "test:atomic-success": "npx hardhat run test/test-successful-atomic-flashloan.js --network localhost", "demo:atomic": "npx hardhat run scripts/demonstrate-atomic-mev.js --network localhost", "verify:production": "npx hardhat run scripts/final-verification.js --network localhost", "test:flashbots": "node test/test-flashbots-integration.js", "test:simulation": "node test/test-simulation-mode.js", "validate:apis": "node test/validate-flashloan-apis.js", "validate:curve": "node test/validate-curve-strategy.js", "test:config": "npx tsc && node test/test-dex-config.js", "deploy:simple-uniswap-v3": "npx hardhat run scripts/deploy-simple-uniswap-v3.js --network local-mainnet", "deploy:test-uniswap-v3": "npx hardhat run scripts/deploy-test-uniswap-v3.js --network local-mainnet", "deploy:minimal-test": "npx hardhat run scripts/deploy-minimal-test.js --network local-mainnet", "// === MEV STRATEGY TESTING ===": "", "test:diagnostic": "npm run compile && mocha test/integration/comprehensive-mev-diagnostic.ts --require ts-node/register --timeout 300000", "test:real-life": "npm run compile && mocha test/integration/real-life-opportunity-test.ts --require ts-node/register --timeout 600000", "test:full-analysis": "npm run test:diagnostic && npm run test:real-life", "diagnose": "node scripts/diagnose-mev-bot.js", "analyze:bundles": "ts-node scripts/analyze-bundle-performance.ts", "check:bundles": "ts-node scripts/check-bundle-stats.ts", "test:bundle-analyzer": "ts-node test/test-bundle-analyzer.ts", "test:web-dashboard-bundles": "ts-node test/test-web-dashboard-bundle-stats.ts", "test:web-dashboard-complete": "ts-node test/test-web-dashboard-complete.ts", "start:web-dashboard-demo": "ts-node scripts/start-web-dashboard-with-bundle-stats.ts", "test:aave-no-wait": "ts-node scripts/test-aave-no-wait.ts", "monitor:bundle-results": "ts-node scripts/monitor-bundle-results.ts", "test:bot-execution": "npm run compile && mocha test/integration/bot-execution-test.ts --require ts-node/register --timeout 300000", "optimize": "./scripts/apply-optimized-config.sh", "test:price-fix": "npm run compile && mocha test/integration/price-calculation-fix-test.ts --require ts-node/register --timeout 300000", "test:final": "npm run compile && mocha test/integration/final-verification-test.ts --require ts-node/register --timeout 600000", "test:confidence": "npm run compile && mocha test/unit/confidence-threshold-test.ts --require ts-node/register --timeout 60000", "generate:mev-opportunities": "npx hardhat run scripts/generate-mev-opportunities.js --network localhost", "generate:transactions": "npx hardhat run scripts/generate-test-transactions.js --network localhost", "test:ultimate-mev": "npx hardhat run test/test-ultimate-mev-strategies.js --network localhost", "test:sandwich": "npx hardhat run test/test-sandwich-attack.js --network localhost", "test:frontrunning": "npx hardhat run test/test-frontrunning-attack.js --network localhost", "test:all-strategies": "npm run generate:mev-opportunities && npm run test:ultimate-mev", "test:arbitrage-performance": "npm run build && node test/test-arbitrage-performance.js", "test:workers": "npm run build && node test/test-workers-only.js", "test:single-threaded": "npm run build && node test/test-bot-without-workers.js", "test:sendrawtransaction": "npx hardhat run test/test-sendrawtransaction.js --network localhost", "test:gas-validation": "npx hardhat run test/test-gas-validation.js --network localhost", "test:bundle-inclusion": "npm run build && npx hardhat run test/test-bundle-inclusion.js", "test:advanced-mev": "node test/test-advanced-mev-features.js", "// === SETUP & UTILITIES ===": "", "setup:sepolia": "node test/setup-sepolia.js", "setup:local": "./scripts/setup-local-node.sh"}, "dependencies": {"@flashbots/ethers-provider-bundle": "^1.0.0", "@flashbots/mev-share-client": "^0.7.13", "@types/blessed": "^0.1.25", "@types/express": "^5.0.3", "@uniswap/sdk-core": "^7.7.2", "@uniswap/v2-periphery": "^1.1.0-beta.0", "@uniswap/v2-sdk": "^4.15.2", "@uniswap/v3-core": "^1.0.1", "@uniswap/v3-periphery": "^1.4.4", "@uniswap/v3-sdk": "^3.25.2", "axios": "^1.10.0", "big.js": "^7.0.1", "blessed": "^0.1.81", "chalk": "^4.1.2", "ethers": "^6.7.1", "express": "^5.1.0", "node-fetch": "^3.3.2", "socket.io": "^4.8.1", "winston": "^3.17.0", "ws": "^8.18.2"}, "devDependencies": {"@aave/core-v3": "^1.19.3", "@balancer-labs/v2-interfaces": "^0.4.0", "@nomicfoundation/hardhat-chai-matchers": "^2.0.9", "@nomicfoundation/hardhat-ethers": "^3.0.9", "@nomicfoundation/hardhat-ignition": "^0.15.11", "@nomicfoundation/hardhat-ignition-ethers": "^0.15.12", "@nomicfoundation/hardhat-network-helpers": "^1.0.12", "@nomicfoundation/hardhat-toolbox": "^5.0.0", "@nomicfoundation/hardhat-verify": "^2.0.14", "@nomicfoundation/ignition-core": "^0.15.11", "@openzeppelin/contracts": "^5.3.0", "@typechain/ethers-v6": "^0.5.1", "@typechain/hardhat": "^9.1.0", "@types/chai": "^4.3.20", "@types/mocha": "^10.0.10", "@types/node": "^22.15.29", "@types/ws": "^8.18.1", "chai": "^4.5.0", "dotenv": "^16.5.0", "hardhat": "^2.24.3", "hardhat-gas-reporter": "^1.0.10", "nodemon": "^3.1.10", "solidity-coverage": "^0.8.16", "ts-node": "^10.9.2", "typechain": "^8.3.2", "typescript": "^5.5.3"}, "private": true}