{"name": "flashloan-arbitrage-bot", "version": "1.0.0", "description": "Production-ready NestJS flashloan arbitrage bot with MEV bundle execution", "main": "dist/main.js", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json"}, "dependencies": {"@apollo/server": "^5.0.0", "@flashbots/ethers-provider-bundle": "^1.0.0", "@nestjs/apollo": "^13.1.0", "@nestjs/common": "^11.1.6", "@nestjs/config": "^4.0.2", "@nestjs/core": "^11.1.6", "@nestjs/event-emitter": "^3.0.1", "@nestjs/graphql": "^13.1.0", "@nestjs/platform-express": "^11.1.6", "@nestjs/platform-socket.io": "^11.1.6", "@nestjs/schedule": "^6.0.0", "@nestjs/websockets": "^11.1.6", "apollo-server-core": "^3.13.0", "apollo-server-express": "^3.13.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "ethers": "^6.15.0", "graphql": "^16.11.0", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.2", "socket.io": "^4.8.1", "ws": "^8.18.3"}, "devDependencies": {"@nestjs/cli": "^11.0.10", "@nestjs/testing": "^11.1.6", "@types/express": "^5.0.3", "@types/jest": "^30.0.0", "@types/node": "^24.3.0", "@types/ws": "^8.18.1", "jest": "^30.0.5", "nodemon": "^3.1.10", "ts-jest": "^29.4.1", "ts-node": "^10.9.2", "typescript": "^5.5.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}, "private": true}