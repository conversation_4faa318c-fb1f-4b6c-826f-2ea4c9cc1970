/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */

import { ethers } from "ethers";
import {
  DeployContractOptions,
  FactoryOptions,
  HardhatEthersHelpers as HardhatEthersHelpersBase,
} from "@nomicfoundation/hardhat-ethers/types";

import * as Contracts from ".";

declare module "hardhat/types/runtime" {
  interface HardhatEthersHelpers extends HardhatEthersHelpersBase {
    getContractFactory(
      name: "FlashLoanSimpleReceiverBase",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.FlashLoanSimpleReceiverBase__factory>;
    getContractFactory(
      name: "IFlashLoanSimpleReceiver",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.IFlashLoanSimpleReceiver__factory>;
    getContractFactory(
      name: "IPool",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.IPool__factory>;
    getContractFactory(
      name: "IPoolAddressesProvider",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.IPoolAddressesProvider__factory>;
    getContractFactory(
      name: "IAuthentication",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.IAuthentication__factory>;
    getContractFactory(
      name: "ISignaturesValidator",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.ISignaturesValidator__factory>;
    getContractFactory(
      name: "ITemporarilyPausable",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.ITemporarilyPausable__factory>;
    getContractFactory(
      name: "IWETH",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.IWETH__factory>;
    getContractFactory(
      name: "IERC20",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.IERC20__factory>;
    getContractFactory(
      name: "IAuthorizer",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.IAuthorizer__factory>;
    getContractFactory(
      name: "IFlashLoanRecipient",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.IFlashLoanRecipient__factory>;
    getContractFactory(
      name: "IProtocolFeesCollector",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.IProtocolFeesCollector__factory>;
    getContractFactory(
      name: "IVault",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.IVault__factory>;
    getContractFactory(
      name: "Ownable",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.Ownable__factory>;
    getContractFactory(
      name: "IERC1155Errors",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.IERC1155Errors__factory>;
    getContractFactory(
      name: "IERC20Errors",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.IERC20Errors__factory>;
    getContractFactory(
      name: "IERC721Errors",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.IERC721Errors__factory>;
    getContractFactory(
      name: "ERC20",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.ERC20__factory>;
    getContractFactory(
      name: "IERC20Metadata",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.IERC20Metadata__factory>;
    getContractFactory(
      name: "IERC20",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.IERC20__factory>;
    getContractFactory(
      name: "Pausable",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.Pausable__factory>;
    getContractFactory(
      name: "ReentrancyGuard",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.ReentrancyGuard__factory>;
    getContractFactory(
      name: "IUniswapV2Router01",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.IUniswapV2Router01__factory>;
    getContractFactory(
      name: "IUniswapV2Router02",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.IUniswapV2Router02__factory>;
    getContractFactory(
      name: "IUniswapV3FlashCallback",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.IUniswapV3FlashCallback__factory>;
    getContractFactory(
      name: "IUniswapV3SwapCallback",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.IUniswapV3SwapCallback__factory>;
    getContractFactory(
      name: "IUniswapV3Pool",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.IUniswapV3Pool__factory>;
    getContractFactory(
      name: "IUniswapV3PoolActions",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.IUniswapV3PoolActions__factory>;
    getContractFactory(
      name: "IUniswapV3PoolDerivedState",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.IUniswapV3PoolDerivedState__factory>;
    getContractFactory(
      name: "IUniswapV3PoolEvents",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.IUniswapV3PoolEvents__factory>;
    getContractFactory(
      name: "IUniswapV3PoolImmutables",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.IUniswapV3PoolImmutables__factory>;
    getContractFactory(
      name: "IUniswapV3PoolOwnerActions",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.IUniswapV3PoolOwnerActions__factory>;
    getContractFactory(
      name: "IUniswapV3PoolState",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.IUniswapV3PoolState__factory>;
    getContractFactory(
      name: "ISwapRouter",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.ISwapRouter__factory>;
    getContractFactory(
      name: "BalancerFlashloanArbitrage",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.BalancerFlashloanArbitrage__factory>;
    getContractFactory(
      name: "IUniswapV2Router",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.IUniswapV2Router__factory>;
    getContractFactory(
      name: "IUniswapV3Router",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.IUniswapV3Router__factory>;
    getContractFactory(
      name: "DirectArbitrageContract",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.DirectArbitrageContract__factory>;
    getContractFactory(
      name: "DynamicFlashloanArbitrage",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.DynamicFlashloanArbitrage__factory>;
    getContractFactory(
      name: "FlashloanArbitrage",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.FlashloanArbitrage__factory>;
    getContractFactory(
      name: "HybridFlashloanArbitrage",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.HybridFlashloanArbitrage__factory>;
    getContractFactory(
      name: "IUniswapV2Router",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.IUniswapV2Router__factory>;
    getContractFactory(
      name: "IUniswapV3Router",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.IUniswapV3Router__factory>;
    getContractFactory(
      name: "HybridFlashloanArbitrageFixed",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.HybridFlashloanArbitrageFixed__factory>;
    getContractFactory(
      name: "IAavePool",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.IAavePool__factory>;
    getContractFactory(
      name: "IBalancerV2Pool",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.IBalancerV2Pool__factory>;
    getContractFactory(
      name: "IBalancerV2PoolRegistry",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.IBalancerV2PoolRegistry__factory>;
    getContractFactory(
      name: "IBalancerV2Vault",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.IBalancerV2Vault__factory>;
    getContractFactory(
      name: "IBalancerVault",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.IBalancerVault__factory>;
    getContractFactory(
      name: "ICurvePool",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.ICurvePool__factory>;
    getContractFactory(
      name: "IUniswapV2Router",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.IUniswapV2Router__factory>;
    getContractFactory(
      name: "IUniswapV3Quoter",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.IUniswapV3Quoter__factory>;
    getContractFactory(
      name: "IUniswapV3QuoterV2",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.IUniswapV3QuoterV2__factory>;
    getContractFactory(
      name: "IUniswapV3Router",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.IUniswapV3Router__factory>;
    getContractFactory(
      name: "HybridFlashloanArbitrageLite",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.HybridFlashloanArbitrageLite__factory>;
    getContractFactory(
      name: "IAavePool",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.IAavePool__factory>;
    getContractFactory(
      name: "IBalancerV2Vault",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.IBalancerV2Vault__factory>;
    getContractFactory(
      name: "IBalancerVault",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.IBalancerVault__factory>;
    getContractFactory(
      name: "ICurvePool",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.ICurvePool__factory>;
    getContractFactory(
      name: "IUniswapV2Router",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.IUniswapV2Router__factory>;
    getContractFactory(
      name: "IUniswapV3Quoter",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.IUniswapV3Quoter__factory>;
    getContractFactory(
      name: "IUniswapV3Router",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.IUniswapV3Router__factory>;
    getContractFactory(
      name: "MinimalFlashloanTest",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.MinimalFlashloanTest__factory>;
    getContractFactory(
      name: "MinimalTest",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.MinimalTest__factory>;
    getContractFactory(
      name: "MockAavePool",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.MockAavePool__factory>;
    getContractFactory(
      name: "MockDEXRouter",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.MockDEXRouter__factory>;
    getContractFactory(
      name: "MockERC20",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.MockERC20__factory>;
    getContractFactory(
      name: "MockPoolAddressesProvider",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.MockPoolAddressesProvider__factory>;
    getContractFactory(
      name: "SimpleEnumTest",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.SimpleEnumTest__factory>;
    getContractFactory(
      name: "SimpleFlashloanArbitrage",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.SimpleFlashloanArbitrage__factory>;
    getContractFactory(
      name: "IUniswapV3Factory",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.IUniswapV3Factory__factory>;
    getContractFactory(
      name: "IUniswapV3Pool",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.IUniswapV3Pool__factory>;
    getContractFactory(
      name: "IUniswapV3Router",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.IUniswapV3Router__factory>;
    getContractFactory(
      name: "SimpleUniswapV3FlashSwap",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.SimpleUniswapV3FlashSwap__factory>;
    getContractFactory(
      name: "IBalancerVault",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.IBalancerVault__factory>;
    getContractFactory(
      name: "TestFlashloanReceiver",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.TestFlashloanReceiver__factory>;
    getContractFactory(
      name: "IUniswapV3Factory",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.IUniswapV3Factory__factory>;
    getContractFactory(
      name: "TestUniswapV3",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.TestUniswapV3__factory>;
    getContractFactory(
      name: "IUniswapV3Factory",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.IUniswapV3Factory__factory>;
    getContractFactory(
      name: "IUniswapV3Pool",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.IUniswapV3Pool__factory>;
    getContractFactory(
      name: "IUniswapV3Router",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.IUniswapV3Router__factory>;
    getContractFactory(
      name: "UniswapV3FlashSwap",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.UniswapV3FlashSwap__factory>;
    getContractFactory(
      name: "WorkingFlashloanArbitrage",
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<Contracts.WorkingFlashloanArbitrage__factory>;

    getContractAt(
      name: "FlashLoanSimpleReceiverBase",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.FlashLoanSimpleReceiverBase>;
    getContractAt(
      name: "IFlashLoanSimpleReceiver",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.IFlashLoanSimpleReceiver>;
    getContractAt(
      name: "IPool",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.IPool>;
    getContractAt(
      name: "IPoolAddressesProvider",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.IPoolAddressesProvider>;
    getContractAt(
      name: "IAuthentication",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.IAuthentication>;
    getContractAt(
      name: "ISignaturesValidator",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.ISignaturesValidator>;
    getContractAt(
      name: "ITemporarilyPausable",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.ITemporarilyPausable>;
    getContractAt(
      name: "IWETH",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.IWETH>;
    getContractAt(
      name: "IERC20",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.IERC20>;
    getContractAt(
      name: "IAuthorizer",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.IAuthorizer>;
    getContractAt(
      name: "IFlashLoanRecipient",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.IFlashLoanRecipient>;
    getContractAt(
      name: "IProtocolFeesCollector",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.IProtocolFeesCollector>;
    getContractAt(
      name: "IVault",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.IVault>;
    getContractAt(
      name: "Ownable",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.Ownable>;
    getContractAt(
      name: "IERC1155Errors",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.IERC1155Errors>;
    getContractAt(
      name: "IERC20Errors",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.IERC20Errors>;
    getContractAt(
      name: "IERC721Errors",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.IERC721Errors>;
    getContractAt(
      name: "ERC20",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.ERC20>;
    getContractAt(
      name: "IERC20Metadata",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.IERC20Metadata>;
    getContractAt(
      name: "IERC20",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.IERC20>;
    getContractAt(
      name: "Pausable",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.Pausable>;
    getContractAt(
      name: "ReentrancyGuard",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.ReentrancyGuard>;
    getContractAt(
      name: "IUniswapV2Router01",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.IUniswapV2Router01>;
    getContractAt(
      name: "IUniswapV2Router02",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.IUniswapV2Router02>;
    getContractAt(
      name: "IUniswapV3FlashCallback",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.IUniswapV3FlashCallback>;
    getContractAt(
      name: "IUniswapV3SwapCallback",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.IUniswapV3SwapCallback>;
    getContractAt(
      name: "IUniswapV3Pool",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.IUniswapV3Pool>;
    getContractAt(
      name: "IUniswapV3PoolActions",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.IUniswapV3PoolActions>;
    getContractAt(
      name: "IUniswapV3PoolDerivedState",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.IUniswapV3PoolDerivedState>;
    getContractAt(
      name: "IUniswapV3PoolEvents",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.IUniswapV3PoolEvents>;
    getContractAt(
      name: "IUniswapV3PoolImmutables",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.IUniswapV3PoolImmutables>;
    getContractAt(
      name: "IUniswapV3PoolOwnerActions",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.IUniswapV3PoolOwnerActions>;
    getContractAt(
      name: "IUniswapV3PoolState",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.IUniswapV3PoolState>;
    getContractAt(
      name: "ISwapRouter",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.ISwapRouter>;
    getContractAt(
      name: "BalancerFlashloanArbitrage",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.BalancerFlashloanArbitrage>;
    getContractAt(
      name: "IUniswapV2Router",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.IUniswapV2Router>;
    getContractAt(
      name: "IUniswapV3Router",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.IUniswapV3Router>;
    getContractAt(
      name: "DirectArbitrageContract",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.DirectArbitrageContract>;
    getContractAt(
      name: "DynamicFlashloanArbitrage",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.DynamicFlashloanArbitrage>;
    getContractAt(
      name: "FlashloanArbitrage",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.FlashloanArbitrage>;
    getContractAt(
      name: "HybridFlashloanArbitrage",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.HybridFlashloanArbitrage>;
    getContractAt(
      name: "IUniswapV2Router",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.IUniswapV2Router>;
    getContractAt(
      name: "IUniswapV3Router",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.IUniswapV3Router>;
    getContractAt(
      name: "HybridFlashloanArbitrageFixed",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.HybridFlashloanArbitrageFixed>;
    getContractAt(
      name: "IAavePool",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.IAavePool>;
    getContractAt(
      name: "IBalancerV2Pool",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.IBalancerV2Pool>;
    getContractAt(
      name: "IBalancerV2PoolRegistry",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.IBalancerV2PoolRegistry>;
    getContractAt(
      name: "IBalancerV2Vault",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.IBalancerV2Vault>;
    getContractAt(
      name: "IBalancerVault",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.IBalancerVault>;
    getContractAt(
      name: "ICurvePool",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.ICurvePool>;
    getContractAt(
      name: "IUniswapV2Router",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.IUniswapV2Router>;
    getContractAt(
      name: "IUniswapV3Quoter",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.IUniswapV3Quoter>;
    getContractAt(
      name: "IUniswapV3QuoterV2",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.IUniswapV3QuoterV2>;
    getContractAt(
      name: "IUniswapV3Router",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.IUniswapV3Router>;
    getContractAt(
      name: "HybridFlashloanArbitrageLite",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.HybridFlashloanArbitrageLite>;
    getContractAt(
      name: "IAavePool",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.IAavePool>;
    getContractAt(
      name: "IBalancerV2Vault",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.IBalancerV2Vault>;
    getContractAt(
      name: "IBalancerVault",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.IBalancerVault>;
    getContractAt(
      name: "ICurvePool",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.ICurvePool>;
    getContractAt(
      name: "IUniswapV2Router",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.IUniswapV2Router>;
    getContractAt(
      name: "IUniswapV3Quoter",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.IUniswapV3Quoter>;
    getContractAt(
      name: "IUniswapV3Router",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.IUniswapV3Router>;
    getContractAt(
      name: "MinimalFlashloanTest",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.MinimalFlashloanTest>;
    getContractAt(
      name: "MinimalTest",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.MinimalTest>;
    getContractAt(
      name: "MockAavePool",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.MockAavePool>;
    getContractAt(
      name: "MockDEXRouter",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.MockDEXRouter>;
    getContractAt(
      name: "MockERC20",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.MockERC20>;
    getContractAt(
      name: "MockPoolAddressesProvider",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.MockPoolAddressesProvider>;
    getContractAt(
      name: "SimpleEnumTest",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.SimpleEnumTest>;
    getContractAt(
      name: "SimpleFlashloanArbitrage",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.SimpleFlashloanArbitrage>;
    getContractAt(
      name: "IUniswapV3Factory",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.IUniswapV3Factory>;
    getContractAt(
      name: "IUniswapV3Pool",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.IUniswapV3Pool>;
    getContractAt(
      name: "IUniswapV3Router",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.IUniswapV3Router>;
    getContractAt(
      name: "SimpleUniswapV3FlashSwap",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.SimpleUniswapV3FlashSwap>;
    getContractAt(
      name: "IBalancerVault",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.IBalancerVault>;
    getContractAt(
      name: "TestFlashloanReceiver",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.TestFlashloanReceiver>;
    getContractAt(
      name: "IUniswapV3Factory",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.IUniswapV3Factory>;
    getContractAt(
      name: "TestUniswapV3",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.TestUniswapV3>;
    getContractAt(
      name: "IUniswapV3Factory",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.IUniswapV3Factory>;
    getContractAt(
      name: "IUniswapV3Pool",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.IUniswapV3Pool>;
    getContractAt(
      name: "IUniswapV3Router",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.IUniswapV3Router>;
    getContractAt(
      name: "UniswapV3FlashSwap",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.UniswapV3FlashSwap>;
    getContractAt(
      name: "WorkingFlashloanArbitrage",
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<Contracts.WorkingFlashloanArbitrage>;

    deployContract(
      name: "FlashLoanSimpleReceiverBase",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.FlashLoanSimpleReceiverBase>;
    deployContract(
      name: "IFlashLoanSimpleReceiver",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IFlashLoanSimpleReceiver>;
    deployContract(
      name: "IPool",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IPool>;
    deployContract(
      name: "IPoolAddressesProvider",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IPoolAddressesProvider>;
    deployContract(
      name: "IAuthentication",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IAuthentication>;
    deployContract(
      name: "ISignaturesValidator",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.ISignaturesValidator>;
    deployContract(
      name: "ITemporarilyPausable",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.ITemporarilyPausable>;
    deployContract(
      name: "IWETH",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IWETH>;
    deployContract(
      name: "IERC20",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IERC20>;
    deployContract(
      name: "IAuthorizer",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IAuthorizer>;
    deployContract(
      name: "IFlashLoanRecipient",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IFlashLoanRecipient>;
    deployContract(
      name: "IProtocolFeesCollector",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IProtocolFeesCollector>;
    deployContract(
      name: "IVault",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IVault>;
    deployContract(
      name: "Ownable",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.Ownable>;
    deployContract(
      name: "IERC1155Errors",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IERC1155Errors>;
    deployContract(
      name: "IERC20Errors",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IERC20Errors>;
    deployContract(
      name: "IERC721Errors",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IERC721Errors>;
    deployContract(
      name: "ERC20",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.ERC20>;
    deployContract(
      name: "IERC20Metadata",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IERC20Metadata>;
    deployContract(
      name: "IERC20",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IERC20>;
    deployContract(
      name: "Pausable",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.Pausable>;
    deployContract(
      name: "ReentrancyGuard",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.ReentrancyGuard>;
    deployContract(
      name: "IUniswapV2Router01",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IUniswapV2Router01>;
    deployContract(
      name: "IUniswapV2Router02",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IUniswapV2Router02>;
    deployContract(
      name: "IUniswapV3FlashCallback",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IUniswapV3FlashCallback>;
    deployContract(
      name: "IUniswapV3SwapCallback",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IUniswapV3SwapCallback>;
    deployContract(
      name: "IUniswapV3Pool",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IUniswapV3Pool>;
    deployContract(
      name: "IUniswapV3PoolActions",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IUniswapV3PoolActions>;
    deployContract(
      name: "IUniswapV3PoolDerivedState",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IUniswapV3PoolDerivedState>;
    deployContract(
      name: "IUniswapV3PoolEvents",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IUniswapV3PoolEvents>;
    deployContract(
      name: "IUniswapV3PoolImmutables",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IUniswapV3PoolImmutables>;
    deployContract(
      name: "IUniswapV3PoolOwnerActions",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IUniswapV3PoolOwnerActions>;
    deployContract(
      name: "IUniswapV3PoolState",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IUniswapV3PoolState>;
    deployContract(
      name: "ISwapRouter",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.ISwapRouter>;
    deployContract(
      name: "BalancerFlashloanArbitrage",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.BalancerFlashloanArbitrage>;
    deployContract(
      name: "IUniswapV2Router",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IUniswapV2Router>;
    deployContract(
      name: "IUniswapV3Router",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IUniswapV3Router>;
    deployContract(
      name: "DirectArbitrageContract",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.DirectArbitrageContract>;
    deployContract(
      name: "DynamicFlashloanArbitrage",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.DynamicFlashloanArbitrage>;
    deployContract(
      name: "FlashloanArbitrage",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.FlashloanArbitrage>;
    deployContract(
      name: "HybridFlashloanArbitrage",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.HybridFlashloanArbitrage>;
    deployContract(
      name: "IUniswapV2Router",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IUniswapV2Router>;
    deployContract(
      name: "IUniswapV3Router",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IUniswapV3Router>;
    deployContract(
      name: "HybridFlashloanArbitrageFixed",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.HybridFlashloanArbitrageFixed>;
    deployContract(
      name: "IAavePool",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IAavePool>;
    deployContract(
      name: "IBalancerV2Pool",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IBalancerV2Pool>;
    deployContract(
      name: "IBalancerV2PoolRegistry",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IBalancerV2PoolRegistry>;
    deployContract(
      name: "IBalancerV2Vault",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IBalancerV2Vault>;
    deployContract(
      name: "IBalancerVault",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IBalancerVault>;
    deployContract(
      name: "ICurvePool",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.ICurvePool>;
    deployContract(
      name: "IUniswapV2Router",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IUniswapV2Router>;
    deployContract(
      name: "IUniswapV3Quoter",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IUniswapV3Quoter>;
    deployContract(
      name: "IUniswapV3QuoterV2",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IUniswapV3QuoterV2>;
    deployContract(
      name: "IUniswapV3Router",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IUniswapV3Router>;
    deployContract(
      name: "HybridFlashloanArbitrageLite",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.HybridFlashloanArbitrageLite>;
    deployContract(
      name: "IAavePool",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IAavePool>;
    deployContract(
      name: "IBalancerV2Vault",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IBalancerV2Vault>;
    deployContract(
      name: "IBalancerVault",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IBalancerVault>;
    deployContract(
      name: "ICurvePool",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.ICurvePool>;
    deployContract(
      name: "IUniswapV2Router",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IUniswapV2Router>;
    deployContract(
      name: "IUniswapV3Quoter",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IUniswapV3Quoter>;
    deployContract(
      name: "IUniswapV3Router",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IUniswapV3Router>;
    deployContract(
      name: "MinimalFlashloanTest",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.MinimalFlashloanTest>;
    deployContract(
      name: "MinimalTest",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.MinimalTest>;
    deployContract(
      name: "MockAavePool",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.MockAavePool>;
    deployContract(
      name: "MockDEXRouter",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.MockDEXRouter>;
    deployContract(
      name: "MockERC20",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.MockERC20>;
    deployContract(
      name: "MockPoolAddressesProvider",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.MockPoolAddressesProvider>;
    deployContract(
      name: "SimpleEnumTest",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.SimpleEnumTest>;
    deployContract(
      name: "SimpleFlashloanArbitrage",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.SimpleFlashloanArbitrage>;
    deployContract(
      name: "IUniswapV3Factory",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IUniswapV3Factory>;
    deployContract(
      name: "IUniswapV3Pool",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IUniswapV3Pool>;
    deployContract(
      name: "IUniswapV3Router",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IUniswapV3Router>;
    deployContract(
      name: "SimpleUniswapV3FlashSwap",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.SimpleUniswapV3FlashSwap>;
    deployContract(
      name: "IBalancerVault",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IBalancerVault>;
    deployContract(
      name: "TestFlashloanReceiver",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.TestFlashloanReceiver>;
    deployContract(
      name: "IUniswapV3Factory",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IUniswapV3Factory>;
    deployContract(
      name: "TestUniswapV3",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.TestUniswapV3>;
    deployContract(
      name: "IUniswapV3Factory",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IUniswapV3Factory>;
    deployContract(
      name: "IUniswapV3Pool",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IUniswapV3Pool>;
    deployContract(
      name: "IUniswapV3Router",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IUniswapV3Router>;
    deployContract(
      name: "UniswapV3FlashSwap",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.UniswapV3FlashSwap>;
    deployContract(
      name: "WorkingFlashloanArbitrage",
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.WorkingFlashloanArbitrage>;

    deployContract(
      name: "FlashLoanSimpleReceiverBase",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.FlashLoanSimpleReceiverBase>;
    deployContract(
      name: "IFlashLoanSimpleReceiver",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IFlashLoanSimpleReceiver>;
    deployContract(
      name: "IPool",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IPool>;
    deployContract(
      name: "IPoolAddressesProvider",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IPoolAddressesProvider>;
    deployContract(
      name: "IAuthentication",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IAuthentication>;
    deployContract(
      name: "ISignaturesValidator",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.ISignaturesValidator>;
    deployContract(
      name: "ITemporarilyPausable",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.ITemporarilyPausable>;
    deployContract(
      name: "IWETH",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IWETH>;
    deployContract(
      name: "IERC20",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IERC20>;
    deployContract(
      name: "IAuthorizer",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IAuthorizer>;
    deployContract(
      name: "IFlashLoanRecipient",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IFlashLoanRecipient>;
    deployContract(
      name: "IProtocolFeesCollector",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IProtocolFeesCollector>;
    deployContract(
      name: "IVault",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IVault>;
    deployContract(
      name: "Ownable",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.Ownable>;
    deployContract(
      name: "IERC1155Errors",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IERC1155Errors>;
    deployContract(
      name: "IERC20Errors",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IERC20Errors>;
    deployContract(
      name: "IERC721Errors",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IERC721Errors>;
    deployContract(
      name: "ERC20",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.ERC20>;
    deployContract(
      name: "IERC20Metadata",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IERC20Metadata>;
    deployContract(
      name: "IERC20",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IERC20>;
    deployContract(
      name: "Pausable",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.Pausable>;
    deployContract(
      name: "ReentrancyGuard",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.ReentrancyGuard>;
    deployContract(
      name: "IUniswapV2Router01",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IUniswapV2Router01>;
    deployContract(
      name: "IUniswapV2Router02",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IUniswapV2Router02>;
    deployContract(
      name: "IUniswapV3FlashCallback",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IUniswapV3FlashCallback>;
    deployContract(
      name: "IUniswapV3SwapCallback",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IUniswapV3SwapCallback>;
    deployContract(
      name: "IUniswapV3Pool",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IUniswapV3Pool>;
    deployContract(
      name: "IUniswapV3PoolActions",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IUniswapV3PoolActions>;
    deployContract(
      name: "IUniswapV3PoolDerivedState",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IUniswapV3PoolDerivedState>;
    deployContract(
      name: "IUniswapV3PoolEvents",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IUniswapV3PoolEvents>;
    deployContract(
      name: "IUniswapV3PoolImmutables",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IUniswapV3PoolImmutables>;
    deployContract(
      name: "IUniswapV3PoolOwnerActions",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IUniswapV3PoolOwnerActions>;
    deployContract(
      name: "IUniswapV3PoolState",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IUniswapV3PoolState>;
    deployContract(
      name: "ISwapRouter",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.ISwapRouter>;
    deployContract(
      name: "BalancerFlashloanArbitrage",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.BalancerFlashloanArbitrage>;
    deployContract(
      name: "IUniswapV2Router",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IUniswapV2Router>;
    deployContract(
      name: "IUniswapV3Router",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IUniswapV3Router>;
    deployContract(
      name: "DirectArbitrageContract",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.DirectArbitrageContract>;
    deployContract(
      name: "DynamicFlashloanArbitrage",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.DynamicFlashloanArbitrage>;
    deployContract(
      name: "FlashloanArbitrage",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.FlashloanArbitrage>;
    deployContract(
      name: "HybridFlashloanArbitrage",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.HybridFlashloanArbitrage>;
    deployContract(
      name: "IUniswapV2Router",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IUniswapV2Router>;
    deployContract(
      name: "IUniswapV3Router",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IUniswapV3Router>;
    deployContract(
      name: "HybridFlashloanArbitrageFixed",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.HybridFlashloanArbitrageFixed>;
    deployContract(
      name: "IAavePool",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IAavePool>;
    deployContract(
      name: "IBalancerV2Pool",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IBalancerV2Pool>;
    deployContract(
      name: "IBalancerV2PoolRegistry",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IBalancerV2PoolRegistry>;
    deployContract(
      name: "IBalancerV2Vault",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IBalancerV2Vault>;
    deployContract(
      name: "IBalancerVault",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IBalancerVault>;
    deployContract(
      name: "ICurvePool",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.ICurvePool>;
    deployContract(
      name: "IUniswapV2Router",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IUniswapV2Router>;
    deployContract(
      name: "IUniswapV3Quoter",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IUniswapV3Quoter>;
    deployContract(
      name: "IUniswapV3QuoterV2",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IUniswapV3QuoterV2>;
    deployContract(
      name: "IUniswapV3Router",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IUniswapV3Router>;
    deployContract(
      name: "HybridFlashloanArbitrageLite",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.HybridFlashloanArbitrageLite>;
    deployContract(
      name: "IAavePool",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IAavePool>;
    deployContract(
      name: "IBalancerV2Vault",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IBalancerV2Vault>;
    deployContract(
      name: "IBalancerVault",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IBalancerVault>;
    deployContract(
      name: "ICurvePool",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.ICurvePool>;
    deployContract(
      name: "IUniswapV2Router",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IUniswapV2Router>;
    deployContract(
      name: "IUniswapV3Quoter",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IUniswapV3Quoter>;
    deployContract(
      name: "IUniswapV3Router",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IUniswapV3Router>;
    deployContract(
      name: "MinimalFlashloanTest",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.MinimalFlashloanTest>;
    deployContract(
      name: "MinimalTest",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.MinimalTest>;
    deployContract(
      name: "MockAavePool",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.MockAavePool>;
    deployContract(
      name: "MockDEXRouter",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.MockDEXRouter>;
    deployContract(
      name: "MockERC20",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.MockERC20>;
    deployContract(
      name: "MockPoolAddressesProvider",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.MockPoolAddressesProvider>;
    deployContract(
      name: "SimpleEnumTest",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.SimpleEnumTest>;
    deployContract(
      name: "SimpleFlashloanArbitrage",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.SimpleFlashloanArbitrage>;
    deployContract(
      name: "IUniswapV3Factory",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IUniswapV3Factory>;
    deployContract(
      name: "IUniswapV3Pool",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IUniswapV3Pool>;
    deployContract(
      name: "IUniswapV3Router",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IUniswapV3Router>;
    deployContract(
      name: "SimpleUniswapV3FlashSwap",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.SimpleUniswapV3FlashSwap>;
    deployContract(
      name: "IBalancerVault",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IBalancerVault>;
    deployContract(
      name: "TestFlashloanReceiver",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.TestFlashloanReceiver>;
    deployContract(
      name: "IUniswapV3Factory",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IUniswapV3Factory>;
    deployContract(
      name: "TestUniswapV3",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.TestUniswapV3>;
    deployContract(
      name: "IUniswapV3Factory",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IUniswapV3Factory>;
    deployContract(
      name: "IUniswapV3Pool",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IUniswapV3Pool>;
    deployContract(
      name: "IUniswapV3Router",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.IUniswapV3Router>;
    deployContract(
      name: "UniswapV3FlashSwap",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.UniswapV3FlashSwap>;
    deployContract(
      name: "WorkingFlashloanArbitrage",
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<Contracts.WorkingFlashloanArbitrage>;

    // default types
    getContractFactory(
      name: string,
      signerOrOptions?: ethers.Signer | FactoryOptions
    ): Promise<ethers.ContractFactory>;
    getContractFactory(
      abi: any[],
      bytecode: ethers.BytesLike,
      signer?: ethers.Signer
    ): Promise<ethers.ContractFactory>;
    getContractAt(
      nameOrAbi: string | any[],
      address: string | ethers.Addressable,
      signer?: ethers.Signer
    ): Promise<ethers.Contract>;
    deployContract(
      name: string,
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<ethers.Contract>;
    deployContract(
      name: string,
      args: any[],
      signerOrOptions?: ethers.Signer | DeployContractOptions
    ): Promise<ethers.Contract>;
  }
}
