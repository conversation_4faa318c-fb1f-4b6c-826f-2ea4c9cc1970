/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */
import type {
  BaseContract,
  BigNumberish,
  BytesLike,
  FunctionFragment,
  Result,
  Interface,
  AddressLike,
  ContractRunner,
  ContractMethod,
  Listener,
} from "ethers";
import type {
  TypedContractEvent,
  TypedDeferredTopicFilter,
  TypedEventLog,
  TypedListener,
  TypedContractMethod,
} from "../common";

export declare namespace AbiDecodeTest {
  export type ArbitrageParamsStruct = {
    tokenA: AddressLike;
    tokenB: AddressLike;
    buyDex: AddressLike;
    sellDex: AddressLike;
    v3Fee: BigNumberish;
    minProfit: BigNumberish;
    provider: BigNumberish;
  };

  export type ArbitrageParamsStructOutput = [
    tokenA: string,
    tokenB: string,
    buyDex: string,
    sellDex: string,
    v3Fee: bigint,
    minProfit: bigint,
    provider: bigint
  ] & {
    tokenA: string;
    tokenB: string;
    buyDex: string;
    sellDex: string;
    v3Fee: bigint;
    minProfit: bigint;
    provider: bigint;
  };
}

export interface AbiDecodeTestInterface extends Interface {
  getFunction(
    nameOrSignature: "testDecode" | "testDecodeWithAmount"
  ): FunctionFragment;

  encodeFunctionData(
    functionFragment: "testDecode",
    values: [BytesLike]
  ): string;
  encodeFunctionData(
    functionFragment: "testDecodeWithAmount",
    values: [BigNumberish, BytesLike]
  ): string;

  decodeFunctionResult(functionFragment: "testDecode", data: BytesLike): Result;
  decodeFunctionResult(
    functionFragment: "testDecodeWithAmount",
    data: BytesLike
  ): Result;
}

export interface AbiDecodeTest extends BaseContract {
  connect(runner?: ContractRunner | null): AbiDecodeTest;
  waitForDeployment(): Promise<this>;

  interface: AbiDecodeTestInterface;

  queryFilter<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    fromBlockOrBlockhash?: string | number | undefined,
    toBlock?: string | number | undefined
  ): Promise<Array<TypedEventLog<TCEvent>>>;
  queryFilter<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    fromBlockOrBlockhash?: string | number | undefined,
    toBlock?: string | number | undefined
  ): Promise<Array<TypedEventLog<TCEvent>>>;

  on<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    listener: TypedListener<TCEvent>
  ): Promise<this>;
  on<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    listener: TypedListener<TCEvent>
  ): Promise<this>;

  once<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    listener: TypedListener<TCEvent>
  ): Promise<this>;
  once<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    listener: TypedListener<TCEvent>
  ): Promise<this>;

  listeners<TCEvent extends TypedContractEvent>(
    event: TCEvent
  ): Promise<Array<TypedListener<TCEvent>>>;
  listeners(eventName?: string): Promise<Array<Listener>>;
  removeAllListeners<TCEvent extends TypedContractEvent>(
    event?: TCEvent
  ): Promise<this>;

  testDecode: TypedContractMethod<
    [params: BytesLike],
    [AbiDecodeTest.ArbitrageParamsStructOutput],
    "view"
  >;

  testDecodeWithAmount: TypedContractMethod<
    [amount: BigNumberish, params: BytesLike],
    [[AbiDecodeTest.ArbitrageParamsStructOutput, bigint]],
    "view"
  >;

  getFunction<T extends ContractMethod = ContractMethod>(
    key: string | FunctionFragment
  ): T;

  getFunction(
    nameOrSignature: "testDecode"
  ): TypedContractMethod<
    [params: BytesLike],
    [AbiDecodeTest.ArbitrageParamsStructOutput],
    "view"
  >;
  getFunction(
    nameOrSignature: "testDecodeWithAmount"
  ): TypedContractMethod<
    [amount: BigNumberish, params: BytesLike],
    [[AbiDecodeTest.ArbitrageParamsStructOutput, bigint]],
    "view"
  >;

  filters: {};
}
