/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */
import type {
  BaseContract,
  BigNumberish,
  BytesLike,
  FunctionFragment,
  Result,
  Interface,
  EventFragment,
  AddressLike,
  ContractRunner,
  ContractMethod,
  Listener,
} from "ethers";
import type {
  TypedContractEvent,
  TypedDeferredTopicFilter,
  TypedEventLog,
  TypedLogDescription,
  TypedListener,
  TypedContractMethod,
} from "../../common";

export declare namespace HybridFlashloanArbitrageFixed {
  export type ArbitrageParamsStruct = {
    buyPath: AddressLike[];
    sellPath: AddressLike[];
    buyDex: AddressLike;
    sellDex: AddressLike;
    v3Fees: BigNumberish[];
    minProfit: BigNumberish;
    provider: BigNumberish;
    slippageToleranceBps: BigNumberish;
    maxGasCostWei: BigNumberish;
  };

  export type ArbitrageParamsStructOutput = [
    buyPath: string[],
    sellPath: string[],
    buyDex: string,
    sellDex: string,
    v3Fees: bigint[],
    minProfit: bigint,
    provider: bigint,
    slippageToleranceBps: bigint,
    maxGasCostWei: bigint
  ] & {
    buyPath: string[];
    sellPath: string[];
    buyDex: string;
    sellDex: string;
    v3Fees: bigint[];
    minProfit: bigint;
    provider: bigint;
    slippageToleranceBps: bigint;
    maxGasCostWei: bigint;
  };
}

export interface HybridFlashloanArbitrageFixedInterface extends Interface {
  getFunction(
    nameOrSignature:
      | "AAVE_POOL"
      | "BALANCER_VAULT"
      | "CHAIN_ID"
      | "UNISWAP_V2_ROUTER"
      | "UNISWAP_V3_QUOTER"
      | "UNISWAP_V3_ROUTER"
      | "_simulateArbitrage"
      | "addBalancerPool"
      | "balancerPoolExists"
      | "balancerPoolFees"
      | "balancerPoolIds"
      | "checkProfitability"
      | "emergencyWithdraw"
      | "executeOperation"
      | "executeOptimalFlashloan"
      | "getBalancerPoolInfo"
      | "owner"
      | "pause"
      | "paused"
      | "receiveFlashLoan"
      | "removeBalancerPool"
      | "renounceOwnership"
      | "supportedRouterTypes"
      | "supportedRouters"
      | "transferOwnership"
      | "unpause"
      | "withdrawProfits"
  ): FunctionFragment;

  getEvent(
    nameOrSignatureOrTopic:
      | "ArbitrageExecuted"
      | "BalancerPoolAdded"
      | "BalancerPoolRemoved"
      | "FlashloanExecuted"
      | "OwnershipTransferred"
      | "Paused"
      | "Unpaused"
  ): EventFragment;

  encodeFunctionData(functionFragment: "AAVE_POOL", values?: undefined): string;
  encodeFunctionData(
    functionFragment: "BALANCER_VAULT",
    values?: undefined
  ): string;
  encodeFunctionData(functionFragment: "CHAIN_ID", values?: undefined): string;
  encodeFunctionData(
    functionFragment: "UNISWAP_V2_ROUTER",
    values?: undefined
  ): string;
  encodeFunctionData(
    functionFragment: "UNISWAP_V3_QUOTER",
    values?: undefined
  ): string;
  encodeFunctionData(
    functionFragment: "UNISWAP_V3_ROUTER",
    values?: undefined
  ): string;
  encodeFunctionData(
    functionFragment: "_simulateArbitrage",
    values: [
      AddressLike,
      BigNumberish,
      HybridFlashloanArbitrageFixed.ArbitrageParamsStruct
    ]
  ): string;
  encodeFunctionData(
    functionFragment: "addBalancerPool",
    values: [AddressLike, AddressLike, BytesLike, BigNumberish]
  ): string;
  encodeFunctionData(
    functionFragment: "balancerPoolExists",
    values: [BytesLike]
  ): string;
  encodeFunctionData(
    functionFragment: "balancerPoolFees",
    values: [BytesLike]
  ): string;
  encodeFunctionData(
    functionFragment: "balancerPoolIds",
    values: [AddressLike, AddressLike]
  ): string;
  encodeFunctionData(
    functionFragment: "checkProfitability",
    values: [AddressLike, BigNumberish, BytesLike]
  ): string;
  encodeFunctionData(
    functionFragment: "emergencyWithdraw",
    values: [AddressLike]
  ): string;
  encodeFunctionData(
    functionFragment: "executeOperation",
    values: [AddressLike, BigNumberish, BigNumberish, AddressLike, BytesLike]
  ): string;
  encodeFunctionData(
    functionFragment: "executeOptimalFlashloan",
    values: [AddressLike, BigNumberish, BytesLike]
  ): string;
  encodeFunctionData(
    functionFragment: "getBalancerPoolInfo",
    values: [AddressLike, AddressLike]
  ): string;
  encodeFunctionData(functionFragment: "owner", values?: undefined): string;
  encodeFunctionData(functionFragment: "pause", values?: undefined): string;
  encodeFunctionData(functionFragment: "paused", values?: undefined): string;
  encodeFunctionData(
    functionFragment: "receiveFlashLoan",
    values: [AddressLike[], BigNumberish[], BigNumberish[], BytesLike]
  ): string;
  encodeFunctionData(
    functionFragment: "removeBalancerPool",
    values: [AddressLike, AddressLike, BytesLike]
  ): string;
  encodeFunctionData(
    functionFragment: "renounceOwnership",
    values?: undefined
  ): string;
  encodeFunctionData(
    functionFragment: "supportedRouterTypes",
    values: [AddressLike]
  ): string;
  encodeFunctionData(
    functionFragment: "supportedRouters",
    values: [AddressLike]
  ): string;
  encodeFunctionData(
    functionFragment: "transferOwnership",
    values: [AddressLike]
  ): string;
  encodeFunctionData(functionFragment: "unpause", values?: undefined): string;
  encodeFunctionData(
    functionFragment: "withdrawProfits",
    values: [AddressLike, BigNumberish]
  ): string;

  decodeFunctionResult(functionFragment: "AAVE_POOL", data: BytesLike): Result;
  decodeFunctionResult(
    functionFragment: "BALANCER_VAULT",
    data: BytesLike
  ): Result;
  decodeFunctionResult(functionFragment: "CHAIN_ID", data: BytesLike): Result;
  decodeFunctionResult(
    functionFragment: "UNISWAP_V2_ROUTER",
    data: BytesLike
  ): Result;
  decodeFunctionResult(
    functionFragment: "UNISWAP_V3_QUOTER",
    data: BytesLike
  ): Result;
  decodeFunctionResult(
    functionFragment: "UNISWAP_V3_ROUTER",
    data: BytesLike
  ): Result;
  decodeFunctionResult(
    functionFragment: "_simulateArbitrage",
    data: BytesLike
  ): Result;
  decodeFunctionResult(
    functionFragment: "addBalancerPool",
    data: BytesLike
  ): Result;
  decodeFunctionResult(
    functionFragment: "balancerPoolExists",
    data: BytesLike
  ): Result;
  decodeFunctionResult(
    functionFragment: "balancerPoolFees",
    data: BytesLike
  ): Result;
  decodeFunctionResult(
    functionFragment: "balancerPoolIds",
    data: BytesLike
  ): Result;
  decodeFunctionResult(
    functionFragment: "checkProfitability",
    data: BytesLike
  ): Result;
  decodeFunctionResult(
    functionFragment: "emergencyWithdraw",
    data: BytesLike
  ): Result;
  decodeFunctionResult(
    functionFragment: "executeOperation",
    data: BytesLike
  ): Result;
  decodeFunctionResult(
    functionFragment: "executeOptimalFlashloan",
    data: BytesLike
  ): Result;
  decodeFunctionResult(
    functionFragment: "getBalancerPoolInfo",
    data: BytesLike
  ): Result;
  decodeFunctionResult(functionFragment: "owner", data: BytesLike): Result;
  decodeFunctionResult(functionFragment: "pause", data: BytesLike): Result;
  decodeFunctionResult(functionFragment: "paused", data: BytesLike): Result;
  decodeFunctionResult(
    functionFragment: "receiveFlashLoan",
    data: BytesLike
  ): Result;
  decodeFunctionResult(
    functionFragment: "removeBalancerPool",
    data: BytesLike
  ): Result;
  decodeFunctionResult(
    functionFragment: "renounceOwnership",
    data: BytesLike
  ): Result;
  decodeFunctionResult(
    functionFragment: "supportedRouterTypes",
    data: BytesLike
  ): Result;
  decodeFunctionResult(
    functionFragment: "supportedRouters",
    data: BytesLike
  ): Result;
  decodeFunctionResult(
    functionFragment: "transferOwnership",
    data: BytesLike
  ): Result;
  decodeFunctionResult(functionFragment: "unpause", data: BytesLike): Result;
  decodeFunctionResult(
    functionFragment: "withdrawProfits",
    data: BytesLike
  ): Result;
}

export namespace ArbitrageExecutedEvent {
  export type InputTuple = [
    asset: AddressLike,
    amount: BigNumberish,
    profit: BigNumberish,
    buyPath: AddressLike[],
    sellPath: AddressLike[]
  ];
  export type OutputTuple = [
    asset: string,
    amount: bigint,
    profit: bigint,
    buyPath: string[],
    sellPath: string[]
  ];
  export interface OutputObject {
    asset: string;
    amount: bigint;
    profit: bigint;
    buyPath: string[];
    sellPath: string[];
  }
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>;
  export type Filter = TypedDeferredTopicFilter<Event>;
  export type Log = TypedEventLog<Event>;
  export type LogDescription = TypedLogDescription<Event>;
}

export namespace BalancerPoolAddedEvent {
  export type InputTuple = [
    tokenA: AddressLike,
    tokenB: AddressLike,
    poolId: BytesLike,
    feePercentage: BigNumberish
  ];
  export type OutputTuple = [
    tokenA: string,
    tokenB: string,
    poolId: string,
    feePercentage: bigint
  ];
  export interface OutputObject {
    tokenA: string;
    tokenB: string;
    poolId: string;
    feePercentage: bigint;
  }
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>;
  export type Filter = TypedDeferredTopicFilter<Event>;
  export type Log = TypedEventLog<Event>;
  export type LogDescription = TypedLogDescription<Event>;
}

export namespace BalancerPoolRemovedEvent {
  export type InputTuple = [
    tokenA: AddressLike,
    tokenB: AddressLike,
    poolId: BytesLike
  ];
  export type OutputTuple = [tokenA: string, tokenB: string, poolId: string];
  export interface OutputObject {
    tokenA: string;
    tokenB: string;
    poolId: string;
  }
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>;
  export type Filter = TypedDeferredTopicFilter<Event>;
  export type Log = TypedEventLog<Event>;
  export type LogDescription = TypedLogDescription<Event>;
}

export namespace FlashloanExecutedEvent {
  export type InputTuple = [
    provider: BigNumberish,
    asset: AddressLike,
    amount: BigNumberish,
    premium: BigNumberish,
    profit: BigNumberish
  ];
  export type OutputTuple = [
    provider: bigint,
    asset: string,
    amount: bigint,
    premium: bigint,
    profit: bigint
  ];
  export interface OutputObject {
    provider: bigint;
    asset: string;
    amount: bigint;
    premium: bigint;
    profit: bigint;
  }
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>;
  export type Filter = TypedDeferredTopicFilter<Event>;
  export type Log = TypedEventLog<Event>;
  export type LogDescription = TypedLogDescription<Event>;
}

export namespace OwnershipTransferredEvent {
  export type InputTuple = [previousOwner: AddressLike, newOwner: AddressLike];
  export type OutputTuple = [previousOwner: string, newOwner: string];
  export interface OutputObject {
    previousOwner: string;
    newOwner: string;
  }
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>;
  export type Filter = TypedDeferredTopicFilter<Event>;
  export type Log = TypedEventLog<Event>;
  export type LogDescription = TypedLogDescription<Event>;
}

export namespace PausedEvent {
  export type InputTuple = [account: AddressLike];
  export type OutputTuple = [account: string];
  export interface OutputObject {
    account: string;
  }
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>;
  export type Filter = TypedDeferredTopicFilter<Event>;
  export type Log = TypedEventLog<Event>;
  export type LogDescription = TypedLogDescription<Event>;
}

export namespace UnpausedEvent {
  export type InputTuple = [account: AddressLike];
  export type OutputTuple = [account: string];
  export interface OutputObject {
    account: string;
  }
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>;
  export type Filter = TypedDeferredTopicFilter<Event>;
  export type Log = TypedEventLog<Event>;
  export type LogDescription = TypedLogDescription<Event>;
}

export interface HybridFlashloanArbitrageFixed extends BaseContract {
  connect(runner?: ContractRunner | null): HybridFlashloanArbitrageFixed;
  waitForDeployment(): Promise<this>;

  interface: HybridFlashloanArbitrageFixedInterface;

  queryFilter<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    fromBlockOrBlockhash?: string | number | undefined,
    toBlock?: string | number | undefined
  ): Promise<Array<TypedEventLog<TCEvent>>>;
  queryFilter<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    fromBlockOrBlockhash?: string | number | undefined,
    toBlock?: string | number | undefined
  ): Promise<Array<TypedEventLog<TCEvent>>>;

  on<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    listener: TypedListener<TCEvent>
  ): Promise<this>;
  on<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    listener: TypedListener<TCEvent>
  ): Promise<this>;

  once<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    listener: TypedListener<TCEvent>
  ): Promise<this>;
  once<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    listener: TypedListener<TCEvent>
  ): Promise<this>;

  listeners<TCEvent extends TypedContractEvent>(
    event: TCEvent
  ): Promise<Array<TypedListener<TCEvent>>>;
  listeners(eventName?: string): Promise<Array<Listener>>;
  removeAllListeners<TCEvent extends TypedContractEvent>(
    event?: TCEvent
  ): Promise<this>;

  AAVE_POOL: TypedContractMethod<[], [string], "view">;

  BALANCER_VAULT: TypedContractMethod<[], [string], "view">;

  CHAIN_ID: TypedContractMethod<[], [bigint], "view">;

  UNISWAP_V2_ROUTER: TypedContractMethod<[], [string], "view">;

  UNISWAP_V3_QUOTER: TypedContractMethod<[], [string], "view">;

  UNISWAP_V3_ROUTER: TypedContractMethod<[], [string], "view">;

  _simulateArbitrage: TypedContractMethod<
    [
      arg0: AddressLike,
      amount: BigNumberish,
      params: HybridFlashloanArbitrageFixed.ArbitrageParamsStruct
    ],
    [bigint],
    "nonpayable"
  >;

  addBalancerPool: TypedContractMethod<
    [
      tokenA: AddressLike,
      tokenB: AddressLike,
      poolId: BytesLike,
      feePercentage: BigNumberish
    ],
    [void],
    "nonpayable"
  >;

  balancerPoolExists: TypedContractMethod<[arg0: BytesLike], [boolean], "view">;

  balancerPoolFees: TypedContractMethod<[arg0: BytesLike], [bigint], "view">;

  balancerPoolIds: TypedContractMethod<
    [arg0: AddressLike, arg1: AddressLike],
    [string],
    "view"
  >;

  checkProfitability: TypedContractMethod<
    [asset: AddressLike, amount: BigNumberish, params: BytesLike],
    [
      [boolean, bigint, bigint] & {
        profitable: boolean;
        expectedProfit: bigint;
        gasEstimate: bigint;
      }
    ],
    "nonpayable"
  >;

  emergencyWithdraw: TypedContractMethod<
    [token: AddressLike],
    [void],
    "nonpayable"
  >;

  executeOperation: TypedContractMethod<
    [
      asset: AddressLike,
      amount: BigNumberish,
      premium: BigNumberish,
      initiator: AddressLike,
      params: BytesLike
    ],
    [boolean],
    "nonpayable"
  >;

  executeOptimalFlashloan: TypedContractMethod<
    [asset: AddressLike, amount: BigNumberish, params: BytesLike],
    [void],
    "nonpayable"
  >;

  getBalancerPoolInfo: TypedContractMethod<
    [tokenA: AddressLike, tokenB: AddressLike],
    [
      [string, boolean, bigint] & {
        poolId: string;
        exists: boolean;
        feePercentage: bigint;
      }
    ],
    "view"
  >;

  owner: TypedContractMethod<[], [string], "view">;

  pause: TypedContractMethod<[], [void], "nonpayable">;

  paused: TypedContractMethod<[], [boolean], "view">;

  receiveFlashLoan: TypedContractMethod<
    [
      tokens: AddressLike[],
      amounts: BigNumberish[],
      arg2: BigNumberish[],
      userData: BytesLike
    ],
    [void],
    "nonpayable"
  >;

  removeBalancerPool: TypedContractMethod<
    [tokenA: AddressLike, tokenB: AddressLike, poolId: BytesLike],
    [void],
    "nonpayable"
  >;

  renounceOwnership: TypedContractMethod<[], [void], "nonpayable">;

  supportedRouterTypes: TypedContractMethod<
    [arg0: AddressLike],
    [bigint],
    "view"
  >;

  supportedRouters: TypedContractMethod<
    [router: AddressLike],
    [boolean],
    "view"
  >;

  transferOwnership: TypedContractMethod<
    [newOwner: AddressLike],
    [void],
    "nonpayable"
  >;

  unpause: TypedContractMethod<[], [void], "nonpayable">;

  withdrawProfits: TypedContractMethod<
    [token: AddressLike, amount: BigNumberish],
    [void],
    "nonpayable"
  >;

  getFunction<T extends ContractMethod = ContractMethod>(
    key: string | FunctionFragment
  ): T;

  getFunction(
    nameOrSignature: "AAVE_POOL"
  ): TypedContractMethod<[], [string], "view">;
  getFunction(
    nameOrSignature: "BALANCER_VAULT"
  ): TypedContractMethod<[], [string], "view">;
  getFunction(
    nameOrSignature: "CHAIN_ID"
  ): TypedContractMethod<[], [bigint], "view">;
  getFunction(
    nameOrSignature: "UNISWAP_V2_ROUTER"
  ): TypedContractMethod<[], [string], "view">;
  getFunction(
    nameOrSignature: "UNISWAP_V3_QUOTER"
  ): TypedContractMethod<[], [string], "view">;
  getFunction(
    nameOrSignature: "UNISWAP_V3_ROUTER"
  ): TypedContractMethod<[], [string], "view">;
  getFunction(
    nameOrSignature: "_simulateArbitrage"
  ): TypedContractMethod<
    [
      arg0: AddressLike,
      amount: BigNumberish,
      params: HybridFlashloanArbitrageFixed.ArbitrageParamsStruct
    ],
    [bigint],
    "nonpayable"
  >;
  getFunction(
    nameOrSignature: "addBalancerPool"
  ): TypedContractMethod<
    [
      tokenA: AddressLike,
      tokenB: AddressLike,
      poolId: BytesLike,
      feePercentage: BigNumberish
    ],
    [void],
    "nonpayable"
  >;
  getFunction(
    nameOrSignature: "balancerPoolExists"
  ): TypedContractMethod<[arg0: BytesLike], [boolean], "view">;
  getFunction(
    nameOrSignature: "balancerPoolFees"
  ): TypedContractMethod<[arg0: BytesLike], [bigint], "view">;
  getFunction(
    nameOrSignature: "balancerPoolIds"
  ): TypedContractMethod<
    [arg0: AddressLike, arg1: AddressLike],
    [string],
    "view"
  >;
  getFunction(
    nameOrSignature: "checkProfitability"
  ): TypedContractMethod<
    [asset: AddressLike, amount: BigNumberish, params: BytesLike],
    [
      [boolean, bigint, bigint] & {
        profitable: boolean;
        expectedProfit: bigint;
        gasEstimate: bigint;
      }
    ],
    "nonpayable"
  >;
  getFunction(
    nameOrSignature: "emergencyWithdraw"
  ): TypedContractMethod<[token: AddressLike], [void], "nonpayable">;
  getFunction(
    nameOrSignature: "executeOperation"
  ): TypedContractMethod<
    [
      asset: AddressLike,
      amount: BigNumberish,
      premium: BigNumberish,
      initiator: AddressLike,
      params: BytesLike
    ],
    [boolean],
    "nonpayable"
  >;
  getFunction(
    nameOrSignature: "executeOptimalFlashloan"
  ): TypedContractMethod<
    [asset: AddressLike, amount: BigNumberish, params: BytesLike],
    [void],
    "nonpayable"
  >;
  getFunction(
    nameOrSignature: "getBalancerPoolInfo"
  ): TypedContractMethod<
    [tokenA: AddressLike, tokenB: AddressLike],
    [
      [string, boolean, bigint] & {
        poolId: string;
        exists: boolean;
        feePercentage: bigint;
      }
    ],
    "view"
  >;
  getFunction(
    nameOrSignature: "owner"
  ): TypedContractMethod<[], [string], "view">;
  getFunction(
    nameOrSignature: "pause"
  ): TypedContractMethod<[], [void], "nonpayable">;
  getFunction(
    nameOrSignature: "paused"
  ): TypedContractMethod<[], [boolean], "view">;
  getFunction(
    nameOrSignature: "receiveFlashLoan"
  ): TypedContractMethod<
    [
      tokens: AddressLike[],
      amounts: BigNumberish[],
      arg2: BigNumberish[],
      userData: BytesLike
    ],
    [void],
    "nonpayable"
  >;
  getFunction(
    nameOrSignature: "removeBalancerPool"
  ): TypedContractMethod<
    [tokenA: AddressLike, tokenB: AddressLike, poolId: BytesLike],
    [void],
    "nonpayable"
  >;
  getFunction(
    nameOrSignature: "renounceOwnership"
  ): TypedContractMethod<[], [void], "nonpayable">;
  getFunction(
    nameOrSignature: "supportedRouterTypes"
  ): TypedContractMethod<[arg0: AddressLike], [bigint], "view">;
  getFunction(
    nameOrSignature: "supportedRouters"
  ): TypedContractMethod<[router: AddressLike], [boolean], "view">;
  getFunction(
    nameOrSignature: "transferOwnership"
  ): TypedContractMethod<[newOwner: AddressLike], [void], "nonpayable">;
  getFunction(
    nameOrSignature: "unpause"
  ): TypedContractMethod<[], [void], "nonpayable">;
  getFunction(
    nameOrSignature: "withdrawProfits"
  ): TypedContractMethod<
    [token: AddressLike, amount: BigNumberish],
    [void],
    "nonpayable"
  >;

  getEvent(
    key: "ArbitrageExecuted"
  ): TypedContractEvent<
    ArbitrageExecutedEvent.InputTuple,
    ArbitrageExecutedEvent.OutputTuple,
    ArbitrageExecutedEvent.OutputObject
  >;
  getEvent(
    key: "BalancerPoolAdded"
  ): TypedContractEvent<
    BalancerPoolAddedEvent.InputTuple,
    BalancerPoolAddedEvent.OutputTuple,
    BalancerPoolAddedEvent.OutputObject
  >;
  getEvent(
    key: "BalancerPoolRemoved"
  ): TypedContractEvent<
    BalancerPoolRemovedEvent.InputTuple,
    BalancerPoolRemovedEvent.OutputTuple,
    BalancerPoolRemovedEvent.OutputObject
  >;
  getEvent(
    key: "FlashloanExecuted"
  ): TypedContractEvent<
    FlashloanExecutedEvent.InputTuple,
    FlashloanExecutedEvent.OutputTuple,
    FlashloanExecutedEvent.OutputObject
  >;
  getEvent(
    key: "OwnershipTransferred"
  ): TypedContractEvent<
    OwnershipTransferredEvent.InputTuple,
    OwnershipTransferredEvent.OutputTuple,
    OwnershipTransferredEvent.OutputObject
  >;
  getEvent(
    key: "Paused"
  ): TypedContractEvent<
    PausedEvent.InputTuple,
    PausedEvent.OutputTuple,
    PausedEvent.OutputObject
  >;
  getEvent(
    key: "Unpaused"
  ): TypedContractEvent<
    UnpausedEvent.InputTuple,
    UnpausedEvent.OutputTuple,
    UnpausedEvent.OutputObject
  >;

  filters: {
    "ArbitrageExecuted(address,uint256,uint256,address[],address[])": TypedContractEvent<
      ArbitrageExecutedEvent.InputTuple,
      ArbitrageExecutedEvent.OutputTuple,
      ArbitrageExecutedEvent.OutputObject
    >;
    ArbitrageExecuted: TypedContractEvent<
      ArbitrageExecutedEvent.InputTuple,
      ArbitrageExecutedEvent.OutputTuple,
      ArbitrageExecutedEvent.OutputObject
    >;

    "BalancerPoolAdded(address,address,bytes32,uint256)": TypedContractEvent<
      BalancerPoolAddedEvent.InputTuple,
      BalancerPoolAddedEvent.OutputTuple,
      BalancerPoolAddedEvent.OutputObject
    >;
    BalancerPoolAdded: TypedContractEvent<
      BalancerPoolAddedEvent.InputTuple,
      BalancerPoolAddedEvent.OutputTuple,
      BalancerPoolAddedEvent.OutputObject
    >;

    "BalancerPoolRemoved(address,address,bytes32)": TypedContractEvent<
      BalancerPoolRemovedEvent.InputTuple,
      BalancerPoolRemovedEvent.OutputTuple,
      BalancerPoolRemovedEvent.OutputObject
    >;
    BalancerPoolRemoved: TypedContractEvent<
      BalancerPoolRemovedEvent.InputTuple,
      BalancerPoolRemovedEvent.OutputTuple,
      BalancerPoolRemovedEvent.OutputObject
    >;

    "FlashloanExecuted(uint8,address,uint256,uint256,uint256)": TypedContractEvent<
      FlashloanExecutedEvent.InputTuple,
      FlashloanExecutedEvent.OutputTuple,
      FlashloanExecutedEvent.OutputObject
    >;
    FlashloanExecuted: TypedContractEvent<
      FlashloanExecutedEvent.InputTuple,
      FlashloanExecutedEvent.OutputTuple,
      FlashloanExecutedEvent.OutputObject
    >;

    "OwnershipTransferred(address,address)": TypedContractEvent<
      OwnershipTransferredEvent.InputTuple,
      OwnershipTransferredEvent.OutputTuple,
      OwnershipTransferredEvent.OutputObject
    >;
    OwnershipTransferred: TypedContractEvent<
      OwnershipTransferredEvent.InputTuple,
      OwnershipTransferredEvent.OutputTuple,
      OwnershipTransferredEvent.OutputObject
    >;

    "Paused(address)": TypedContractEvent<
      PausedEvent.InputTuple,
      PausedEvent.OutputTuple,
      PausedEvent.OutputObject
    >;
    Paused: TypedContractEvent<
      PausedEvent.InputTuple,
      PausedEvent.OutputTuple,
      PausedEvent.OutputObject
    >;

    "Unpaused(address)": TypedContractEvent<
      UnpausedEvent.InputTuple,
      UnpausedEvent.OutputTuple,
      UnpausedEvent.OutputObject
    >;
    Unpaused: TypedContractEvent<
      UnpausedEvent.InputTuple,
      UnpausedEvent.OutputTuple,
      UnpausedEvent.OutputObject
    >;
  };
}
