/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */
import type {
  Base<PERSON>ontract,
  BigNumberish,
  BytesLike,
  FunctionFragment,
  Result,
  Interface,
  AddressLike,
  ContractRunner,
  ContractMethod,
  Listener,
} from "ethers";
import type {
  TypedContractEvent,
  TypedDeferredTopicFilter,
  TypedEventLog,
  TypedListener,
  TypedContractMethod,
} from "../../common";

export interface IUniswapV3QuoterV2Interface extends Interface {
  getFunction(nameOrSignature: "quoteExactInputSingle"): FunctionFragment;

  encodeFunctionData(
    functionFragment: "quoteExactInputSingle",
    values: [AddressLike, AddressLike, BigNumberish, BigNumberish, BigNumberish]
  ): string;

  decodeFunctionResult(
    functionFragment: "quoteExactInputSingle",
    data: BytesLike
  ): Result;
}

export interface IUniswapV3QuoterV2 extends BaseContract {
  connect(runner?: ContractRunner | null): IUniswapV3QuoterV2;
  waitForDeployment(): Promise<this>;

  interface: IUniswapV3QuoterV2Interface;

  queryFilter<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    fromBlockOrBlockhash?: string | number | undefined,
    toBlock?: string | number | undefined
  ): Promise<Array<TypedEventLog<TCEvent>>>;
  queryFilter<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    fromBlockOrBlockhash?: string | number | undefined,
    toBlock?: string | number | undefined
  ): Promise<Array<TypedEventLog<TCEvent>>>;

  on<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    listener: TypedListener<TCEvent>
  ): Promise<this>;
  on<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    listener: TypedListener<TCEvent>
  ): Promise<this>;

  once<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    listener: TypedListener<TCEvent>
  ): Promise<this>;
  once<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    listener: TypedListener<TCEvent>
  ): Promise<this>;

  listeners<TCEvent extends TypedContractEvent>(
    event: TCEvent
  ): Promise<Array<TypedListener<TCEvent>>>;
  listeners(eventName?: string): Promise<Array<Listener>>;
  removeAllListeners<TCEvent extends TypedContractEvent>(
    event?: TCEvent
  ): Promise<this>;

  quoteExactInputSingle: TypedContractMethod<
    [
      tokenIn: AddressLike,
      tokenOut: AddressLike,
      fee: BigNumberish,
      amountIn: BigNumberish,
      sqrtPriceLimitX96: BigNumberish
    ],
    [
      [bigint, bigint, bigint, bigint] & {
        amountOut: bigint;
        sqrtPriceX96After: bigint;
        initializedTicksCrossed: bigint;
        gasEstimate: bigint;
      }
    ],
    "view"
  >;

  getFunction<T extends ContractMethod = ContractMethod>(
    key: string | FunctionFragment
  ): T;

  getFunction(
    nameOrSignature: "quoteExactInputSingle"
  ): TypedContractMethod<
    [
      tokenIn: AddressLike,
      tokenOut: AddressLike,
      fee: BigNumberish,
      amountIn: BigNumberish,
      sqrtPriceLimitX96: BigNumberish
    ],
    [
      [bigint, bigint, bigint, bigint] & {
        amountOut: bigint;
        sqrtPriceX96After: bigint;
        initializedTicksCrossed: bigint;
        gasEstimate: bigint;
      }
    ],
    "view"
  >;

  filters: {};
}
