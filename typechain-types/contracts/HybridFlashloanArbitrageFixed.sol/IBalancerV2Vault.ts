/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */
import type {
  BaseContract,
  BigNumberish,
  BytesLike,
  FunctionFragment,
  Result,
  Interface,
  AddressLike,
  ContractRunner,
  ContractMethod,
  Listener,
} from "ethers";
import type {
  TypedContractEvent,
  TypedDeferredTopicFilter,
  TypedEventLog,
  TypedListener,
  TypedContractMethod,
} from "../../common";

export declare namespace IBalancerV2Vault {
  export type SingleSwapStruct = {
    poolId: BytesLike;
    kind: BigNumberish;
    assetIn: AddressLike;
    assetOut: AddressLike;
    amount: BigNumberish;
    userData: BytesLike;
  };

  export type SingleSwapStructOutput = [
    poolId: string,
    kind: bigint,
    assetIn: string,
    assetOut: string,
    amount: bigint,
    userData: string
  ] & {
    poolId: string;
    kind: bigint;
    assetIn: string;
    assetOut: string;
    amount: bigint;
    userData: string;
  };

  export type FundManagementStruct = {
    sender: AddressLike;
    fromInternalBalance: boolean;
    recipient: AddressLike;
    toInternalBalance: boolean;
  };

  export type FundManagementStructOutput = [
    sender: string,
    fromInternalBalance: boolean,
    recipient: string,
    toInternalBalance: boolean
  ] & {
    sender: string;
    fromInternalBalance: boolean;
    recipient: string;
    toInternalBalance: boolean;
  };
}

export interface IBalancerV2VaultInterface extends Interface {
  getFunction(
    nameOrSignature: "getPool" | "getPoolTokens" | "querySwap" | "swap"
  ): FunctionFragment;

  encodeFunctionData(functionFragment: "getPool", values: [BytesLike]): string;
  encodeFunctionData(
    functionFragment: "getPoolTokens",
    values: [BytesLike]
  ): string;
  encodeFunctionData(
    functionFragment: "querySwap",
    values: [
      IBalancerV2Vault.SingleSwapStruct,
      IBalancerV2Vault.FundManagementStruct
    ]
  ): string;
  encodeFunctionData(
    functionFragment: "swap",
    values: [
      IBalancerV2Vault.SingleSwapStruct,
      IBalancerV2Vault.FundManagementStruct,
      BigNumberish,
      BigNumberish
    ]
  ): string;

  decodeFunctionResult(functionFragment: "getPool", data: BytesLike): Result;
  decodeFunctionResult(
    functionFragment: "getPoolTokens",
    data: BytesLike
  ): Result;
  decodeFunctionResult(functionFragment: "querySwap", data: BytesLike): Result;
  decodeFunctionResult(functionFragment: "swap", data: BytesLike): Result;
}

export interface IBalancerV2Vault extends BaseContract {
  connect(runner?: ContractRunner | null): IBalancerV2Vault;
  waitForDeployment(): Promise<this>;

  interface: IBalancerV2VaultInterface;

  queryFilter<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    fromBlockOrBlockhash?: string | number | undefined,
    toBlock?: string | number | undefined
  ): Promise<Array<TypedEventLog<TCEvent>>>;
  queryFilter<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    fromBlockOrBlockhash?: string | number | undefined,
    toBlock?: string | number | undefined
  ): Promise<Array<TypedEventLog<TCEvent>>>;

  on<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    listener: TypedListener<TCEvent>
  ): Promise<this>;
  on<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    listener: TypedListener<TCEvent>
  ): Promise<this>;

  once<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    listener: TypedListener<TCEvent>
  ): Promise<this>;
  once<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    listener: TypedListener<TCEvent>
  ): Promise<this>;

  listeners<TCEvent extends TypedContractEvent>(
    event: TCEvent
  ): Promise<Array<TypedListener<TCEvent>>>;
  listeners(eventName?: string): Promise<Array<Listener>>;
  removeAllListeners<TCEvent extends TypedContractEvent>(
    event?: TCEvent
  ): Promise<this>;

  getPool: TypedContractMethod<
    [poolId: BytesLike],
    [[string, bigint] & { poolAddress: string; specialization: bigint }],
    "view"
  >;

  getPoolTokens: TypedContractMethod<
    [poolId: BytesLike],
    [
      [string[], bigint[], bigint] & {
        tokens: string[];
        balances: bigint[];
        lastChangeBlock: bigint;
      }
    ],
    "view"
  >;

  querySwap: TypedContractMethod<
    [
      singleSwap: IBalancerV2Vault.SingleSwapStruct,
      funds: IBalancerV2Vault.FundManagementStruct
    ],
    [bigint],
    "nonpayable"
  >;

  swap: TypedContractMethod<
    [
      singleSwap: IBalancerV2Vault.SingleSwapStruct,
      funds: IBalancerV2Vault.FundManagementStruct,
      limit: BigNumberish,
      deadline: BigNumberish
    ],
    [bigint],
    "nonpayable"
  >;

  getFunction<T extends ContractMethod = ContractMethod>(
    key: string | FunctionFragment
  ): T;

  getFunction(
    nameOrSignature: "getPool"
  ): TypedContractMethod<
    [poolId: BytesLike],
    [[string, bigint] & { poolAddress: string; specialization: bigint }],
    "view"
  >;
  getFunction(
    nameOrSignature: "getPoolTokens"
  ): TypedContractMethod<
    [poolId: BytesLike],
    [
      [string[], bigint[], bigint] & {
        tokens: string[];
        balances: bigint[];
        lastChangeBlock: bigint;
      }
    ],
    "view"
  >;
  getFunction(
    nameOrSignature: "querySwap"
  ): TypedContractMethod<
    [
      singleSwap: IBalancerV2Vault.SingleSwapStruct,
      funds: IBalancerV2Vault.FundManagementStruct
    ],
    [bigint],
    "nonpayable"
  >;
  getFunction(
    nameOrSignature: "swap"
  ): TypedContractMethod<
    [
      singleSwap: IBalancerV2Vault.SingleSwapStruct,
      funds: IBalancerV2Vault.FundManagementStruct,
      limit: BigNumberish,
      deadline: BigNumberish
    ],
    [bigint],
    "nonpayable"
  >;

  filters: {};
}
