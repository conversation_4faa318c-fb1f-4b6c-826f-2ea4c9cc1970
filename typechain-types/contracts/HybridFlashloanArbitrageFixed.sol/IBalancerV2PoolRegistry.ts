/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */
import type {
  BaseContract,
  BytesLike,
  FunctionFragment,
  Result,
  Interface,
  AddressLike,
  ContractRunner,
  Contract<PERSON>ethod,
  Listener,
} from "ethers";
import type {
  TypedContractEvent,
  TypedDeferredTopicFilter,
  TypedEventLog,
  TypedListener,
  TypedContractMethod,
} from "../../common";

export interface IBalancerV2PoolRegistryInterface extends Interface {
  getFunction(
    nameOrSignature: "getPoolsWithTokens" | "isPoolRegistered"
  ): FunctionFragment;

  encodeFunctionData(
    functionFragment: "getPoolsWithTokens",
    values: [AddressLike[]]
  ): string;
  encodeFunctionData(
    functionFragment: "isPoolRegistered",
    values: [BytesLike]
  ): string;

  decodeFunctionResult(
    functionFragment: "getPoolsWithTokens",
    data: BytesLike
  ): Result;
  decodeFunctionResult(
    functionFragment: "isPoolRegistered",
    data: Bytes<PERSON>ike
  ): Result;
}

export interface IBalancerV2PoolRegistry extends BaseContract {
  connect(runner?: ContractRunner | null): IBalancerV2PoolRegistry;
  waitForDeployment(): Promise<this>;

  interface: IBalancerV2PoolRegistryInterface;

  queryFilter<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    fromBlockOrBlockhash?: string | number | undefined,
    toBlock?: string | number | undefined
  ): Promise<Array<TypedEventLog<TCEvent>>>;
  queryFilter<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    fromBlockOrBlockhash?: string | number | undefined,
    toBlock?: string | number | undefined
  ): Promise<Array<TypedEventLog<TCEvent>>>;

  on<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    listener: TypedListener<TCEvent>
  ): Promise<this>;
  on<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    listener: TypedListener<TCEvent>
  ): Promise<this>;

  once<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    listener: TypedListener<TCEvent>
  ): Promise<this>;
  once<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    listener: TypedListener<TCEvent>
  ): Promise<this>;

  listeners<TCEvent extends TypedContractEvent>(
    event: TCEvent
  ): Promise<Array<TypedListener<TCEvent>>>;
  listeners(eventName?: string): Promise<Array<Listener>>;
  removeAllListeners<TCEvent extends TypedContractEvent>(
    event?: TCEvent
  ): Promise<this>;

  getPoolsWithTokens: TypedContractMethod<
    [tokens: AddressLike[]],
    [string[]],
    "view"
  >;

  isPoolRegistered: TypedContractMethod<[poolId: BytesLike], [boolean], "view">;

  getFunction<T extends ContractMethod = ContractMethod>(
    key: string | FunctionFragment
  ): T;

  getFunction(
    nameOrSignature: "getPoolsWithTokens"
  ): TypedContractMethod<[tokens: AddressLike[]], [string[]], "view">;
  getFunction(
    nameOrSignature: "isPoolRegistered"
  ): TypedContractMethod<[poolId: BytesLike], [boolean], "view">;

  filters: {};
}
