/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */
import type {
  BaseContract,
  BigNumberish,
  BytesLike,
  FunctionFragment,
  Result,
  Interface,
  EventFragment,
  AddressLike,
  ContractRunner,
  ContractMethod,
  Listener,
} from "ethers";
import type {
  TypedContractEvent,
  TypedDeferredTopicFilter,
  TypedEventLog,
  TypedLogDescription,
  TypedListener,
  TypedContractMethod,
} from "../common";

export declare namespace SimpleEnumTest {
  export type ArbitrageParamsStruct = {
    tokenA: AddressLike;
    tokenB: AddressLike;
    buyDex: AddressLike;
    sellDex: AddressLike;
    v3Fee: BigNumberish;
    minProfit: BigNumberish;
    provider: BigNumberish;
  };

  export type ArbitrageParamsStructOutput = [
    tokenA: string,
    tokenB: string,
    buyDex: string,
    sellDex: string,
    v3Fee: bigint,
    minProfit: bigint,
    provider: bigint
  ] & {
    tokenA: string;
    tokenB: string;
    buyDex: string;
    sellDex: string;
    v3Fee: bigint;
    minProfit: bigint;
    provider: bigint;
  };
}

export interface SimpleEnumTestInterface extends Interface {
  getFunction(
    nameOrSignature:
      | "decodeNoMinProfit"
      | "decodeProviderFirst"
      | "owner"
      | "renounceOwnership"
      | "testAlternativeStruct"
      | "testDecodeOnly"
      | "testDecodeWithAmount"
      | "testEnumConversion"
      | "testExecuteOptimalFlashloan"
      | "transferOwnership"
  ): FunctionFragment;

  getEvent(
    nameOrSignatureOrTopic: "OwnershipTransferred" | "TestResult"
  ): EventFragment;

  encodeFunctionData(
    functionFragment: "decodeNoMinProfit",
    values: [BytesLike]
  ): string;
  encodeFunctionData(
    functionFragment: "decodeProviderFirst",
    values: [BytesLike]
  ): string;
  encodeFunctionData(functionFragment: "owner", values?: undefined): string;
  encodeFunctionData(
    functionFragment: "renounceOwnership",
    values?: undefined
  ): string;
  encodeFunctionData(
    functionFragment: "testAlternativeStruct",
    values: [BytesLike]
  ): string;
  encodeFunctionData(
    functionFragment: "testDecodeOnly",
    values: [BytesLike]
  ): string;
  encodeFunctionData(
    functionFragment: "testDecodeWithAmount",
    values: [BigNumberish, BytesLike]
  ): string;
  encodeFunctionData(
    functionFragment: "testEnumConversion",
    values: [BigNumberish]
  ): string;
  encodeFunctionData(
    functionFragment: "testExecuteOptimalFlashloan",
    values: [AddressLike, BigNumberish, BytesLike]
  ): string;
  encodeFunctionData(
    functionFragment: "transferOwnership",
    values: [AddressLike]
  ): string;

  decodeFunctionResult(
    functionFragment: "decodeNoMinProfit",
    data: BytesLike
  ): Result;
  decodeFunctionResult(
    functionFragment: "decodeProviderFirst",
    data: BytesLike
  ): Result;
  decodeFunctionResult(functionFragment: "owner", data: BytesLike): Result;
  decodeFunctionResult(
    functionFragment: "renounceOwnership",
    data: BytesLike
  ): Result;
  decodeFunctionResult(
    functionFragment: "testAlternativeStruct",
    data: BytesLike
  ): Result;
  decodeFunctionResult(
    functionFragment: "testDecodeOnly",
    data: BytesLike
  ): Result;
  decodeFunctionResult(
    functionFragment: "testDecodeWithAmount",
    data: BytesLike
  ): Result;
  decodeFunctionResult(
    functionFragment: "testEnumConversion",
    data: BytesLike
  ): Result;
  decodeFunctionResult(
    functionFragment: "testExecuteOptimalFlashloan",
    data: BytesLike
  ): Result;
  decodeFunctionResult(
    functionFragment: "transferOwnership",
    data: BytesLike
  ): Result;
}

export namespace OwnershipTransferredEvent {
  export type InputTuple = [previousOwner: AddressLike, newOwner: AddressLike];
  export type OutputTuple = [previousOwner: string, newOwner: string];
  export interface OutputObject {
    previousOwner: string;
    newOwner: string;
  }
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>;
  export type Filter = TypedDeferredTopicFilter<Event>;
  export type Log = TypedEventLog<Event>;
  export type LogDescription = TypedLogDescription<Event>;
}

export namespace TestResultEvent {
  export type InputTuple = [
    message: string,
    amount: BigNumberish,
    provider: BigNumberish
  ];
  export type OutputTuple = [message: string, amount: bigint, provider: bigint];
  export interface OutputObject {
    message: string;
    amount: bigint;
    provider: bigint;
  }
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>;
  export type Filter = TypedDeferredTopicFilter<Event>;
  export type Log = TypedEventLog<Event>;
  export type LogDescription = TypedLogDescription<Event>;
}

export interface SimpleEnumTest extends BaseContract {
  connect(runner?: ContractRunner | null): SimpleEnumTest;
  waitForDeployment(): Promise<this>;

  interface: SimpleEnumTestInterface;

  queryFilter<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    fromBlockOrBlockhash?: string | number | undefined,
    toBlock?: string | number | undefined
  ): Promise<Array<TypedEventLog<TCEvent>>>;
  queryFilter<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    fromBlockOrBlockhash?: string | number | undefined,
    toBlock?: string | number | undefined
  ): Promise<Array<TypedEventLog<TCEvent>>>;

  on<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    listener: TypedListener<TCEvent>
  ): Promise<this>;
  on<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    listener: TypedListener<TCEvent>
  ): Promise<this>;

  once<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    listener: TypedListener<TCEvent>
  ): Promise<this>;
  once<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    listener: TypedListener<TCEvent>
  ): Promise<this>;

  listeners<TCEvent extends TypedContractEvent>(
    event: TCEvent
  ): Promise<Array<TypedListener<TCEvent>>>;
  listeners(eventName?: string): Promise<Array<Listener>>;
  removeAllListeners<TCEvent extends TypedContractEvent>(
    event?: TCEvent
  ): Promise<this>;

  decodeNoMinProfit: TypedContractMethod<[params: BytesLike], [void], "view">;

  decodeProviderFirst: TypedContractMethod<[params: BytesLike], [void], "view">;

  owner: TypedContractMethod<[], [string], "view">;

  renounceOwnership: TypedContractMethod<[], [void], "nonpayable">;

  testAlternativeStruct: TypedContractMethod<
    [params: BytesLike],
    [boolean],
    "view"
  >;

  testDecodeOnly: TypedContractMethod<
    [params: BytesLike],
    [SimpleEnumTest.ArbitrageParamsStructOutput],
    "view"
  >;

  testDecodeWithAmount: TypedContractMethod<
    [amount: BigNumberish, params: BytesLike],
    [[SimpleEnumTest.ArbitrageParamsStructOutput, bigint]],
    "view"
  >;

  testEnumConversion: TypedContractMethod<
    [providerValue: BigNumberish],
    [bigint],
    "view"
  >;

  testExecuteOptimalFlashloan: TypedContractMethod<
    [asset: AddressLike, amount: BigNumberish, params: BytesLike],
    [boolean],
    "nonpayable"
  >;

  transferOwnership: TypedContractMethod<
    [newOwner: AddressLike],
    [void],
    "nonpayable"
  >;

  getFunction<T extends ContractMethod = ContractMethod>(
    key: string | FunctionFragment
  ): T;

  getFunction(
    nameOrSignature: "decodeNoMinProfit"
  ): TypedContractMethod<[params: BytesLike], [void], "view">;
  getFunction(
    nameOrSignature: "decodeProviderFirst"
  ): TypedContractMethod<[params: BytesLike], [void], "view">;
  getFunction(
    nameOrSignature: "owner"
  ): TypedContractMethod<[], [string], "view">;
  getFunction(
    nameOrSignature: "renounceOwnership"
  ): TypedContractMethod<[], [void], "nonpayable">;
  getFunction(
    nameOrSignature: "testAlternativeStruct"
  ): TypedContractMethod<[params: BytesLike], [boolean], "view">;
  getFunction(
    nameOrSignature: "testDecodeOnly"
  ): TypedContractMethod<
    [params: BytesLike],
    [SimpleEnumTest.ArbitrageParamsStructOutput],
    "view"
  >;
  getFunction(
    nameOrSignature: "testDecodeWithAmount"
  ): TypedContractMethod<
    [amount: BigNumberish, params: BytesLike],
    [[SimpleEnumTest.ArbitrageParamsStructOutput, bigint]],
    "view"
  >;
  getFunction(
    nameOrSignature: "testEnumConversion"
  ): TypedContractMethod<[providerValue: BigNumberish], [bigint], "view">;
  getFunction(
    nameOrSignature: "testExecuteOptimalFlashloan"
  ): TypedContractMethod<
    [asset: AddressLike, amount: BigNumberish, params: BytesLike],
    [boolean],
    "nonpayable"
  >;
  getFunction(
    nameOrSignature: "transferOwnership"
  ): TypedContractMethod<[newOwner: AddressLike], [void], "nonpayable">;

  getEvent(
    key: "OwnershipTransferred"
  ): TypedContractEvent<
    OwnershipTransferredEvent.InputTuple,
    OwnershipTransferredEvent.OutputTuple,
    OwnershipTransferredEvent.OutputObject
  >;
  getEvent(
    key: "TestResult"
  ): TypedContractEvent<
    TestResultEvent.InputTuple,
    TestResultEvent.OutputTuple,
    TestResultEvent.OutputObject
  >;

  filters: {
    "OwnershipTransferred(address,address)": TypedContractEvent<
      OwnershipTransferredEvent.InputTuple,
      OwnershipTransferredEvent.OutputTuple,
      OwnershipTransferredEvent.OutputObject
    >;
    OwnershipTransferred: TypedContractEvent<
      OwnershipTransferredEvent.InputTuple,
      OwnershipTransferredEvent.OutputTuple,
      OwnershipTransferredEvent.OutputObject
    >;

    "TestResult(string,uint256,uint8)": TypedContractEvent<
      TestResultEvent.InputTuple,
      TestResultEvent.OutputTuple,
      TestResultEvent.OutputObject
    >;
    TestResult: TypedContractEvent<
      TestResultEvent.InputTuple,
      TestResultEvent.OutputTuple,
      TestResultEvent.OutputObject
    >;
  };
}
