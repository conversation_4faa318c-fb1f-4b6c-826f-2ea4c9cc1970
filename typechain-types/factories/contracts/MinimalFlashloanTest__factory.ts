/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */
import {
  Contract,
  ContractFactory,
  ContractTransactionResponse,
  Interface,
} from "ethers";
import type { Signer, ContractDeployTransaction, ContractRunner } from "ethers";
import type { NonPayableOverrides } from "../../common";
import type {
  MinimalFlashloanTest,
  MinimalFlashloanTestInterface,
} from "../../contracts/MinimalFlashloanTest";

const _abi = [
  {
    inputs: [],
    stateMutability: "nonpayable",
    type: "constructor",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "owner",
        type: "address",
      },
    ],
    name: "OwnableInvalidOwner",
    type: "error",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "account",
        type: "address",
      },
    ],
    name: "OwnableUnauthorizedAccount",
    type: "error",
  },
  {
    anonymous: false,
    inputs: [
      {
        indexed: true,
        internalType: "address",
        name: "previousOwner",
        type: "address",
      },
      {
        indexed: true,
        internalType: "address",
        name: "newOwner",
        type: "address",
      },
    ],
    name: "OwnershipTransferred",
    type: "event",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "asset",
        type: "address",
      },
      {
        internalType: "uint256",
        name: "amount",
        type: "uint256",
      },
      {
        internalType: "bytes",
        name: "params",
        type: "bytes",
      },
    ],
    name: "executeOptimalFlashloan",
    outputs: [],
    stateMutability: "nonpayable",
    type: "function",
  },
  {
    inputs: [],
    name: "owner",
    outputs: [
      {
        internalType: "address",
        name: "",
        type: "address",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
  {
    inputs: [],
    name: "renounceOwnership",
    outputs: [],
    stateMutability: "nonpayable",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "newOwner",
        type: "address",
      },
    ],
    name: "transferOwnership",
    outputs: [],
    stateMutability: "nonpayable",
    type: "function",
  },
] as const;

const _bytecode =
  "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";

type MinimalFlashloanTestConstructorParams =
  | [signer?: Signer]
  | ConstructorParameters<typeof ContractFactory>;

const isSuperArgs = (
  xs: MinimalFlashloanTestConstructorParams
): xs is ConstructorParameters<typeof ContractFactory> => xs.length > 1;

export class MinimalFlashloanTest__factory extends ContractFactory {
  constructor(...args: MinimalFlashloanTestConstructorParams) {
    if (isSuperArgs(args)) {
      super(...args);
    } else {
      super(_abi, _bytecode, args[0]);
    }
  }

  override getDeployTransaction(
    overrides?: NonPayableOverrides & { from?: string }
  ): Promise<ContractDeployTransaction> {
    return super.getDeployTransaction(overrides || {});
  }
  override deploy(overrides?: NonPayableOverrides & { from?: string }) {
    return super.deploy(overrides || {}) as Promise<
      MinimalFlashloanTest & {
        deploymentTransaction(): ContractTransactionResponse;
      }
    >;
  }
  override connect(
    runner: ContractRunner | null
  ): MinimalFlashloanTest__factory {
    return super.connect(runner) as MinimalFlashloanTest__factory;
  }

  static readonly bytecode = _bytecode;
  static readonly abi = _abi;
  static createInterface(): MinimalFlashloanTestInterface {
    return new Interface(_abi) as MinimalFlashloanTestInterface;
  }
  static connect(
    address: string,
    runner?: ContractRunner | null
  ): MinimalFlashloanTest {
    return new Contract(
      address,
      _abi,
      runner
    ) as unknown as MinimalFlashloanTest;
  }
}
