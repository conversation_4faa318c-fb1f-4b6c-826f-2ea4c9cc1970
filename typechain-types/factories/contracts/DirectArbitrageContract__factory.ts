/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */
import {
  Contract,
  ContractFactory,
  ContractTransactionResponse,
  Interface,
} from "ethers";
import type { Signer, ContractDeployTransaction, ContractRunner } from "ethers";
import type { NonPayableOverrides } from "../../common";
import type {
  DirectArbitrageContract,
  DirectArbitrageContractInterface,
} from "../../contracts/DirectArbitrageContract";

const _abi = [
  {
    inputs: [],
    stateMutability: "nonpayable",
    type: "constructor",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "owner",
        type: "address",
      },
    ],
    name: "OwnableInvalidOwner",
    type: "error",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "account",
        type: "address",
      },
    ],
    name: "OwnableUnauthorizedAccount",
    type: "error",
  },
  {
    anonymous: false,
    inputs: [
      {
        indexed: false,
        internalType: "address",
        name: "tokenA",
        type: "address",
      },
      {
        indexed: false,
        internalType: "address",
        name: "tokenB",
        type: "address",
      },
      {
        indexed: false,
        internalType: "uint256",
        name: "amountIn",
        type: "uint256",
      },
      {
        indexed: false,
        internalType: "uint256",
        name: "profit",
        type: "uint256",
      },
      {
        indexed: false,
        internalType: "enum DirectArbitrageContract.FlashloanProvider",
        name: "provider",
        type: "uint8",
      },
    ],
    name: "ArbitrageExecuted",
    type: "event",
  },
  {
    anonymous: false,
    inputs: [
      {
        indexed: true,
        internalType: "address",
        name: "previousOwner",
        type: "address",
      },
      {
        indexed: true,
        internalType: "address",
        name: "newOwner",
        type: "address",
      },
    ],
    name: "OwnershipTransferred",
    type: "event",
  },
  {
    inputs: [],
    name: "CHAIN_ID",
    outputs: [
      {
        internalType: "uint256",
        name: "",
        type: "uint256",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
  {
    inputs: [],
    name: "UNISWAP_V2_ROUTER",
    outputs: [
      {
        internalType: "address",
        name: "",
        type: "address",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
  {
    inputs: [],
    name: "UNISWAP_V3_ROUTER",
    outputs: [
      {
        internalType: "address",
        name: "",
        type: "address",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "asset",
        type: "address",
      },
      {
        internalType: "uint256",
        name: "amount",
        type: "uint256",
      },
      {
        internalType: "bytes",
        name: "params",
        type: "bytes",
      },
    ],
    name: "executeDirectArbitrage",
    outputs: [
      {
        internalType: "bool",
        name: "",
        type: "bool",
      },
    ],
    stateMutability: "nonpayable",
    type: "function",
  },
  {
    inputs: [],
    name: "owner",
    outputs: [
      {
        internalType: "address",
        name: "",
        type: "address",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
  {
    inputs: [],
    name: "renounceOwnership",
    outputs: [],
    stateMutability: "nonpayable",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "asset",
        type: "address",
      },
      {
        internalType: "uint256",
        name: "amount",
        type: "uint256",
      },
      {
        internalType: "bytes",
        name: "params",
        type: "bytes",
      },
    ],
    name: "testOriginalFlow",
    outputs: [
      {
        internalType: "bool",
        name: "",
        type: "bool",
      },
    ],
    stateMutability: "nonpayable",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "newOwner",
        type: "address",
      },
    ],
    name: "transferOwnership",
    outputs: [],
    stateMutability: "nonpayable",
    type: "function",
  },
] as const;

const _bytecode =
  "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";

type DirectArbitrageContractConstructorParams =
  | [signer?: Signer]
  | ConstructorParameters<typeof ContractFactory>;

const isSuperArgs = (
  xs: DirectArbitrageContractConstructorParams
): xs is ConstructorParameters<typeof ContractFactory> => xs.length > 1;

export class DirectArbitrageContract__factory extends ContractFactory {
  constructor(...args: DirectArbitrageContractConstructorParams) {
    if (isSuperArgs(args)) {
      super(...args);
    } else {
      super(_abi, _bytecode, args[0]);
    }
  }

  override getDeployTransaction(
    overrides?: NonPayableOverrides & { from?: string }
  ): Promise<ContractDeployTransaction> {
    return super.getDeployTransaction(overrides || {});
  }
  override deploy(overrides?: NonPayableOverrides & { from?: string }) {
    return super.deploy(overrides || {}) as Promise<
      DirectArbitrageContract & {
        deploymentTransaction(): ContractTransactionResponse;
      }
    >;
  }
  override connect(
    runner: ContractRunner | null
  ): DirectArbitrageContract__factory {
    return super.connect(runner) as DirectArbitrageContract__factory;
  }

  static readonly bytecode = _bytecode;
  static readonly abi = _abi;
  static createInterface(): DirectArbitrageContractInterface {
    return new Interface(_abi) as DirectArbitrageContractInterface;
  }
  static connect(
    address: string,
    runner?: ContractRunner | null
  ): DirectArbitrageContract {
    return new Contract(
      address,
      _abi,
      runner
    ) as unknown as DirectArbitrageContract;
  }
}
