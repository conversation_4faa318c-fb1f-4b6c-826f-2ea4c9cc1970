/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */
import {
  Contract,
  ContractFactory,
  ContractTransactionResponse,
  Interface,
} from "ethers";
import type { Signer, ContractDeployTransaction, ContractRunner } from "ethers";
import type { NonPayableOverrides } from "../../common";
import type {
  SimpleEnumTest,
  SimpleEnumTestInterface,
} from "../../contracts/SimpleEnumTest";

const _abi = [
  {
    inputs: [],
    stateMutability: "nonpayable",
    type: "constructor",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "owner",
        type: "address",
      },
    ],
    name: "OwnableInvalidOwner",
    type: "error",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "account",
        type: "address",
      },
    ],
    name: "OwnableUnauthorizedAccount",
    type: "error",
  },
  {
    anonymous: false,
    inputs: [
      {
        indexed: true,
        internalType: "address",
        name: "previousOwner",
        type: "address",
      },
      {
        indexed: true,
        internalType: "address",
        name: "newOwner",
        type: "address",
      },
    ],
    name: "OwnershipTransferred",
    type: "event",
  },
  {
    anonymous: false,
    inputs: [
      {
        indexed: false,
        internalType: "string",
        name: "message",
        type: "string",
      },
      {
        indexed: false,
        internalType: "uint256",
        name: "amount",
        type: "uint256",
      },
      {
        indexed: false,
        internalType: "uint8",
        name: "provider",
        type: "uint8",
      },
    ],
    name: "TestResult",
    type: "event",
  },
  {
    inputs: [
      {
        internalType: "bytes",
        name: "params",
        type: "bytes",
      },
    ],
    name: "decodeNoMinProfit",
    outputs: [],
    stateMutability: "pure",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "bytes",
        name: "params",
        type: "bytes",
      },
    ],
    name: "decodeProviderFirst",
    outputs: [],
    stateMutability: "pure",
    type: "function",
  },
  {
    inputs: [],
    name: "owner",
    outputs: [
      {
        internalType: "address",
        name: "",
        type: "address",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
  {
    inputs: [],
    name: "renounceOwnership",
    outputs: [],
    stateMutability: "nonpayable",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "bytes",
        name: "params",
        type: "bytes",
      },
    ],
    name: "testAlternativeStruct",
    outputs: [
      {
        internalType: "bool",
        name: "",
        type: "bool",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "bytes",
        name: "params",
        type: "bytes",
      },
    ],
    name: "testDecodeOnly",
    outputs: [
      {
        components: [
          {
            internalType: "address",
            name: "tokenA",
            type: "address",
          },
          {
            internalType: "address",
            name: "tokenB",
            type: "address",
          },
          {
            internalType: "address",
            name: "buyDex",
            type: "address",
          },
          {
            internalType: "address",
            name: "sellDex",
            type: "address",
          },
          {
            internalType: "uint24",
            name: "v3Fee",
            type: "uint24",
          },
          {
            internalType: "uint256",
            name: "minProfit",
            type: "uint256",
          },
          {
            internalType: "enum SimpleEnumTest.FlashloanProvider",
            name: "provider",
            type: "uint8",
          },
        ],
        internalType: "struct SimpleEnumTest.ArbitrageParams",
        name: "",
        type: "tuple",
      },
    ],
    stateMutability: "pure",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "uint256",
        name: "amount",
        type: "uint256",
      },
      {
        internalType: "bytes",
        name: "params",
        type: "bytes",
      },
    ],
    name: "testDecodeWithAmount",
    outputs: [
      {
        components: [
          {
            internalType: "address",
            name: "tokenA",
            type: "address",
          },
          {
            internalType: "address",
            name: "tokenB",
            type: "address",
          },
          {
            internalType: "address",
            name: "buyDex",
            type: "address",
          },
          {
            internalType: "address",
            name: "sellDex",
            type: "address",
          },
          {
            internalType: "uint24",
            name: "v3Fee",
            type: "uint24",
          },
          {
            internalType: "uint256",
            name: "minProfit",
            type: "uint256",
          },
          {
            internalType: "enum SimpleEnumTest.FlashloanProvider",
            name: "provider",
            type: "uint8",
          },
        ],
        internalType: "struct SimpleEnumTest.ArbitrageParams",
        name: "",
        type: "tuple",
      },
      {
        internalType: "uint256",
        name: "",
        type: "uint256",
      },
    ],
    stateMutability: "pure",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "uint8",
        name: "providerValue",
        type: "uint8",
      },
    ],
    name: "testEnumConversion",
    outputs: [
      {
        internalType: "enum SimpleEnumTest.FlashloanProvider",
        name: "",
        type: "uint8",
      },
    ],
    stateMutability: "pure",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "asset",
        type: "address",
      },
      {
        internalType: "uint256",
        name: "amount",
        type: "uint256",
      },
      {
        internalType: "bytes",
        name: "params",
        type: "bytes",
      },
    ],
    name: "testExecuteOptimalFlashloan",
    outputs: [
      {
        internalType: "bool",
        name: "",
        type: "bool",
      },
    ],
    stateMutability: "nonpayable",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "newOwner",
        type: "address",
      },
    ],
    name: "transferOwnership",
    outputs: [],
    stateMutability: "nonpayable",
    type: "function",
  },
] as const;

const _bytecode =
  "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";

type SimpleEnumTestConstructorParams =
  | [signer?: Signer]
  | ConstructorParameters<typeof ContractFactory>;

const isSuperArgs = (
  xs: SimpleEnumTestConstructorParams
): xs is ConstructorParameters<typeof ContractFactory> => xs.length > 1;

export class SimpleEnumTest__factory extends ContractFactory {
  constructor(...args: SimpleEnumTestConstructorParams) {
    if (isSuperArgs(args)) {
      super(...args);
    } else {
      super(_abi, _bytecode, args[0]);
    }
  }

  override getDeployTransaction(
    overrides?: NonPayableOverrides & { from?: string }
  ): Promise<ContractDeployTransaction> {
    return super.getDeployTransaction(overrides || {});
  }
  override deploy(overrides?: NonPayableOverrides & { from?: string }) {
    return super.deploy(overrides || {}) as Promise<
      SimpleEnumTest & {
        deploymentTransaction(): ContractTransactionResponse;
      }
    >;
  }
  override connect(runner: ContractRunner | null): SimpleEnumTest__factory {
    return super.connect(runner) as SimpleEnumTest__factory;
  }

  static readonly bytecode = _bytecode;
  static readonly abi = _abi;
  static createInterface(): SimpleEnumTestInterface {
    return new Interface(_abi) as SimpleEnumTestInterface;
  }
  static connect(
    address: string,
    runner?: ContractRunner | null
  ): SimpleEnumTest {
    return new Contract(address, _abi, runner) as unknown as SimpleEnumTest;
  }
}
