/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */

import { Contract, Interface, type ContractRunner } from "ethers";
import type {
  IAavePool,
  IAavePoolInterface,
} from "../../../contracts/HybridFlashloanArbitrageLight.sol/IAavePool";

const _abi = [
  {
    inputs: [
      {
        internalType: "address",
        name: "",
        type: "address",
      },
      {
        internalType: "address",
        name: "",
        type: "address",
      },
      {
        internalType: "uint256",
        name: "",
        type: "uint256",
      },
      {
        internalType: "bytes",
        name: "",
        type: "bytes",
      },
      {
        internalType: "uint16",
        name: "",
        type: "uint16",
      },
    ],
    name: "flashLoanSimple",
    outputs: [],
    stateMutability: "nonpayable",
    type: "function",
  },
] as const;

export class IAavePool__factory {
  static readonly abi = _abi;
  static createInterface(): IAavePoolInterface {
    return new Interface(_abi) as IAavePoolInterface;
  }
  static connect(address: string, runner?: ContractRunner | null): IAavePool {
    return new Contract(address, _abi, runner) as unknown as IAavePool;
  }
}
