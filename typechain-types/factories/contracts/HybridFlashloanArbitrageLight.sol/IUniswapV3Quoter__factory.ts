/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */

import { Contract, Interface, type ContractRunner } from "ethers";
import type {
  IUniswapV3Quoter,
  IUniswapV3QuoterInterface,
} from "../../../contracts/HybridFlashloanArbitrageLight.sol/IUniswapV3Quoter";

const _abi = [
  {
    inputs: [
      {
        internalType: "address",
        name: "",
        type: "address",
      },
      {
        internalType: "address",
        name: "",
        type: "address",
      },
      {
        internalType: "uint24",
        name: "",
        type: "uint24",
      },
      {
        internalType: "uint256",
        name: "",
        type: "uint256",
      },
      {
        internalType: "uint160",
        name: "",
        type: "uint160",
      },
    ],
    name: "quoteExactInputSingle",
    outputs: [
      {
        internalType: "uint256",
        name: "",
        type: "uint256",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
] as const;

export class IUniswapV3Quoter__factory {
  static readonly abi = _abi;
  static createInterface(): IUniswapV3QuoterInterface {
    return new Interface(_abi) as IUniswapV3QuoterInterface;
  }
  static connect(
    address: string,
    runner?: ContractRunner | null
  ): IUniswapV3Quoter {
    return new Contract(address, _abi, runner) as unknown as IUniswapV3Quoter;
  }
}
