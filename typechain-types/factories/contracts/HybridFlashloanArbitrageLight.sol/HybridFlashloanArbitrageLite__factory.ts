/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */
import {
  Contract,
  ContractFactory,
  ContractTransactionResponse,
  Interface,
} from "ethers";
import type {
  Signer,
  AddressLike,
  ContractDeployTransaction,
  ContractRunner,
} from "ethers";
import type { NonPayableOverrides } from "../../../common";
import type {
  HybridFlashloanArbitrageLite,
  HybridFlashloanArbitrageLiteInterface,
} from "../../../contracts/HybridFlashloanArbitrageLight.sol/HybridFlashloanArbitrageLite";

const _abi = [
  {
    inputs: [
      {
        internalType: "address",
        name: "_aavePool",
        type: "address",
      },
      {
        internalType: "address",
        name: "_balancerVault",
        type: "address",
      },
      {
        internalType: "address",
        name: "_uniswapV3Quoter",
        type: "address",
      },
    ],
    stateMutability: "nonpayable",
    type: "constructor",
  },
  {
    inputs: [],
    name: "EnforcedPause",
    type: "error",
  },
  {
    inputs: [],
    name: "ExpectedPause",
    type: "error",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "owner",
        type: "address",
      },
    ],
    name: "OwnableInvalidOwner",
    type: "error",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "account",
        type: "address",
      },
    ],
    name: "OwnableUnauthorizedAccount",
    type: "error",
  },
  {
    inputs: [],
    name: "ReentrancyGuardReentrantCall",
    type: "error",
  },
  {
    anonymous: false,
    inputs: [
      {
        indexed: true,
        internalType: "enum HybridFlashloanArbitrageLite.FlashloanProvider",
        name: "provider",
        type: "uint8",
      },
      {
        indexed: true,
        internalType: "address",
        name: "asset",
        type: "address",
      },
      {
        indexed: false,
        internalType: "uint256",
        name: "amount",
        type: "uint256",
      },
      {
        indexed: false,
        internalType: "uint256",
        name: "netProfit",
        type: "uint256",
      },
    ],
    name: "ArbitrageExecuted",
    type: "event",
  },
  {
    anonymous: false,
    inputs: [
      {
        indexed: true,
        internalType: "address",
        name: "previousOwner",
        type: "address",
      },
      {
        indexed: true,
        internalType: "address",
        name: "newOwner",
        type: "address",
      },
    ],
    name: "OwnershipTransferred",
    type: "event",
  },
  {
    anonymous: false,
    inputs: [
      {
        indexed: false,
        internalType: "address",
        name: "account",
        type: "address",
      },
    ],
    name: "Paused",
    type: "event",
  },
  {
    anonymous: false,
    inputs: [
      {
        indexed: false,
        internalType: "address",
        name: "account",
        type: "address",
      },
    ],
    name: "Unpaused",
    type: "event",
  },
  {
    inputs: [],
    name: "AAVE_POOL",
    outputs: [
      {
        internalType: "contract IAavePool",
        name: "",
        type: "address",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
  {
    inputs: [],
    name: "BALANCER_VAULT",
    outputs: [
      {
        internalType: "contract IBalancerVault",
        name: "",
        type: "address",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
  {
    inputs: [],
    name: "UNISWAP_V3_QUOTER",
    outputs: [
      {
        internalType: "contract IUniswapV3Quoter",
        name: "",
        type: "address",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "asset",
        type: "address",
      },
      {
        internalType: "uint256",
        name: "amount",
        type: "uint256",
      },
      {
        internalType: "uint256",
        name: "premium",
        type: "uint256",
      },
      {
        internalType: "address",
        name: "",
        type: "address",
      },
      {
        internalType: "bytes",
        name: "params",
        type: "bytes",
      },
    ],
    name: "executeOperation",
    outputs: [
      {
        internalType: "bool",
        name: "",
        type: "bool",
      },
    ],
    stateMutability: "nonpayable",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "asset",
        type: "address",
      },
      {
        internalType: "uint256",
        name: "amount",
        type: "uint256",
      },
      {
        internalType: "bytes",
        name: "params",
        type: "bytes",
      },
    ],
    name: "executeOptimalFlashloan",
    outputs: [],
    stateMutability: "nonpayable",
    type: "function",
  },
  {
    inputs: [],
    name: "owner",
    outputs: [
      {
        internalType: "address",
        name: "",
        type: "address",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
  {
    inputs: [],
    name: "pause",
    outputs: [],
    stateMutability: "nonpayable",
    type: "function",
  },
  {
    inputs: [],
    name: "paused",
    outputs: [
      {
        internalType: "bool",
        name: "",
        type: "bool",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "contract IERC20[]",
        name: "",
        type: "address[]",
      },
      {
        internalType: "uint256[]",
        name: "amounts",
        type: "uint256[]",
      },
      {
        internalType: "uint256[]",
        name: "",
        type: "uint256[]",
      },
      {
        internalType: "bytes",
        name: "userData",
        type: "bytes",
      },
    ],
    name: "receiveFlashLoan",
    outputs: [],
    stateMutability: "nonpayable",
    type: "function",
  },
  {
    inputs: [],
    name: "renounceOwnership",
    outputs: [],
    stateMutability: "nonpayable",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "newOwner",
        type: "address",
      },
    ],
    name: "transferOwnership",
    outputs: [],
    stateMutability: "nonpayable",
    type: "function",
  },
  {
    inputs: [],
    name: "unpause",
    outputs: [],
    stateMutability: "nonpayable",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "token",
        type: "address",
      },
    ],
    name: "withdraw",
    outputs: [],
    stateMutability: "nonpayable",
    type: "function",
  },
  {
    stateMutability: "payable",
    type: "receive",
  },
] as const;

const _bytecode =
  "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";

type HybridFlashloanArbitrageLiteConstructorParams =
  | [signer?: Signer]
  | ConstructorParameters<typeof ContractFactory>;

const isSuperArgs = (
  xs: HybridFlashloanArbitrageLiteConstructorParams
): xs is ConstructorParameters<typeof ContractFactory> => xs.length > 1;

export class HybridFlashloanArbitrageLite__factory extends ContractFactory {
  constructor(...args: HybridFlashloanArbitrageLiteConstructorParams) {
    if (isSuperArgs(args)) {
      super(...args);
    } else {
      super(_abi, _bytecode, args[0]);
    }
  }

  override getDeployTransaction(
    _aavePool: AddressLike,
    _balancerVault: AddressLike,
    _uniswapV3Quoter: AddressLike,
    overrides?: NonPayableOverrides & { from?: string }
  ): Promise<ContractDeployTransaction> {
    return super.getDeployTransaction(
      _aavePool,
      _balancerVault,
      _uniswapV3Quoter,
      overrides || {}
    );
  }
  override deploy(
    _aavePool: AddressLike,
    _balancerVault: AddressLike,
    _uniswapV3Quoter: AddressLike,
    overrides?: NonPayableOverrides & { from?: string }
  ) {
    return super.deploy(
      _aavePool,
      _balancerVault,
      _uniswapV3Quoter,
      overrides || {}
    ) as Promise<
      HybridFlashloanArbitrageLite & {
        deploymentTransaction(): ContractTransactionResponse;
      }
    >;
  }
  override connect(
    runner: ContractRunner | null
  ): HybridFlashloanArbitrageLite__factory {
    return super.connect(runner) as HybridFlashloanArbitrageLite__factory;
  }

  static readonly bytecode = _bytecode;
  static readonly abi = _abi;
  static createInterface(): HybridFlashloanArbitrageLiteInterface {
    return new Interface(_abi) as HybridFlashloanArbitrageLiteInterface;
  }
  static connect(
    address: string,
    runner?: ContractRunner | null
  ): HybridFlashloanArbitrageLite {
    return new Contract(
      address,
      _abi,
      runner
    ) as unknown as HybridFlashloanArbitrageLite;
  }
}
