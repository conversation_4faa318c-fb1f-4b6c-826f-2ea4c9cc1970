/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */
import {
  Contract,
  ContractFactory,
  ContractTransactionResponse,
  Interface,
} from "ethers";
import type {
  Signer,
  AddressLike,
  ContractDeployTransaction,
  ContractRunner,
} from "ethers";
import type { NonPayableOverrides } from "../../common";
import type {
  MinimalAaveReceiver,
  MinimalAaveReceiverInterface,
} from "../../contracts/MinimalAaveReceiver";

const _abi = [
  {
    inputs: [
      {
        internalType: "contract IPoolAddressesProvider",
        name: "_addressProvider",
        type: "address",
      },
    ],
    stateMutability: "nonpayable",
    type: "constructor",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "owner",
        type: "address",
      },
    ],
    name: "OwnableInvalidOwner",
    type: "error",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "account",
        type: "address",
      },
    ],
    name: "OwnableUnauthorizedAccount",
    type: "error",
  },
  {
    anonymous: false,
    inputs: [
      {
        indexed: true,
        internalType: "address",
        name: "previousOwner",
        type: "address",
      },
      {
        indexed: true,
        internalType: "address",
        name: "newOwner",
        type: "address",
      },
    ],
    name: "OwnershipTransferred",
    type: "event",
  },
  {
    inputs: [],
    name: "ADDRESSES_PROVIDER",
    outputs: [
      {
        internalType: "contract IPoolAddressesProvider",
        name: "",
        type: "address",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
  {
    inputs: [],
    name: "POOL",
    outputs: [
      {
        internalType: "contract IPool",
        name: "",
        type: "address",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "asset",
        type: "address",
      },
      {
        internalType: "uint256",
        name: "amount",
        type: "uint256",
      },
      {
        internalType: "uint256",
        name: "premium",
        type: "uint256",
      },
      {
        internalType: "address",
        name: "initiator",
        type: "address",
      },
      {
        internalType: "bytes",
        name: "params",
        type: "bytes",
      },
    ],
    name: "executeOperation",
    outputs: [
      {
        internalType: "bool",
        name: "",
        type: "bool",
      },
    ],
    stateMutability: "nonpayable",
    type: "function",
  },
  {
    inputs: [],
    name: "owner",
    outputs: [
      {
        internalType: "address",
        name: "",
        type: "address",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
  {
    inputs: [],
    name: "renounceOwnership",
    outputs: [],
    stateMutability: "nonpayable",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "asset",
        type: "address",
      },
      {
        internalType: "uint256",
        name: "amount",
        type: "uint256",
      },
    ],
    name: "testFlashloan",
    outputs: [],
    stateMutability: "nonpayable",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "newOwner",
        type: "address",
      },
    ],
    name: "transferOwnership",
    outputs: [],
    stateMutability: "nonpayable",
    type: "function",
  },
] as const;

const _bytecode =
  "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";

type MinimalAaveReceiverConstructorParams =
  | [signer?: Signer]
  | ConstructorParameters<typeof ContractFactory>;

const isSuperArgs = (
  xs: MinimalAaveReceiverConstructorParams
): xs is ConstructorParameters<typeof ContractFactory> => xs.length > 1;

export class MinimalAaveReceiver__factory extends ContractFactory {
  constructor(...args: MinimalAaveReceiverConstructorParams) {
    if (isSuperArgs(args)) {
      super(...args);
    } else {
      super(_abi, _bytecode, args[0]);
    }
  }

  override getDeployTransaction(
    _addressProvider: AddressLike,
    overrides?: NonPayableOverrides & { from?: string }
  ): Promise<ContractDeployTransaction> {
    return super.getDeployTransaction(_addressProvider, overrides || {});
  }
  override deploy(
    _addressProvider: AddressLike,
    overrides?: NonPayableOverrides & { from?: string }
  ) {
    return super.deploy(_addressProvider, overrides || {}) as Promise<
      MinimalAaveReceiver & {
        deploymentTransaction(): ContractTransactionResponse;
      }
    >;
  }
  override connect(
    runner: ContractRunner | null
  ): MinimalAaveReceiver__factory {
    return super.connect(runner) as MinimalAaveReceiver__factory;
  }

  static readonly bytecode = _bytecode;
  static readonly abi = _abi;
  static createInterface(): MinimalAaveReceiverInterface {
    return new Interface(_abi) as MinimalAaveReceiverInterface;
  }
  static connect(
    address: string,
    runner?: ContractRunner | null
  ): MinimalAaveReceiver {
    return new Contract(
      address,
      _abi,
      runner
    ) as unknown as MinimalAaveReceiver;
  }
}
