/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */
import {
  Contract,
  ContractFactory,
  ContractTransactionResponse,
  Interface,
} from "ethers";
import type { Signer, ContractDeployTransaction, ContractRunner } from "ethers";
import type { NonPayableOverrides } from "../../common";
import type {
  AbiDecodeTest,
  AbiDecodeTestInterface,
} from "../../contracts/AbiDecodeTest";

const _abi = [
  {
    inputs: [
      {
        internalType: "bytes",
        name: "params",
        type: "bytes",
      },
    ],
    name: "testDecode",
    outputs: [
      {
        components: [
          {
            internalType: "address",
            name: "tokenA",
            type: "address",
          },
          {
            internalType: "address",
            name: "tokenB",
            type: "address",
          },
          {
            internalType: "address",
            name: "buyDex",
            type: "address",
          },
          {
            internalType: "address",
            name: "sellDex",
            type: "address",
          },
          {
            internalType: "uint24",
            name: "v3Fee",
            type: "uint24",
          },
          {
            internalType: "uint256",
            name: "minProfit",
            type: "uint256",
          },
          {
            internalType: "enum AbiDecodeTest.FlashloanProvider",
            name: "provider",
            type: "uint8",
          },
        ],
        internalType: "struct AbiDecodeTest.ArbitrageParams",
        name: "",
        type: "tuple",
      },
    ],
    stateMutability: "pure",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "uint256",
        name: "amount",
        type: "uint256",
      },
      {
        internalType: "bytes",
        name: "params",
        type: "bytes",
      },
    ],
    name: "testDecodeWithAmount",
    outputs: [
      {
        components: [
          {
            internalType: "address",
            name: "tokenA",
            type: "address",
          },
          {
            internalType: "address",
            name: "tokenB",
            type: "address",
          },
          {
            internalType: "address",
            name: "buyDex",
            type: "address",
          },
          {
            internalType: "address",
            name: "sellDex",
            type: "address",
          },
          {
            internalType: "uint24",
            name: "v3Fee",
            type: "uint24",
          },
          {
            internalType: "uint256",
            name: "minProfit",
            type: "uint256",
          },
          {
            internalType: "enum AbiDecodeTest.FlashloanProvider",
            name: "provider",
            type: "uint8",
          },
        ],
        internalType: "struct AbiDecodeTest.ArbitrageParams",
        name: "",
        type: "tuple",
      },
      {
        internalType: "uint256",
        name: "",
        type: "uint256",
      },
    ],
    stateMutability: "pure",
    type: "function",
  },
] as const;

const _bytecode =
  "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";

type AbiDecodeTestConstructorParams =
  | [signer?: Signer]
  | ConstructorParameters<typeof ContractFactory>;

const isSuperArgs = (
  xs: AbiDecodeTestConstructorParams
): xs is ConstructorParameters<typeof ContractFactory> => xs.length > 1;

export class AbiDecodeTest__factory extends ContractFactory {
  constructor(...args: AbiDecodeTestConstructorParams) {
    if (isSuperArgs(args)) {
      super(...args);
    } else {
      super(_abi, _bytecode, args[0]);
    }
  }

  override getDeployTransaction(
    overrides?: NonPayableOverrides & { from?: string }
  ): Promise<ContractDeployTransaction> {
    return super.getDeployTransaction(overrides || {});
  }
  override deploy(overrides?: NonPayableOverrides & { from?: string }) {
    return super.deploy(overrides || {}) as Promise<
      AbiDecodeTest & {
        deploymentTransaction(): ContractTransactionResponse;
      }
    >;
  }
  override connect(runner: ContractRunner | null): AbiDecodeTest__factory {
    return super.connect(runner) as AbiDecodeTest__factory;
  }

  static readonly bytecode = _bytecode;
  static readonly abi = _abi;
  static createInterface(): AbiDecodeTestInterface {
    return new Interface(_abi) as AbiDecodeTestInterface;
  }
  static connect(
    address: string,
    runner?: ContractRunner | null
  ): AbiDecodeTest {
    return new Contract(address, _abi, runner) as unknown as AbiDecodeTest;
  }
}
