/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */

import { Contract, Interface, type ContractRunner } from "ethers";
import type {
  IUniswapV3QuoterV2,
  IUniswapV3QuoterV2Interface,
} from "../../../contracts/HybridFlashloanArbitrageFixed.sol/IUniswapV3QuoterV2";

const _abi = [
  {
    inputs: [
      {
        internalType: "address",
        name: "tokenIn",
        type: "address",
      },
      {
        internalType: "address",
        name: "tokenOut",
        type: "address",
      },
      {
        internalType: "uint24",
        name: "fee",
        type: "uint24",
      },
      {
        internalType: "uint256",
        name: "amountIn",
        type: "uint256",
      },
      {
        internalType: "uint160",
        name: "sqrtPriceLimitX96",
        type: "uint160",
      },
    ],
    name: "quoteExactInputSingle",
    outputs: [
      {
        internalType: "uint256",
        name: "amountOut",
        type: "uint256",
      },
      {
        internalType: "uint160",
        name: "sqrtPriceX96After",
        type: "uint160",
      },
      {
        internalType: "uint32",
        name: "initializedTicksCrossed",
        type: "uint32",
      },
      {
        internalType: "uint256",
        name: "gasEstimate",
        type: "uint256",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
] as const;

export class IUniswapV3QuoterV2__factory {
  static readonly abi = _abi;
  static createInterface(): IUniswapV3QuoterV2Interface {
    return new Interface(_abi) as IUniswapV3QuoterV2Interface;
  }
  static connect(
    address: string,
    runner?: ContractRunner | null
  ): IUniswapV3QuoterV2 {
    return new Contract(address, _abi, runner) as unknown as IUniswapV3QuoterV2;
  }
}
