/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */
import {
  Contract,
  ContractFactory,
  ContractTransactionResponse,
  Interface,
} from "ethers";
import type {
  Signer,
  AddressLike,
  ContractDeployTransaction,
  ContractRunner,
} from "ethers";
import type { NonPayableOverrides } from "../../../common";
import type {
  HybridFlashloanArbitrageFixed,
  HybridFlashloanArbitrageFixedInterface,
} from "../../../contracts/HybridFlashloanArbitrageFixed.sol/HybridFlashloanArbitrageFixed";

const _abi = [
  {
    inputs: [
      {
        internalType: "address",
        name: "_aavePool",
        type: "address",
      },
      {
        internalType: "address",
        name: "_balancerVault",
        type: "address",
      },
    ],
    stateMutability: "nonpayable",
    type: "constructor",
  },
  {
    inputs: [],
    name: "EnforcedPause",
    type: "error",
  },
  {
    inputs: [],
    name: "ExpectedPause",
    type: "error",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "owner",
        type: "address",
      },
    ],
    name: "OwnableInvalidOwner",
    type: "error",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "account",
        type: "address",
      },
    ],
    name: "OwnableUnauthorizedAccount",
    type: "error",
  },
  {
    inputs: [],
    name: "ReentrancyGuardReentrantCall",
    type: "error",
  },
  {
    anonymous: false,
    inputs: [
      {
        indexed: true,
        internalType: "address",
        name: "asset",
        type: "address",
      },
      {
        indexed: false,
        internalType: "uint256",
        name: "amount",
        type: "uint256",
      },
      {
        indexed: false,
        internalType: "uint256",
        name: "profit",
        type: "uint256",
      },
      {
        indexed: false,
        internalType: "address[]",
        name: "buyPath",
        type: "address[]",
      },
      {
        indexed: false,
        internalType: "address[]",
        name: "sellPath",
        type: "address[]",
      },
    ],
    name: "ArbitrageExecuted",
    type: "event",
  },
  {
    anonymous: false,
    inputs: [
      {
        indexed: true,
        internalType: "address",
        name: "tokenA",
        type: "address",
      },
      {
        indexed: true,
        internalType: "address",
        name: "tokenB",
        type: "address",
      },
      {
        indexed: true,
        internalType: "bytes32",
        name: "poolId",
        type: "bytes32",
      },
      {
        indexed: false,
        internalType: "uint256",
        name: "feePercentage",
        type: "uint256",
      },
    ],
    name: "BalancerPoolAdded",
    type: "event",
  },
  {
    anonymous: false,
    inputs: [
      {
        indexed: true,
        internalType: "address",
        name: "tokenA",
        type: "address",
      },
      {
        indexed: true,
        internalType: "address",
        name: "tokenB",
        type: "address",
      },
      {
        indexed: true,
        internalType: "bytes32",
        name: "poolId",
        type: "bytes32",
      },
    ],
    name: "BalancerPoolRemoved",
    type: "event",
  },
  {
    anonymous: false,
    inputs: [
      {
        indexed: true,
        internalType: "enum HybridFlashloanArbitrageFixed.FlashloanProvider",
        name: "provider",
        type: "uint8",
      },
      {
        indexed: true,
        internalType: "address",
        name: "asset",
        type: "address",
      },
      {
        indexed: false,
        internalType: "uint256",
        name: "amount",
        type: "uint256",
      },
      {
        indexed: false,
        internalType: "uint256",
        name: "premium",
        type: "uint256",
      },
      {
        indexed: false,
        internalType: "uint256",
        name: "profit",
        type: "uint256",
      },
    ],
    name: "FlashloanExecuted",
    type: "event",
  },
  {
    anonymous: false,
    inputs: [
      {
        indexed: true,
        internalType: "address",
        name: "previousOwner",
        type: "address",
      },
      {
        indexed: true,
        internalType: "address",
        name: "newOwner",
        type: "address",
      },
    ],
    name: "OwnershipTransferred",
    type: "event",
  },
  {
    anonymous: false,
    inputs: [
      {
        indexed: false,
        internalType: "address",
        name: "account",
        type: "address",
      },
    ],
    name: "Paused",
    type: "event",
  },
  {
    anonymous: false,
    inputs: [
      {
        indexed: false,
        internalType: "address",
        name: "account",
        type: "address",
      },
    ],
    name: "Unpaused",
    type: "event",
  },
  {
    inputs: [],
    name: "AAVE_POOL",
    outputs: [
      {
        internalType: "contract IAavePool",
        name: "",
        type: "address",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
  {
    inputs: [],
    name: "BALANCER_VAULT",
    outputs: [
      {
        internalType: "contract IBalancerVault",
        name: "",
        type: "address",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
  {
    inputs: [],
    name: "CHAIN_ID",
    outputs: [
      {
        internalType: "uint256",
        name: "",
        type: "uint256",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
  {
    inputs: [],
    name: "UNISWAP_V2_ROUTER",
    outputs: [
      {
        internalType: "address",
        name: "",
        type: "address",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
  {
    inputs: [],
    name: "UNISWAP_V3_QUOTER",
    outputs: [
      {
        internalType: "address",
        name: "",
        type: "address",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
  {
    inputs: [],
    name: "UNISWAP_V3_ROUTER",
    outputs: [
      {
        internalType: "address",
        name: "",
        type: "address",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "",
        type: "address",
      },
      {
        internalType: "uint256",
        name: "amount",
        type: "uint256",
      },
      {
        components: [
          {
            internalType: "address[]",
            name: "buyPath",
            type: "address[]",
          },
          {
            internalType: "address[]",
            name: "sellPath",
            type: "address[]",
          },
          {
            internalType: "address",
            name: "buyDex",
            type: "address",
          },
          {
            internalType: "address",
            name: "sellDex",
            type: "address",
          },
          {
            internalType: "uint24[]",
            name: "v3Fees",
            type: "uint24[]",
          },
          {
            internalType: "uint256",
            name: "minProfit",
            type: "uint256",
          },
          {
            internalType:
              "enum HybridFlashloanArbitrageFixed.FlashloanProvider",
            name: "provider",
            type: "uint8",
          },
          {
            internalType: "uint256",
            name: "slippageToleranceBps",
            type: "uint256",
          },
          {
            internalType: "uint256",
            name: "maxGasCostWei",
            type: "uint256",
          },
        ],
        internalType: "struct HybridFlashloanArbitrageFixed.ArbitrageParams",
        name: "params",
        type: "tuple",
      },
    ],
    name: "_simulateArbitrage",
    outputs: [
      {
        internalType: "uint256",
        name: "profit",
        type: "uint256",
      },
    ],
    stateMutability: "nonpayable",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "tokenA",
        type: "address",
      },
      {
        internalType: "address",
        name: "tokenB",
        type: "address",
      },
      {
        internalType: "bytes32",
        name: "poolId",
        type: "bytes32",
      },
      {
        internalType: "uint256",
        name: "feePercentage",
        type: "uint256",
      },
    ],
    name: "addBalancerPool",
    outputs: [],
    stateMutability: "nonpayable",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "bytes32",
        name: "",
        type: "bytes32",
      },
    ],
    name: "balancerPoolExists",
    outputs: [
      {
        internalType: "bool",
        name: "",
        type: "bool",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "bytes32",
        name: "",
        type: "bytes32",
      },
    ],
    name: "balancerPoolFees",
    outputs: [
      {
        internalType: "uint256",
        name: "",
        type: "uint256",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "",
        type: "address",
      },
      {
        internalType: "address",
        name: "",
        type: "address",
      },
    ],
    name: "balancerPoolIds",
    outputs: [
      {
        internalType: "bytes32",
        name: "",
        type: "bytes32",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "asset",
        type: "address",
      },
      {
        internalType: "uint256",
        name: "amount",
        type: "uint256",
      },
      {
        internalType: "bytes",
        name: "params",
        type: "bytes",
      },
    ],
    name: "checkProfitability",
    outputs: [
      {
        internalType: "bool",
        name: "profitable",
        type: "bool",
      },
      {
        internalType: "uint256",
        name: "expectedProfit",
        type: "uint256",
      },
      {
        internalType: "uint256",
        name: "gasEstimate",
        type: "uint256",
      },
    ],
    stateMutability: "nonpayable",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "token",
        type: "address",
      },
    ],
    name: "emergencyWithdraw",
    outputs: [],
    stateMutability: "nonpayable",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "asset",
        type: "address",
      },
      {
        internalType: "uint256",
        name: "amount",
        type: "uint256",
      },
      {
        internalType: "uint256",
        name: "premium",
        type: "uint256",
      },
      {
        internalType: "address",
        name: "initiator",
        type: "address",
      },
      {
        internalType: "bytes",
        name: "params",
        type: "bytes",
      },
    ],
    name: "executeOperation",
    outputs: [
      {
        internalType: "bool",
        name: "",
        type: "bool",
      },
    ],
    stateMutability: "nonpayable",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "asset",
        type: "address",
      },
      {
        internalType: "uint256",
        name: "amount",
        type: "uint256",
      },
      {
        internalType: "bytes",
        name: "params",
        type: "bytes",
      },
    ],
    name: "executeOptimalFlashloan",
    outputs: [],
    stateMutability: "nonpayable",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "tokenA",
        type: "address",
      },
      {
        internalType: "address",
        name: "tokenB",
        type: "address",
      },
    ],
    name: "getBalancerPoolInfo",
    outputs: [
      {
        internalType: "bytes32",
        name: "poolId",
        type: "bytes32",
      },
      {
        internalType: "bool",
        name: "exists",
        type: "bool",
      },
      {
        internalType: "uint256",
        name: "feePercentage",
        type: "uint256",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
  {
    inputs: [],
    name: "owner",
    outputs: [
      {
        internalType: "address",
        name: "",
        type: "address",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
  {
    inputs: [],
    name: "pause",
    outputs: [],
    stateMutability: "nonpayable",
    type: "function",
  },
  {
    inputs: [],
    name: "paused",
    outputs: [
      {
        internalType: "bool",
        name: "",
        type: "bool",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "contract IERC20[]",
        name: "tokens",
        type: "address[]",
      },
      {
        internalType: "uint256[]",
        name: "amounts",
        type: "uint256[]",
      },
      {
        internalType: "uint256[]",
        name: "",
        type: "uint256[]",
      },
      {
        internalType: "bytes",
        name: "userData",
        type: "bytes",
      },
    ],
    name: "receiveFlashLoan",
    outputs: [],
    stateMutability: "nonpayable",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "tokenA",
        type: "address",
      },
      {
        internalType: "address",
        name: "tokenB",
        type: "address",
      },
      {
        internalType: "bytes32",
        name: "poolId",
        type: "bytes32",
      },
    ],
    name: "removeBalancerPool",
    outputs: [],
    stateMutability: "nonpayable",
    type: "function",
  },
  {
    inputs: [],
    name: "renounceOwnership",
    outputs: [],
    stateMutability: "nonpayable",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "",
        type: "address",
      },
    ],
    name: "supportedRouterTypes",
    outputs: [
      {
        internalType: "enum HybridFlashloanArbitrageFixed.DEX_TYPE",
        name: "",
        type: "uint8",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "router",
        type: "address",
      },
    ],
    name: "supportedRouters",
    outputs: [
      {
        internalType: "bool",
        name: "",
        type: "bool",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "newOwner",
        type: "address",
      },
    ],
    name: "transferOwnership",
    outputs: [],
    stateMutability: "nonpayable",
    type: "function",
  },
  {
    inputs: [],
    name: "unpause",
    outputs: [],
    stateMutability: "nonpayable",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "token",
        type: "address",
      },
      {
        internalType: "uint256",
        name: "amount",
        type: "uint256",
      },
    ],
    name: "withdrawProfits",
    outputs: [],
    stateMutability: "nonpayable",
    type: "function",
  },
  {
    stateMutability: "payable",
    type: "receive",
  },
] as const;

const _bytecode =
  "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";

type HybridFlashloanArbitrageFixedConstructorParams =
  | [signer?: Signer]
  | ConstructorParameters<typeof ContractFactory>;

const isSuperArgs = (
  xs: HybridFlashloanArbitrageFixedConstructorParams
): xs is ConstructorParameters<typeof ContractFactory> => xs.length > 1;

export class HybridFlashloanArbitrageFixed__factory extends ContractFactory {
  constructor(...args: HybridFlashloanArbitrageFixedConstructorParams) {
    if (isSuperArgs(args)) {
      super(...args);
    } else {
      super(_abi, _bytecode, args[0]);
    }
  }

  override getDeployTransaction(
    _aavePool: AddressLike,
    _balancerVault: AddressLike,
    overrides?: NonPayableOverrides & { from?: string }
  ): Promise<ContractDeployTransaction> {
    return super.getDeployTransaction(
      _aavePool,
      _balancerVault,
      overrides || {}
    );
  }
  override deploy(
    _aavePool: AddressLike,
    _balancerVault: AddressLike,
    overrides?: NonPayableOverrides & { from?: string }
  ) {
    return super.deploy(_aavePool, _balancerVault, overrides || {}) as Promise<
      HybridFlashloanArbitrageFixed & {
        deploymentTransaction(): ContractTransactionResponse;
      }
    >;
  }
  override connect(
    runner: ContractRunner | null
  ): HybridFlashloanArbitrageFixed__factory {
    return super.connect(runner) as HybridFlashloanArbitrageFixed__factory;
  }

  static readonly bytecode = _bytecode;
  static readonly abi = _abi;
  static createInterface(): HybridFlashloanArbitrageFixedInterface {
    return new Interface(_abi) as HybridFlashloanArbitrageFixedInterface;
  }
  static connect(
    address: string,
    runner?: ContractRunner | null
  ): HybridFlashloanArbitrageFixed {
    return new Contract(
      address,
      _abi,
      runner
    ) as unknown as HybridFlashloanArbitrageFixed;
  }
}
