/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */

import { Contract, Interface, type ContractRunner } from "ethers";
import type {
  IBalancerV2Pool,
  IBalancerV2PoolInterface,
} from "../../../contracts/HybridFlashloanArbitrageFixed.sol/IBalancerV2Pool";

const _abi = [
  {
    inputs: [],
    name: "getPoolId",
    outputs: [
      {
        internalType: "bytes32",
        name: "",
        type: "bytes32",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
  {
    inputs: [],
    name: "getSwapFeePercentage",
    outputs: [
      {
        internalType: "uint256",
        name: "",
        type: "uint256",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
  {
    inputs: [],
    name: "getVault",
    outputs: [
      {
        internalType: "address",
        name: "",
        type: "address",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
] as const;

export class IBalancerV2Pool__factory {
  static readonly abi = _abi;
  static createInterface(): IBalancerV2PoolInterface {
    return new Interface(_abi) as IBalancerV2PoolInterface;
  }
  static connect(
    address: string,
    runner?: ContractRunner | null
  ): IBalancerV2Pool {
    return new Contract(address, _abi, runner) as unknown as IBalancerV2Pool;
  }
}
