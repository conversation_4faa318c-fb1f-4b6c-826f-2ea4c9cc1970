/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */

import { Contract, Interface, type ContractRunner } from "ethers";
import type {
  IBalancerVault,
  IBalancerVaultInterface,
} from "../../../contracts/HybridFlashloanArbitrageFixed.sol/IBalancerVault";

const _abi = [
  {
    inputs: [
      {
        internalType: "address",
        name: "recipient",
        type: "address",
      },
      {
        internalType: "contract IERC20[]",
        name: "tokens",
        type: "address[]",
      },
      {
        internalType: "uint256[]",
        name: "amounts",
        type: "uint256[]",
      },
      {
        internalType: "bytes",
        name: "userData",
        type: "bytes",
      },
    ],
    name: "flashLoan",
    outputs: [],
    stateMutability: "nonpayable",
    type: "function",
  },
] as const;

export class IBalancerVault__factory {
  static readonly abi = _abi;
  static createInterface(): IBalancerVaultInterface {
    return new Interface(_abi) as IBalancerVaultInterface;
  }
  static connect(
    address: string,
    runner?: ContractRunner | null
  ): IBalancerVault {
    return new Contract(address, _abi, runner) as unknown as IBalancerVault;
  }
}
