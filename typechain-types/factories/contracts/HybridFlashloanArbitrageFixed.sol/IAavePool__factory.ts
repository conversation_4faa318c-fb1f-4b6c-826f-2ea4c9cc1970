/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */

import { Contract, Interface, type ContractRunner } from "ethers";
import type {
  IAavePool,
  IAavePoolInterface,
} from "../../../contracts/HybridFlashloanArbitrageFixed.sol/IAavePool";

const _abi = [
  {
    inputs: [
      {
        internalType: "address",
        name: "receiverAddress",
        type: "address",
      },
      {
        internalType: "address",
        name: "asset",
        type: "address",
      },
      {
        internalType: "uint256",
        name: "amount",
        type: "uint256",
      },
      {
        internalType: "bytes",
        name: "params",
        type: "bytes",
      },
      {
        internalType: "uint16",
        name: "referralCode",
        type: "uint16",
      },
    ],
    name: "flashLoanSimple",
    outputs: [],
    stateMutability: "nonpayable",
    type: "function",
  },
] as const;

export class IAavePool__factory {
  static readonly abi = _abi;
  static createInterface(): IAavePoolInterface {
    return new Interface(_abi) as IAavePoolInterface;
  }
  static connect(address: string, runner?: ContractRunner | null): IAavePool {
    return new Contract(address, _abi, runner) as unknown as IAavePool;
  }
}
