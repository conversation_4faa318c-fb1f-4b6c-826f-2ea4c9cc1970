/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */

import { Contract, Interface, type ContractRunner } from "ethers";
import type {
  IBalancerV2PoolRegistry,
  IBalancerV2PoolRegistryInterface,
} from "../../../contracts/HybridFlashloanArbitrageFixed.sol/IBalancerV2PoolRegistry";

const _abi = [
  {
    inputs: [
      {
        internalType: "address[]",
        name: "tokens",
        type: "address[]",
      },
    ],
    name: "getPoolsWithTokens",
    outputs: [
      {
        internalType: "bytes32[]",
        name: "",
        type: "bytes32[]",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "bytes32",
        name: "poolId",
        type: "bytes32",
      },
    ],
    name: "isPoolRegistered",
    outputs: [
      {
        internalType: "bool",
        name: "",
        type: "bool",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
] as const;

export class IBalancerV2PoolRegistry__factory {
  static readonly abi = _abi;
  static createInterface(): IBalancerV2PoolRegistryInterface {
    return new Interface(_abi) as IBalancerV2PoolRegistryInterface;
  }
  static connect(
    address: string,
    runner?: ContractRunner | null
  ): IBalancerV2PoolRegistry {
    return new Contract(
      address,
      _abi,
      runner
    ) as unknown as IBalancerV2PoolRegistry;
  }
}
