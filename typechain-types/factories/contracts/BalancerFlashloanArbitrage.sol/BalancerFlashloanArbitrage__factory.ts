/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */
import {
  Contract,
  ContractFactory,
  ContractTransactionResponse,
  Interface,
} from "ethers";
import type { Signer, ContractDeployTransaction, ContractRunner } from "ethers";
import type { NonPayableOverrides } from "../../../common";
import type {
  BalancerFlashloanArbitrage,
  BalancerFlashloanArbitrageInterface,
} from "../../../contracts/BalancerFlashloanArbitrage.sol/BalancerFlashloanArbitrage";

const _abi = [
  {
    inputs: [],
    stateMutability: "nonpayable",
    type: "constructor",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "owner",
        type: "address",
      },
    ],
    name: "OwnableInvalidOwner",
    type: "error",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "account",
        type: "address",
      },
    ],
    name: "OwnableUnauthorizedAccount",
    type: "error",
  },
  {
    anonymous: false,
    inputs: [
      {
        indexed: true,
        internalType: "address",
        name: "tokenA",
        type: "address",
      },
      {
        indexed: true,
        internalType: "address",
        name: "tokenB",
        type: "address",
      },
      {
        indexed: false,
        internalType: "uint256",
        name: "profit",
        type: "uint256",
      },
    ],
    name: "ArbitrageCompleted",
    type: "event",
  },
  {
    anonymous: false,
    inputs: [
      {
        indexed: true,
        internalType: "address",
        name: "asset",
        type: "address",
      },
      {
        indexed: false,
        internalType: "uint256",
        name: "amount",
        type: "uint256",
      },
      {
        indexed: false,
        internalType: "uint256",
        name: "profit",
        type: "uint256",
      },
    ],
    name: "FlashloanExecuted",
    type: "event",
  },
  {
    anonymous: false,
    inputs: [
      {
        indexed: true,
        internalType: "address",
        name: "previousOwner",
        type: "address",
      },
      {
        indexed: true,
        internalType: "address",
        name: "newOwner",
        type: "address",
      },
    ],
    name: "OwnershipTransferred",
    type: "event",
  },
  {
    inputs: [],
    name: "CHAIN_ID",
    outputs: [
      {
        internalType: "uint256",
        name: "",
        type: "uint256",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
  {
    inputs: [],
    name: "UNISWAP_V2_ROUTER",
    outputs: [
      {
        internalType: "address",
        name: "",
        type: "address",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
  {
    inputs: [],
    name: "UNISWAP_V3_ROUTER",
    outputs: [
      {
        internalType: "address",
        name: "",
        type: "address",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "contract IERC20",
        name: "token",
        type: "address",
      },
      {
        internalType: "uint256",
        name: "amount",
        type: "uint256",
      },
    ],
    name: "emergencyWithdraw",
    outputs: [],
    stateMutability: "nonpayable",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "contract IERC20[]",
        name: "tokens",
        type: "address[]",
      },
      {
        internalType: "uint256[]",
        name: "amounts",
        type: "uint256[]",
      },
      {
        internalType: "bytes",
        name: "userData",
        type: "bytes",
      },
    ],
    name: "executeFlashloanArbitrage",
    outputs: [],
    stateMutability: "nonpayable",
    type: "function",
  },
  {
    inputs: [],
    name: "getVault",
    outputs: [
      {
        internalType: "address",
        name: "",
        type: "address",
      },
    ],
    stateMutability: "pure",
    type: "function",
  },
  {
    inputs: [],
    name: "owner",
    outputs: [
      {
        internalType: "address",
        name: "",
        type: "address",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "contract IERC20[]",
        name: "tokens",
        type: "address[]",
      },
      {
        internalType: "uint256[]",
        name: "amounts",
        type: "uint256[]",
      },
      {
        internalType: "uint256[]",
        name: "feeAmounts",
        type: "uint256[]",
      },
      {
        internalType: "bytes",
        name: "userData",
        type: "bytes",
      },
    ],
    name: "receiveFlashLoan",
    outputs: [],
    stateMutability: "nonpayable",
    type: "function",
  },
  {
    inputs: [],
    name: "renounceOwnership",
    outputs: [],
    stateMutability: "nonpayable",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "newOwner",
        type: "address",
      },
    ],
    name: "transferOwnership",
    outputs: [],
    stateMutability: "nonpayable",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "contract IERC20",
        name: "token",
        type: "address",
      },
    ],
    name: "withdrawProfits",
    outputs: [],
    stateMutability: "nonpayable",
    type: "function",
  },
] as const;

const _bytecode =
  "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";

type BalancerFlashloanArbitrageConstructorParams =
  | [signer?: Signer]
  | ConstructorParameters<typeof ContractFactory>;

const isSuperArgs = (
  xs: BalancerFlashloanArbitrageConstructorParams
): xs is ConstructorParameters<typeof ContractFactory> => xs.length > 1;

export class BalancerFlashloanArbitrage__factory extends ContractFactory {
  constructor(...args: BalancerFlashloanArbitrageConstructorParams) {
    if (isSuperArgs(args)) {
      super(...args);
    } else {
      super(_abi, _bytecode, args[0]);
    }
  }

  override getDeployTransaction(
    overrides?: NonPayableOverrides & { from?: string }
  ): Promise<ContractDeployTransaction> {
    return super.getDeployTransaction(overrides || {});
  }
  override deploy(overrides?: NonPayableOverrides & { from?: string }) {
    return super.deploy(overrides || {}) as Promise<
      BalancerFlashloanArbitrage & {
        deploymentTransaction(): ContractTransactionResponse;
      }
    >;
  }
  override connect(
    runner: ContractRunner | null
  ): BalancerFlashloanArbitrage__factory {
    return super.connect(runner) as BalancerFlashloanArbitrage__factory;
  }

  static readonly bytecode = _bytecode;
  static readonly abi = _abi;
  static createInterface(): BalancerFlashloanArbitrageInterface {
    return new Interface(_abi) as BalancerFlashloanArbitrageInterface;
  }
  static connect(
    address: string,
    runner?: ContractRunner | null
  ): BalancerFlashloanArbitrage {
    return new Contract(
      address,
      _abi,
      runner
    ) as unknown as BalancerFlashloanArbitrage;
  }
}
