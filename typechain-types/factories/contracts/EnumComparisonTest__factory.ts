/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */
import {
  Contract,
  ContractFactory,
  ContractTransactionResponse,
  Interface,
} from "ethers";
import type { Signer, ContractDeployTransaction, ContractRunner } from "ethers";
import type { NonPayableOverrides } from "../../common";
import type {
  EnumComparisonTest,
  EnumComparisonTestInterface,
} from "../../contracts/EnumComparisonTest";

const _abi = [
  {
    inputs: [],
    stateMutability: "nonpayable",
    type: "constructor",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "owner",
        type: "address",
      },
    ],
    name: "OwnableInvalidOwner",
    type: "error",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "account",
        type: "address",
      },
    ],
    name: "OwnableUnauthorizedAccount",
    type: "error",
  },
  {
    anonymous: false,
    inputs: [
      {
        indexed: true,
        internalType: "address",
        name: "previousOwner",
        type: "address",
      },
      {
        indexed: true,
        internalType: "address",
        name: "newOwner",
        type: "address",
      },
    ],
    name: "OwnershipTransferred",
    type: "event",
  },
  {
    inputs: [],
    name: "owner",
    outputs: [
      {
        internalType: "address",
        name: "",
        type: "address",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
  {
    inputs: [],
    name: "renounceOwnership",
    outputs: [],
    stateMutability: "nonpayable",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "uint8",
        name: "providerValue",
        type: "uint8",
      },
    ],
    name: "testDirectEnum",
    outputs: [
      {
        internalType: "string",
        name: "",
        type: "string",
      },
    ],
    stateMutability: "pure",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "bytes",
        name: "params",
        type: "bytes",
      },
    ],
    name: "testEnumComparison",
    outputs: [
      {
        internalType: "string",
        name: "",
        type: "string",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "bytes",
        name: "params",
        type: "bytes",
      },
    ],
    name: "testEnumValue",
    outputs: [
      {
        internalType: "uint8",
        name: "",
        type: "uint8",
      },
    ],
    stateMutability: "pure",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "newOwner",
        type: "address",
      },
    ],
    name: "transferOwnership",
    outputs: [],
    stateMutability: "nonpayable",
    type: "function",
  },
] as const;

const _bytecode =
  "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";

type EnumComparisonTestConstructorParams =
  | [signer?: Signer]
  | ConstructorParameters<typeof ContractFactory>;

const isSuperArgs = (
  xs: EnumComparisonTestConstructorParams
): xs is ConstructorParameters<typeof ContractFactory> => xs.length > 1;

export class EnumComparisonTest__factory extends ContractFactory {
  constructor(...args: EnumComparisonTestConstructorParams) {
    if (isSuperArgs(args)) {
      super(...args);
    } else {
      super(_abi, _bytecode, args[0]);
    }
  }

  override getDeployTransaction(
    overrides?: NonPayableOverrides & { from?: string }
  ): Promise<ContractDeployTransaction> {
    return super.getDeployTransaction(overrides || {});
  }
  override deploy(overrides?: NonPayableOverrides & { from?: string }) {
    return super.deploy(overrides || {}) as Promise<
      EnumComparisonTest & {
        deploymentTransaction(): ContractTransactionResponse;
      }
    >;
  }
  override connect(runner: ContractRunner | null): EnumComparisonTest__factory {
    return super.connect(runner) as EnumComparisonTest__factory;
  }

  static readonly bytecode = _bytecode;
  static readonly abi = _abi;
  static createInterface(): EnumComparisonTestInterface {
    return new Interface(_abi) as EnumComparisonTestInterface;
  }
  static connect(
    address: string,
    runner?: ContractRunner | null
  ): EnumComparisonTest {
    return new Contract(address, _abi, runner) as unknown as EnumComparisonTest;
  }
}
