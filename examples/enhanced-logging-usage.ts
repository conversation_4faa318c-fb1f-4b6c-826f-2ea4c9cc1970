#!/usr/bin/env ts-node

/**
 * Example usage of enhanced logging with <PERSON> formatting
 * This shows how to integrate the new logging features into your MEV bot
 */

import { ethers } from 'ethers';
import { logger } from '../src/utils/logger';
import { ErrorHandler, handleInsufficientFunds, handleTransactionError } from '../src/utils/error-handler';
import { WeiFormatter, formatWei, formatWeiWithBoth } from '../src/utils/formatting/wei-formatter';

async function demonstrateEnhancedLogging() {
  console.log('🚀 Enhanced Logging Usage Examples with USD Values\n');

  // Wait for ETH price to be fetched
  console.log('📊 Fetching current ETH price for USD conversion...');
  await new Promise(resolve => setTimeout(resolve, 2000));

  // Example 1: Your original error message
  console.log('1️⃣ Original Error Message Formatting:');
  const originalError = new Error('insufficient funds for gas * price + value: have 3800073449395503 want 8989122000000000');
  logger.error('Transaction failed', originalError);

  console.log('\n' + '='.repeat(60) + '\n');

  // Example 2: Using the specific insufficient funds method
  console.log('2️⃣ Specific Insufficient Funds Logging:');
  await logger.insufficientFunds('3800073449395503', '8989122000000000', 'MEV arbitrage transaction');

  console.log('\n' + '='.repeat(60) + '\n');

  // Example 3: Gas estimation logging
  console.log('3️⃣ Gas Estimation Logging:');
  const gasLimit = '300000';
  const gasPrice = ethers.parseUnits('25', 'gwei'); // 25 gwei
  const totalCost = BigInt(gasLimit) * gasPrice;
  await logger.gasEstimation(gasLimit, gasPrice, totalCost);

  console.log('\n' + '='.repeat(60) + '\n');

  // Example 4: Using ErrorHandler for transaction errors
  console.log('4️⃣ Enhanced Error Handling:');
  try {
    // Simulate a transaction error
    throw new Error('insufficient funds for gas * price + value: have 1500000000000000000 want 2000000000000000000');
  } catch (error) {
    await ErrorHandler.handleTransactionError(error as Error, 'Flashloan execution');
  }
  
  console.log('\n' + '='.repeat(60) + '\n');

  // Example 5: Balance checking
  console.log('5️⃣ Balance Checking:');
  const walletBalance = '1500000000000000000'; // 1.5 ETH
  const requiredAmount = '2000000000000000000'; // 2 ETH
  
  const comparison = WeiFormatter.formatBalanceComparisonSync(walletBalance, requiredAmount);
  if (!comparison.sufficient) {
    logger.error('Insufficient balance for operation', {
      available: comparison.available,
      required: comparison.required,
      deficit: comparison.deficit
    });
  }
  
  console.log('\n' + '='.repeat(60) + '\n');

  // Example 6: Using withErrorHandling wrapper
  console.log('6️⃣ Error Handling Wrapper:');
  
  const riskyOperation = async () => {
    // Simulate a failing operation
    throw new Error('insufficient funds for gas * price + value: have 500000000000000000 want 1000000000000000000');
  };

  const result = await ErrorHandler.withErrorHandling(
    riskyOperation,
    'MEV sandwich attack',
    { retryCount: 2, retryDelay: 1000 }
  );
  
  if (!result) {
    logger.info('Operation failed after retries');
  }
  
  console.log('\n' + '='.repeat(60) + '\n');

  // Example 7: Formatting Wei values in custom messages
  console.log('7️⃣ Custom Message Formatting:');
  const customMessage = 'Transaction requires 1500000000000000000 wei but wallet only has 800000000000000000 wei available';
  const formattedMessage = WeiFormatter.formatWeiInText(customMessage);
  logger.info(formattedMessage);
  
  console.log('\n' + '='.repeat(60) + '\n');

  // Example 8: Utility functions for quick formatting
  console.log('8️⃣ Quick Formatting Utilities:');
  const weiAmount = '1234567890123456789';
  logger.info(`Quick ETH format: ${formatWei(weiAmount)} ETH`);
  const formattedWithUsd = await formatWeiWithBoth(weiAmount);
  logger.info(`With USD format: ${formattedWithUsd}`);

  console.log('\n✅ Enhanced logging demonstration completed!');
}

// Example of how to integrate into existing MEV bot code
async function exampleMEVBotIntegration() {
  console.log('\n🤖 MEV Bot Integration Example:\n');

  // Simulate checking wallet balance before executing MEV strategy
  const walletAddress = '******************************************';
  const requiredGas = ethers.parseUnits('0.01', 'ether'); // 0.01 ETH for gas
  
  try {
    // This would normally be: const balance = await provider.getBalance(walletAddress);
    const simulatedBalance = ethers.parseUnits('0.005', 'ether'); // 0.005 ETH (insufficient)
    
    if (simulatedBalance < requiredGas) {
      await logger.insufficientFunds(
        simulatedBalance.toString(),
        requiredGas.toString(),
        'MEV strategy execution'
      );
      return false;
    }
    
    // Proceed with MEV strategy...
    logger.info('✅ Sufficient balance for MEV execution');
    return true;
    
  } catch (error) {
    await ErrorHandler.handleTransactionError(error as Error, 'MEV balance check');
    return false;
  }
}

// Run examples if this file is executed directly
if (require.main === module) {
  demonstrateEnhancedLogging()
    .then(() => exampleMEVBotIntegration())
    .catch(console.error);
}

export { demonstrateEnhancedLogging, exampleMEVBotIntegration };
